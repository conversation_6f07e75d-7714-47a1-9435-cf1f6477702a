const { Party, CompteComptable, LigneEcriture, EcritureComptable } = require('../models');
const { Op } = require('sequelize');
const logger = require('../config/logger');

/**
 * Contrôleur pour la gestion des tiers (clients/fournisseurs)
 */
class PartyController {
  /**
   * Obtient la liste des tiers d'une société
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  static async getParties(req, res) {
    try {
      const { societeId } = req.params;
      const { 
        type, 
        actif = true, 
        search, 
        page = 1, 
        limit = 50,
        sortBy = 'nom',
        sortOrder = 'ASC'
      } = req.query;

      const offset = (page - 1) * limit;
      const whereClause = { societeId };

      // Filtrage par type
      if (type && ['CLIENT', 'FOURNISSEUR', 'CLIENT_FOURNISSEUR'].includes(type)) {
        whereClause.type = type;
      }

      // Filtrage par statut actif
      if (actif !== undefined) {
        whereClause.actif = actif === 'true';
      }

      // Recherche textuelle
      if (search) {
        whereClause[Op.or] = [
          { nom: { [Op.iLike]: `%${search}%` } },
          { code: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } }
        ];
      }

      const { count, rows: parties } = await Party.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: CompteComptable,
            as: 'compte',
            attributes: ['numero', 'intitule']
          }
        ],
        order: [[sortBy, sortOrder.toUpperCase()]],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      // Calcul des soldes pour chaque tiers
      const partiesAvecSoldes = await Promise.all(
        parties.map(async (party) => {
          const solde = await party.calculerSolde();
          return {
            ...party.formater(),
            solde: solde.solde,
            nombreOperations: solde.nombreOperations
          };
        })
      );

      res.json({
        success: true,
        data: {
          parties: partiesAvecSoldes,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            pages: Math.ceil(count / limit)
          }
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des tiers:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des tiers',
        error: error.message
      });
    }
  }

  /**
   * Obtient un tiers par son ID
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  static async getParty(req, res) {
    try {
      const { id } = req.params;

      const party = await Party.findByPk(id, {
        include: [
          {
            model: CompteComptable,
            as: 'compte',
            attributes: ['numero', 'intitule']
          }
        ]
      });

      if (!party) {
        return res.status(404).json({
          success: false,
          message: 'Tiers non trouvé'
        });
      }

      // Calcul du solde
      const solde = await party.calculerSolde();

      res.json({
        success: true,
        data: {
          ...party.formater(),
          solde: solde.solde,
          detailSolde: solde
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération du tiers:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du tiers',
        error: error.message
      });
    }
  }

  /**
   * Crée un nouveau tiers
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  static async createParty(req, res) {
    try {
      const { societeId } = req.params;
      const donneesParty = { ...req.body, societeId };

      // Validation des données
      const validation = Party.validatePartyData(donneesParty);
      if (!validation.valide) {
        return res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: validation.erreurs
        });
      }

      // Vérification de l'unicité du code dans la société
      const partyExistant = await Party.findOne({
        where: {
          societeId,
          code: donneesParty.code
        }
      });

      if (partyExistant) {
        return res.status(409).json({
          success: false,
          message: 'Un tiers avec ce code existe déjà dans cette société'
        });
      }

      // Génération automatique du compte comptable si non fourni
      if (!donneesParty.compteComptable) {
        const tempParty = Party.build(donneesParty);
        donneesParty.compteComptable = tempParty.genererCompteComptable();
      }

      // Vérification que le compte comptable existe
      if (donneesParty.compteComptable) {
        const compte = await CompteComptable.findOne({
          where: {
            numero: donneesParty.compteComptable,
            societeId
          }
        });

        if (!compte) {
          return res.status(400).json({
            success: false,
            message: `Le compte comptable ${donneesParty.compteComptable} n'existe pas`
          });
        }
      }

      const party = await Party.create(donneesParty);

      logger.info(`Tiers créé: ${party.code} - ${party.nom}`, {
        partyId: party.id,
        societeId,
        userId: req.user?.id
      });

      res.status(201).json({
        success: true,
        message: 'Tiers créé avec succès',
        data: party.formater()
      });

    } catch (error) {
      logger.error('Erreur lors de la création du tiers:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la création du tiers',
        error: error.message
      });
    }
  }

  /**
   * Met à jour un tiers
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  static async updateParty(req, res) {
    try {
      const { id } = req.params;
      const donneesParty = req.body;

      const party = await Party.findByPk(id);
      if (!party) {
        return res.status(404).json({
          success: false,
          message: 'Tiers non trouvé'
        });
      }

      // Validation des données
      const validation = Party.validatePartyData({ ...party.toJSON(), ...donneesParty });
      if (!validation.valide) {
        return res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: validation.erreurs
        });
      }

      // Vérification de l'unicité du code si modifié
      if (donneesParty.code && donneesParty.code !== party.code) {
        const partyExistant = await Party.findOne({
          where: {
            societeId: party.societeId,
            code: donneesParty.code,
            id: { [Op.ne]: id }
          }
        });

        if (partyExistant) {
          return res.status(409).json({
            success: false,
            message: 'Un tiers avec ce code existe déjà dans cette société'
          });
        }
      }

      // Vérification du compte comptable si modifié
      if (donneesParty.compteComptable && donneesParty.compteComptable !== party.compteComptable) {
        const compte = await CompteComptable.findOne({
          where: {
            numero: donneesParty.compteComptable,
            societeId: party.societeId
          }
        });

        if (!compte) {
          return res.status(400).json({
            success: false,
            message: `Le compte comptable ${donneesParty.compteComptable} n'existe pas`
          });
        }
      }

      await party.update(donneesParty);

      logger.info(`Tiers modifié: ${party.code} - ${party.nom}`, {
        partyId: party.id,
        societeId: party.societeId,
        userId: req.user?.id
      });

      res.json({
        success: true,
        message: 'Tiers modifié avec succès',
        data: party.formater()
      });

    } catch (error) {
      logger.error('Erreur lors de la modification du tiers:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la modification du tiers',
        error: error.message
      });
    }
  }

  /**
   * Supprime un tiers
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  static async deleteParty(req, res) {
    try {
      const { id } = req.params;

      const party = await Party.findByPk(id);
      if (!party) {
        return res.status(404).json({
          success: false,
          message: 'Tiers non trouvé'
        });
      }

      // Vérification qu'il n'y a pas d'écritures liées
      const lignesEcriture = await LigneEcriture.count({
        where: { tiersId: id }
      });

      if (lignesEcriture > 0) {
        return res.status(409).json({
          success: false,
          message: 'Impossible de supprimer ce tiers car il est utilisé dans des écritures comptables'
        });
      }

      await party.destroy();

      logger.info(`Tiers supprimé: ${party.code} - ${party.nom}`, {
        partyId: party.id,
        societeId: party.societeId,
        userId: req.user?.id
      });

      res.json({
        success: true,
        message: 'Tiers supprimé avec succès'
      });

    } catch (error) {
      logger.error('Erreur lors de la suppression du tiers:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression du tiers',
        error: error.message
      });
    }
  }

  /**
   * Obtient la liste des clients d'une société
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  static async getClients(req, res) {
    try {
      const { societeId } = req.params;
      const clients = await Party.getClients(societeId);

      const clientsAvecSoldes = await Promise.all(
        clients.map(async (client) => {
          const solde = await client.calculerSolde();
          return {
            ...client.formater(),
            solde: solde.solde
          };
        })
      );

      res.json({
        success: true,
        data: clientsAvecSoldes
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des clients:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des clients',
        error: error.message
      });
    }
  }

  /**
   * Obtient la liste des fournisseurs d'une société
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  static async getFournisseurs(req, res) {
    try {
      const { societeId } = req.params;
      const fournisseurs = await Party.getFournisseurs(societeId);

      const fournisseursAvecSoldes = await Promise.all(
        fournisseurs.map(async (fournisseur) => {
          const solde = await fournisseur.calculerSolde();
          return {
            ...fournisseur.formater(),
            solde: solde.solde
          };
        })
      );

      res.json({
        success: true,
        data: fournisseursAvecSoldes
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des fournisseurs:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des fournisseurs',
        error: error.message
      });
    }
  }

  /**
   * Calcule le solde d'un tiers
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  static async getBalance(req, res) {
    try {
      const { id } = req.params;
      const { dateFinale } = req.query;

      const party = await Party.findByPk(id);
      if (!party) {
        return res.status(404).json({
          success: false,
          message: 'Tiers non trouvé'
        });
      }

      const dateCalcul = dateFinale ? new Date(dateFinale) : null;
      const solde = await party.calculerSolde(dateCalcul);

      res.json({
        success: true,
        data: {
          tiersId: party.id,
          tiersNom: party.nom,
          dateCalcul: dateCalcul || new Date(),
          ...solde
        }
      });

    } catch (error) {
      logger.error('Erreur lors du calcul du solde du tiers:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors du calcul du solde du tiers',
        error: error.message
      });
    }
  }

  /**
   * Recherche des tiers
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  static async searchParties(req, res) {
    try {
      const { societeId } = req.params;
      const { q: terme, type } = req.query;

      if (!terme || terme.length < 2) {
        return res.status(400).json({
          success: false,
          message: 'Le terme de recherche doit contenir au moins 2 caractères'
        });
      }

      const parties = await Party.rechercher(societeId, terme, type);

      res.json({
        success: true,
        data: parties.map(party => party.formater())
      });

    } catch (error) {
      logger.error('Erreur lors de la recherche de tiers:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la recherche de tiers',
        error: error.message
      });
    }
  }
}

module.exports = PartyController;