'use strict';

const { Op } = require('sequelize');
const { logger } = require('../config/logger');
const ClotureService = require('../services/clotureService');
const { 
  AppError, 
  ValidationError, 
  NotFoundError,
  ConflictError 
} = require('../middleware/errorHandler');

/**
 * Controller pour la gestion des exercices comptables
 */
class ExerciceController {
  constructor(models) {
    this.models = models;
    this.clotureService = new ClotureService(models);
  }

  /**
   * Liste tous les exercices avec pagination et filtres
   */
  async getAll(req, res, next) {
    try {
      const {
        page = 1,
        limit = 10,
        societeId,
        statut,
        annee,
        search
      } = req.query;

      const offset = (page - 1) * limit;
      const whereClause = {};

      // Filtres
      if (societeId) {
        whereClause.societeId = societeId;
      }

      if (statut) {
        whereClause.statut = statut;
      }

      if (annee) {
        whereClause[Op.and] = [
          this.models.sequelize.where(
            this.models.sequelize.fn('EXTRACT', this.models.sequelize.literal('YEAR FROM date_debut')),
            annee
          )
        ];
      }

      if (search) {
        whereClause.libelle = {
          [Op.iLike]: `%${search}%`
        };
      }

      const { count, rows: exercices } = await this.models.ExerciceComptable.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: this.models.Societe,
            as: 'societe',
            attributes: ['id', 'nom']
          },
          {
            model: this.models.ExerciceComptable,
            as: 'exercicePrecedent',
            attributes: ['id', 'libelle', 'dateFin']
          }
        ],
        order: [['dateDebut', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      // Ajouter des informations calculées
      const exercicesAvecInfos = await Promise.all(
        exercices.map(async (exercice) => {
          const exerciceJson = exercice.toJSON();
          exerciceJson.dureeJours = exercice.getDureeExercice();
          exerciceJson.estCourant = exercice.isExerciceCourant();
          
          // Statistiques rapides si demandées
          if (req.query.includeStats === 'true') {
            exerciceJson.statistiques = await this.clotureService.getStatistiquesExercice(exercice.id);
          }

          return exerciceJson;
        })
      );

      res.json({
        success: true,
        data: exercicesAvecInfos,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des exercices', {
        error: error.message,
        query: req.query
      });
      next(error);
    }
  }

  /**
   * Récupère un exercice par son ID
   */
  async getById(req, res, next) {
    try {
      const { id } = req.params;

      const exercice = await this.models.ExerciceComptable.findByPk(id, {
        include: [
          {
            model: this.models.Societe,
            as: 'societe'
          },
          {
            model: this.models.ExerciceComptable,
            as: 'exercicePrecedent'
          },
          {
            model: this.models.ExerciceComptable,
            as: 'exercicesSuivants'
          }
        ]
      });

      if (!exercice) {
        throw new NotFoundError('Exercice comptable');
      }

      const exerciceJson = exercice.toJSON();
      exerciceJson.dureeJours = exercice.getDureeExercice();
      exerciceJson.estCourant = exercice.isExerciceCourant();
      exerciceJson.peutEtreCloture = await exercice.canBeClosed();

      // Statistiques détaillées
      exerciceJson.statistiques = await this.clotureService.getStatistiquesExercice(id);

      res.json({
        success: true,
        data: exerciceJson
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'exercice', {
        exerciceId: req.params.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Crée un nouvel exercice comptable
   */
  async create(req, res, next) {
    try {
      const {
        societeId,
        libelle,
        dateDebut,
        dateFin,
        exercicePrecedentId,
        reportANouveau = 0
      } = req.body;

      // Vérifier que la société existe
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Créer l'exercice
      const exercice = await this.models.ExerciceComptable.create({
        societeId,
        libelle,
        dateDebut,
        dateFin,
        exercicePrecedentId,
        reportANouveau
      });

      // Récupérer l'exercice créé avec ses relations
      const exerciceComplet = await this.models.ExerciceComptable.findByPk(exercice.id, {
        include: [
          {
            model: this.models.Societe,
            as: 'societe',
            attributes: ['id', 'nom']
          },
          {
            model: this.models.ExerciceComptable,
            as: 'exercicePrecedent',
            attributes: ['id', 'libelle']
          }
        ]
      });

      logger.info('Exercice comptable créé', {
        exerciceId: exercice.id,
        societeId,
        libelle
      });

      res.status(201).json({
        success: true,
        data: exerciceComplet,
        message: 'Exercice comptable créé avec succès'
      });

    } catch (error) {
      logger.error('Erreur lors de la création de l\'exercice', {
        body: req.body,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Met à jour un exercice comptable
   */
  async update(req, res, next) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const exercice = await this.models.ExerciceComptable.findByPk(id);
      if (!exercice) {
        throw new NotFoundError('Exercice comptable');
      }

      // Vérifier que l'exercice peut être modifié
      if (exercice.statut === 'CLOTURE') {
        throw new ConflictError('Impossible de modifier un exercice clôturé');
      }

      if (exercice.statut === 'ARCHIVE') {
        throw new ConflictError('Impossible de modifier un exercice archivé');
      }

      // Mettre à jour
      await exercice.update(updateData);

      // Récupérer l'exercice mis à jour
      const exerciceMisAJour = await this.models.ExerciceComptable.findByPk(id, {
        include: [
          {
            model: this.models.Societe,
            as: 'societe',
            attributes: ['id', 'nom']
          }
        ]
      });

      logger.info('Exercice comptable mis à jour', {
        exerciceId: id,
        updateData
      });

      res.json({
        success: true,
        data: exerciceMisAJour,
        message: 'Exercice comptable mis à jour avec succès'
      });

    } catch (error) {
      logger.error('Erreur lors de la mise à jour de l\'exercice', {
        exerciceId: req.params.id,
        body: req.body,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Supprime un exercice comptable
   */
  async delete(req, res, next) {
    try {
      const { id } = req.params;

      const exercice = await this.models.ExerciceComptable.findByPk(id);
      if (!exercice) {
        throw new NotFoundError('Exercice comptable');
      }

      // Vérifier que l'exercice peut être supprimé
      if (exercice.statut === 'CLOTURE') {
        throw new ConflictError('Impossible de supprimer un exercice clôturé');
      }

      // Vérifier qu'il n'y a pas d'écritures
      const nombreEcritures = await this.models.EcritureComptable.count({
        where: { exerciceId: id }
      });

      if (nombreEcritures > 0) {
        throw new ConflictError(`Impossible de supprimer l'exercice : ${nombreEcritures} écriture(s) associée(s)`);
      }

      await exercice.destroy();

      logger.info('Exercice comptable supprimé', {
        exerciceId: id
      });

      res.json({
        success: true,
        message: 'Exercice comptable supprimé avec succès'
      });

    } catch (error) {
      logger.error('Erreur lors de la suppression de l\'exercice', {
        exerciceId: req.params.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Vérifie les conditions de clôture d'un exercice
   */
  async verifierCloture(req, res, next) {
    try {
      const { id } = req.params;

      const conditions = await this.clotureService.verifierConditionsCloture(id);

      res.json({
        success: true,
        data: conditions
      });

    } catch (error) {
      logger.error('Erreur lors de la vérification des conditions de clôture', {
        exerciceId: req.params.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Clôture un exercice comptable
   */
  async cloturer(req, res, next) {
    try {
      const { id } = req.params;
      const { commentaire } = req.body;
      const utilisateurId = req.user?.id || 'system'; // À adapter selon votre système d'auth

      const resultat = await this.clotureService.cloturerExercice(id, utilisateurId, commentaire);

      res.json({
        success: true,
        data: resultat,
        message: 'Exercice clôturé avec succès'
      });

    } catch (error) {
      logger.error('Erreur lors de la clôture de l\'exercice', {
        exerciceId: req.params.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Obtient l'exercice courant d'une société
   */
  async getExerciceCourant(req, res, next) {
    try {
      const { societeId } = req.params;

      const exerciceCourant = await this.models.ExerciceComptable.findOne({
        where: {
          societeId,
          statut: 'OUVERT'
        },
        include: [
          {
            model: this.models.Societe,
            as: 'societe',
            attributes: ['id', 'nom']
          }
        ],
        order: [['dateDebut', 'DESC']]
      });

      if (!exerciceCourant) {
        return res.json({
          success: true,
          data: null,
          message: 'Aucun exercice ouvert trouvé pour cette société'
        });
      }

      const exerciceJson = exerciceCourant.toJSON();
      exerciceJson.dureeJours = exerciceCourant.getDureeExercice();
      exerciceJson.estCourant = exerciceCourant.isExerciceCourant();

      res.json({
        success: true,
        data: exerciceJson
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'exercice courant', {
        societeId: req.params.societeId,
        error: error.message
      });
      next(error);
    }
  }
}

module.exports = ExerciceController;
