'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Journaux comptables par défaut selon les normes SYSCOHADA
    const journaux = [
      {
        code: 'AC',
        libelle: 'Journal des Achats',
        type: 'ACHAT',
        compte_contropartie: '401' // Fournisseurs
      },
      {
        code: 'VT',
        libelle: 'Journal des Ventes',
        type: 'VENTE',
        compte_contropartie: '411' // Clients
      },
      {
        code: 'BQ',
        libelle: 'Journal de Banque',
        type: 'BANQUE',
        compte_contropartie: '521' // Banques locales
      },
      {
        code: 'CA',
        libelle: 'Journal de Caisse',
        type: 'CAISSE',
        compte_contropartie: '571' // Caisse siège social
      },
      {
        code: 'OD',
        libelle: 'Journal des Opérations Diverses',
        type: 'OD',
        compte_contropartie: null // Pas de compte de contrepartie fixe
      },
      {
        code: 'BQ2',
        libelle: 'Journal Banque Secondaire',
        type: 'BANQUE',
        compte_contropartie: '522' // Banques étrangères
      },
      {
        code: 'CA2',
        libelle: 'Journal Caisse Succursale A',
        type: 'CAISSE',
        compte_contropartie: '572' // Caisse succursale A
      }
    ];

    await queryInterface.bulkInsert('journaux', journaux.map(journal => ({
      ...journal,
      created_at: new Date(),
      updated_at: new Date()
    })));
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('journaux', null, {});
  }
};
