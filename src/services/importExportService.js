'use strict';

const XLSX = require('xlsx');
const csv = require('csv-parser');
const fs = require('fs');
const path = require('path');
const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Service pour l'import/export du plan comptable et des écritures
 */
class ImportExportService {
  constructor(models) {
    this.models = models || require('../models');
  }

  /**
   * Importe un plan comptable depuis un fichier Excel/CSV
   * @param {string} societeId - ID de la société
   * @param {string} filePath - Chemin vers le fichier
   * @param {string} utilisateurId - ID de l'utilisateur
   * @param {Object} options - Options d'import
   * @returns {Object} Résultat de l'import
   */
  async importerPlanComptable(societeId, filePath, utilisateurId, options = {}) {
    const transaction = await this.models.sequelize.transaction();
    
    try {
      const { 
        remplacerExistants = false, 
        validerUniquement = false,
        formatFichier = 'auto'
      } = options;

      // Vérifier que la société existe
      const societe = await this.models.Societe.findByPk(societeId, { transaction });
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Lire le fichier selon son format
      const donnees = await this.lireFichierImport(filePath, formatFichier);
      
      // Valider les données
      const validationResult = await this.validerDonneesImport(donnees, societeId);
      
      if (!validationResult.valide) {
        if (validerUniquement) {
          return {
            success: false,
            validation: validationResult,
            message: 'Validation échouée'
          };
        } else {
          throw new ValidationError('Données d\'import invalides', validationResult.erreurs);
        }
      }

      if (validerUniquement) {
        return {
          success: true,
          validation: validationResult,
          message: 'Validation réussie'
        };
      }

      // Traiter l'import
      const resultats = {
        comptesTraites: 0,
        comptesCreés: 0,
        comptesMisAJour: 0,
        comptesIgnorés: 0,
        erreurs: []
      };

      for (const ligneData of validationResult.donneesValides) {
        try {
          const compteExistant = await this.models.CompteComptable.findOne({
            where: { numero: ligneData.numero, societeId },
            transaction
          });

          if (compteExistant) {
            if (remplacerExistants && compteExistant.isCompteModifiable()) {
              // Mettre à jour le compte existant
              await compteExistant.update({
                libelle: ligneData.libelle,
                nature: ligneData.nature,
                sens: ligneData.sens,
                raisonSociale: ligneData.raisonSociale,
                actif: ligneData.actif !== undefined ? ligneData.actif : true,
                obligatoireLettrage: ligneData.obligatoireLettrage || false,
                typeAnalytique: ligneData.typeAnalytique || 'AUCUN'
              }, { transaction });
              
              resultats.comptesMisAJour++;
            } else {
              resultats.comptesIgnorés++;
            }
          } else {
            // Créer un nouveau compte
            await this.models.CompteComptable.create({
              numero: ligneData.numero,
              libelle: ligneData.libelle,
              classe: parseInt(ligneData.numero.charAt(0)),
              nature: ligneData.nature,
              sens: ligneData.sens,
              niveau: ligneData.numero.length,
              compteParent: ligneData.compteParent,
              societeId,
              personnalise: true,
              modifiable: true,
              dateCreation: new Date(),
              utilisateurCreation: utilisateurId,
              raisonSociale: ligneData.raisonSociale,
              actif: ligneData.actif !== undefined ? ligneData.actif : true,
              obligatoireLettrage: ligneData.obligatoireLettrage || false,
              typeAnalytique: ligneData.typeAnalytique || 'AUCUN'
            }, { transaction });
            
            resultats.comptesCreés++;
          }
          
          resultats.comptesTraites++;
          
        } catch (error) {
          resultats.erreurs.push({
            ligne: ligneData.numeroLigne,
            numero: ligneData.numero,
            erreur: error.message
          });
        }
      }

      await transaction.commit();

      logger.info('Import plan comptable terminé', {
        societeId,
        utilisateurId,
        resultats
      });

      return {
        success: true,
        resultats,
        validation: validationResult,
        message: `Import terminé: ${resultats.comptesCreés} créés, ${resultats.comptesMisAJour} mis à jour`
      };

    } catch (error) {
      await transaction.rollback();
      
      logger.error('Erreur lors de l\'import du plan comptable', {
        societeId,
        filePath,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Exporte le plan comptable vers Excel/CSV
   * @param {string} societeId - ID de la société
   * @param {string} format - Format d'export ('excel', 'csv')
   * @param {Object} options - Options d'export
   * @returns {Object} Informations sur le fichier exporté
   */
  async exporterPlanComptable(societeId, format = 'excel', options = {}) {
    try {
      const {
        includeInactifs = false,
        seulementPersonnalises = false,
        classes = null
      } = options;

      // Construire la requête
      const whereClause = { societeId };
      
      if (!includeInactifs) {
        whereClause.actif = true;
      }
      
      if (seulementPersonnalises) {
        whereClause.personnalise = true;
      }
      
      if (classes && classes.length > 0) {
        whereClause.classe = { [this.models.sequelize.Op.in]: classes };
      }

      // Récupérer les comptes
      const comptes = await this.models.CompteComptable.findAll({
        where: whereClause,
        order: [['numero', 'ASC']],
        include: [{
          model: this.models.Societe,
          as: 'societe',
          attributes: ['nom']
        }]
      });

      // Préparer les données pour l'export
      const donneesExport = comptes.map(compte => ({
        'Numéro': compte.numero,
        'Libellé': compte.libelle,
        'Classe': compte.classe,
        'Nature': compte.nature,
        'Sens': compte.sens,
        'Niveau': compte.niveau,
        'Compte Parent': compte.compteParent || '',
        'Raison Sociale': compte.raisonSociale || '',
        'Actif': compte.actif ? 'Oui' : 'Non',
        'Personnalisé': compte.personnalise ? 'Oui' : 'Non',
        'Modifiable': compte.modifiable ? 'Oui' : 'Non',
        'Lettrage Obligatoire': compte.obligatoireLettrage ? 'Oui' : 'Non',
        'Type Analytique': compte.typeAnalytique,
        'Date Création': compte.dateCreation ? compte.dateCreation.toISOString().split('T')[0] : ''
      }));

      // Générer le fichier
      const nomFichier = `plan_comptable_${societeId}_${Date.now()}`;
      const cheminFichier = path.join(process.cwd(), 'temp', `${nomFichier}.${format === 'excel' ? 'xlsx' : 'csv'}`);

      // Créer le dossier temp s'il n'existe pas
      const dossierTemp = path.dirname(cheminFichier);
      if (!fs.existsSync(dossierTemp)) {
        fs.mkdirSync(dossierTemp, { recursive: true });
      }

      if (format === 'excel') {
        // Export Excel
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(donneesExport);
        
        // Ajuster la largeur des colonnes
        const colWidths = [
          { wch: 10 }, // Numéro
          { wch: 40 }, // Libellé
          { wch: 8 },  // Classe
          { wch: 12 }, // Nature
          { wch: 8 },  // Sens
          { wch: 8 },  // Niveau
          { wch: 15 }, // Compte Parent
          { wch: 30 }, // Raison Sociale
          { wch: 8 },  // Actif
          { wch: 12 }, // Personnalisé
          { wch: 12 }, // Modifiable
          { wch: 15 }, // Lettrage
          { wch: 15 }, // Type Analytique
          { wch: 12 }  // Date Création
        ];
        worksheet['!cols'] = colWidths;
        
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Plan Comptable');
        XLSX.writeFile(workbook, cheminFichier);
        
      } else {
        // Export CSV
        const csvContent = [
          Object.keys(donneesExport[0]).join(';'),
          ...donneesExport.map(ligne => Object.values(ligne).join(';'))
        ].join('\n');
        
        fs.writeFileSync(cheminFichier, csvContent, 'utf8');
      }

      logger.info('Export plan comptable terminé', {
        societeId,
        format,
        nombreComptes: comptes.length,
        cheminFichier
      });

      return {
        success: true,
        cheminFichier,
        nomFichier: path.basename(cheminFichier),
        nombreComptes: comptes.length,
        tailleFichier: fs.statSync(cheminFichier).size,
        format
      };

    } catch (error) {
      logger.error('Erreur lors de l\'export du plan comptable', {
        societeId,
        format,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Lit un fichier d'import (Excel ou CSV)
   * @param {string} filePath - Chemin vers le fichier
   * @param {string} format - Format du fichier
   * @returns {Array} Données lues
   */
  async lireFichierImport(filePath, format = 'auto') {
    try {
      const extension = path.extname(filePath).toLowerCase();
      const formatDetecte = format === 'auto' ? 
        (extension === '.xlsx' || extension === '.xls' ? 'excel' : 'csv') : 
        format;

      if (formatDetecte === 'excel') {
        // Lecture Excel
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        return XLSX.utils.sheet_to_json(worksheet);
        
      } else {
        // Lecture CSV
        return new Promise((resolve, reject) => {
          const donnees = [];
          fs.createReadStream(filePath)
            .pipe(csv({ separator: ';' }))
            .on('data', (ligne) => donnees.push(ligne))
            .on('end', () => resolve(donnees))
            .on('error', reject);
        });
      }
      
    } catch (error) {
      logger.error('Erreur lors de la lecture du fichier', {
        filePath,
        format,
        error: error.message
      });
      throw new AppError('Impossible de lire le fichier d\'import');
    }
  }

  /**
   * Valide les données d'import
   * @param {Array} donnees - Données à valider
   * @param {string} societeId - ID de la société
   * @returns {Object} Résultat de la validation
   */
  async validerDonneesImport(donnees, societeId) {
    const erreurs = [];
    const avertissements = [];
    const donneesValides = [];

    // Colonnes obligatoires
    const colonnesObligatoires = ['Numéro', 'Libellé', 'Nature', 'Sens'];

    for (let i = 0; i < donnees.length; i++) {
      const ligne = donnees[i];
      const numeroLigne = i + 2; // +2 car ligne 1 = en-têtes
      const erreursLigne = [];

      // Vérifier les colonnes obligatoires
      for (const colonne of colonnesObligatoires) {
        if (!ligne[colonne] || ligne[colonne].toString().trim() === '') {
          erreursLigne.push(`Colonne '${colonne}' manquante ou vide`);
        }
      }

      if (erreursLigne.length > 0) {
        erreurs.push({
          ligne: numeroLigne,
          erreurs: erreursLigne
        });
        continue;
      }

      // Validation du numéro de compte
      const numero = ligne['Numéro'].toString().trim();
      try {
        this.models.CompteComptable.validateNumeroSYCOHADA(numero);
      } catch (error) {
        erreursLigne.push(`Numéro de compte invalide: ${error.message}`);
      }

      // Validation de la nature
      const nature = ligne['Nature'].toString().trim().toUpperCase();
      if (!['DETAIL', 'TOTAL'].includes(nature)) {
        erreursLigne.push('Nature doit être DETAIL ou TOTAL');
      }

      // Validation du sens
      const sens = ligne['Sens'].toString().trim().toUpperCase();
      if (!['DEBITEUR', 'CREDITEUR'].includes(sens)) {
        erreursLigne.push('Sens doit être DEBITEUR ou CREDITEUR');
      }

      if (erreursLigne.length > 0) {
        erreurs.push({
          ligne: numeroLigne,
          numero,
          erreurs: erreursLigne
        });
      } else {
        donneesValides.push({
          numeroLigne,
          numero,
          libelle: ligne['Libellé'].toString().trim(),
          nature,
          sens,
          compteParent: ligne['Compte Parent'] ? ligne['Compte Parent'].toString().trim() : null,
          raisonSociale: ligne['Raison Sociale'] ? ligne['Raison Sociale'].toString().trim() : null,
          actif: ligne['Actif'] ? ligne['Actif'].toString().toLowerCase() === 'oui' : true,
          obligatoireLettrage: ligne['Lettrage Obligatoire'] ? ligne['Lettrage Obligatoire'].toString().toLowerCase() === 'oui' : false,
          typeAnalytique: ligne['Type Analytique'] ? ligne['Type Analytique'].toString().trim() : 'AUCUN'
        });
      }
    }

    return {
      valide: erreurs.length === 0,
      erreurs,
      avertissements,
      donneesValides,
      nombreLignesTraitees: donnees.length,
      nombreLignesValides: donneesValides.length
    };
  }

  /**
   * Génère un template Excel pour l'import
   * @returns {Object} Informations sur le template généré
   */
  async genererTemplateImport() {
    try {
      const template = [
        {
          'Numéro': '4111001',
          'Libellé': 'Client ABC SARL',
          'Classe': '4',
          'Nature': 'DETAIL',
          'Sens': 'DEBITEUR',
          'Niveau': '7',
          'Compte Parent': '411',
          'Raison Sociale': 'ABC SARL',
          'Actif': 'Oui',
          'Personnalisé': 'Oui',
          'Modifiable': 'Oui',
          'Lettrage Obligatoire': 'Oui',
          'Type Analytique': 'AUCUN',
          'Date Création': ''
        },
        {
          'Numéro': '4011001',
          'Libellé': 'Fournisseur XYZ SA',
          'Classe': '4',
          'Nature': 'DETAIL',
          'Sens': 'CREDITEUR',
          'Niveau': '7',
          'Compte Parent': '401',
          'Raison Sociale': 'XYZ SA',
          'Actif': 'Oui',
          'Personnalisé': 'Oui',
          'Modifiable': 'Oui',
          'Lettrage Obligatoire': 'Oui',
          'Type Analytique': 'CENTRE_COUT',
          'Date Création': ''
        }
      ];

      const nomFichier = `template_import_plan_comptable_${Date.now()}.xlsx`;
      const cheminFichier = path.join(process.cwd(), 'temp', nomFichier);

      // Créer le dossier temp s'il n'existe pas
      const dossierTemp = path.dirname(cheminFichier);
      if (!fs.existsSync(dossierTemp)) {
        fs.mkdirSync(dossierTemp, { recursive: true });
      }

      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(template);
      
      // Ajuster la largeur des colonnes
      const colWidths = [
        { wch: 10 }, { wch: 40 }, { wch: 8 }, { wch: 12 }, { wch: 8 },
        { wch: 8 }, { wch: 15 }, { wch: 30 }, { wch: 8 }, { wch: 12 },
        { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 12 }
      ];
      worksheet['!cols'] = colWidths;
      
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Template Import');
      XLSX.writeFile(workbook, cheminFichier);

      return {
        success: true,
        cheminFichier,
        nomFichier,
        tailleFichier: fs.statSync(cheminFichier).size
      };

    } catch (error) {
      logger.error('Erreur lors de la génération du template', {
        error: error.message
      });
      throw error;
    }
  }

  // ==========================================
  // MÉTHODES IMPORT/EXPORT ÉCRITURES COMPTABLES
  // ==========================================

  /**
   * Importe des écritures comptables depuis un fichier Excel
   * @param {string} filePath - Chemin vers le fichier Excel
   * @param {Object} options - Options d'import
   * @returns {Object} Résultat de l'import
   */
  async importerEcrituresExcel(filePath, options = {}) {
    const transaction = await this.models.sequelize.transaction();

    try {
      const {
        societeId,
        exerciceId,
        remplacerExistants = false,
        validerUniquement = false,
        validerEcritures = true
      } = options;

      // Vérifier que la société existe
      const societe = await this.models.Societe.findByPk(societeId, { transaction });
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Lire le fichier Excel
      const donnees = await this.lireFichierEcrituresExcel(filePath);

      // Valider les données
      const validationResult = await this.validerDonneesEcritures(donnees, societeId, exerciceId);

      if (!validationResult.valide) {
        if (validerUniquement) {
          await transaction.rollback();
          return {
            success: false,
            validation: validationResult,
            message: 'Validation échouée'
          };
        } else {
          throw new ValidationError('Données d\'import invalides', validationResult.erreurs);
        }
      }

      if (validerUniquement) {
        await transaction.rollback();
        return {
          success: true,
          validation: validationResult,
          message: 'Validation réussie'
        };
      }

      // Importer les écritures
      const resultatsImport = await this.traiterImportEcritures(
        validationResult.donneesValides,
        societeId,
        exerciceId,
        { remplacerExistants, validerEcritures },
        transaction
      );

      await transaction.commit();

      logger.info('Import écritures Excel terminé', {
        societeId,
        exerciceId,
        nombreEcritures: resultatsImport.ecrituresCreees,
        nombreLignes: resultatsImport.lignesCreees
      });

      return {
        success: true,
        ...resultatsImport,
        message: `${resultatsImport.ecrituresCreees} écritures importées avec succès`
      };

    } catch (error) {
      await transaction.rollback();
      logger.error('Erreur lors de l\'import Excel des écritures', {
        filePath,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Importe des écritures comptables depuis un fichier CSV
   * @param {string} filePath - Chemin vers le fichier CSV
   * @param {Object} options - Options d'import
   * @returns {Object} Résultat de l'import
   */
  async importerEcrituresCSV(filePath, options = {}) {
    const transaction = await this.models.sequelize.transaction();

    try {
      const {
        societeId,
        exerciceId,
        remplacerExistants = false,
        validerUniquement = false,
        validerEcritures = true,
        separateur = ';'
      } = options;

      // Vérifier que la société existe
      const societe = await this.models.Societe.findByPk(societeId, { transaction });
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Lire le fichier CSV
      const donnees = await this.lireFichierEcrituresCSV(filePath, separateur);

      // Valider les données
      const validationResult = await this.validerDonneesEcritures(donnees, societeId, exerciceId);

      if (!validationResult.valide) {
        if (validerUniquement) {
          await transaction.rollback();
          return {
            success: false,
            validation: validationResult,
            message: 'Validation échouée'
          };
        } else {
          throw new ValidationError('Données d\'import invalides', validationResult.erreurs);
        }
      }

      if (validerUniquement) {
        await transaction.rollback();
        return {
          success: true,
          validation: validationResult,
          message: 'Validation réussie'
        };
      }

      // Importer les écritures
      const resultatsImport = await this.traiterImportEcritures(
        validationResult.donneesValides,
        societeId,
        exerciceId,
        { remplacerExistants, validerEcritures },
        transaction
      );

      await transaction.commit();

      logger.info('Import écritures CSV terminé', {
        societeId,
        exerciceId,
        nombreEcritures: resultatsImport.ecrituresCreees,
        nombreLignes: resultatsImport.lignesCreees
      });

      return {
        success: true,
        ...resultatsImport,
        message: `${resultatsImport.ecrituresCreees} écritures importées avec succès`
      };

    } catch (error) {
      await transaction.rollback();
      logger.error('Erreur lors de l\'import CSV des écritures', {
        filePath,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Exporte des écritures comptables vers Excel
   * @param {Object} filtres - Filtres pour sélectionner les écritures
   * @param {Object} options - Options d'export
   * @returns {Object} Informations sur le fichier exporté
   */
  async exporterEcrituresExcel(filtres = {}, options = {}) {
    try {
      const {
        includeDetails = true,
        includeStatistiques = false,
        formatDate = 'DD/MM/YYYY'
      } = options;

      // Récupérer les écritures selon les filtres
      const ecritures = await this.obtenirEcrituresPourExport(filtres);

      if (ecritures.length === 0) {
        throw new ValidationError('Aucune écriture trouvée avec les critères spécifiés');
      }

      // Préparer les données pour l'export
      const donneesExport = await this.preparerDonneesExportEcritures(ecritures, { includeDetails, formatDate });

      // Créer le fichier Excel
      const nomFichier = `export_ecritures_${Date.now()}.xlsx`;
      const cheminFichier = path.join(process.cwd(), 'temp', nomFichier);

      // Créer le dossier temp s'il n'existe pas
      const dossierTemp = path.dirname(cheminFichier);
      if (!fs.existsSync(dossierTemp)) {
        fs.mkdirSync(dossierTemp, { recursive: true });
      }

      const workbook = XLSX.utils.book_new();

      // Feuille principale des écritures
      const worksheetEcritures = XLSX.utils.json_to_sheet(donneesExport.ecritures);

      // Ajuster la largeur des colonnes
      const colWidths = [
        { wch: 15 }, // Numéro Écriture
        { wch: 12 }, // Date
        { wch: 10 }, // Journal
        { wch: 40 }, // Libellé
        { wch: 15 }, // Référence
        { wch: 12 }, // Statut
        { wch: 15 }, // Total Débit
        { wch: 15 }, // Total Crédit
        { wch: 20 }, // Pièce Justificative
        { wch: 15 }  // Exercice
      ];
      worksheetEcritures['!cols'] = colWidths;

      XLSX.utils.book_append_sheet(workbook, worksheetEcritures, 'Écritures');

      // Feuille des lignes d'écriture si demandée
      if (includeDetails && donneesExport.lignes) {
        const worksheetLignes = XLSX.utils.json_to_sheet(donneesExport.lignes);

        const colWidthsLignes = [
          { wch: 15 }, // Numéro Écriture
          { wch: 12 }, // Date
          { wch: 10 }, // Compte
          { wch: 30 }, // Libellé Compte
          { wch: 40 }, // Libellé Ligne
          { wch: 15 }, // Débit
          { wch: 15 }, // Crédit
          { wch: 10 }, // Lettrage
          { wch: 15 }  // Référence
        ];
        worksheetLignes['!cols'] = colWidthsLignes;

        XLSX.utils.book_append_sheet(workbook, worksheetLignes, 'Lignes');
      }

      // Feuille des statistiques si demandée
      if (includeStatistiques && donneesExport.statistiques) {
        const worksheetStats = XLSX.utils.json_to_sheet(donneesExport.statistiques);
        XLSX.utils.book_append_sheet(workbook, worksheetStats, 'Statistiques');
      }

      XLSX.writeFile(workbook, cheminFichier);

      logger.info('Export écritures Excel terminé', {
        filtres,
        nombreEcritures: ecritures.length,
        cheminFichier
      });

      return {
        success: true,
        cheminFichier,
        nomFichier: path.basename(cheminFichier),
        nombreEcritures: ecritures.length,
        tailleFichier: fs.statSync(cheminFichier).size,
        format: 'excel'
      };

    } catch (error) {
      logger.error('Erreur lors de l\'export Excel des écritures', {
        filtres,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Exporte des écritures comptables vers CSV
   * @param {Object} filtres - Filtres pour sélectionner les écritures
   * @param {Object} options - Options d'export
   * @returns {Object} Informations sur le fichier exporté
   */
  async exporterEcrituresCSV(filtres = {}, options = {}) {
    try {
      const {
        separateur = ';',
        includeDetails = false,
        formatDate = 'DD/MM/YYYY'
      } = options;

      // Récupérer les écritures selon les filtres
      const ecritures = await this.obtenirEcrituresPourExport(filtres);

      if (ecritures.length === 0) {
        throw new ValidationError('Aucune écriture trouvée avec les critères spécifiés');
      }

      // Préparer les données pour l'export
      const donneesExport = await this.preparerDonneesExportEcritures(ecritures, { includeDetails, formatDate });

      // Créer le fichier CSV
      const nomFichier = `export_ecritures_${Date.now()}.csv`;
      const cheminFichier = path.join(process.cwd(), 'temp', nomFichier);

      // Créer le dossier temp s'il n'existe pas
      const dossierTemp = path.dirname(cheminFichier);
      if (!fs.existsSync(dossierTemp)) {
        fs.mkdirSync(dossierTemp, { recursive: true });
      }

      // Générer le contenu CSV
      const donnees = includeDetails ? donneesExport.lignes : donneesExport.ecritures;
      const csvContent = [
        Object.keys(donnees[0]).join(separateur),
        ...donnees.map(ligne => Object.values(ligne).map(val =>
          typeof val === 'string' && val.includes(separateur) ? `"${val}"` : val
        ).join(separateur))
      ].join('\n');

      fs.writeFileSync(cheminFichier, csvContent, 'utf8');

      logger.info('Export écritures CSV terminé', {
        filtres,
        nombreEcritures: ecritures.length,
        cheminFichier
      });

      return {
        success: true,
        cheminFichier,
        nomFichier: path.basename(cheminFichier),
        nombreEcritures: ecritures.length,
        tailleFichier: fs.statSync(cheminFichier).size,
        format: 'csv'
      };

    } catch (error) {
      logger.error('Erreur lors de l\'export CSV des écritures', {
        filtres,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Exporte le Fichier des Écritures Comptables (FEC) pour un exercice
   * @param {string} exerciceId - ID de l'exercice comptable
   * @param {Object} options - Options d'export
   * @returns {Object} Informations sur le fichier FEC
   */
  async exporterFEC(exerciceId, options = {}) {
    try {
      const {
        separateur = '|',
        encodage = 'utf8',
        includeEnTetes = true
      } = options;

      // Vérifier que l'exercice existe
      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId, {
        include: [{ model: this.models.Societe, as: 'societe' }]
      });

      if (!exercice) {
        throw new NotFoundError('Exercice comptable');
      }

      // Récupérer toutes les écritures validées de l'exercice
      const ecritures = await this.models.EcritureComptable.findAll({
        where: {
          exerciceId,
          statut: 'VALIDEE'
        },
        include: [
          {
            model: this.models.LigneEcriture,
            as: 'lignes',
            include: [
              {
                model: this.models.CompteComptable,
                as: 'compte'
              }
            ]
          },
          {
            model: this.models.Journal,
            as: 'journal'
          }
        ],
        order: [
          ['dateEcriture', 'ASC'],
          ['numeroEcriture', 'ASC'],
          [{ model: this.models.LigneEcriture, as: 'lignes' }, 'id', 'ASC']
        ]
      });

      if (ecritures.length === 0) {
        throw new ValidationError('Aucune écriture validée trouvée pour cet exercice');
      }

      // Préparer les données FEC
      const donneesFEC = await this.preparerDonneesFEC(ecritures, exercice);

      // Créer le fichier FEC
      const nomFichier = `FEC_${exercice.societe.siret || 'SOCIETE'}_${exercice.annee}.txt`;
      const cheminFichier = path.join(process.cwd(), 'temp', nomFichier);

      // Créer le dossier temp s'il n'existe pas
      const dossierTemp = path.dirname(cheminFichier);
      if (!fs.existsSync(dossierTemp)) {
        fs.mkdirSync(dossierTemp, { recursive: true });
      }

      // Générer le contenu FEC
      let contenuFEC = '';

      if (includeEnTetes) {
        // En-têtes FEC selon la norme
        const entetes = [
          'JournalCode', 'JournalLib', 'EcritureNum', 'EcritureDate',
          'CompteNum', 'CompteLib', 'CompAuxNum', 'CompAuxLib',
          'PieceRef', 'PieceDate', 'EcritureLib', 'Debit', 'Credit',
          'EcritureLet', 'DateLet', 'ValidDate', 'Montantdevise', 'Idevise'
        ];
        contenuFEC += entetes.join(separateur) + '\n';
      }

      // Ajouter les lignes de données
      donneesFEC.forEach(ligne => {
        contenuFEC += Object.values(ligne).join(separateur) + '\n';
      });

      fs.writeFileSync(cheminFichier, contenuFEC, encodage);

      logger.info('Export FEC terminé', {
        exerciceId,
        nombreEcritures: ecritures.length,
        nombreLignes: donneesFEC.length,
        cheminFichier
      });

      return {
        success: true,
        cheminFichier,
        nomFichier: path.basename(cheminFichier),
        nombreEcritures: ecritures.length,
        nombreLignes: donneesFEC.length,
        tailleFichier: fs.statSync(cheminFichier).size,
        format: 'fec',
        exercice: {
          id: exercice.id,
          annee: exercice.annee,
          societe: exercice.societe.raisonSociale
        }
      };

    } catch (error) {
      logger.error('Erreur lors de l\'export FEC', {
        exerciceId,
        error: error.message
      });
      throw error;
    }
  }

  // ==========================================
  // MÉTHODES UTILITAIRES POUR ÉCRITURES
  // ==========================================

  /**
   * Lit un fichier Excel d'écritures comptables
   * @param {string} filePath - Chemin vers le fichier
   * @returns {Array} Données lues
   */
  async lireFichierEcrituresExcel(filePath) {
    try {
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const donnees = XLSX.utils.sheet_to_json(worksheet);

      if (donnees.length === 0) {
        throw new ValidationError('Le fichier Excel est vide ou ne contient pas de données');
      }

      return donnees;

    } catch (error) {
      logger.error('Erreur lors de la lecture du fichier Excel', {
        filePath,
        error: error.message
      });
      throw new ValidationError(`Impossible de lire le fichier Excel: ${error.message}`);
    }
  }

  /**
   * Lit un fichier CSV d'écritures comptables
   * @param {string} filePath - Chemin vers le fichier
   * @param {string} separateur - Séparateur CSV
   * @returns {Array} Données lues
   */
  async lireFichierEcrituresCSV(filePath, separateur = ';') {
    return new Promise((resolve, reject) => {
      const donnees = [];
      const csvParser = require('csv-parser');

      fs.createReadStream(filePath)
        .pipe(csvParser({ separator: separateur }))
        .on('data', (row) => {
          donnees.push(row);
        })
        .on('end', () => {
          if (donnees.length === 0) {
            reject(new ValidationError('Le fichier CSV est vide ou ne contient pas de données'));
          } else {
            resolve(donnees);
          }
        })
        .on('error', (error) => {
          logger.error('Erreur lors de la lecture du fichier CSV', {
            filePath,
            error: error.message
          });
          reject(new ValidationError(`Impossible de lire le fichier CSV: ${error.message}`));
        });
    });
  }

  /**
   * Valide les données d'import d'écritures
   * @param {Array} donnees - Données à valider
   * @param {string} societeId - ID de la société
   * @param {string} exerciceId - ID de l'exercice
   * @returns {Object} Résultat de validation
   */
  async validerDonneesEcritures(donnees, societeId, exerciceId) {
    const erreurs = [];
    const donneesValides = [];
    const ecrituresGroupees = new Map();

    // Colonnes obligatoires pour les écritures
    const colonnesObligatoires = [
      'Numéro Écriture', 'Date Écriture', 'Journal', 'Libellé Écriture',
      'Compte', 'Libellé Ligne', 'Débit', 'Crédit'
    ];

    for (let i = 0; i < donnees.length; i++) {
      const ligne = donnees[i];
      const numeroLigne = i + 2; // +2 car ligne 1 = en-têtes
      const erreursLigne = [];

      // Vérifier les colonnes obligatoires
      for (const colonne of colonnesObligatoires) {
        if (!ligne[colonne] || ligne[colonne].toString().trim() === '') {
          erreursLigne.push(`Colonne '${colonne}' manquante ou vide`);
        }
      }

      if (erreursLigne.length > 0) {
        erreurs.push({
          ligne: numeroLigne,
          erreurs: erreursLigne
        });
        continue;
      }

      // Validation des données
      const numeroEcriture = ligne['Numéro Écriture'].toString().trim();
      const dateEcriture = ligne['Date Écriture'];
      const journalCode = ligne['Journal'].toString().trim();
      const compteNumero = ligne['Compte'].toString().trim();
      const debit = parseFloat(ligne['Débit'] || 0);
      const credit = parseFloat(ligne['Crédit'] || 0);

      // Validation du numéro d'écriture
      if (numeroEcriture.length > 20) {
        erreursLigne.push('Numéro d\'écriture trop long (max 20 caractères)');
      }

      // Validation de la date
      let dateEcritureValide;
      try {
        dateEcritureValide = new Date(dateEcriture);
        if (isNaN(dateEcritureValide.getTime())) {
          throw new Error('Date invalide');
        }
      } catch (error) {
        erreursLigne.push('Date d\'écriture invalide');
      }

      // Validation des montants
      if (debit < 0 || credit < 0) {
        erreursLigne.push('Les montants débit et crédit doivent être positifs');
      }

      if (debit > 0 && credit > 0) {
        erreursLigne.push('Une ligne ne peut pas avoir à la fois un débit et un crédit');
      }

      if (debit === 0 && credit === 0) {
        erreursLigne.push('Une ligne doit avoir soit un débit soit un crédit');
      }

      // Validation du compte comptable
      try {
        await this.models.CompteComptable.findOne({
          where: { numero: compteNumero, societeId }
        });
      } catch (error) {
        erreursLigne.push(`Compte comptable ${compteNumero} introuvable`);
      }

      if (erreursLigne.length > 0) {
        erreurs.push({
          ligne: numeroLigne,
          numeroEcriture,
          erreurs: erreursLigne
        });
      } else {
        // Grouper les lignes par écriture
        if (!ecrituresGroupees.has(numeroEcriture)) {
          ecrituresGroupees.set(numeroEcriture, {
            numeroEcriture,
            dateEcriture: dateEcritureValide,
            journalCode,
            libelle: ligne['Libellé Écriture'].toString().trim(),
            reference: ligne['Référence'] ? ligne['Référence'].toString().trim() : null,
            pieceJustificative: ligne['Pièce Justificative'] ? ligne['Pièce Justificative'].toString().trim() : null,
            lignes: []
          });
        }

        ecrituresGroupees.get(numeroEcriture).lignes.push({
          compteNumero,
          libelle: ligne['Libellé Ligne'].toString().trim(),
          debit,
          credit,
          reference: ligne['Référence Ligne'] ? ligne['Référence Ligne'].toString().trim() : null
        });
      }
    }

    // Valider l'équilibre de chaque écriture
    for (const [numeroEcriture, ecriture] of ecrituresGroupees) {
      const totalDebit = ecriture.lignes.reduce((sum, ligne) => sum + ligne.debit, 0);
      const totalCredit = ecriture.lignes.reduce((sum, ligne) => sum + ligne.credit, 0);

      if (Math.abs(totalDebit - totalCredit) > 0.01) {
        erreurs.push({
          numeroEcriture,
          erreurs: [`Écriture non équilibrée: Débit ${totalDebit} ≠ Crédit ${totalCredit}`]
        });
      } else {
        donneesValides.push(ecriture);
      }
    }

    return {
      valide: erreurs.length === 0,
      erreurs,
      donneesValides,
      nombreLignesTraitees: donnees.length,
      nombreEcrituresValides: donneesValides.length
    };
  }

  /**
   * Traite l'import des écritures validées
   * @param {Array} ecritures - Écritures validées à importer
   * @param {string} societeId - ID de la société
   * @param {string} exerciceId - ID de l'exercice
   * @param {Object} options - Options de traitement
   * @param {Object} transaction - Transaction Sequelize
   * @returns {Object} Résultats de l'import
   */
  async traiterImportEcritures(ecritures, societeId, exerciceId, options, transaction) {
    const { remplacerExistants, validerEcritures } = options;
    let ecrituresCreees = 0;
    let lignesCreees = 0;
    const erreursImport = [];

    for (const ecritureData of ecritures) {
      try {
        // Vérifier si l'écriture existe déjà
        const ecritureExistante = await this.models.EcritureComptable.findOne({
          where: { numeroEcriture: ecritureData.numeroEcriture, societeId },
          transaction
        });

        if (ecritureExistante && !remplacerExistants) {
          erreursImport.push({
            numeroEcriture: ecritureData.numeroEcriture,
            erreur: 'Écriture déjà existante'
          });
          continue;
        }

        if (ecritureExistante && remplacerExistants) {
          // Supprimer l'ancienne écriture et ses lignes
          await this.models.LigneEcriture.destroy({
            where: { ecritureId: ecritureExistante.id },
            transaction
          });
          await ecritureExistante.destroy({ transaction });
        }

        // Créer la nouvelle écriture
        const nouvelleEcriture = await this.models.EcritureComptable.create({
          numeroEcriture: ecritureData.numeroEcriture,
          dateEcriture: ecritureData.dateEcriture,
          journalCode: ecritureData.journalCode,
          libelle: ecritureData.libelle,
          reference: ecritureData.reference,
          pieceJustificative: ecritureData.pieceJustificative,
          exerciceId,
          societeId,
          statut: validerEcritures ? 'VALIDEE' : 'BROUILLARD'
        }, { transaction });

        // Créer les lignes d'écriture
        for (const ligneData of ecritureData.lignes) {
          await this.models.LigneEcriture.create({
            ecritureId: nouvelleEcriture.id,
            compteNumero: ligneData.compteNumero,
            libelle: ligneData.libelle,
            debit: ligneData.debit,
            credit: ligneData.credit,
            reference: ligneData.reference
          }, { transaction });
          lignesCreees++;
        }

        ecrituresCreees++;

      } catch (error) {
        erreursImport.push({
          numeroEcriture: ecritureData.numeroEcriture,
          erreur: error.message
        });
      }
    }

    return {
      ecrituresCreees,
      lignesCreees,
      erreursImport,
      nombreEcrituresTraitees: ecritures.length
    };
  }

  /**
   * Obtient les écritures pour export selon les filtres
   * @param {Object} filtres - Filtres de sélection
   * @returns {Array} Écritures trouvées
   */
  async obtenirEcrituresPourExport(filtres) {
    const whereClause = {};

    if (filtres.societeId) whereClause.societeId = filtres.societeId;
    if (filtres.exerciceId) whereClause.exerciceId = filtres.exerciceId;
    if (filtres.journalCode) whereClause.journalCode = filtres.journalCode;
    if (filtres.statut) whereClause.statut = filtres.statut;

    if (filtres.dateDebut && filtres.dateFin) {
      whereClause.dateEcriture = {
        [this.models.sequelize.Op.between]: [filtres.dateDebut, filtres.dateFin]
      };
    }

    return await this.models.EcritureComptable.findAll({
      where: whereClause,
      include: [
        {
          model: this.models.LigneEcriture,
          as: 'lignes',
          include: [
            {
              model: this.models.CompteComptable,
              as: 'compte'
            }
          ]
        },
        {
          model: this.models.Journal,
          as: 'journal'
        },
        {
          model: this.models.ExerciceComptable,
          as: 'exercice'
        }
      ],
      order: [
        ['dateEcriture', 'ASC'],
        ['numeroEcriture', 'ASC']
      ]
    });
  }

  /**
   * Prépare les données d'écritures pour l'export
   * @param {Array} ecritures - Écritures à exporter
   * @param {Object} options - Options de formatage
   * @returns {Object} Données formatées
   */
  async preparerDonneesExportEcritures(ecritures, options = {}) {
    const { includeDetails, formatDate } = options;
    const donneesEcritures = [];
    const donneesLignes = [];
    const statistiques = [];

    for (const ecriture of ecritures) {
      // Calculer les totaux
      const totalDebit = ecriture.lignes.reduce((sum, ligne) => sum + parseFloat(ligne.debit || 0), 0);
      const totalCredit = ecriture.lignes.reduce((sum, ligne) => sum + parseFloat(ligne.credit || 0), 0);

      // Données de l'écriture
      donneesEcritures.push({
        'Numéro Écriture': ecriture.numeroEcriture,
        'Date': this.formaterDate(ecriture.dateEcriture, formatDate),
        'Journal': ecriture.journalCode,
        'Libellé': ecriture.libelle,
        'Référence': ecriture.reference || '',
        'Statut': ecriture.statut,
        'Total Débit': totalDebit.toFixed(2),
        'Total Crédit': totalCredit.toFixed(2),
        'Pièce Justificative': ecriture.pieceJustificative || '',
        'Exercice': ecriture.exercice ? ecriture.exercice.annee : ''
      });

      // Données des lignes si demandées
      if (includeDetails) {
        for (const ligne of ecriture.lignes) {
          donneesLignes.push({
            'Numéro Écriture': ecriture.numeroEcriture,
            'Date': this.formaterDate(ecriture.dateEcriture, formatDate),
            'Compte': ligne.compteNumero,
            'Libellé Compte': ligne.compte ? ligne.compte.libelle : '',
            'Libellé Ligne': ligne.libelle,
            'Débit': parseFloat(ligne.debit || 0).toFixed(2),
            'Crédit': parseFloat(ligne.credit || 0).toFixed(2),
            'Lettrage': ligne.lettrage || '',
            'Référence': ligne.reference || ''
          });
        }
      }
    }

    // Statistiques
    const totalEcritures = ecritures.length;
    const totalLignes = ecritures.reduce((sum, e) => sum + e.lignes.length, 0);
    const totalDebitGeneral = ecritures.reduce((sum, e) =>
      sum + e.lignes.reduce((s, l) => s + parseFloat(l.debit || 0), 0), 0);
    const totalCreditGeneral = ecritures.reduce((sum, e) =>
      sum + e.lignes.reduce((s, l) => s + parseFloat(l.credit || 0), 0), 0);

    statistiques.push({
      'Indicateur': 'Nombre d\'écritures',
      'Valeur': totalEcritures
    });
    statistiques.push({
      'Indicateur': 'Nombre de lignes',
      'Valeur': totalLignes
    });
    statistiques.push({
      'Indicateur': 'Total Débit',
      'Valeur': totalDebitGeneral.toFixed(2)
    });
    statistiques.push({
      'Indicateur': 'Total Crédit',
      'Valeur': totalCreditGeneral.toFixed(2)
    });

    return {
      ecritures: donneesEcritures,
      lignes: donneesLignes,
      statistiques
    };
  }

  /**
   * Prépare les données pour l'export FEC
   * @param {Array} ecritures - Écritures à exporter
   * @param {Object} exercice - Exercice comptable
   * @returns {Array} Données FEC formatées
   */
  async preparerDonneesFEC(ecritures, exercice) {
    const donneesFEC = [];

    for (const ecriture of ecritures) {
      for (const ligne of ecriture.lignes) {
        donneesFEC.push({
          JournalCode: ecriture.journalCode,
          JournalLib: ecriture.journal ? ecriture.journal.libelle : '',
          EcritureNum: ecriture.numeroEcriture,
          EcritureDate: this.formaterDateFEC(ecriture.dateEcriture),
          CompteNum: ligne.compteNumero,
          CompteLib: ligne.compte ? ligne.compte.libelle : '',
          CompAuxNum: '', // Compte auxiliaire (non implémenté)
          CompAuxLib: '', // Libellé compte auxiliaire
          PieceRef: ecriture.pieceJustificative || '',
          PieceDate: this.formaterDateFEC(ecriture.dateEcriture),
          EcritureLib: ligne.libelle,
          Debit: parseFloat(ligne.debit || 0).toFixed(2),
          Credit: parseFloat(ligne.credit || 0).toFixed(2),
          EcritureLet: ligne.lettrage || '',
          DateLet: ligne.dateLettrage ? this.formaterDateFEC(ligne.dateLettrage) : '',
          ValidDate: ecriture.dateValidation ? this.formaterDateFEC(ecriture.dateValidation) : '',
          Montantdevise: '', // Montant en devise (non implémenté)
          Idevise: '' // Code devise (non implémenté)
        });
      }
    }

    return donneesFEC;
  }

  /**
   * Formate une date selon le format spécifié
   * @param {Date} date - Date à formater
   * @param {string} format - Format de sortie
   * @returns {string} Date formatée
   */
  formaterDate(date, format = 'DD/MM/YYYY') {
    if (!date) return '';

    const d = new Date(date);
    const jour = d.getDate().toString().padStart(2, '0');
    const mois = (d.getMonth() + 1).toString().padStart(2, '0');
    const annee = d.getFullYear();

    switch (format) {
      case 'DD/MM/YYYY':
        return `${jour}/${mois}/${annee}`;
      case 'YYYY-MM-DD':
        return `${annee}-${mois}-${jour}`;
      case 'YYYYMMDD':
        return `${annee}${mois}${jour}`;
      default:
        return `${jour}/${mois}/${annee}`;
    }
  }

  /**
   * Formate une date pour le FEC (format YYYYMMDD)
   * @param {Date} date - Date à formater
   * @returns {string} Date formatée pour FEC
   */
  formaterDateFEC(date) {
    return this.formaterDate(date, 'YYYYMMDD');
  }
}

module.exports = ImportExportService;
