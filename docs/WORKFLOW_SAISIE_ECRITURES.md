## Importation d'Écritures

### Description

Cette section détaille le processus d'importation d'écritures comptables à partir de fichiers externes (Excel, CSV) ou d'autres systèmes, permettant d'automatiser la saisie de volumes importants d'écritures.

### Interface Utilisateur

#### Écran d'Importation

```
┌─────────────────────────────────────────────────────┐
│ Importation d'Écritures                             │
├─────────────────────────────────────────────────────┤
│                                                     │
│  Société: SARL AFRICAN BUSINESS                     │
│  Exercice: Exercice 2025 (01/01/2025 - 31/12/2025)  │
│                                                     │
│  Source d'Import                                    │
│  ┌─────────────────────────────────────────────────┐│
│  │ (•) Fichier Excel                               ││
│  │ ( ) Fichier CSV                                 ││
│  │ ( ) Autre système comptable                     ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  Fichier                                            │
│  ┌─────────────────────────────────────────────────┐│
│  │ [Sélectionner un fichier...]                    ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  Options                                            │
│  ┌─────────────────────────────────────────────────┐│
│  │ Journal par défaut                              ││
│  │ ┌─────────────────────────────────────────────┐ ││
│  │ │ OD - Opérations Diverses                    │ ││
│  │ └─────────────────────────────────────────────┘ ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────────────────────────────────────────┐│
│  │ [x] Valider avant import                        ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────────────────────────────────────────┐│
│  │ [x] Créer en brouillard                         ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────────────────────────────────────────┐│
│  │ [ ] Ignorer les erreurs non bloquantes          ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────┐    ┌──────────────┐                │
│  │   Annuler   │    │   Importer   │                │
│  └─────────────┘    └──────────────┘                │
└─────────────────────────────────────────────────────┘
```

#### Mapping des Colonnes

```
┌─────────────────────────────────────────────────────┐
│ Mapping des Colonnes                                │
├─────────────────────────────────────────────────────┤
│                                                     │
│  Fichier: ecritures_import.xlsx                     │
│  Feuille: Écritures                                 │
│  Lignes détectées: 45                               │
│                                                     │
│  Correspondance des Colonnes                        │
│  ┌─────────────────────────────────────────────────┐│
│  │ Champ Système │ Colonne Fichier │ Exemple       ││
│  ├─────────────────────────────────────────────────┤│
│  │ Date *        │ A - Date        │ 07/08/2025    ││
│  ├─────────────────────────────────────────────────┤│
│  │ Journal *     │ B - Journal     │ AC            ││
│  ├─────────────────────────────────────────────────┤│
│  │ Libellé *     │ C - Libellé     │ Achat fourn.  ││
│  ├─────────────────────────────────────────────────┤│
│  │ Référence     │ D - Référence   │ FACT-123      ││
│  ├─────────────────────────────────────────────────┤│
│  │ Compte *      │ E - Compte      │ 601100        ││
│  ├─────────────────────────────────────────────────┤│
│  │ Libellé ligne │ F - Détail      │ Achat papier  ││
│  ├─────────────────────────────────────────────────┤│
│  │ Débit *       │ G - Débit       │ 100000        ││
│  ├─────────────────────────────────────────────────┤│
│  │ Crédit *      │ H - Crédit      │ 0             ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────┐    ┌──────────────┐                │
│  │   Retour    │    │   Continuer  │                │
│  └─────────────┘    └──────────────┘                │
└─────────────────────────────────────────────────────┘
```

#### Validation et Résultats

```
┌─────────────────────────────────────────────────────┐
│ Résultats de l'Importation                          │
├─────────────────────────────────────────────────────┤
│                                                     │
│  Résumé                                             │
│  ┌─────────────────────────────────────────────────┐│
│  │ Total lignes traitées: 45                       ││
│  │ Écritures créées: 15                            ││
│  │ Écritures avec erreurs: 2                       ││
│  │ Lignes ignorées: 0                              ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  Détail des Erreurs                                 │
│  ┌─────────────────────────────────────────────────┐│
│  │ Ligne │ Erreur                                  ││
│  ├─────────────────────────────────────────────────┤│
│  │ 23    │ Le compte 999999 n'existe pas           ││
│  ├─────────────────────────────────────────────────┤│
│  │ 37-39 │ L'écriture n'est pas équilibrée         ││
│  │       │ (Débit: 5000, Crédit: 4500)             ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  Actions                                            │
│  ┌─────────────────────────┐ ┌─────────────────────┐│
│  │ 📋 Exporter rapport     │ │ 🔍 Voir écritures   ││
│  └─────────────────────────┘ └─────────────────────┘│
│                                                     │
│  ┌─────────────────────────────────────────────────┐│
│  │ [ ] Valider automatiquement les écritures       ││
│  │     importées sans erreur                       ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────┐    ┌──────────────┐                │
│  │   Fermer    │    │    Valider   │                │
│  └─────────────┘    └──────────────┘                │
└─────────────────────────────────────────────────────┘
```

### Format de Fichier

#### Structure Excel Standard

| Colonne | Contenu | Obligatoire | Format | Description |
|---------|---------|-------------|--------|-------------|
| A | Date | Oui | JJ/MM/AAAA | Date de l'écriture |
| B | Journal | Oui | Code 2 car. | Code du journal |
| C | Libellé | Oui | Texte | Libellé général |
| D | Référence | Non | Texte | Référence externe |
| E | Compte | Oui | Numéro | Numéro de compte |
| F | Libellé ligne | Non | Texte | Libellé de la ligne |
| G | Débit | Oui si H=0 | Nombre | Montant au débit |
| H | Crédit | Oui si G=0 | Nombre | Montant au crédit |

#### Exemple de Fichier CSV

```csv
Date;Journal;Libelle;Reference;Compte;LibelleLigne;Debit;Credit
07/08/2025;AC;Achat fournitures;FACT-123;601100;Achat papier;100000;0
07/08/2025;AC;Achat fournitures;FACT-123;445200;TVA déductible;18000;0
07/08/2025;AC;Achat fournitures;FACT-123;401000;Fournisseur;0;118000
08/08/2025;VT;Vente marchandises;VT-456;411000;Client;236000;0
08/08/2025;VT;Vente marchandises;VT-456;701000;Ventes;0;200000
08/08/2025;VT;Vente marchandises;VT-456;443100;TVA collectée;0;36000
```

### Interactions API

#### Importation de Fichier

```http
POST /api/v1/import/ecritures
Content-Type: multipart/form-data

{
  "societeId": "uuid-societe",
  "exerciceId": "uuid-exercice",
  "fichier": [binary-file-data],
  "options": {
    "format": "EXCEL",
    "journalDefaut": "OD",
    "validerAvantImport": true,
    "creerEnBrouillard": true,
    "ignorerErreursNonBloquantes": false
  }
}
```

#### Réponse Succès

```http
Status: 200 OK
{
  "success": true,
  "message": "Importation terminée avec 15 écritures créées et 2 erreurs",
  "data": {
    "totalLignes": 45,
    "ecrituresCreees": 15,
    "ecrituresAvecErreurs": 2,
    "lignesIgnorees": 0,
    "erreurs": [
      {
        "ligne": 23,
        "message": "Le compte 999999 n'existe pas"
      },
      {
        "lignes": [37, 38, 39],
        "message": "L'écriture n'est pas équilibrée (Débit: 5000, Crédit: 4500)"
      }
    ],
    "ecritures": [
      {
        "id": "uuid-ecriture-1",
        "numeroProvisoire": "AC-BROUILLARD-2025-45",
        "dateEcriture": "2025-08-07",
        "libelle": "Achat fournitures",
        "totalDebit": 118000,
        "totalCredit": 118000
      },
      // Autres écritures...
    ]
  }
}
```

### Gestion des Erreurs

| Code | Message | Action Utilisateur |
|------|---------|-------------------|
| 400 | "Format de fichier non supporté" | Utiliser Excel ou CSV |
| 400 | "Colonnes obligatoires manquantes" | Vérifier le format |
| 400 | "Erreurs de validation dans le fichier" | Corriger les erreurs |
| 413 | "Fichier trop volumineux" | Diviser en fichiers plus petits |

### Considérations Spéciales

- Possibilité de sauvegarder des modèles de mapping pour réutilisation
- Prévisualisation des données avant importation
- Validation en temps réel pendant le mapping
- Option pour corriger les erreurs directement dans l'interface
- Importation programmée pour les fichiers récurrents

---

## Considérations Techniques

### Architecture Frontend-Backend

La mise en œuvre de ce workflow de saisie d'écritures repose sur une architecture robuste :

1. **Frontend** :
   - Composants React optimisés pour la saisie rapide
   - Validation en temps réel des données saisies
   - Gestion d'état avec Redux pour maintenir la cohérence
   - Cache local pour les données fréquemment utilisées

2. **Backend** :
   - API RESTful pour toutes les opérations
   - Validation côté serveur pour garantir l'intégrité
   - Transactions pour assurer la cohérence des données
   - Système de verrouillage optimiste pour éviter les conflits

### Optimisations de Performance

- **Saisie rapide** avec autocomplétion et suggestions
- **Calculs automatiques** pour accélérer la saisie
- **Templates préchargés** pour les opérations fréquentes
- **Validation asynchrone** pour ne pas bloquer l'interface
- **Pagination** pour gérer de grands volumes d'écritures

### Sécurité

- **Contrôle d'accès** basé sur les rôles pour chaque action
- **Validation des données** côté serveur pour prévenir les injections
- **Journalisation** de toutes les actions de saisie
- **Vérification des permissions** avant chaque opération sensible

### Extensibilité

Le workflow est conçu pour être extensible :

- **Hooks personnalisés** pour ajouter des validations spécifiques
- **Système de plugins** pour étendre les fonctionnalités
- **API documentée** pour intégration avec d'autres systèmes
- **Architecture événementielle** pour réagir aux changements

---

## Annexes

### A. Modèle de Données

```mermaid
erDiagram
    ECRITURE {
        uuid id
        uuid societeId
        uuid exerciceId
        string journalCode
        date dateEcriture
        string libelle
        string reference
        string statut
        string numeroProvisoire
        string numeroDefinitif
        decimal totalDebit
        decimal totalCredit
    }
    LIGNE_ECRITURE {
        uuid id
        uuid ecritureId
        string compteNumero
        string libelle
        decimal montantDebit
        decimal montantCredit
        string centreCout
    }
    TEMPLATE {
        uuid id
        uuid societeId
        string nom
        string description
        string categorie
        string journalCode
    }
    PARAMETRE_TEMPLATE {
        uuid id
        uuid templateId
        string nom
        string type
        boolean obligatoire
        string valeurDefaut
    }
    LIGNE_TEMPLATE {
        uuid id
        uuid templateId
        string compteNumero
        string libelle
        string formuleMontant
        string sens
    }
    
    ECRITURE ||--o{ LIGNE_ECRITURE : "contient"
    TEMPLATE ||--o{ PARAMETRE_TEMPLATE : "définit"
    TEMPLATE ||--o{ LIGNE_TEMPLATE : "contient"
```

### B. Exemples de Templates

#### Template Achat avec TVA

```json
{
  "nom": "Achat avec TVA",
  "description": "Achat standard avec TVA 18%",
  "categorie": "ACHATS",
  "journalCode": "AC",
  "parametres": [
    {
      "nom": "montant_ht",
      "type": "NOMBRE",
      "obligatoire": true,
      "valeurDefaut": null
    },
    {
      "nom": "taux_tva",
      "type": "POURCENTAGE",
      "obligatoire": true,
      "valeurDefaut": 0.18
    },
    {
      "nom": "fournisseur",
      "type": "TEXTE",
      "obligatoire": false,
      "valeurDefaut": "Fournisseur divers"
    }
  ],
  "lignes": [
    {
      "compteNumero": "601100",
      "libelle": "Achat fournitures",
      "formuleMontant": "{{montant_ht}}",
      "sens": "DEBIT"
    },
    {
      "compteNumero": "445200",
      "libelle": "TVA déductible",
      "formuleMontant": "{{montant_ht}} * {{taux_tva}}",
      "sens": "DEBIT"
    },
    {
      "compteNumero": "401000",
      "libelle": "{{fournisseur}}",
      "formuleMontant": "{{montant_ht}} * (1 + {{taux_tva}})",
      "sens": "CREDIT"
    }
  ]
}
```

#### Template Vente avec TVA

```json
{
  "nom": "Vente avec TVA",
  "description": "Vente standard avec TVA 18%",
  "categorie": "VENTES",
  "journalCode": "VT",
  "parametres": [
    {
      "nom": "montant_ht",
      "type": "NOMBRE",
      "obligatoire": true,
      "valeurDefaut": null
    },
    {
      "nom": "taux_tva",
      "type": "POURCENTAGE",
      "obligatoire": true,
      "valeurDefaut": 0.18
    },
    {
      "nom": "client",
      "type": "TEXTE",
      "obligatoire": false,
      "valeurDefaut": "Client divers"
    }
  ],
  "lignes": [
    {
      "compteNumero": "411000",
      "libelle": "{{client}}",
      "formuleMontant": "{{montant_ht}} * (1 + {{taux_tva}})",
      "sens": "DEBIT"
    },
    {
      "compteNumero": "701000",
      "libelle": "Vente marchandises",
      "formuleMontant": "{{montant_ht}}",
      "sens": "CREDIT"
    },
    {
      "compteNumero": "443100",
      "libelle": "TVA collectée",
      "formuleMontant": "{{montant_ht}} * {{taux_tva}}",
      "sens": "CREDIT"
    }
  ]
}
```

### C. Checklist de Saisie

- [ ] **Préparation**
  - [ ] Vérifier l'exercice comptable ouvert
  - [ ] Préparer les pièces justificatives
  - [ ] Identifier le journal approprié

- [ ] **Saisie**
  - [ ] Utiliser un template si applicable
  - [ ] Renseigner la date correcte
  - [ ] Saisir un libellé explicite
  - [ ] Ajouter une référence externe
  - [ ] Vérifier l'équilibre débit/crédit

- [ ] **Vérification**
  - [ ] Contrôler les comptes utilisés
  - [ ] Vérifier les montants saisis
  - [ ] S'assurer de la conformité SYSCOHADA
  - [ ] Joindre les pièces justificatives

- [ ] **Finalisation**
  - [ ] Enregistrer en brouillard
  - [ ] Effectuer une revue finale
  - [ ] Valider l'écriture
  - [ ] Vérifier la numérotation définitive

---

*Document préparé par l'équipe d'architecture, version 1.0*