'use strict';

const { logger } = require('../config/logger');
const { ValidationError, NotFoundError } = require('../middleware/errorHandler');
const analyseCalculations = require('../utils/analyseCalculations');

/**
 * Service pour les analyses financières avancées
 * Conforme aux normes SYSCOHADA
 */
class AnalyseService {
  constructor(models) {
    this.models = models || require('../models');
    this.calculService = new (require('./calculService'))(this.models);
  }

  /**
   * Analyse l'évolution des comptes sur plusieurs périodes
   * @param {Array<string>} comptes - Liste des numéros de comptes à analyser
   * @param {Array<Object>} periodes - Liste des périodes à analyser [{dateDebut, dateFin, libelle}]
   * @param {Object} options - Options d'analyse
   * @returns {Promise<Object>} Analyse de l'évolution des comptes
   */
  async analyseEvolutionComptes(comptes, periodes, options = {}) {
    try {
      const { societeId, exerciceId } = options;

      // Valider les paramètres
      if (!Array.isArray(comptes) || comptes.length === 0) {
        throw new ValidationError('Au moins un compte doit être spécifié');
      }

      if (!Array.isArray(periodes) || periodes.length < 2) {
        throw new ValidationError('Au moins deux périodes doivent être spécifiées');
      }

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Trier les périodes par date de début
      periodes.sort((a, b) => new Date(a.dateDebut) - new Date(b.dateDebut));

      // Analyser chaque compte
      const resultatsComptes = [];

      for (const compteNumero of comptes) {
        // Vérifier que le compte existe
        const compte = await this.models.CompteComptable.findOne({
          where: { numero: compteNumero, societeId }
        });

        if (!compte) {
          throw new NotFoundError(`Compte ${compteNumero}`);
        }

        // Calculer les soldes pour chaque période
        const soldesParPeriode = [];
        for (const periode of periodes) {
          const { dateDebut, dateFin, libelle } = periode;
          
          // Calculer le solde pour cette période
          const solde = await this.calculService.calculerSoldeCompte(
            compteNumero,
            new Date(dateDebut),
            new Date(dateFin),
            { societeId, exerciceId }
          );

          soldesParPeriode.push({
            periode: {
              dateDebut,
              dateFin,
              libelle: libelle || `${dateDebut.toISOString().split('T')[0]} - ${dateFin.toISOString().split('T')[0]}`
            },
            solde: solde.solde,
            sens: solde.sensActuel
          });
        }

        // Extraire les valeurs pour l'analyse de tendance
        const valeurs = soldesParPeriode.map(p => 
          p.sens === 'DEBIT' ? p.solde : -p.solde
        );

        // Calculer la tendance
        const tendance = analyseCalculations.calculerTendance(valeurs);

        // Détecter les anomalies
        const anomalies = analyseCalculations.detecterAnomalies(valeurs);

        // Calculer les prévisions
        const previsions = analyseCalculations.calculerPrevisions(valeurs, 2);

        resultatsComptes.push({
          compte: {
            numero: compte.numero,
            libelle: compte.libelle,
            sensNaturel: compte.sens
          },
          soldesParPeriode,
          tendance,
          anomalies,
          previsions: previsions.map((valeur, index) => ({
            periode: `P+${index + 1}`,
            valeur: Math.abs(valeur),
            sens: valeur >= 0 ? 'DEBIT' : 'CREDIT'
          }))
        });
      }

      logger.info('Analyse de l\'évolution des comptes calculée', {
        societeId,
        nombreComptes: comptes.length,
        nombrePeriodes: periodes.length
      });

      return {
        societe: {
          id: societe.id,
          raisonSociale: societe.raisonSociale
        },
        periodes: periodes.map(p => ({
          dateDebut: p.dateDebut,
          dateFin: p.dateFin,
          libelle: p.libelle || `${p.dateDebut.toISOString().split('T')[0]} - ${p.dateFin.toISOString().split('T')[0]}`
        })),
        resultatsComptes,
        dateAnalyse: new Date()
      };
    } catch (error) {
      logger.error('Erreur lors de l\'analyse de l\'évolution des comptes', {
        comptes,
        periodes,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Détecte les anomalies dans les écritures comptables
   * @param {string} societeId - ID de la société
   * @param {Object} periode - Période d'analyse {dateDebut, dateFin}
   * @param {Object} options - Options de détection
   * @returns {Promise<Object>} Anomalies détectées
   */
  async detectionAnomalies(societeId, periode, options = {}) {
    try {
      const { dateDebut, dateFin } = periode;
      const { 
        exerciceId,
        seuilMontant = 10000,
        seuilEcartType = 2,
        seuilFrequence = 3
      } = options;

      // Valider les paramètres
      this.calculService.validerPeriode(dateDebut, dateFin);

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Construire les conditions WHERE pour les écritures
      const whereEcriture = {
        dateEcriture: {
          [this.models.sequelize.Op.between]: [dateDebut, dateFin]
        },
        societeId
      };

      if (exerciceId) {
        whereEcriture.exerciceId = exerciceId;
      }

      // Récupérer toutes les écritures de la période
      const ecritures = await this.models.EcritureComptable.findAll({
        where: whereEcriture,
        include: [
          {
            model: this.models.LigneEcriture,
            as: 'lignes',
            include: [
              {
                model: this.models.CompteComptable,
                as: 'compte'
              }
            ]
          }
        ],
        order: [['dateEcriture', 'ASC']]
      });

      // Anomalies à détecter
      const anomalies = {
        montantsEleves: [],
        ecrituresNonEquilibrees: [],
        comptesInhabituels: [],
        frequencesAnormales: []
      };

      // 1. Détecter les montants élevés
      for (const ecriture of ecritures) {
        for (const ligne of ecriture.lignes) {
          const montant = Math.max(ligne.debit || 0, ligne.credit || 0);
          if (montant > seuilMontant) {
            anomalies.montantsEleves.push({
              type: 'MONTANT_ELEVE',
              ecritureId: ecriture.id,
              numeroEcriture: ecriture.numeroEcriture,
              dateEcriture: ecriture.dateEcriture,
              compteNumero: ligne.compteNumero,
              compteLibelle: ligne.compte ? ligne.compte.libelle : '',
              montant,
              seuil: seuilMontant
            });
          }
        }
      }

      // 2. Détecter les écritures non équilibrées
      for (const ecriture of ecritures) {
        let totalDebit = 0;
        let totalCredit = 0;
        
        for (const ligne of ecriture.lignes) {
          totalDebit += parseFloat(ligne.debit || 0);
          totalCredit += parseFloat(ligne.credit || 0);
        }
        
        const difference = Math.abs(totalDebit - totalCredit);
        if (difference > 0.01) {
          anomalies.ecrituresNonEquilibrees.push({
            type: 'ECRITURE_NON_EQUILIBREE',
            ecritureId: ecriture.id,
            numeroEcriture: ecriture.numeroEcriture,
            dateEcriture: ecriture.dateEcriture,
            totalDebit,
            totalCredit,
            difference
          });
        }
      }

      // 3. Détecter les comptes inhabituels (statistique)
      const comptesUtilises = {};
      for (const ecriture of ecritures) {
        for (const ligne of ecriture.lignes) {
          if (!comptesUtilises[ligne.compteNumero]) {
            comptesUtilises[ligne.compteNumero] = {
              compte: ligne.compte,
              occurrences: 0,
              montantTotal: 0
            };
          }
          
          comptesUtilises[ligne.compteNumero].occurrences++;
          comptesUtilises[ligne.compteNumero].montantTotal += 
            parseFloat(ligne.debit || 0) + parseFloat(ligne.credit || 0);
        }
      }

      // Calculer les statistiques des comptes
      const comptesArray = Object.values(comptesUtilises);
      const occurrencesMoyenne = comptesArray.reduce((sum, c) => sum + c.occurrences, 0) / comptesArray.length;
      // Calculer l'écart-type des occurrences pour analyse statistique
      Math.sqrt(
        comptesArray.reduce((sum, c) => sum + Math.pow(c.occurrences - occurrencesMoyenne, 2), 0) / comptesArray.length
      );

      // Identifier les comptes peu utilisés
      for (const [compteNumero, stats] of Object.entries(comptesUtilises)) {
        if (stats.occurrences === 1) {
          anomalies.comptesInhabituels.push({
            type: 'COMPTE_INHABITUEL',
            compteNumero,
            compteLibelle: stats.compte ? stats.compte.libelle : '',
            occurrences: stats.occurrences,
            montantTotal: stats.montantTotal
          });
        }
      }

      // 4. Détecter les fréquences anormales
      const frequencesParJour = {};
      for (const ecriture of ecritures) {
        const dateKey = ecriture.dateEcriture.toISOString().split('T')[0];
        if (!frequencesParJour[dateKey]) {
          frequencesParJour[dateKey] = 0;
        }
        frequencesParJour[dateKey]++;
      }

      const frequences = Object.values(frequencesParJour);
      const frequenceMoyenne = frequences.reduce((sum, f) => sum + f, 0) / frequences.length;
      const frequenceEcartType = Math.sqrt(
        frequences.reduce((sum, f) => sum + Math.pow(f - frequenceMoyenne, 2), 0) / frequences.length
      );

      for (const [date, frequence] of Object.entries(frequencesParJour)) {
        if (frequence > frequenceMoyenne + seuilEcartType * frequenceEcartType && frequence > seuilFrequence) {
          anomalies.frequencesAnormales.push({
            type: 'FREQUENCE_ANORMALE',
            date,
            frequence,
            frequenceMoyenne,
            seuil: frequenceMoyenne + seuilEcartType * frequenceEcartType
          });
        }
      }

      logger.info('Détection d\'anomalies effectuée', {
        societeId,
        periode: { dateDebut, dateFin },
        nombreAnomalies: 
          anomalies.montantsEleves.length + 
          anomalies.ecrituresNonEquilibrees.length + 
          anomalies.comptesInhabituels.length + 
          anomalies.frequencesAnormales.length
      });

      return {
        societe: {
          id: societe.id,
          raisonSociale: societe.raisonSociale
        },
        periode: { dateDebut, dateFin },
        anomalies,
        statistiques: {
          nombreEcritures: ecritures.length,
          nombreComptes: Object.keys(comptesUtilises).length,
          frequenceMoyenne,
          frequenceEcartType
        },
        dateAnalyse: new Date()
      };
    } catch (error) {
      logger.error('Erreur lors de la détection d\'anomalies', {
        societeId,
        periode,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Calcule des prévisions financières
   * @param {string} societeId - ID de la société
   * @param {number} horizon - Nombre de périodes à prévoir
   * @param {Object} options - Options de prévision
   * @returns {Promise<Object>} Prévisions financières
   */
  async previsionsFinancieres(societeId, horizon = 3, options = {}) {
    try {
      const { 
        methode = 'regression_lineaire',
        periodeReference = 'mois',
        nombrePeriodesPasses = 12,
        comptes = []
      } = options;

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Déterminer la date de début pour l'historique
      const dateFin = new Date();
      let dateDebut;
      
      switch (periodeReference) {
      case 'jour':
        dateDebut = new Date(dateFin);
        dateDebut.setDate(dateDebut.getDate() - nombrePeriodesPasses);
        break;
      case 'mois':
        dateDebut = new Date(dateFin);
        dateDebut.setMonth(dateDebut.getMonth() - nombrePeriodesPasses);
        break;
      case 'trimestre':
        dateDebut = new Date(dateFin);
        dateDebut.setMonth(dateDebut.getMonth() - (nombrePeriodesPasses * 3));
        break;
      case 'annee':
        dateDebut = new Date(dateFin);
        dateDebut.setFullYear(dateDebut.getFullYear() - nombrePeriodesPasses);
        break;
      default:
        dateDebut = new Date(dateFin);
        dateDebut.setMonth(dateDebut.getMonth() - nombrePeriodesPasses);
      }

      // Générer les périodes passées
      const periodesPasses = this._genererPeriodes(dateDebut, dateFin, periodeReference);

      // Générer les périodes futures
      const dateDebutFuture = new Date(dateFin);
      let dateFinFuture;
      
      switch (periodeReference) {
      case 'jour':
        dateFinFuture = new Date(dateDebutFuture);
        dateFinFuture.setDate(dateFinFuture.getDate() + horizon);
        break;
      case 'mois':
        dateFinFuture = new Date(dateDebutFuture);
        dateFinFuture.setMonth(dateFinFuture.getMonth() + horizon);
        break;
      case 'trimestre':
        dateFinFuture = new Date(dateDebutFuture);
        dateFinFuture.setMonth(dateFinFuture.getMonth() + (horizon * 3));
        break;
      case 'annee':
        dateFinFuture = new Date(dateDebutFuture);
        dateFinFuture.setFullYear(dateFinFuture.getFullYear() + horizon);
        break;
      default:
        dateFinFuture = new Date(dateDebutFuture);
        dateFinFuture.setMonth(dateFinFuture.getMonth() + horizon);
      }

      const periodesFutures = this._genererPeriodes(dateDebutFuture, dateFinFuture, periodeReference);

      // Déterminer les comptes à analyser
      let comptesAAnalyser = comptes;
      if (!comptesAAnalyser || comptesAAnalyser.length === 0) {
        // Par défaut, analyser les comptes de produits (classe 7) et charges (classe 6)
        const comptesParDefaut = await this.models.CompteComptable.findAll({
          where: {
            societeId,
            [this.models.sequelize.Op.or]: [
              { numero: { [this.models.sequelize.Op.like]: '6%' } },
              { numero: { [this.models.sequelize.Op.like]: '7%' } }
            ],
            nature: 'COLLECTIF'
          },
          order: [['numero', 'ASC']]
        });
        
        comptesAAnalyser = comptesParDefaut.map(c => c.numero);
      }

      // Analyser chaque compte
      const resultatsComptes = [];

      for (const compteNumero of comptesAAnalyser) {
        // Vérifier que le compte existe
        const compte = await this.models.CompteComptable.findOne({
          where: { numero: compteNumero, societeId }
        });

        if (!compte) {
          continue; // Ignorer les comptes qui n'existent pas
        }

        // Calculer les soldes pour chaque période passée
        const historique = [];
        for (const periode of periodesPasses) {
          const { dateDebut, dateFin } = periode;
          
          // Calculer le solde pour cette période
          const solde = await this.calculService.calculerSoldeCompte(
            compteNumero,
            dateDebut,
            dateFin,
            { societeId }
          );

          historique.push({
            periode,
            solde: solde.solde,
            sens: solde.sensActuel
          });
        }

        // Extraire les valeurs pour les prévisions
        const valeurs = historique.map(h => 
          h.sens === 'DEBIT' ? h.solde : -h.solde
        );

        // Calculer les prévisions
        const previsionsValeurs = analyseCalculations.calculerPrevisions(valeurs, periodesFutures.length, methode);

        // Formater les prévisions
        const previsions = previsionsValeurs.map((valeur, index) => ({
          periode: periodesFutures[index],
          valeur: Math.abs(valeur),
          sens: valeur >= 0 ? 'DEBIT' : 'CREDIT'
        }));

        resultatsComptes.push({
          compte: {
            numero: compte.numero,
            libelle: compte.libelle,
            sensNaturel: compte.sens
          },
          historique,
          previsions
        });
      }

      logger.info('Prévisions financières calculées', {
        societeId,
        horizon,
        methode,
        nombreComptes: resultatsComptes.length
      });

      return {
        societe: {
          id: societe.id,
          raisonSociale: societe.raisonSociale
        },
        parametres: {
          horizon,
          methode,
          periodeReference,
          nombrePeriodesPasses
        },
        periodesPasses,
        periodesFutures,
        resultatsComptes,
        dateAnalyse: new Date()
      };
    } catch (error) {
      logger.error('Erreur lors du calcul des prévisions financières', {
        societeId,
        horizon,
        options,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Compare les résultats réels avec les budgets
   * @param {string} societeId - ID de la société
   * @param {Object} periode - Période d'analyse {dateDebut, dateFin}
   * @param {Object} options - Options de comparaison
   * @returns {Promise<Object>} Comparaisons budgétaires
   */
  async comparaisonsBudgetaires(societeId, periode, options = {}) {
    try {
      const { dateDebut, dateFin } = periode;
      const { exerciceId } = options;

      // Valider les paramètres
      this.calculService.validerPeriode(dateDebut, dateFin);

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // TODO: Implémenter la gestion des budgets
      // Pour l'instant, on utilise des données fictives pour la démonstration
      const budgetsFictifs = {
        '6': 100000, // Charges
        '7': 150000  // Produits
      };

      // Calculer la balance générale pour la période
      const balance = await this.calculService.calculerBalanceGenerale(
        dateDebut,
        dateFin,
        {
          societeId,
          exerciceId,
          niveauDetail: 'COLLECTIF'
        }
      );

      // Analyser les écarts par classe de comptes
      const comparaisons = [];
      
      for (const ligne of balance.lignesBalance) {
        const classeCompte = ligne.compteNumero.charAt(0);
        
        if (classeCompte === '6' || classeCompte === '7') {
          const budgetClasse = budgetsFictifs[classeCompte] || 0;
          const reelClasse = classeCompte === '6' 
            ? ligne.totalDebit - ligne.totalCredit 
            : ligne.totalCredit - ligne.totalDebit;
          
          const ecart = reelClasse - budgetClasse;
          const ecartPourcentage = budgetClasse !== 0 
            ? (ecart / budgetClasse) * 100 
            : 0;
          
          comparaisons.push({
            compte: {
              numero: ligne.compteNumero,
              libelle: ligne.libelle,
              classe: classeCompte
            },
            budget: budgetClasse,
            reel: reelClasse,
            ecart,
            ecartPourcentage,
            favorable: (classeCompte === '6' && ecart < 0) || (classeCompte === '7' && ecart > 0)
          });
        }
      }

      // Calculer les totaux
      const totalBudget = Object.values(budgetsFictifs).reduce((sum, val) => sum + val, 0);
      const totalReel = comparaisons.reduce((sum, comp) => sum + comp.reel, 0);
      const ecartTotal = totalReel - totalBudget;
      const ecartPourcentageTotal = totalBudget !== 0 
        ? (ecartTotal / totalBudget) * 100 
        : 0;

      logger.info('Comparaisons budgétaires calculées', {
        societeId,
        periode: { dateDebut, dateFin },
        nombreComparaisons: comparaisons.length
      });

      return {
        societe: {
          id: societe.id,
          raisonSociale: societe.raisonSociale
        },
        periode: { dateDebut, dateFin },
        comparaisons,
        totaux: {
          budget: totalBudget,
          reel: totalReel,
          ecart: ecartTotal,
          ecartPourcentage: ecartPourcentageTotal,
          favorable: ecartTotal > 0
        },
        dateAnalyse: new Date()
      };
    } catch (error) {
      logger.error('Erreur lors des comparaisons budgétaires', {
        societeId,
        periode,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Effectue un benchmarking sectoriel
   * @param {string} secteur - Secteur d'activité
   * @param {Array<string>} ratios - Ratios à comparer
   * @param {Object} options - Options de benchmarking
   * @returns {Promise<Object>} Benchmarking sectoriel
   */
  async benchmarkingSectoriel(secteur, ratios, options = {}) {
    try {
      const { societeId } = options;

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // TODO: Implémenter la récupération des données sectorielles
      // Pour l'instant, on utilise des données fictives pour la démonstration
      const donneesSectorielles = {
        'commerce': {
          'rentabilite': 0.08,
          'liquidite': 1.2,
          'endettement': 0.6,
          'autonomieFinanciere': 0.4
        },
        'industrie': {
          'rentabilite': 0.06,
          'liquidite': 1.5,
          'endettement': 0.5,
          'autonomieFinanciere': 0.5
        },
        'services': {
          'rentabilite': 0.1,
          'liquidite': 1.3,
          'endettement': 0.4,
          'autonomieFinanciere': 0.6
        }
      };

      // Utiliser le secteur par défaut si celui spécifié n'existe pas
      const secteurEffectif = donneesSectorielles[secteur] ? secteur : 'commerce';
      const referentielSectoriel = donneesSectorielles[secteurEffectif];

      // Récupérer les ratios de la société
      const exerciceEnCours = await this.models.ExerciceComptable.findOne({
        where: {
          societeId,
          statut: 'OUVERT'
        },
        order: [['dateFin', 'DESC']]
      });

      if (!exerciceEnCours) {
        throw new NotFoundError('Exercice comptable ouvert');
      }

      const dateDebut = exerciceEnCours.dateDebut;
      const dateFin = new Date(); // Date actuelle

      // Récupérer les ratios financiers
      const dashboardService = new (require('./dashboardService'))(this.models);
      const ratiosFinanciers = await dashboardService.getRatiosFinanciers(
        societeId,
        { dateDebut, dateFin },
        { exerciceId: exerciceEnCours.id }
      );

      // Comparer les ratios
      const comparaisons = [];
      
      for (const [ratio, valeurSectorielle] of Object.entries(referentielSectoriel)) {
        if (!ratios || ratios.includes(ratio)) {
          const valeurSociete = ratiosFinanciers.ratios[ratio] || 0;
          const ecart = valeurSociete - valeurSectorielle;
          const ecartPourcentage = valeurSectorielle !== 0 
            ? (ecart / valeurSectorielle) * 100 
            : 0;
          
          let interpretation;
          switch (ratio) {
          case 'rentabilite':
            interpretation = ecart > 0 ? 'FAVORABLE' : 'DEFAVORABLE';
            break;
          case 'liquidite':
            interpretation = ecart > 0 ? 'FAVORABLE' : 'DEFAVORABLE';
            break;
          case 'endettement':
            interpretation = ecart < 0 ? 'FAVORABLE' : 'DEFAVORABLE';
            break;
          case 'autonomieFinanciere':
            interpretation = ecart > 0 ? 'FAVORABLE' : 'DEFAVORABLE';
            break;
          default:
            interpretation = 'NEUTRE';
          }
          
          comparaisons.push({
            ratio,
            valeurSociete,
            valeurSectorielle,
            ecart,
            ecartPourcentage,
            interpretation
          });
        }
      }

      logger.info('Benchmarking sectoriel effectué', {
        societeId,
        secteur: secteurEffectif,
        nombreRatios: comparaisons.length
      });

      return {
        societe: {
          id: societe.id,
          raisonSociale: societe.raisonSociale
        },
        secteur: secteurEffectif,
        comparaisons,
        dateAnalyse: new Date()
      };
    } catch (error) {
      logger.error('Erreur lors du benchmarking sectoriel', {
        secteur,
        ratios,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Génère des périodes en fonction de la référence
   * @private
   * @param {Date} dateDebut - Date de début
   * @param {Date} dateFin - Date de fin
   * @param {string} reference - Référence de période ('jour', 'mois', 'trimestre', 'annee')
   * @returns {Array<Object>} Périodes générées
   */
  _genererPeriodes(dateDebut, dateFin, reference) {
    const periodes = [];
    let dateActuelle = new Date(dateDebut);
    
    while (dateActuelle < dateFin) {
      let dateFinPeriode;
      
      switch (reference) {
      case 'jour':
        dateFinPeriode = new Date(dateActuelle);
        dateFinPeriode.setDate(dateFinPeriode.getDate() + 1);
        break;
      case 'mois':
        dateFinPeriode = new Date(dateActuelle);
        dateFinPeriode.setMonth(dateFinPeriode.getMonth() + 1);
        dateFinPeriode.setDate(0); // Dernier jour du mois
        break;
      case 'trimestre':
        dateFinPeriode = new Date(dateActuelle);
        dateFinPeriode.setMonth(dateFinPeriode.getMonth() + 3);
        dateFinPeriode.setDate(0); // Dernier jour du mois
        break;
      case 'annee':
        dateFinPeriode = new Date(dateActuelle);
        dateFinPeriode.setFullYear(dateFinPeriode.getFullYear() + 1);
        dateFinPeriode.setMonth(0);
        dateFinPeriode.setDate(0); // Dernier jour de l'année précédente
        break;
      default:
        dateFinPeriode = new Date(dateActuelle);
        dateFinPeriode.setMonth(dateFinPeriode.getMonth() + 1);
        dateFinPeriode.setDate(0); // Dernier jour du mois
      }
      
      // Ajuster la date de fin si elle dépasse la date de fin globale
      if (dateFinPeriode > dateFin) {
        dateFinPeriode = new Date(dateFin);
      }
      
      // Formater la période
      const periode = {
        dateDebut: new Date(dateActuelle),
        dateFin: new Date(dateFinPeriode),
        libelle: this._formaterLibellePeriode(dateActuelle, dateFinPeriode, reference)
      };
      
      periodes.push(periode);
      
      // Passer à la période suivante
      dateActuelle = new Date(dateFinPeriode);
      dateActuelle.setDate(dateActuelle.getDate() + 1);
    }
    
    return periodes;
  }
  
  /**
   * Formate le libellé d'une période
   * @private
   * @param {Date} dateDebut - Date de début
   * @param {Date} dateFin - Date de fin
   * @param {string} reference - Référence de période
   * @returns {string} Libellé formaté
   */
  _formaterLibellePeriode(dateDebut, dateFin, reference) {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    
    switch (reference) {
    case 'jour':
      return dateDebut.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short', year: 'numeric' });
    case 'mois':
      return dateDebut.toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' });
    case 'trimestre': {
      const trimestre = Math.floor(dateDebut.getMonth() / 3) + 1;
      return `T${trimestre} ${dateDebut.getFullYear()}`;
    }
    case 'annee':
      return dateDebut.getFullYear().toString();
    default:
      return `${dateDebut.toLocaleDateString('fr-FR', options)} - ${dateFin.toLocaleDateString('fr-FR', options)}`;
    }
  }
}

module.exports = AnalyseService;
        