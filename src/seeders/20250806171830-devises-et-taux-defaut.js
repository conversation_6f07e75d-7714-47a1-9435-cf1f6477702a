'use strict';

const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Créer les devises par défaut
    const devises = [
      {
        code: 'XOF',
        libelle: 'Franc CFA UEMOA',
        symbole: 'FCFA',
        decimales: 0,
        actif: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        code: 'XAF',
        libelle: 'Franc CFA CEMAC',
        symbole: 'FCFA',
        decimales: 0,
        actif: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        code: 'EUR',
        libelle: 'Euro',
        symbole: '€',
        decimales: 2,
        actif: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        code: 'USD',
        libelle: 'Dollar américain',
        symbole: '$',
        decimales: 2,
        actif: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        code: 'GBP',
        libelle: '<PERSON>re sterling',
        symbole: '£',
        decimales: 2,
        actif: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('devises', devises);

    // Créer les taux de change par défaut
    const maintenant = new Date();
    const tauxChanges = [
      // EUR vers devises CFA (taux fixes BCEAO/BEAC)
      {
        id: uuidv4(),
        devise_source: 'EUR',
        devise_cible: 'XOF',
        taux: 655.957,
        date_application: maintenant,
        source: 'BCEAO',
        created_at: maintenant,
        updated_at: maintenant
      },
      {
        id: uuidv4(),
        devise_source: 'XOF',
        devise_cible: 'EUR',
        taux: 0.001524,
        date_application: maintenant,
        source: 'BCEAO',
        created_at: maintenant,
        updated_at: maintenant
      },
      {
        id: uuidv4(),
        devise_source: 'EUR',
        devise_cible: 'XAF',
        taux: 655.957,
        date_application: maintenant,
        source: 'BEAC',
        created_at: maintenant,
        updated_at: maintenant
      },
      {
        id: uuidv4(),
        devise_source: 'XAF',
        devise_cible: 'EUR',
        taux: 0.001524,
        date_application: maintenant,
        source: 'BEAC',
        created_at: maintenant,
        updated_at: maintenant
      },

      // USD vers devises CFA (taux approximatifs)
      {
        id: uuidv4(),
        devise_source: 'USD',
        devise_cible: 'XOF',
        taux: 600.0,
        date_application: maintenant,
        source: 'MARCHE',
        created_at: maintenant,
        updated_at: maintenant
      },
      {
        id: uuidv4(),
        devise_source: 'XOF',
        devise_cible: 'USD',
        taux: 0.001667,
        date_application: maintenant,
        source: 'MARCHE',
        created_at: maintenant,
        updated_at: maintenant
      },
      {
        id: uuidv4(),
        devise_source: 'USD',
        devise_cible: 'XAF',
        taux: 600.0,
        date_application: maintenant,
        source: 'MARCHE',
        created_at: maintenant,
        updated_at: maintenant
      },
      {
        id: uuidv4(),
        devise_source: 'XAF',
        devise_cible: 'USD',
        taux: 0.001667,
        date_application: maintenant,
        source: 'MARCHE',
        created_at: maintenant,
        updated_at: maintenant
      },

      // EUR vers USD
      {
        id: uuidv4(),
        devise_source: 'EUR',
        devise_cible: 'USD',
        taux: 1.10,
        date_application: maintenant,
        source: 'MARCHE',
        created_at: maintenant,
        updated_at: maintenant
      },
      {
        id: uuidv4(),
        devise_source: 'USD',
        devise_cible: 'EUR',
        taux: 0.909091,
        date_application: maintenant,
        source: 'MARCHE',
        created_at: maintenant,
        updated_at: maintenant
      },

      // XOF vers XAF (parité fixe)
      {
        id: uuidv4(),
        devise_source: 'XOF',
        devise_cible: 'XAF',
        taux: 1.0,
        date_application: maintenant,
        source: 'OFFICIEL',
        created_at: maintenant,
        updated_at: maintenant
      },
      {
        id: uuidv4(),
        devise_source: 'XAF',
        devise_cible: 'XOF',
        taux: 1.0,
        date_application: maintenant,
        source: 'OFFICIEL',
        created_at: maintenant,
        updated_at: maintenant
      },

      // GBP vers autres devises
      {
        id: uuidv4(),
        devise_source: 'GBP',
        devise_cible: 'EUR',
        taux: 1.15,
        date_application: maintenant,
        source: 'MARCHE',
        created_at: maintenant,
        updated_at: maintenant
      },
      {
        id: uuidv4(),
        devise_source: 'EUR',
        devise_cible: 'GBP',
        taux: 0.869565,
        date_application: maintenant,
        source: 'MARCHE',
        created_at: maintenant,
        updated_at: maintenant
      }
    ];

    await queryInterface.bulkInsert('taux_changes', tauxChanges);

    console.log('✅ Devises et taux de change par défaut créés');
  },

  async down (queryInterface, Sequelize) {
    // Supprimer les taux de change
    await queryInterface.bulkDelete('taux_changes', null, {});

    // Supprimer les devises
    await queryInterface.bulkDelete('devises', null, {});

    console.log('✅ Devises et taux de change supprimés');
  }
};
