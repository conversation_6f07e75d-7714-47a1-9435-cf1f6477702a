'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('parametre_comptables', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      societe_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'societes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      cle: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      valeur: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      type: {
        type: Sequelize.ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON'),
        allowNull: false,
        defaultValue: 'STRING'
      },
      categorie: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'GENERAL'
      },
      description: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Index unique pour éviter les doublons de clé par société
    await queryInterface.addIndex('parametre_comptables', {
      fields: ['societe_id', 'cle'],
      unique: true,
      name: 'idx_parametre_societe_cle_unique'
    });

    // Index pour les recherches par société et catégorie
    await queryInterface.addIndex('parametre_comptables', {
      fields: ['societe_id', 'categorie'],
      name: 'idx_parametre_societe_categorie'
    });

    // Index pour les recherches par catégorie
    await queryInterface.addIndex('parametre_comptables', {
      fields: ['categorie'],
      name: 'idx_parametre_categorie'
    });
  },

  async down (queryInterface, Sequelize) {
    // Supprimer les index
    await queryInterface.removeIndex('parametre_comptables', 'idx_parametre_societe_cle_unique');
    await queryInterface.removeIndex('parametre_comptables', 'idx_parametre_societe_categorie');
    await queryInterface.removeIndex('parametre_comptables', 'idx_parametre_categorie');

    // Supprimer la table
    await queryInterface.dropTable('parametre_comptables');
  }
};
