'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Service pour la gestion des exercices comptables et clôtures
 * Conforme aux normes SYSCOHADA
 */
class ExerciseService {
  constructor(models) {
    this.models = models;
  }

  /**
   * Valide les pré-requis pour la clôture d'exercice
   * @param {string} exerciceId - ID de l'exercice
   * @returns {Promise<Object>} Résultat de la validation
   */
  async validerPrerequisCloture(exerciceId) {
    try {
      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId, {
        include: [{ model: this.models.Societe, as: 'societe' }]
      });

      if (!exercice) {
        throw new NotFoundError('Exercice inexistant');
      }

      if (exercice.statut === 'CLOTURE') {
        throw new ValidationError('Exercice déjà clôturé');
      }

      const validations = {
        exercice: {
          valide: true,
          message: 'Exercice valide pour clôture'
        },
        ecritures: await this.validerEcritures(exercice),
        balances: await this.validerBalances(exercice),
        lettrage: await this.validerLettrage(exercice),
        amortissements: await this.validerAmortissements(exercice),
        inventaire: await this.validerInventaire(exercice)
      };

      // Calcul du statut global
      const toutesValidations = Object.values(validations);
      const validationGlobale = toutesValidations.every(v => v.valide);

      const resultat = {
        exerciceId,
        exercice: {
          id: exercice.id,
          libelle: exercice.libelle,
          dateDebut: exercice.dateDebut,
          dateFin: exercice.dateFin,
          statut: exercice.statut
        },
        validationGlobale,
        validations,
        recommandations: this.genererRecommandations(validations)
      };

      logger.info('Validation pré-requis clôture', {
        exerciceId,
        validationGlobale,
        nombreValidations: toutesValidations.length
      });

      return resultat;

    } catch (error) {
      logger.error('Erreur validation pré-requis clôture', {
        error: error.message,
        exerciceId
      });
      throw error;
    }
  }

  /**
   * Lance le processus de clôture d'exercice
   * @param {string} exerciceId - ID de l'exercice
   * @param {Object} options - Options de clôture
   * @returns {Promise<Object>} Résultat de la clôture
   */
  async cloturerExercice(exerciceId, options = {}) {
    const transaction = await this.models.sequelize.transaction();

    try {
      const {
        forcerCloture = false,
        genererEcrituresResultat = true,
        genererEcrituresReouverture = true,
        archiverDonnees = false
      } = options;

      // Validation des pré-requis
      if (!forcerCloture) {
        const validation = await this.validerPrerequisCloture(exerciceId);
        if (!validation.validationGlobale) {
          throw new ValidationError('Pré-requis de clôture non satisfaits', validation.validations);
        }
      }

      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId, {
        include: [{ model: this.models.Societe, as: 'societe' }]
      });

      const etapes = [];

      // Étape 1: Calcul du résultat
      logger.info('Début calcul résultat exercice', { exerciceId });
      const resultatCalcul = await this.calculerResultatExercice(exercice, transaction);
      etapes.push({
        etape: 'CALCUL_RESULTAT',
        statut: 'TERMINE',
        resultat: resultatCalcul
      });

      // Étape 2: Génération des écritures de résultat
      if (genererEcrituresResultat) {
        logger.info('Génération écritures de résultat', { exerciceId });
        const ecrituresResultat = await this.genererEcrituresResultat(exercice, resultatCalcul, transaction);
        etapes.push({
          etape: 'ECRITURES_RESULTAT',
          statut: 'TERMINE',
          ecritures: ecrituresResultat
        });
      }

      // Étape 3: Écritures de clôture
      logger.info('Génération écritures de clôture', { exerciceId });
      const ecrituresCloture = await this.genererEcrituresCloture(exercice, transaction);
      etapes.push({
        etape: 'ECRITURES_CLOTURE',
        statut: 'TERMINE',
        ecritures: ecrituresCloture
      });

      // Étape 4: Création exercice suivant et écritures de réouverture
      if (genererEcrituresReouverture) {
        logger.info('Création exercice suivant et réouverture', { exerciceId });
        const exerciceSuivant = await this.creerExerciceSuivant(exercice, transaction);
        const ecrituresReouverture = await this.genererEcrituresReouverture(exercice, exerciceSuivant, transaction);
        etapes.push({
          etape: 'EXERCICE_SUIVANT',
          statut: 'TERMINE',
          exerciceSuivant,
          ecritures: ecrituresReouverture
        });
      }

      // Étape 5: Mise à jour statut exercice
      await exercice.update({
        statut: 'CLOTURE',
        dateCloture: new Date(),
        resultatExercice: resultatCalcul.resultatNet
      }, { transaction });

      etapes.push({
        etape: 'FINALISATION',
        statut: 'TERMINE',
        message: 'Exercice clôturé avec succès'
      });

      // Étape 6: Archivage (optionnel)
      if (archiverDonnees) {
        logger.info('Archivage des données', { exerciceId });
        await this.archiverDonneesExercice(exercice, transaction);
        etapes.push({
          etape: 'ARCHIVAGE',
          statut: 'TERMINE',
          message: 'Données archivées'
        });
      }

      await transaction.commit();

      const resultatCloture = {
        exerciceId,
        exercice: {
          id: exercice.id,
          libelle: exercice.libelle,
          statut: 'CLOTURE',
          dateCloture: exercice.dateCloture,
          resultatExercice: exercice.resultatExercice
        },
        etapes,
        resultatCalcul,
        dateCloture: new Date()
      };

      logger.info('Clôture exercice terminée', {
        exerciceId,
        resultatNet: resultatCalcul.resultatNet,
        nombreEtapes: etapes.length
      });

      return resultatCloture;

    } catch (error) {
      await transaction.rollback();
      logger.error('Erreur clôture exercice', {
        error: error.message,
        exerciceId,
        options
      });
      throw error;
    }
  }

  /**
   * Rouvre un exercice clôturé
   * @param {string} exerciceId - ID de l'exercice
   * @returns {Promise<Object>} Résultat de la réouverture
   */
  async rouvrirExercice(exerciceId) {
    const transaction = await this.models.sequelize.transaction();

    try {
      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId);
      if (!exercice) {
        throw new NotFoundError('Exercice inexistant');
      }

      if (exercice.statut !== 'CLOTURE') {
        throw new ValidationError('Seul un exercice clôturé peut être rouvert');
      }

      // Vérification qu'il n'y a pas d'exercice suivant déjà commencé
      const exerciceSuivant = await this.models.ExerciceComptable.findOne({
        where: {
          societeId: exercice.societeId,
          dateDebut: { [this.models.Sequelize.Op.gt]: exercice.dateFin }
        }
      });

      if (exerciceSuivant && exerciceSuivant.statut !== 'BROUILLON') {
        throw new ValidationError('Impossible de rouvrir : exercice suivant déjà commencé');
      }

      // Suppression des écritures de clôture/réouverture
      await this.supprimerEcrituresCloture(exercice, transaction);

      // Mise à jour du statut
      await exercice.update({
        statut: 'OUVERT',
        dateCloture: null,
        resultatExercice: null
      }, { transaction });

      // Suppression de l'exercice suivant s'il est en brouillon
      if (exerciceSuivant && exerciceSuivant.statut === 'BROUILLON') {
        await exerciceSuivant.destroy({ transaction });
      }

      await transaction.commit();

      logger.info('Exercice rouvert', {
        exerciceId,
        exerciceLibelle: exercice.libelle
      });

      return {
        exerciceId,
        exercice: {
          id: exercice.id,
          libelle: exercice.libelle,
          statut: 'OUVERT'
        },
        message: 'Exercice rouvert avec succès',
        dateReouverture: new Date()
      };

    } catch (error) {
      await transaction.rollback();
      logger.error('Erreur réouverture exercice', {
        error: error.message,
        exerciceId
      });
      throw error;
    }
  }

  /**
   * Génère le rapport de clôture
   * @param {string} exerciceId - ID de l'exercice
   * @returns {Promise<Object>} Rapport de clôture
   */
  async genererRapportCloture(exerciceId) {
    try {
      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId, {
        include: [{ model: this.models.Societe, as: 'societe' }]
      });

      if (!exercice) {
        throw new NotFoundError('Exercice inexistant');
      }

      // Calcul des données du rapport
      const donneesRapport = await Promise.all([
        this.calculerStatistiquesEcritures(exercice),
        this.calculerBalanceFinale(exercice),
        this.calculerResultatDetaille(exercice),
        this.calculerRatiosFinanciers(exercice),
        this.calculerEvolutionExercice(exercice)
      ]);

      const [statistiques, balance, resultat, ratios, evolution] = donneesRapport;

      const rapport = {
        exercice: {
          id: exercice.id,
          libelle: exercice.libelle,
          dateDebut: exercice.dateDebut,
          dateFin: exercice.dateFin,
          statut: exercice.statut,
          dateCloture: exercice.dateCloture
        },
        societe: {
          nom: exercice.societe.nom,
          numeroRccm: exercice.societe.numeroRccm,
          formeJuridique: exercice.societe.formeJuridique
        },
        statistiques,
        balance,
        resultat,
        ratios,
        evolution,
        dateGeneration: new Date()
      };

      logger.info('Rapport de clôture généré', {
        exerciceId,
        nombreComptes: balance.nombreComptes,
        resultatNet: resultat.resultatNet
      });

      return rapport;

    } catch (error) {
      logger.error('Erreur génération rapport clôture', {
        error: error.message,
        exerciceId
      });
      throw error;
    }
  }

  // === MÉTHODES PRIVÉES ===

  /**
   * Valide les écritures de l'exercice
   */
  async validerEcritures(exercice) {
    const stats = await this.models.EcritureComptable.findAll({
      where: {
        societeId: exercice.societeId,
        dateEcriture: {
          [this.models.Sequelize.Op.between]: [exercice.dateDebut, exercice.dateFin]
        }
      },
      attributes: [
        'statut',
        [this.models.sequelize.fn('COUNT', this.models.sequelize.col('id')), 'nombre']
      ],
      group: ['statut'],
      raw: true
    });

    const statistiques = {
      total: 0,
      validees: 0,
      brouillon: 0
    };

    stats.forEach(stat => {
      const nombre = parseInt(stat.nombre);
      statistiques.total += nombre;
      if (stat.statut === 'VALIDEE') {
        statistiques.validees += nombre;
      } else {
        statistiques.brouillon += nombre;
      }
    });

    const valide = statistiques.brouillon === 0;

    return {
      valide,
      message: valide ? 'Toutes les écritures sont validées' : `${statistiques.brouillon} écritures en brouillon`,
      statistiques
    };
  }

  /**
   * Valide l'équilibre des balances
   */
  async validerBalances(exercice) {
    const query = `
      SELECT 
        SUM(l.debit) as total_debit,
        SUM(l.credit) as total_credit
      FROM ligne_ecritures l
      JOIN ecriture_comptables e ON l.ecriture_id = e.id
      WHERE e.societe_id = :societeId
        AND e.date_ecriture BETWEEN :dateDebut AND :dateFin
        AND e.statut = 'VALIDEE'
    `;

    const [result] = await this.models.sequelize.query(query, {
      replacements: {
        societeId: exercice.societeId,
        dateDebut: exercice.dateDebut,
        dateFin: exercice.dateFin
      },
      type: this.models.Sequelize.QueryTypes.SELECT
    });

    const totalDebit = parseFloat(result[0]?.total_debit || 0);
    const totalCredit = parseFloat(result[0]?.total_credit || 0);
    const difference = Math.abs(totalDebit - totalCredit);
    const valide = difference < 0.01;

    return {
      valide,
      message: valide ? 'Balance équilibrée' : `Déséquilibre de ${difference.toFixed(2)}`,
      totalDebit,
      totalCredit,
      difference
    };
  }

  /**
   * Valide le lettrage des comptes
   */
  async validerLettrage(exercice) {
    const comptesNonLettres = await this.models.sequelize.query(`
      SELECT 
        c.numero,
        c.intitule,
        COUNT(l.id) as lignes_non_lettrees
      FROM compte_comptables c
      JOIN ligne_ecritures l ON c.numero = l.compte_numero
      JOIN ecriture_comptables e ON l.ecriture_id = e.id
      WHERE c.societe_id = :societeId
        AND c.lettrable = true
        AND l.lettrage IS NULL
        AND e.date_ecriture BETWEEN :dateDebut AND :dateFin
        AND e.statut = 'VALIDEE'
      GROUP BY c.numero, c.intitule
      HAVING COUNT(l.id) > 0
    `, {
      replacements: {
        societeId: exercice.societeId,
        dateDebut: exercice.dateDebut,
        dateFin: exercice.dateFin
      },
      type: this.models.Sequelize.QueryTypes.SELECT
    });

    const valide = comptesNonLettres.length === 0;

    return {
      valide,
      message: valide ? 'Tous les comptes lettrables sont lettrés' : `${comptesNonLettres.length} comptes non lettrés`,
      comptesNonLettres
    };
  }

  /**
   * Valide les amortissements
   */
  async validerAmortissements(exercice) {
    const exerciceAnnee = new Date(exercice.dateFin).getFullYear();

    const amortissementsSansEcriture = await this.models.DepreciationPlan.findAll({
      where: {
        exercice: exerciceAnnee,
        statut: 'PREVISIONNEL',
        ecritureGeneree: false
      },
      include: [{
        model: this.models.Depreciation,
        as: 'depreciation',
        where: { societeId: exercice.societeId }
      }]
    });

    const valide = amortissementsSansEcriture.length === 0;

    return {
      valide,
      message: valide ? 'Tous les amortissements sont à jour' : `${amortissementsSansEcriture.length} amortissements sans écriture`,
      amortissementsSansEcriture: amortissementsSansEcriture.map(p => ({
        code: p.depreciation.code,
        libelle: p.depreciation.libelle,
        dotation: p.dotationPeriode
      }))
    };
  }

  /**
   * Valide l'inventaire
   */
  async validerInventaire(exercice) {
    // Validation simplifiée - à adapter selon les besoins
    const comptesStocks = await this.models.CompteComptable.findAll({
      where: {
        societeId: exercice.societeId,
        classe: 3,
        actif: true
      }
    });

    // Pour l'instant, on considère que l'inventaire est valide
    // Dans une implémentation complète, il faudrait vérifier :
    // - Les écritures d'inventaire
    // - Les variations de stocks
    // - Les provisions pour dépréciation

    return {
      valide: true,
      message: 'Inventaire validé',
      nombreComptesStocks: comptesStocks.length
    };
  }

  /**
   * Génère les recommandations
   */
  genererRecommandations(validations) {
    const recommandations = [];

    if (!validations.ecritures.valide) {
      recommandations.push({
        type: 'CRITIQUE',
        message: 'Valider toutes les écritures en brouillon avant la clôture',
        action: 'VALIDER_ECRITURES'
      });
    }

    if (!validations.balances.valide) {
      recommandations.push({
        type: 'CRITIQUE',
        message: 'Corriger le déséquilibre de la balance générale',
        action: 'CORRIGER_BALANCE'
      });
    }

    if (!validations.lettrage.valide) {
      recommandations.push({
        type: 'IMPORTANT',
        message: 'Finaliser le lettrage des comptes de tiers',
        action: 'LETTRER_COMPTES'
      });
    }

    if (!validations.amortissements.valide) {
      recommandations.push({
        type: 'IMPORTANT',
        message: 'Générer les écritures d\'amortissement manquantes',
        action: 'GENERER_AMORTISSEMENTS'
      });
    }

    return recommandations;
  }

  /**
   * Calcule le résultat de l'exercice
   */
  async calculerResultatExercice(exercice, transaction) {
    const query = `
      SELECT 
        c.classe,
        SUM(CASE WHEN c.nature = 'DEBIT' THEN l.debit - l.credit ELSE l.credit - l.debit END) as solde
      FROM compte_comptables c
      JOIN ligne_ecritures l ON c.numero = l.compte_numero
      JOIN ecriture_comptables e ON l.ecriture_id = e.id
      WHERE c.societe_id = :societeId
        AND c.classe IN (6, 7)
        AND e.date_ecriture BETWEEN :dateDebut AND :dateFin
        AND e.statut = 'VALIDEE'
      GROUP BY c.classe
    `;

    const [results] = await this.models.sequelize.query(query, {
      replacements: {
        societeId: exercice.societeId,
        dateDebut: exercice.dateDebut,
        dateFin: exercice.dateFin
      },
      type: this.models.Sequelize.QueryTypes.SELECT,
      transaction
    });

    let totalCharges = 0;
    let totalProduits = 0;

    results.forEach(result => {
      const solde = parseFloat(result.solde);
      if (result.classe === 6) {
        totalCharges += solde;
      } else if (result.classe === 7) {
        totalProduits += solde;
      }
    });

    const resultatNet = totalProduits - totalCharges;

    return {
      totalCharges,
      totalProduits,
      resultatNet,
      typeResultat: resultatNet >= 0 ? 'BENEFICE' : 'PERTE'
    };
  }

  /**
   * Génère les écritures de détermination du résultat
   */
  async genererEcrituresResultat(exercice, resultatCalcul, transaction) {
    const ecritures = [];

    // Écriture de solde des comptes de charges (classe 6)
    const ecritureCharges = await this.models.EcritureComptable.create({
      societeId: exercice.societeId,
      journalCode: 'OD',
      dateEcriture: exercice.dateFin,
      libelle: `Solde des comptes de charges - ${exercice.libelle}`,
      reference: `CLOTURE-CHARGES-${exercice.id}`,
      totalDebit: resultatCalcul.totalCharges,
      totalCredit: resultatCalcul.totalCharges,
      statut: 'VALIDEE',
      typeEcriture: 'CLOTURE'
    }, { transaction });

    // Lignes pour solder les comptes de charges
    await this.genererLignesSoldeComptes(ecritureCharges.id, exercice, 6, 'CREDIT', transaction);

    ecritures.push(ecritureCharges);

    // Écriture de solde des comptes de produits (classe 7)
    const ecritureProduits = await this.models.EcritureComptable.create({
      societeId: exercice.societeId,
      journalCode: 'OD',
      dateEcriture: exercice.dateFin,
      libelle: `Solde des comptes de produits - ${exercice.libelle}`,
      reference: `CLOTURE-PRODUITS-${exercice.id}`,
      totalDebit: resultatCalcul.totalProduits,
      totalCredit: resultatCalcul.totalProduits,
      statut: 'VALIDEE',
      typeEcriture: 'CLOTURE'
    }, { transaction });

    // Lignes pour solder les comptes de produits
    await this.genererLignesSoldeComptes(ecritureProduits.id, exercice, 7, 'DEBIT', transaction);

    ecritures.push(ecritureProduits);

    return ecritures;
  }

  /**
   * Génère les écritures de clôture
   */
  async genererEcrituresCloture(exercice, transaction) {
    const ecritures = [];

    // Écriture de clôture des comptes de bilan
    const ecritureCloture = await this.models.EcritureComptable.create({
      societeId: exercice.societeId,
      journalCode: 'OD',
      dateEcriture: exercice.dateFin,
      libelle: `Clôture des comptes - ${exercice.libelle}`,
      reference: `CLOTURE-BILAN-${exercice.id}`,
      statut: 'VALIDEE',
      typeEcriture: 'CLOTURE'
    }, { transaction });

    // Génération des lignes de clôture pour les classes 1 à 5
    let totalDebit = 0;
    let totalCredit = 0;

    for (let classe = 1; classe <= 5; classe++) {
      const { debit, credit } = await this.genererLignesSoldeComptes(ecritureCloture.id, exercice, classe, 'INVERSE', transaction);
      totalDebit += debit;
      totalCredit += credit;
    }

    // Mise à jour des totaux
    await ecritureCloture.update({
      totalDebit,
      totalCredit
    }, { transaction });

    ecritures.push(ecritureCloture);

    return ecritures;
  }

  /**
   * Crée l'exercice suivant
   */
  async creerExerciceSuivant(exercice, transaction) {
    const dateDebutSuivant = new Date(exercice.dateFin);
    dateDebutSuivant.setDate(dateDebutSuivant.getDate() + 1);

    const dateFinSuivant = new Date(dateDebutSuivant);
    dateFinSuivant.setFullYear(dateFinSuivant.getFullYear() + 1);
    dateFinSuivant.setDate(dateFinSuivant.getDate() - 1);

    const exerciceSuivant = await this.models.ExerciceComptable.create({
      societeId: exercice.societeId,
      libelle: `Exercice ${dateDebutSuivant.getFullYear()}`,
      dateDebut: dateDebutSuivant,
      dateFin: dateFinSuivant,
      statut: 'OUVERT',
      deviseId: exercice.deviseId
    }, { transaction });

    return exerciceSuivant;
  }

  /**
   * Génère les écritures de réouverture
   */
  async genererEcrituresReouverture(exercice, exerciceSuivant, transaction) {
    const ecritures = [];

    // Écriture de réouverture des comptes de bilan
    const ecritureReouverture = await this.models.EcritureComptable.create({
      societeId: exerciceSuivant.societeId,
      journalCode: 'OD',
      dateEcriture: exerciceSuivant.dateDebut,
      libelle: `Réouverture des comptes - ${exerciceSuivant.libelle}`,
      reference: `REOUVERTURE-${exerciceSuivant.id}`,
      statut: 'VALIDEE',
      typeEcriture: 'REOUVERTURE'
    }, { transaction });

    // Génération des lignes de réouverture pour les classes 1 à 5
    let totalDebit = 0;
    let totalCredit = 0;

    for (let classe = 1; classe <= 5; classe++) {
      const { debit, credit } = await this.genererLignesReouvertureComptes(ecritureReouverture.id, exercice, classe, transaction);
      totalDebit += debit;
      totalCredit += credit;
    }

    // Mise à jour des totaux
    await ecritureReouverture.update({
      totalDebit,
      totalCredit
    }, { transaction });

    ecritures.push(ecritureReouverture);

    return ecritures;
  }

  /**
   * Génère les lignes pour solder les comptes d'une classe
   */
  async genererLignesSoldeComptes(ecritureId, exercice, classe, sens, transaction) {
    const query = `
      SELECT 
        c.numero,
        c.intitule,
        c.nature,
        SUM(l.debit) as total_debit,
        SUM(l.credit) as total_credit
      FROM compte_comptables c
      JOIN ligne_ecritures l ON c.numero = l.compte_numero
      JOIN ecriture_comptables e ON l.ecriture_id = e.id
      WHERE c.societe_id = :societeId
        AND c.classe = :classe
        AND e.date_ecriture BETWEEN :dateDebut AND :dateFin
        AND e.statut = 'VALIDEE'
      GROUP BY c.numero, c.intitule, c.nature
      HAVING ABS(SUM(l.debit) - SUM(l.credit)) > 0.01
    `;

    const [comptes] = await this.models.sequelize.query(query, {
      replacements: {
        societeId: exercice.societeId,
        classe,
        dateDebut: exercice.dateDebut,
        dateFin: exercice.dateFin
      },
      type: this.models.Sequelize.QueryTypes.SELECT,
      transaction
    });

    const lignes = [];
    let totalDebit = 0;
    let totalCredit = 0;
    let ordre = 1;

    for (const compte of comptes) {
      const solde = parseFloat(compte.total_debit) - parseFloat(compte.total_credit);
      let montantDebit = 0;
      let montantCredit = 0;

      if (sens === 'CREDIT') {
        montantCredit = Math.abs(solde);
        totalCredit += montantCredit;
      } else if (sens === 'DEBIT') {
        montantDebit = Math.abs(solde);
        totalDebit += montantDebit;
      } else if (sens === 'INVERSE') {
        if (solde > 0) {
          montantCredit = solde;
          totalCredit += montantCredit;
        } else {
          montantDebit = Math.abs(solde);
          totalDebit += montantDebit;
        }
      }

      if (montantDebit > 0 || montantCredit > 0) {
        lignes.push({
          ecritureId,
          compteNumero: compte.numero,
          libelle: `Solde ${compte.intitule}`,
          montantDebit,
          montantCredit,
          ordre: ordre++
        });
      }
    }

    if (lignes.length > 0) {
      await this.models.LigneEcriture.bulkCreate(lignes, { transaction });
    }

    return { debit: totalDebit, credit: totalCredit };
  }

  /**
   * Génère les lignes de réouverture des comptes
   */
  async genererLignesReouvertureComptes(ecritureId, exercice, classe, transaction) {
    // Récupération des soldes de fin d'exercice
    const query = `
      SELECT 
        c.numero,
        c.intitule,
        c.nature,
        SUM(l.debit) - SUM(l.credit) as solde
      FROM compte_comptables c
      JOIN ligne_ecritures l ON c.numero = l.compte_numero
      JOIN ecriture_comptables e ON l.ecriture_id = e.id
      WHERE c.societe_id = :societeId
        AND c.classe = :classe
        AND e.date_ecriture <= :dateFin
        AND e.statut = 'VALIDEE'
      GROUP BY c.numero, c.intitule, c.nature
      HAVING ABS(SUM(l.debit) - SUM(l.credit)) > 0.01
    `;

    const [comptes] = await this.models.sequelize.query(query, {
      replacements: {
        societeId: exercice.societeId,
        classe,
        dateFin: exercice.dateFin
      },
      type: this.models.Sequelize.QueryTypes.SELECT,
      transaction
    });

    const lignes = [];
    let totalDebit = 0;
    let totalCredit = 0;
    let ordre = 1;

    for (const compte of comptes) {
      const solde = parseFloat(compte.solde);
      let montantDebit = 0;
      let montantCredit = 0;

      // Réouverture dans le sens du solde
      if (solde > 0) {
        montantDebit = solde;
        totalDebit += montantDebit;
      } else {
        montantCredit = Math.abs(solde);
        totalCredit += montantCredit;
      }

      lignes.push({
        ecritureId,
        compteNumero: compte.numero,
        libelle: `À nouveau ${compte.intitule}`,
        montantDebit,
        montantCredit,
        ordre: ordre++
      });
    }

    if (lignes.length > 0) {
      await this.models.LigneEcriture.bulkCreate(lignes, { transaction });
    }

    return { debit: totalDebit, credit: totalCredit };
  }

  /**
   * Supprime les écritures de clôture
   */
  async supprimerEcrituresCloture(exercice, transaction) {
    await this.models.EcritureComptable.destroy({
      where: {
        societeId: exercice.societeId,
        typeEcriture: ['CLOTURE', 'REOUVERTURE'],
        reference: {
          [this.models.Sequelize.Op.like]: `%${exercice.id}%`
        }
      },
      transaction
    });
  }

  /**
   * Archive les données de l'exercice
   */
  async archiverDonneesExercice(exercice, transaction) {
    // Implémentation simplifiée
    // Dans une version complète, on pourrait :
    // - Créer des tables d'archive
    // - Compresser les données
    // - Exporter vers un système externe
    
    logger.info('Archivage des données', {
      exerciceId: exercice.id,
      message: 'Fonctionnalité d\'archivage à implémenter'
    });
  }

  /**
   * Calcule les statistiques des écritures
   */
  async calculerStatistiquesEcritures(exercice) {
    const stats = await this.models.EcritureComptable.findAll({
      where: {
        societeId: exercice.societeId,
        dateEcriture: {
          [this.models.Sequelize.Op.between]: [exercice.dateDebut, exercice.dateFin]
        }
      },
      attributes: [
        [this.models.sequelize.fn('COUNT', this.models.sequelize.col('id')), 'nombreEcritures'],
        [this.models.sequelize.fn('SUM', this.models.sequelize.col('totalDebit')), 'totalMouvements']
      ],
      raw: true
    });

    return {
      nombreEcritures: parseInt(stats[0]?.nombreEcritures || 0),
      totalMouvements: parseFloat(stats[0]?.totalMouvements || 0)
    };
  }

  /**
   * Calcule la balance finale
   */
  async calculerBalanceFinale(exercice) {
    const query = `
      SELECT 
        COUNT(DISTINCT c.numero) as nombre_comptes,
        SUM(l.debit) as total_debit,
        SUM(l.credit) as total_credit
      FROM compte_comptables c
      JOIN ligne_ecritures l ON c.numero = l.compte_numero
      JOIN ecriture_comptables e ON l.ecriture_id = e.id
      WHERE c.societe_id = :societeId
        AND e.date_ecriture BETWEEN :dateDebut AND :dateFin
        AND e.statut = 'VALIDEE'
    `;

    const [result] = await this.models.sequelize.query(query, {
      replacements: {
        societeId: exercice.societeId,
        dateDebut: exercice.dateDebut,
        dateFin: exercice.dateFin
      },
      type: this.models.Sequelize.QueryTypes.SELECT
    });

    return {
      nombreComptes: parseInt(result[0]?.nombre_comptes || 0),
      totalDebit: parseFloat(result[0]?.total_debit || 0),
      totalCredit: parseFloat(result[0]?.total_credit || 0)
    };
  }

  /**
   * Calcule le résultat détaillé
   */
  async calculerResultatDetaille(exercice) {
    return await this.calculerResultatExercice(exercice);
  }

  /**
   * Calcule les ratios financiers
   */
  async calculerRatiosFinanciers(exercice) {
    // Implémentation simplifiée
    // Utiliser AccountingCalculationService pour les calculs complets
    return {
      message: 'Ratios financiers - À implémenter avec AccountingCalculationService'
    };
  }

  /**
   * Calcule l'évolution par rapport à l'exercice précédent
   */
  async calculerEvolutionExercice(exercice) {
    const exercicePrecedent = await this.models.ExerciceComptable.findOne({
      where: {
        societeId: exercice.societeId,
        dateFin: {
          [this.models.Sequelize.Op.lt]: exercice.dateDebut
        }
      },
      order: [['dateFin', 'DESC']]
    });

    if (!exercicePrecedent) {
      return {
        message: 'Aucun exercice précédent pour comparaison'
      };
    }

    // Calcul simplifié de l'évolution
    return {
      exercicePrecedent: {
        libelle: exercicePrecedent.libelle,
        resultat: exercicePrecedent.resultatExercice
      },
      evolution: {
        resultat: exercice.resultatExercice - (exercicePrecedent.resultatExercice || 0)
      }
    };
  }
}

module.exports = ExerciseService;