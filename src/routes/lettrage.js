'use strict';

const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');
const { validate } = require('../middleware/validation');
const { protect } = require('../middleware/auth');
const lettrageController = require('../controllers/lettrageController');

// Schémas de validation
const lettrageManuelSchema = [
  body('compteNumero')
    .notEmpty()
    .withMessage('Le numéro de compte est obligatoire')
    .isLength({ min: 3, max: 10 })
    .withMessage('Le numéro de compte doit faire entre 3 et 10 caractères'),
  body('ligneIds')
    .isArray({ min: 2 })
    .withMessage('Au moins 2 lignes d\'écriture sont requises pour le lettrage'),
  body('ligneIds.*')
    .isUUID()
    .withMessage('Chaque ID de ligne doit être un UUID valide'),
  body('codeLettrage')
    .optional()
    .isLength({ min: 1, max: 10 })
    .withMessage('Le code de lettrage doit faire entre 1 et 10 caractères')
];

const lettrageAutomatiqueSchema = [
  body('compteNumero')
    .notEmpty()
    .withMessage('Le numéro de compte est obligatoire')
    .isLength({ min: 3, max: 10 })
    .withMessage('Le numéro de compte doit faire entre 3 et 10 caractères'),
  body('criteres')
    .optional()
    .isObject()
    .withMessage('Les critères doivent être un objet'),
  body('criteres.dateDebut')
    .optional()
    .isISO8601()
    .withMessage('La date de début doit être au format ISO 8601'),
  body('criteres.dateFin')
    .optional()
    .isISO8601()
    .withMessage('La date de fin doit être au format ISO 8601'),
  body('criteres.toleranceMontant')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('La tolérance de montant doit être un nombre positif'),
  body('criteres.toleranceDate')
    .optional()
    .isInt({ min: 0 })
    .withMessage('La tolérance de date doit être un nombre entier positif'),
  body('criteres.lettrerParMontant')
    .optional()
    .isBoolean()
    .withMessage('lettrerParMontant doit être un booléen'),
  body('criteres.lettrerParReference')
    .optional()
    .isBoolean()
    .withMessage('lettrerParReference doit être un booléen')
];

const delettrageSchema = [
  body('ligneIds')
    .isArray({ min: 1 })
    .withMessage('Au moins une ligne d\'écriture est requise pour le délettrage'),
  body('ligneIds.*')
    .isUUID()
    .withMessage('Chaque ID de ligne doit être un UUID valide')
];

const compteNumeroSchema = [
  param('compteNumero')
    .notEmpty()
    .withMessage('Le numéro de compte est obligatoire')
    .isLength({ min: 3, max: 10 })
    .withMessage('Le numéro de compte doit faire entre 3 et 10 caractères')
];

const filtresLignesSchema = [
  ...compteNumeroSchema,
  query('dateDebut')
    .optional()
    .isISO8601()
    .withMessage('La date de début doit être au format ISO 8601'),
  query('dateFin')
    .optional()
    .isISO8601()
    .withMessage('La date de fin doit être au format ISO 8601'),
  query('montantMin')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Le montant minimum doit être un nombre positif'),
  query('montantMax')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Le montant maximum doit être un nombre positif'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Le numéro de page doit être un entier positif'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('La limite doit être entre 1 et 100'),
  query('seulement_non_lettrees')
    .optional()
    .isBoolean()
    .withMessage('seulement_non_lettrees doit être un booléen')
];

const filtresSoldesSchema = [
  ...compteNumeroSchema,
  query('dateDebut')
    .optional()
    .isISO8601()
    .withMessage('La date de début doit être au format ISO 8601'),
  query('dateFin')
    .optional()
    .isISO8601()
    .withMessage('La date de fin doit être au format ISO 8601')
];

/**
 * Middleware d'initialisation du controller
 */
router.use((req, res, next) => {
  if (!lettrageController.lettrageService) {
    lettrageController.init(req.app.get('models'));
  }
  next();
});

/**
 * @swagger
 * /api/v1/lettrage/manuel:
 *   post:
 *     summary: Effectue un lettrage manuel
 *     tags: [Lettrage]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - compteNumero
 *               - ligneIds
 *             properties:
 *               compteNumero:
 *                 type: string
 *                 description: Numéro du compte
 *               ligneIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 description: IDs des lignes à lettrer
 *               codeLettrage:
 *                 type: string
 *                 description: Code de lettrage (optionnel)
 *     responses:
 *       200:
 *         description: Lettrage effectué avec succès
 *       400:
 *         description: Données invalides
 *       401:
 *         description: Non authentifié
 */
router.post('/manuel',
  protect,
  validate(lettrageManuelSchema),
  lettrageController.lettrageManuel.bind(lettrageController)
);

/**
 * @swagger
 * /api/v1/lettrage/automatique:
 *   post:
 *     summary: Effectue un lettrage automatique
 *     tags: [Lettrage]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - compteNumero
 *             properties:
 *               compteNumero:
 *                 type: string
 *                 description: Numéro du compte
 *               criteres:
 *                 type: object
 *                 properties:
 *                   dateDebut:
 *                     type: string
 *                     format: date
 *                   dateFin:
 *                     type: string
 *                     format: date
 *                   toleranceMontant:
 *                     type: number
 *                     default: 0.01
 *                   toleranceDate:
 *                     type: integer
 *                     default: 0
 *                   lettrerParMontant:
 *                     type: boolean
 *                     default: true
 *                   lettrerParReference:
 *                     type: boolean
 *                     default: true
 *     responses:
 *       200:
 *         description: Lettrage automatique terminé
 *       400:
 *         description: Données invalides
 *       401:
 *         description: Non authentifié
 */
router.post('/automatique',
  protect,
  validate(lettrageAutomatiqueSchema),
  lettrageController.lettrageAutomatique.bind(lettrageController)
);

/**
 * @swagger
 * /api/v1/lettrage/delettrage:
 *   post:
 *     summary: Effectue un délettrage
 *     tags: [Lettrage]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ligneIds
 *             properties:
 *               ligneIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 description: IDs des lignes à délettrer
 *     responses:
 *       200:
 *         description: Délettrage effectué avec succès
 *       400:
 *         description: Données invalides
 *       401:
 *         description: Non authentifié
 */
router.post('/delettrage',
  protect,
  validate(delettrageSchema),
  lettrageController.delettrage.bind(lettrageController)
);

/**
 * @swagger
 * /api/v1/lettrage/lignes/{compteNumero}:
 *   get:
 *     summary: Récupère les lignes à lettrer pour un compte
 *     tags: [Lettrage]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: compteNumero
 *         required: true
 *         schema:
 *           type: string
 *         description: Numéro du compte
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin
 *       - in: query
 *         name: montantMin
 *         schema:
 *           type: number
 *         description: Montant minimum
 *       - in: query
 *         name: montantMax
 *         schema:
 *           type: number
 *         description: Montant maximum
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Numéro de page
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Nombre d'éléments par page
 *       - in: query
 *         name: seulement_non_lettrees
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Afficher seulement les lignes non lettrées
 *     responses:
 *       200:
 *         description: Lignes récupérées avec succès
 *       400:
 *         description: Paramètres invalides
 *       401:
 *         description: Non authentifié
 */
router.get('/lignes/:compteNumero',
  protect,
  validate(filtresLignesSchema),
  lettrageController.getLignesALettrer.bind(lettrageController)
);

/**
 * @swagger
 * /api/v1/lettrage/soldes/{compteNumero}:
 *   get:
 *     summary: Récupère les soldes lettrés et non lettrés d'un compte
 *     tags: [Lettrage]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: compteNumero
 *         required: true
 *         schema:
 *           type: string
 *         description: Numéro du compte
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin
 *     responses:
 *       200:
 *         description: Soldes calculés avec succès
 *       400:
 *         description: Paramètres invalides
 *       401:
 *         description: Non authentifié
 */
router.get('/soldes/:compteNumero',
  protect,
  validate(filtresSoldesSchema),
  lettrageController.getSoldes.bind(lettrageController)
);

/**
 * @swagger
 * /api/v1/lettrage/statistiques/{compteNumero}:
 *   get:
 *     summary: Récupère les statistiques de lettrage pour un compte
 *     tags: [Lettrage]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: compteNumero
 *         required: true
 *         schema:
 *           type: string
 *         description: Numéro du compte
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin
 *     responses:
 *       200:
 *         description: Statistiques calculées avec succès
 *       400:
 *         description: Paramètres invalides
 *       401:
 *         description: Non authentifié
 */
router.get('/statistiques/:compteNumero',
  protect,
  validate(filtresSoldesSchema),
  lettrageController.getStatistiques.bind(lettrageController)
);

module.exports = router;
