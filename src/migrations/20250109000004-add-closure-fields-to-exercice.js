'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Ajouter la colonne dateCloture
    await queryInterface.addColumn('exercice_comptables', 'dateCloture', {
      type: Sequelize.DATE,
      allowNull: true
    });

    // Ajouter la colonne resultatExercice
    await queryInterface.addColumn('exercice_comptables', 'resultatExercice', {
      type: Sequelize.DECIMAL(15, 2),
      allowNull: true
    });

    // Ajouter un index sur dateCloture pour les performances
    await queryInterface.addIndex('exercice_comptables', ['dateCloture']);
  },

  async down(queryInterface, Sequelize) {
    // Supprimer l'index
    await queryInterface.removeIndex('exercice_comptables', ['dateCloture']);
    
    // Supprimer les colonnes
    await queryInterface.removeColumn('exercice_comptables', 'resultatExercice');
    await queryInterface.removeColumn('exercice_comptables', 'dateCloture');
  }
};