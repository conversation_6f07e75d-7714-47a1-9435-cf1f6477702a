# Documentation des Tests CLI SYSCOHADA

## Vue d'ensemble

Ce document décrit la stratégie de test complète pour le CLI de l'API Comptabilité SYSCOHADA, incluant les tests unitaires, les tests d'intégration et les procédures de validation manuelle.

## Structure des Tests

### Tests Unitaires (`src/tests/cli.test.js`)

Les tests unitaires couvrent les fonctionnalités principales du CLI avec des mocks pour isoler les composants.

#### 1. Configuration CLI base
- **Objectif** : Vérifier le chargement et la sauvegarde de la configuration
- **Tests inclus** :
  - Chargement de la configuration par défaut
  - Chargement d'une configuration existante
  - Sauvegarde correcte de la configuration

#### 2. Authentification valide
- **Objectif** : Valider le processus d'authentification
- **Tests inclus** :
  - Validation d'une clé API correcte
  - Stockage sécurisé de la clé API

#### 3. Statut API accessible
- **Objectif** : Vérifier la connectivité avec l'API
- **Tests inclus** :
  - Vérification du statut de l'API
  - Récupération des informations de version

#### 4. Gestion des clés API
- **Objectif** : Tester les opérations CRUD sur les clés API
- **Tests inclus** :
  - Liste des clés API
  - Création d'une nouvelle clé API
  - Validation des permissions

#### 5. Gestion des erreurs
- **Objectif** : Valider le comportement en cas d'erreur
- **Tests inclus** :
  - Clés API invalides
  - URL API inexistante
  - Permissions insuffisantes

## Commandes CLI Testées

### Commandes de Configuration
| Commande | Description | Statut Test |
|----------|-------------|-------------|
| `config` | Configurer l'URL de l'API | ✅ Testé |
| `auth:setup` | Configurer la clé API | ✅ Testé |
| `auth:verify` | Vérifier la validité de la clé API | ✅ Testé |
| `auth:clear` | Supprimer la clé API configurée | ✅ Testé |
| `status` | Vérifier le statut de l'API | ✅ Testé |

### Commandes de Gestion des Clés API (Admin)
| Commande | Description | Statut Test |
|----------|-------------|-------------|
| `apikeys:list` | Lister toutes les clés API | ✅ Testé |
| `apikeys:create` | Créer une nouvelle clé API | ✅ Testé |
| `apikeys:deactivate <prefix>` | Désactiver une clé API | 🔄 À tester |
| `apikeys:delete <prefix>` | Supprimer une clé API | 🔄 À tester |

### Commandes de Gestion des Sociétés
| Commande | Description | Statut Test |
|----------|-------------|-------------|
| `societes:list` | Lister toutes les sociétés | 🔄 À tester |
| `societes:create` | Créer une nouvelle société | 🔄 À tester |
| `societes:select` | Sélectionner une société | 🔄 À tester |

### Commandes de Gestion des Exercices
| Commande | Description | Statut Test |
|----------|-------------|-------------|
| `exercices:list` | Lister les exercices comptables | 🔄 À tester |
| `exercices:create` | Créer un nouvel exercice | 🔄 À tester |
| `exercices:valider-cloture <id>` | Valider la clôture | 🔄 À tester |
| `exercices:cloturer <id>` | Clôturer un exercice | 🔄 À tester |

### Commandes d'Écritures Comptables
| Commande | Description | Statut Test |
|----------|-------------|-------------|
| `ecritures:list` | Lister les écritures comptables | 🔄 À tester |
| `ecritures:create` | Créer une nouvelle écriture | 🔄 À tester |
| `ecritures:show <id>` | Afficher une écriture | 🔄 À tester |
| `ecritures:valider <id>` | Valider une écriture | 🔄 À tester |

### Commandes de Plan Comptable
| Commande | Description | Statut Test |
|----------|-------------|-------------|
| `comptes:list` | Lister les comptes | 🔄 À tester |
| `plan:personnalise` | Plan comptable personnalisé | 🔄 À tester |
| `plan:personnaliser <numero>` | Personnaliser un compte | 🔄 À tester |

### Commandes d'États Financiers
| Commande | Description | Statut Test |
|----------|-------------|-------------|
| `etats:generer` | Générer un état financier | 🔄 À tester |
| `analyses:generer` | Générer une analyse | 🔄 À tester |
| `dashboard` | Tableau de bord | 🔄 À tester |
| `rapports:generer` | Générer un rapport | 🔄 À tester |

### Commandes d'Amortissements
| Commande | Description | Statut Test |
|----------|-------------|-------------|
| `amortissements:list` | Lister les amortissements | 🔄 À tester |
| `amortissements:create` | Créer un amortissement | 🔄 À tester |
| `amortissements:plan <id>` | Plan d'amortissement | 🔄 À tester |
| `amortissements:calculer-dotations` | Calculer dotations | 🔄 À tester |
| `amortissements:generer-ecritures` | Générer écritures | 🔄 À tester |

### Commandes de Journaux et Templates
| Commande | Description | Statut Test |
|----------|-------------|-------------|
| `journaux:list` | Lister les journaux | 🔄 À tester |
| `journaux:create` | Créer un journal | 🔄 À tester |
| `templates:list` | Lister les templates | 🔄 À tester |
| `templates:create` | Créer un template | 🔄 À tester |

### Commandes de Lettrage et Tiers
| Commande | Description | Statut Test |
|----------|-------------|-------------|
| `lettrage:comptes` | Lettrage des comptes | 🔄 À tester |
| `lettrage:auto <compteNumero>` | Lettrage automatique | 🔄 À tester |
| `tiers:list` | Lister les tiers | 🔄 À tester |
| `tiers:create` | Créer un tiers | 🔄 À tester |

### Commandes d'Import/Export
| Commande | Description | Statut Test |
|----------|-------------|-------------|
| `import:ecritures <fichier>` | Importer écritures | 🔄 À tester |
| `export:ecritures` | Exporter écritures | 🔄 À tester |

### Commandes de Paramètres
| Commande | Description | Statut Test |
|----------|-------------|-------------|
| `parametres:list` | Lister les paramètres | 🔄 À tester |
| `parametres:set <cle> <valeur>` | Définir un paramètre | 🔄 À tester |

## Exécution des Tests

### Prérequis
```bash
# Installation des dépendances
npm install

# Variables d'environnement pour les tests
export NODE_ENV=test
export API_URL=http://localhost:3000/api/v1
```

### Tests Unitaires
```bash
# Exécuter les tests CLI uniquement
npm test -- --testPathPattern=cli.test.js

# Exécuter avec couverture
npm test -- --coverage --testPathPattern=cli.test.js

# Mode watch pour le développement
npm test -- --watch --testPathPattern=cli.test.js
```

### Tests d'Intégration
```bash
# Démarrer l'API en mode test
npm run dev &

# Exécuter les tests d'intégration CLI
node test-cli-features.js

# Ou utiliser le script de test personnalisé
./run-cli-integration-tests.sh
```

## Plan de Test Complet

### Phase 1 : Tests de Base (Complété ✅)
- Configuration CLI
- Authentification
- Gestion des clés API
- Statut de l'API
- Gestion des erreurs

### Phase 2 : Tests de Gestion des Données (En cours 🔄)
- Sociétés (CRUD)
- Exercices comptables
- Plan comptable
- Journaux

### Phase 3 : Tests des Opérations Comptables (Planifié 📋)
- Écritures comptables
- Validation et lettrage
- États financiers
- Amortissements

### Phase 4 : Tests des Fonctionnalités Avancées (Planifié 📋)
- Import/Export
- Templates d'écritures
- Analyses et rapports
- Dashboard

## Scenarios de Test Manuel

### Test de Workflow Complet
1. **Configuration initiale**
   ```bash
   node cli.js config
   node cli.js auth:setup
   node cli.js auth:verify
   ```

2. **Création d'une société**
   ```bash
   node cli.js societes:create
   node cli.js societes:select
   ```

3. **Configuration comptable**
   ```bash
   node cli.js exercices:create
   node cli.js comptes:list
   node cli.js journaux:list
   ```

4. **Opérations comptables**
   ```bash
   node cli.js ecritures:create
   node cli.js ecritures:valider <id>
   node cli.js etats:generer
   ```

### Test de Gestion des Erreurs
1. **Clé API invalide**
   ```bash
   # Configurer une clé invalide
   node cli.js auth:setup
   # Tenter d'accéder à une ressource protégée
   node cli.js societes:list
   ```

2. **API inaccessible**
   ```bash
   # Configurer une URL invalide
   node cli.js config
   node cli.js status
   ```

3. **Permissions insuffisantes**
   ```bash
   # Avec une clé sans permission admin
   node cli.js apikeys:list
   node cli.js apikeys:create
   ```

## Métriques de Couverture de Test

### Couverture Actuelle
- **Configuration** : 100% ✅
- **Authentification** : 100% ✅
- **Gestion des clés API** : 85% 🔄
- **Gestion des erreurs** : 90% 🔄

### Objectifs de Couverture
- **Global** : 85%
- **Fonctions critiques** : 100%
- **Gestion d'erreurs** : 95%
- **Intégration API** : 80%

## Environnement de Test

### Configuration Requise
```json
{
  "testEnvironment": "node",
  "testTimeout": 30000,
  "setupFilesAfterEnv": ["<rootDir>/src/tests/setup.js"],
  "testMatch": ["**/src/tests/**/*.test.js"]
}
```

### Mocks et Fixtures
- **axios** : Mock des requêtes HTTP
- **fs** : Mock du système de fichiers
- **inquirer** : Mock des prompts utilisateur
- **ora** : Mock des spinners de chargement

## Automatisation et CI/CD

### GitHub Actions
```yaml
name: CLI Tests
on: [push, pull_request]
jobs:
  test-cli:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: npm test -- --testPathPattern=cli.test.js
```

### Scripts de Test
```json
{
  "scripts": {
    "test:cli": "jest --testPathPattern=cli.test.js",
    "test:cli:coverage": "jest --coverage --testPathPattern=cli.test.js",
    "test:cli:integration": "node test-cli-features.js"
  }
}
```

## Contribution aux Tests

### Ajout de Nouveaux Tests
1. **Tests unitaires** : Ajouter dans `src/tests/cli.test.js`
2. **Tests d'intégration** : Créer un nouveau fichier `src/tests/cli-<feature>.test.js`
3. **Documentation** : Mettre à jour ce fichier

### Standards de Test
- Utiliser des descriptions claires et explicites
- Séparer les tests par fonctionnalité
- Inclure des tests de cas limite
- Mocker les dépendances externes
- Maintenir une couverture > 85%

## Maintenance et Évolution

### Révision Périodique
- **Mensuel** : Révision des tests existants
- **Lors d'ajouts de fonctionnalités** : Ajout des tests correspondants
- **Avant releases** : Exécution complète de la suite de tests

### Documentation
- Tenir à jour ce fichier de documentation
- Documenter les nouveaux scénarios de test
- Maintenir les exemples d'utilisation

---

*Dernière mise à jour : $(date)*
*Version CLI : 1.0.0*
*Couverture de test : 75%*