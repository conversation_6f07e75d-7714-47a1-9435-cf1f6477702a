'use strict';

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const logger = require('../config/logger');

/**
 * Configuration du middleware d'upload de fichiers avec Multer
 */

// Créer le dossier temp s'il n'existe pas
const tempDir = path.join(process.cwd(), 'temp');
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

// Configuration du stockage
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, tempDir);
  },
  filename: function (req, file, cb) {
    // Générer un nom unique pour éviter les conflits
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    const baseName = path.basename(file.originalname, extension);
    cb(null, `${baseName}-${uniqueSuffix}${extension}`);
  }
});

// Fonction de filtrage des fichiers
const fileFilter = (req, file, cb) => {
  // Types de fichiers autorisés
  const allowedMimeTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel', // .xls
    'text/csv', // .csv
    'application/csv' // .csv (alternative)
  ];

  const allowedExtensions = ['.xlsx', '.xls', '.csv'];
  const fileExtension = path.extname(file.originalname).toLowerCase();

  if (allowedMimeTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
    cb(null, true);
  } else {
    const error = new Error('Type de fichier non autorisé. Seuls les fichiers Excel (.xlsx, .xls) et CSV (.csv) sont acceptés.');
    error.code = 'INVALID_FILE_TYPE';
    cb(error, false);
  }
};

// Configuration principale de Multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50 MB maximum
    files: 1 // Un seul fichier à la fois
  }
});

/**
 * Middleware pour l'upload d'un seul fichier d'import
 */
const uploadSingleFile = upload.single('fichier');

/**
 * Middleware avec gestion d'erreurs personnalisée
 */
const uploadWithErrorHandling = (req, res, next) => {
  uploadSingleFile(req, res, (err) => {
    if (err instanceof multer.MulterError) {
      logger.error('Erreur Multer lors de l\'upload', {
        code: err.code,
        message: err.message,
        field: err.field,
        utilisateur: req.user?.id
      });

      switch (err.code) {
        case 'LIMIT_FILE_SIZE':
          return res.status(400).json({
            success: false,
            message: 'Le fichier est trop volumineux. Taille maximum autorisée : 50 MB',
            code: 'FILE_TOO_LARGE'
          });
        case 'LIMIT_FILE_COUNT':
          return res.status(400).json({
            success: false,
            message: 'Trop de fichiers. Un seul fichier autorisé à la fois',
            code: 'TOO_MANY_FILES'
          });
        case 'LIMIT_UNEXPECTED_FILE':
          return res.status(400).json({
            success: false,
            message: 'Champ de fichier inattendu. Utilisez le champ "fichier"',
            code: 'UNEXPECTED_FIELD'
          });
        default:
          return res.status(400).json({
            success: false,
            message: 'Erreur lors de l\'upload du fichier',
            code: 'UPLOAD_ERROR'
          });
      }
    } else if (err) {
      logger.error('Erreur lors de l\'upload', {
        message: err.message,
        code: err.code,
        utilisateur: req.user?.id
      });

      if (err.code === 'INVALID_FILE_TYPE') {
        return res.status(400).json({
          success: false,
          message: err.message,
          code: 'INVALID_FILE_TYPE'
        });
      }

      return res.status(500).json({
        success: false,
        message: 'Erreur interne lors de l\'upload',
        code: 'INTERNAL_UPLOAD_ERROR'
      });
    }

    // Validation supplémentaire du fichier uploadé
    if (req.file) {
      const fileExtension = path.extname(req.file.originalname).toLowerCase();
      const fileSize = req.file.size;

      logger.info('Fichier uploadé avec succès', {
        originalName: req.file.originalname,
        filename: req.file.filename,
        size: fileSize,
        extension: fileExtension,
        utilisateur: req.user?.id
      });

      // Vérification de la taille minimale (au moins 1 KB)
      if (fileSize < 1024) {
        // Supprimer le fichier uploadé
        if (fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
        }

        return res.status(400).json({
          success: false,
          message: 'Le fichier est trop petit ou vide',
          code: 'FILE_TOO_SMALL'
        });
      }

      // Vérification de l'extension
      if (!['.xlsx', '.xls', '.csv'].includes(fileExtension)) {
        // Supprimer le fichier uploadé
        if (fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
        }

        return res.status(400).json({
          success: false,
          message: 'Extension de fichier non autorisée',
          code: 'INVALID_EXTENSION'
        });
      }
    }

    next();
  });
};

/**
 * Middleware de nettoyage des fichiers temporaires
 * À utiliser après traitement du fichier
 */
const cleanupTempFile = (req, res, next) => {
  // Nettoyer le fichier temporaire après la réponse
  res.on('finish', () => {
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlink(req.file.path, (err) => {
        if (err) {
          logger.error('Erreur lors de la suppression du fichier temporaire', {
            path: req.file.path,
            error: err.message
          });
        } else {
          logger.debug('Fichier temporaire supprimé', {
            path: req.file.path
          });
        }
      });
    }
  });

  next();
};

/**
 * Fonction utilitaire pour nettoyer manuellement un fichier
 */
const cleanupFile = (filePath) => {
  if (filePath && fs.existsSync(filePath)) {
    try {
      fs.unlinkSync(filePath);
      logger.debug('Fichier nettoyé manuellement', { path: filePath });
    } catch (error) {
      logger.error('Erreur lors du nettoyage manuel du fichier', {
        path: filePath,
        error: error.message
      });
    }
  }
};

/**
 * Fonction de nettoyage périodique des fichiers temporaires anciens
 */
const cleanupOldTempFiles = () => {
  try {
    const files = fs.readdirSync(tempDir);
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 heures

    files.forEach(file => {
      const filePath = path.join(tempDir, file);
      const stats = fs.statSync(filePath);
      
      if (now - stats.mtime.getTime() > maxAge) {
        fs.unlinkSync(filePath);
        logger.debug('Fichier temporaire ancien supprimé', { path: filePath });
      }
    });
  } catch (error) {
    logger.error('Erreur lors du nettoyage des fichiers temporaires anciens', {
      error: error.message
    });
  }
};

// Programmer le nettoyage périodique (toutes les heures)
setInterval(cleanupOldTempFiles, 60 * 60 * 1000);

module.exports = {
  uploadWithErrorHandling,
  cleanupTempFile,
  cleanupFile,
  cleanupOldTempFiles
};
