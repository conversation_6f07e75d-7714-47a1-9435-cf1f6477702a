const express = require('express');
const router = express.Router();
const PartyController = require('../controllers/partyController');
const { protect } = require('../middleware/auth');
const { body, param, query } = require('express-validator');
const validation = require('../middleware/validation');

/**
 * Routes pour la gestion des tiers (clients/fournisseurs)
 */

// Middleware d'authentification pour toutes les routes
router.use(protect);

/**
 * @route GET /api/societes/:societeId/parties
 * @desc Obtient la liste des tiers d'une société
 * @access Private
 */
router.get(
  '/:societeId/parties',
  [
    param('societeId').isUUID().withMessage('ID de société invalide'),
    query('type').optional().isIn(['CLIENT', 'FOURNISSEUR', 'CLIENT_FOURNISSEUR']).withMessage('Type invalide'),
    query('actif').optional().isBoolean().withMessage('Statut actif invalide'),
    query('page').optional().isInt({ min: 1 }).withMessage('Numéro de page invalide'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite invalide'),
    query('sortBy').optional().isIn(['nom', 'code', 'type', 'created_at']).withMessage('Champ de tri invalide'),
    query('sortOrder').optional().isIn(['ASC', 'DESC']).withMessage('Ordre de tri invalide')
  ],
  validation.handleValidationErrors,
  PartyController.getParties
);

/**
 * @route GET /api/societes/:societeId/parties/search
 * @desc Recherche des tiers
 * @access Private
 */
router.get(
  '/:societeId/parties/search',
  [
    param('societeId').isUUID().withMessage('ID de société invalide'),
    query('q').isLength({ min: 2 }).withMessage('Le terme de recherche doit contenir au moins 2 caractères'),
    query('type').optional().isIn(['CLIENT', 'FOURNISSEUR', 'CLIENT_FOURNISSEUR']).withMessage('Type invalide')
  ],
  validation.handleValidationErrors,
  PartyController.searchParties
);

/**
 * @route GET /api/societes/:societeId/parties/clients
 * @desc Obtient la liste des clients d'une société
 * @access Private
 */
router.get(
  '/:societeId/parties/clients',
  [
    param('societeId').isUUID().withMessage('ID de société invalide')
  ],
  validation.handleValidationErrors,
  PartyController.getClients
);

/**
 * @route GET /api/societes/:societeId/parties/fournisseurs
 * @desc Obtient la liste des fournisseurs d'une société
 * @access Private
 */
router.get(
  '/:societeId/parties/fournisseurs',
  [
    param('societeId').isUUID().withMessage('ID de société invalide')
  ],
  validation.handleValidationErrors,
  PartyController.getFournisseurs
);

/**
 * @route POST /api/societes/:societeId/parties
 * @desc Crée un nouveau tiers
 * @access Private
 */
router.post(
  '/:societeId/parties',
  [
    param('societeId').isUUID().withMessage('ID de société invalide'),
    body('code')
      .isLength({ min: 2, max: 20 })
      .withMessage('Le code doit contenir entre 2 et 20 caractères')
      .matches(/^[A-Z0-9_-]+$/)
      .withMessage('Le code ne peut contenir que des lettres majuscules, chiffres, tirets et underscores'),
    body('nom')
      .isLength({ min: 2, max: 255 })
      .withMessage('Le nom doit contenir entre 2 et 255 caractères'),
    body('type')
      .isIn(['CLIENT', 'FOURNISSEUR', 'CLIENT_FOURNISSEUR'])
      .withMessage('Le type doit être CLIENT, FOURNISSEUR ou CLIENT_FOURNISSEUR'),
    body('civilite')
      .optional()
      .isIn(['M', 'MME', 'MLLE', 'DR', 'PROF', 'SARL', 'SA', 'SAS', 'EURL', 'SNC', 'GIE'])
      .withMessage('Civilité invalide'),
    body('adresse')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('L\'adresse ne peut dépasser 1000 caractères'),
    body('ville')
      .optional()
      .isLength({ max: 100 })
      .withMessage('La ville ne peut dépasser 100 caractères'),
    body('pays')
      .optional()
      .isLength({ max: 100 })
      .withMessage('Le pays ne peut dépasser 100 caractères'),
    body('telephone')
      .optional()
      .matches(/^[\d\s\-+()]*$/)
      .withMessage('Format de téléphone invalide'),
    body('email')
      .optional()
      .isEmail()
      .withMessage('Format d\'email invalide'),
    body('compteComptable')
      .optional()
      .matches(/^[0-9]{3,10}$/)
      .withMessage('Le compte comptable doit être numérique et contenir entre 3 et 10 chiffres'),
    body('conditionsPaiement')
      .optional()
      .isLength({ max: 100 })
      .withMessage('Les conditions de paiement ne peuvent dépasser 100 caractères'),
    body('plafondCredit')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Le plafond de crédit doit être positif'),
    body('numeroContribuable')
      .optional()
      .isLength({ max: 50 })
      .withMessage('Le numéro de contribuable ne peut dépasser 50 caractères'),
    body('assujettiTva')
      .optional()
      .isBoolean()
      .withMessage('L\'assujettissement TVA doit être un booléen'),
    body('actif')
      .optional()
      .isBoolean()
      .withMessage('Le statut actif doit être un booléen')
  ],
  validation.handleValidationErrors,
  PartyController.createParty
);

/**
 * @route GET /api/parties/:id
 * @desc Obtient un tiers par son ID
 * @access Private
 */
router.get(
  '/parties/:id',
  [
    param('id').isUUID().withMessage('ID de tiers invalide')
  ],
  validation.handleValidationErrors,
  PartyController.getParty
);

/**
 * @route PUT /api/parties/:id
 * @desc Met à jour un tiers
 * @access Private
 */
router.put(
  '/parties/:id',
  [
    param('id').isUUID().withMessage('ID de tiers invalide'),
    body('code')
      .optional()
      .isLength({ min: 2, max: 20 })
      .withMessage('Le code doit contenir entre 2 et 20 caractères')
      .matches(/^[A-Z0-9_-]+$/)
      .withMessage('Le code ne peut contenir que des lettres majuscules, chiffres, tirets et underscores'),
    body('nom')
      .optional()
      .isLength({ min: 2, max: 255 })
      .withMessage('Le nom doit contenir entre 2 et 255 caractères'),
    body('type')
      .optional()
      .isIn(['CLIENT', 'FOURNISSEUR', 'CLIENT_FOURNISSEUR'])
      .withMessage('Le type doit être CLIENT, FOURNISSEUR ou CLIENT_FOURNISSEUR'),
    body('civilite')
      .optional()
      .isIn(['M', 'MME', 'MLLE', 'DR', 'PROF', 'SARL', 'SA', 'SAS', 'EURL', 'SNC', 'GIE'])
      .withMessage('Civilité invalide'),
    body('adresse')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('L\'adresse ne peut dépasser 1000 caractères'),
    body('ville')
      .optional()
      .isLength({ max: 100 })
      .withMessage('La ville ne peut dépasser 100 caractères'),
    body('pays')
      .optional()
      .isLength({ max: 100 })
      .withMessage('Le pays ne peut dépasser 100 caractères'),
    body('telephone')
      .optional()
      .matches(/^[\d\s\-+()]*$/)
      .withMessage('Format de téléphone invalide'),
    body('email')
      .optional()
      .isEmail()
      .withMessage('Format d\'email invalide'),
    body('compteComptable')
      .optional()
      .matches(/^[0-9]{3,10}$/)
      .withMessage('Le compte comptable doit être numérique et contenir entre 3 et 10 chiffres'),
    body('conditionsPaiement')
      .optional()
      .isLength({ max: 100 })
      .withMessage('Les conditions de paiement ne peuvent dépasser 100 caractères'),
    body('plafondCredit')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Le plafond de crédit doit être positif'),
    body('numeroContribuable')
      .optional()
      .isLength({ max: 50 })
      .withMessage('Le numéro de contribuable ne peut dépasser 50 caractères'),
    body('assujettiTva')
      .optional()
      .isBoolean()
      .withMessage('L\'assujettissement TVA doit être un booléen'),
    body('actif')
      .optional()
      .isBoolean()
      .withMessage('Le statut actif doit être un booléen')
  ],
  validation.handleValidationErrors,
  PartyController.updateParty
);

/**
 * @route DELETE /api/parties/:id
 * @desc Supprime un tiers
 * @access Private
 */
router.delete(
  '/parties/:id',
  [
    param('id').isUUID().withMessage('ID de tiers invalide')
  ],
  validation.handleValidationErrors,
  PartyController.deleteParty
);

/**
 * @route GET /api/parties/:id/balance
 * @desc Calcule le solde d'un tiers
 * @access Private
 */
router.get(
  '/parties/:id/balance',
  [
    param('id').isUUID().withMessage('ID de tiers invalide'),
    query('dateFinale').optional().isISO8601().withMessage('Format de date invalide')
  ],
  validation.handleValidationErrors,
  PartyController.getBalance
);

module.exports = router;