'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ligne_ecritures', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      ecriture_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ecriture_comptables',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      compte_numero: {
        type: Sequelize.STRING(10),
        allowNull: false,
        references: {
          model: 'compte_comptables',
          key: 'numero'
        }
      },
      libelle: {
        type: Sequelize.STRING(200),
        allowNull: true
      },
      montant_debit: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0.00
      },
      montant_credit: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0.00
      },
      lettrage: {
        type: Sequelize.STRING(10),
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Index pour améliorer les performances
    await queryInterface.addIndex('ligne_ecritures', ['ecriture_id']);
    await queryInterface.addIndex('ligne_ecritures', ['compte_numero']);
    await queryInterface.addIndex('ligne_ecritures', ['lettrage']);
    await queryInterface.addIndex('ligne_ecritures', ['montant_debit']);
    await queryInterface.addIndex('ligne_ecritures', ['montant_credit']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ligne_ecritures');
  }
};