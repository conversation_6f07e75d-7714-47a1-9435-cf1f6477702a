/**
 * Middleware pour architecture mono-société
 * Injecte automatiquement l'ID de société configuré dans les requêtes
 */

const { config } = require('../config/env');

/**
 * Middleware qui injecte automatiquement l'ID de société
 * pour les architectures mono-société
 */
const injectSocieteId = (req, res, next) => {
  // Récupérer l'ID de société depuis la configuration de la clé API
  // ou depuis les paramètres de configuration
  const societeId = req.apiKey?.metadata?.societeId || config.DEFAULT_SOCIETE_ID;
  
  if (societeId) {
    // Injecter dans le body pour les POST/PUT
    if (req.body && typeof req.body === 'object') {
      req.body.societeId = societeId;
    }
    
    // Injecter dans les query params pour les GET
    req.query.societeId = societeId;
    
    // Ajouter dans les params pour faciliter l'accès
    req.societeId = societeId;
  }
  
  next();
};

/**
 * Middleware qui vérifie que la société configurée existe
 */
const validateSocieteExists = async (req, res, next) => {
  try {
    const models = req.app.get('models');
    const { Societe } = models;
    
    const societe = await Societe.findByPk(req.societeId);
    
    if (!societe) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SOCIETE_NOT_FOUND',
          message: 'La société configurée pour cette instance n\'existe pas',
          details: { societeId: req.societeId }
        }
      });
    }
    
    // Ajouter les infos de la société dans la requête
    req.societe = societe;
    next();
    
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware combiné pour les routes mono-société
 */
const monoSocieteMiddleware = [
  injectSocieteId,
  validateSocieteExists
];

module.exports = {
  injectSocieteId,
  validateSocieteExists,
  monoSocieteMiddleware
};
