/**
 * Middleware de gestion d'erreurs centralisé
 * API Comptabilité SYSCOHADA
 */

const { logger } = require('../config/logger');
const { config } = require('../config/env');

/**
 * Classes d'erreurs personnalisées
 */
class AppError extends Error {
  constructor(message, statusCode = 500, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.timestamp = new Date().toISOString();
    
    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends AppError {
  constructor(message, details = null) {
    super(message, 400);
    this.details = details;
  }
}

class NotFoundError extends AppError {
  constructor(resource = 'Ressource') {
    super(`${resource} non trouvée`, 404);
  }
}

class UnauthorizedError extends AppError {
  constructor(message = 'Non autorisé') {
    super(message, 401);
  }
}

class ForbiddenError extends AppError {
  constructor(message = 'Accès interdit') {
    super(message, 403);
  }
}

class ConflictError extends AppError {
  constructor(message = 'Conflit de données') {
    super(message, 409);
  }
}

class DatabaseError extends AppError {
  constructor(message = 'Erreur de base de données', originalError = null) {
    super(message, 500);
    this.originalError = originalError;
  }
}

/**
 * Gestionnaire d'erreurs Sequelize
 */
const handleSequelizeError = (error) => {
  let appError;

  switch (error.name) {
  case 'SequelizeValidationError': {
    const validationErrors = error.errors.map(err => ({
      field: err.path,
      message: err.message,
      value: err.value
    }));
    appError = new ValidationError('Erreur de validation', validationErrors);
    break;
  }

  case 'SequelizeUniqueConstraintError': {
    const field = error.errors[0]?.path || 'champ';
    appError = new ConflictError(`${field} doit être unique`);
    break;
  }

  case 'SequelizeForeignKeyConstraintError':
    appError = new ValidationError('Référence invalide vers une entité liée');
    break;

  case 'SequelizeConnectionError':
  case 'SequelizeConnectionRefusedError':
    appError = new DatabaseError('Impossible de se connecter à la base de données');
    break;

  case 'SequelizeTimeoutError':
    appError = new DatabaseError('Timeout de la base de données');
    break;

  default:
    appError = new DatabaseError('Erreur de base de données', error);
  }

  return appError;
};

/**
 * Gestionnaire d'erreurs JWT
 */
const handleJWTError = (error) => {
  if (error.name === 'JsonWebTokenError') {
    return new UnauthorizedError('Token invalide');
  }
  if (error.name === 'TokenExpiredError') {
    return new UnauthorizedError('Token expiré');
  }
  return new UnauthorizedError('Erreur d\'authentification');
};

/**
 * Formatage de la réponse d'erreur
 */
const formatErrorResponse = (error, req) => {
  const response = {
    error: error.message,
    timestamp: error.timestamp || new Date().toISOString(),
    path: req.originalUrl,
    method: req.method
  };

  // Ajouter les détails en développement
  if (config.NODE_ENV !== 'production') {
    response.stack = error.stack;
    
    if (error.originalError) {
      response.originalError = {
        message: error.originalError.message,
        stack: error.originalError.stack
      };
    }
  }

  // Ajouter les détails de validation si présents
  if (error.details) {
    response.details = error.details;
  }

  return response;
};

/**
 * Middleware de gestion d'erreurs principal
 */
const errorHandler = (error, req, res, _next) => {
  let appError = error;

  // Convertir les erreurs connues en AppError
  if (error.name && error.name.startsWith('Sequelize')) {
    appError = handleSequelizeError(error);
  } else if (error.name && (error.name.includes('JWT') || error.name.includes('Token'))) {
    appError = handleJWTError(error);
  } else if (!(error instanceof AppError)) {
    // Erreur inconnue
    appError = new AppError(
      config.NODE_ENV === 'production' 
        ? 'Erreur interne du serveur' 
        : error.message,
      500,
      false
    );
  }

  // Logger l'erreur
  const logData = {
    message: appError.message,
    statusCode: appError.statusCode,
    path: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    stack: appError.stack
  };

  if (appError.statusCode >= 500) {
    logger.error('Server Error', logData);
  } else {
    logger.warn('Client Error', logData);
  }

  // Envoyer la réponse
  res.status(appError.statusCode).json(formatErrorResponse(appError, req));
};

/**
 * Middleware pour les routes non trouvées
 */
const notFoundHandler = (req, _res, next) => {
  const error = new NotFoundError(`Route ${req.originalUrl}`);
  next(error);
};

/**
 * Gestionnaire d'erreurs asynchrones
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Gestionnaire d'exceptions non capturées
 */
const handleUncaughtException = () => {
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception', {
      message: error.message,
      stack: error.stack
    });
    
    // Arrêt gracieux
    process.exit(1);
  });
};

/**
 * Gestionnaire de rejections non gérées
 */
const handleUnhandledRejection = () => {
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection', {
      reason: reason,
      promise: promise
    });
    
    // Arrêt gracieux
    process.exit(1);
  });
};

module.exports = {
  // Classes d'erreurs
  AppError,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
  ConflictError,
  DatabaseError,
  
  // Middleware
  errorHandler,
  notFoundHandler,
  asyncHandler,
  
  // Gestionnaires globaux
  handleUncaughtException,
  handleUnhandledRejection
};
