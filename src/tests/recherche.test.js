'use strict';

const request = require('supertest');
const { expect } = require('chai');
const app = require('../app');
const models = require('../models');
const RechercheService = require('../services/rechercheService');

describe('Tests du Système de Recherche Avancée', () => {
  let societeTest, exerciceTest, journalTest;
  let ecrituresTest = [];
  let rechercheService;

  before(async () => {
    // Initialiser le service de recherche
    rechercheService = new RechercheService(models);

    // Créer des données de test
    societeTest = await models.Societe.create({
      nom: 'Société Test Recherche',
      sigle: 'STR',
      adresse: 'Test Address',
      telephone: '123456789',
      email: '<EMAIL>',
      formeJuridique: 'SARL',
      capital: 1000000,
      numeroRccm: 'RCCM-RECH-001',
      numeroIfu: 'IFU-RECH-001',
      exerciceDebut: '2024-01-01',
      exerciceFin: '2024-12-31'
    });

    exerciceTest = await models.ExerciceComptable.create({
      societeId: societeTest.id,
      libelle: 'Exercice Test Recherche 2024',
      dateDebut: '2024-01-01',
      dateFin: '2024-12-31',
      statut: 'OUVERT'
    });

    journalTest = await models.Journal.create({
      code: 'VT',
      libelle: 'Journal Ventes Test',
      type: 'VENTE',
      societeId: societeTest.id
    });

    // Créer des écritures de test variées
    await creerEcrituresTest();
  });

  async function creerEcrituresTest() {
    const ecrituresData = [
      {
        donnees: {
          societeId: societeTest.id,
          exerciceId: exerciceTest.id,
          journalCode: journalTest.code,
          dateEcriture: '2024-01-15',
          libelle: 'Vente marchandises client ABC',
          reference: 'FACT-001',
          statut: 'VALIDEE'
        },
        lignes: [
          { compteNumero: '411000', libelle: 'Client ABC', debit: 1200.00, credit: 0.00 },
          { compteNumero: '701000', libelle: 'Vente marchandises', debit: 0.00, credit: 1000.00 },
          { compteNumero: '445700', libelle: 'TVA collectée', debit: 0.00, credit: 200.00 }
        ]
      },
      {
        donnees: {
          societeId: societeTest.id,
          exerciceId: exerciceTest.id,
          journalCode: journalTest.code,
          dateEcriture: '2024-02-10',
          libelle: 'Vente services client XYZ',
          reference: 'FACT-002',
          statut: 'VALIDEE'
        },
        lignes: [
          { compteNumero: '411000', libelle: 'Client XYZ', debit: 2400.00, credit: 0.00 },
          { compteNumero: '706000', libelle: 'Prestations services', debit: 0.00, credit: 2000.00 },
          { compteNumero: '445700', libelle: 'TVA collectée', debit: 0.00, credit: 400.00 }
        ]
      },
      {
        donnees: {
          societeId: societeTest.id,
          exerciceId: exerciceTest.id,
          journalCode: journalTest.code,
          dateEcriture: '2024-03-05',
          libelle: 'Achat fournitures bureau',
          reference: 'ACH-001',
          statut: 'BROUILLARD'
        },
        lignes: [
          { compteNumero: '606000', libelle: 'Fournitures bureau', debit: 500.00, credit: 0.00 },
          { compteNumero: '445660', libelle: 'TVA déductible', debit: 100.00, credit: 0.00 },
          { compteNumero: '401000', libelle: 'Fournisseur DEF', debit: 0.00, credit: 600.00 }
        ]
      },
      {
        donnees: {
          societeId: societeTest.id,
          exerciceId: exerciceTest.id,
          journalCode: journalTest.code,
          dateEcriture: '2024-04-20',
          libelle: 'Règlement client ABC',
          reference: 'REG-001',
          statut: 'VALIDEE'
        },
        lignes: [
          { compteNumero: '521000', libelle: 'Banque', debit: 1200.00, credit: 0.00 },
          { compteNumero: '411000', libelle: 'Client ABC', debit: 0.00, credit: 1200.00 }
        ]
      }
    ];

    for (const ecritureData of ecrituresData) {
      const ecriture = await models.EcritureComptable.create(ecritureData.donnees);
      
      for (const ligneData of ecritureData.lignes) {
        await models.LigneEcriture.create({
          ecritureId: ecriture.id,
          compteNumero: ligneData.compteNumero,
          libelle: ligneData.libelle,
          montantDebit: ligneData.debit,
          montantCredit: ligneData.credit
        });
      }

      ecrituresTest.push(ecriture);
    }
  }

  describe('Service RechercheService', () => {
    it('devrait effectuer une recherche avancée par société', async () => {
      const resultats = await rechercheService.rechercheAvancee({
        societeId: societeTest.id,
        page: 1,
        limit: 10
      });

      expect(resultats.ecritures).to.be.an('array');
      expect(resultats.ecritures.length).to.be.greaterThan(0);
      expect(resultats.pagination.total).to.equal(4);
      expect(resultats.statistiques).to.exist;
    });

    it('devrait effectuer une recherche textuelle', async () => {
      const resultats = await rechercheService.rechercheTextuelle('client ABC', {
        societeId: societeTest.id
      });

      expect(resultats.ecritures).to.be.an('array');
      expect(resultats.ecritures.length).to.be.greaterThan(0);
      expect(resultats.termeRecherche).to.equal('client ABC');
      
      // Vérifier que les résultats contiennent le terme recherché
      const trouveDansLibelle = resultats.ecritures.some(ecriture => 
        ecriture.libelle && ecriture.libelle.toLowerCase().includes('client abc')
      );
      expect(trouveDansLibelle).to.be.true;
    });

    it('devrait effectuer une recherche par montant', async () => {
      const resultats = await rechercheService.rechercheParMontant(1000, 2000, {
        societeId: societeTest.id,
        typeMontant: 'DEBIT'
      });

      expect(resultats.ecritures).to.be.an('array');
      expect(resultats.criteres.montantMin).to.equal(1000);
      expect(resultats.criteres.montantMax).to.equal(2000);
    });

    it('devrait effectuer une recherche par compte', async () => {
      const resultats = await rechercheService.rechercheParCompte('411000', {
        societeId: societeTest.id,
        statut: 'VALIDEE'
      });

      expect(resultats.ecritures).to.be.an('array');
      expect(resultats.compte).to.exist;
      expect(resultats.totaux).to.exist;
      expect(resultats.totaux.totalDebit).to.be.a('number');
      expect(resultats.totaux.totalCredit).to.be.a('number');
    });

    it('devrait effectuer une recherche par période', async () => {
      const resultats = await rechercheService.rechercheParPeriode(
        '2024-01-01', 
        '2024-02-28', 
        {
          societeId: societeTest.id,
          includeAnalyses: true,
          groupeParMois: true
        }
      );

      expect(resultats.ecritures).to.be.an('array');
      expect(resultats.periode.dateDebut).to.equal('2024-01-01');
      expect(resultats.periode.dateFin).to.equal('2024-02-28');
      expect(resultats.analyses).to.exist;
      expect(resultats.groupements.parMois).to.exist;
    });

    it('devrait calculer les statistiques correctement', async () => {
      const ecritures = await models.EcritureComptable.findAll({
        where: { societeId: societeTest.id },
        include: [{ model: models.LigneEcriture, as: 'lignes' }]
      });

      const stats = await rechercheService.calculerStatistiques(ecritures);

      expect(stats.nombreEcritures).to.equal(4);
      expect(stats.nombreLignes).to.be.greaterThan(0);
      expect(stats.totalDebit).to.be.a('number');
      expect(stats.totalCredit).to.be.a('number');
      expect(stats.repartitionParStatut).to.exist;
    });
  });

  describe('API Recherche', () => {
    it('POST /api/v1/ecritures/recherche - devrait effectuer une recherche avancée', async () => {
      const criteres = {
        societeId: societeTest.id,
        statut: 'VALIDEE',
        dateDebut: '2024-01-01',
        dateFin: '2024-12-31'
      };

      const response = await request(app)
        .post('/api/v1/ecritures/recherche')
        .send(criteres)
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.ecritures).to.be.an('array');
      expect(response.body.data.pagination).to.exist;
      expect(response.body.data.statistiques).to.exist;
    });

    it('GET /api/v1/ecritures/recherche-textuelle - devrait effectuer une recherche textuelle', async () => {
      const response = await request(app)
        .get('/api/v1/ecritures/recherche-textuelle')
        .query({
          texte: 'vente',
          societeId: societeTest.id,
          rechercherDansLignes: true
        })
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.ecritures).to.be.an('array');
      expect(response.body.data.termeRecherche).to.equal('vente');
    });

    it('GET /api/v1/ecritures/recherche-montant - devrait effectuer une recherche par montant', async () => {
      const response = await request(app)
        .get('/api/v1/ecritures/recherche-montant')
        .query({
          montantMin: 500,
          montantMax: 1500,
          societeId: societeTest.id,
          typeMontant: 'TOUS'
        })
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.ecritures).to.be.an('array');
      expect(response.body.data.criteres.montantMin).to.equal(500);
    });

    it('GET /api/v1/ecritures/recherche-compte/:compteNumero - devrait effectuer une recherche par compte', async () => {
      const response = await request(app)
        .get('/api/v1/ecritures/recherche-compte/411000')
        .query({
          societeId: societeTest.id,
          includeHierarchie: false
        })
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.ecritures).to.be.an('array');
      expect(response.body.data.compte).to.exist;
      expect(response.body.data.totaux).to.exist;
    });

    it('GET /api/v1/ecritures/recherche-periode - devrait effectuer une recherche par période', async () => {
      const response = await request(app)
        .get('/api/v1/ecritures/recherche-periode')
        .query({
          dateDebut: '2024-01-01',
          dateFin: '2024-03-31',
          societeId: societeTest.id,
          groupeParMois: true,
          includeAnalyses: true
        })
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.ecritures).to.be.an('array');
      expect(response.body.data.periode).to.exist;
      expect(response.body.data.groupements).to.exist;
    });

    it('devrait retourner une erreur pour une recherche textuelle avec texte trop court', async () => {
      const response = await request(app)
        .get('/api/v1/ecritures/recherche-textuelle')
        .query({
          texte: 'a',
          societeId: societeTest.id
        })
        .expect(400);

      expect(response.body.success).to.be.false;
      expect(response.body.code).to.equal('TEXTE_RECHERCHE_INVALIDE');
    });

    it('devrait retourner une erreur pour une recherche par montant sans montants', async () => {
      const response = await request(app)
        .get('/api/v1/ecritures/recherche-montant')
        .query({
          societeId: societeTest.id
        })
        .expect(400);

      expect(response.body.success).to.be.false;
      expect(response.body.code).to.equal('MONTANTS_MANQUANTS');
    });
  });

  after(async () => {
    // Nettoyer les données de test
    if (societeTest) {
      await models.LigneEcriture.destroy({ where: {} });
      await models.EcritureComptable.destroy({ where: {} });
      await models.ExerciceComptable.destroy({ where: { societeId: societeTest.id } });
      await models.Journal.destroy({ where: { societeId: societeTest.id } });
      await models.Societe.destroy({ where: { id: societeTest.id } });
    }
  });
});
