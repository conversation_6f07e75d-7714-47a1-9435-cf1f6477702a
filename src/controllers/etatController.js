'use strict';

const { logger } = require('../config/logger');
const { ValidationError } = require('../middleware/errorHandler');

/**
 * Contrôleur pour la génération des états comptables
 */
class EtatController {
  constructor() {
    this.models = require('../models');
    const EtatService = require('../services/etatService');
    this.etatService = new EtatService(this.models);
  }

  /**
   * Génère le grand livre pour un compte ou tous les comptes
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async genererGrandLivre(req, res, next) {
    try {
      const { compteNumero } = req.params;
      const { 
        dateDebut, 
        dateFin, 
        format = 'excel', 
        includeNonValidees = false,
        detailLettrage = false,
        triParDate = true
      } = req.query;

      // Valider les paramètres obligatoires
      if (!dateDebut || !dateFin) {
        throw new ValidationError('Les dates de début et de fin sont obligatoires');
      }

      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;

      // Récupérer l'exercice si fourni
      const exerciceId = req.query.exerciceId;

      // Générer le grand livre
      const resultat = await this.etatService.genererGrandLivre(
        compteNumero,
        new Date(dateDebut),
        new Date(dateFin),
        {
          societeId,
          exerciceId,
          format,
          includeNonValidees: includeNonValidees === 'true',
          detailLettrage: detailLettrage === 'true',
          triParDate: triParDate === 'true'
        }
      );

      // Envoyer le fichier en réponse
      res.download(resultat.cheminFichier, resultat.nomFichier, (err) => {
        if (err) {
          logger.error('Erreur lors de l\'envoi du fichier', {
            cheminFichier: resultat.cheminFichier,
            error: err.message
          });
          next(err);
        }
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * Génère le journal comptable
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async genererJournal(req, res, next) {
    try {
      const { journalCode } = req.params;
      const { 
        dateDebut, 
        dateFin, 
        format = 'excel', 
        includeNonValidees = false,
        groupeParJour = false
      } = req.query;

      // Valider les paramètres obligatoires
      if (!dateDebut || !dateFin) {
        throw new ValidationError('Les dates de début et de fin sont obligatoires');
      }

      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;

      // Récupérer l'exercice si fourni
      const exerciceId = req.query.exerciceId;

      // Générer le journal
      const resultat = await this.etatService.genererJournal(
        journalCode,
        new Date(dateDebut),
        new Date(dateFin),
        {
          societeId,
          exerciceId,
          format,
          includeNonValidees: includeNonValidees === 'true',
          groupeParJour: groupeParJour === 'true'
        }
      );

      // Envoyer le fichier en réponse
      res.download(resultat.cheminFichier, resultat.nomFichier, (err) => {
        if (err) {
          logger.error('Erreur lors de l\'envoi du fichier', {
            cheminFichier: resultat.cheminFichier,
            error: err.message
          });
          next(err);
        }
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * Génère la balance générale
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async genererBalanceGenerale(req, res, next) {
    try {
      const { 
        dateDebut, 
        dateFin, 
        format = 'excel', 
        niveauDetail = 'TOUS',
        classeComptes,
        seulementAvecMouvement = false,
        includeNonValidees = false
      } = req.query;

      // Valider les paramètres obligatoires
      if (!dateDebut || !dateFin) {
        throw new ValidationError('Les dates de début et de fin sont obligatoires');
      }

      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;

      // Récupérer l'exercice si fourni
      const exerciceId = req.query.exerciceId;

      // Traiter les classes de comptes
      let classesArray = null;
      if (classeComptes) {
        classesArray = classeComptes.split(',').map(c => parseInt(c.trim()));
      }

      // Générer la balance
      const resultat = await this.etatService.genererBalanceGenerale(
        new Date(dateDebut),
        new Date(dateFin),
        {
          societeId,
          exerciceId,
          format,
          niveauDetail,
          classeComptes: classesArray,
          seulementAvecMouvement: seulementAvecMouvement === 'true',
          includeNonValidees: includeNonValidees === 'true'
        }
      );

      // Envoyer le fichier en réponse
      res.download(resultat.cheminFichier, resultat.nomFichier, (err) => {
        if (err) {
          logger.error('Erreur lors de l\'envoi du fichier', {
            cheminFichier: resultat.cheminFichier,
            error: err.message
          });
          next(err);
        }
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * Génère la balance âgée
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async genererBalanceAgee(req, res, next) {
    try {
      const { 
        dateReference, 
        format = 'excel', 
        tranches = '30,60,90,180',
        compteDebut = '4',
        compteFin = '4',
        seulementAvecSolde = true
      } = req.query;

      // Valider les paramètres obligatoires
      if (!dateReference) {
        throw new ValidationError('La date de référence est obligatoire');
      }

      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;

      // Récupérer l'exercice si fourni
      const exerciceId = req.query.exerciceId;

      // Traiter les tranches
      const tranchesArray = tranches.split(',').map(t => parseInt(t.trim()));

      // Générer la balance âgée
      const resultat = await this.etatService.genererBalanceAgee(
        new Date(dateReference),
        {
          societeId,
          exerciceId,
          format,
          tranches: tranchesArray,
          compteDebut,
          compteFin,
          seulementAvecSolde: seulementAvecSolde === 'true'
        }
      );

      // Envoyer le fichier en réponse
      res.download(resultat.cheminFichier, resultat.nomFichier, (err) => {
        if (err) {
          logger.error('Erreur lors de l\'envoi du fichier', {
            cheminFichier: resultat.cheminFichier,
            error: err.message
          });
          next(err);
        }
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * Génère le centralisateur des journaux
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async genererCentralisateur(req, res, next) {
    try {
      const { 
        dateDebut, 
        dateFin, 
        format = 'excel', 
        includeNonValidees = false,
        groupeParMois = false
      } = req.query;

      // Valider les paramètres obligatoires
      if (!dateDebut || !dateFin) {
        throw new ValidationError('Les dates de début et de fin sont obligatoires');
      }

      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;

      // Récupérer l'exercice si fourni
      const exerciceId = req.query.exerciceId;

      // Générer le centralisateur
      const resultat = await this.etatService.genererCentralisateur(
        new Date(dateDebut),
        new Date(dateFin),
        {
          societeId,
          exerciceId,
          format,
          includeNonValidees: includeNonValidees === 'true',
          groupeParMois: groupeParMois === 'true'
        }
      );

      // Envoyer le fichier en réponse
      res.download(resultat.cheminFichier, resultat.nomFichier, (err) => {
        if (err) {
          logger.error('Erreur lors de l\'envoi du fichier', {
            cheminFichier: resultat.cheminFichier,
            error: err.message
          });
          next(err);
        }
      });

    } catch (error) {
      next(error);
    }
  }
}

const etatController = new EtatController();
module.exports = etatController;