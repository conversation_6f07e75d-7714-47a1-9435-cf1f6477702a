{"name": "api-compta-generale", "version": "1.0.0", "description": "API RESTful de comptabilité conforme aux normes SYSCOHADA pour l'Afrique de l'Ouest", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ --ext .js", "lint:fix": "eslint src/ --ext .js --fix", "format": "prettier --write src/**/*.js", "db:migrate": "sequelize-cli db:migrate", "db:seed": "sequelize-cli db:seed:all", "db:reset": "sequelize-cli db:migrate:undo:all && npm run db:migrate && npm run db:seed"}, "repository": {"type": "git", "url": "git+https://github.com/geekobueno/api-compta-generale.git"}, "keywords": ["comptabilite", "sysco<PERSON>a", "api", "nodejs", "express", "postgresql", "afrique", "ohada"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/geekobueno/api-compta-generale/issues"}, "homepage": "https://github.com/geekobueno/api-compta-generale#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"axios": "^1.11.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "chalk": "^4.1.2", "cli-table3": "^0.6.3", "commander": "^11.1.0", "compression": "^1.8.1", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^17.2.1", "exceljs": "^4.4.0", "express": "^5.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "inquirer": "^8.2.6", "joi": "^18.0.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "ora": "^5.4.1", "pdfkit": "^0.17.1", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"eslint": "^9.32.0", "husky": "^9.1.7", "jest": "^30.0.5", "nodemon": "^3.1.10", "prettier": "^3.6.2", "sequelize-cli": "^6.6.3", "supertest": "^7.1.4"}}