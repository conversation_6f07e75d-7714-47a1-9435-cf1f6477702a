# États Comptables SYSCOHADA

Ce document décrit les fonctionnalités d'états comptables implémentées dans l'API Comptabilité SYSCOHADA, conformément aux jours 11-12 du plan de développement backend.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture](#architecture)
3. [États disponibles](#états-disponibles)
4. [API REST](#api-rest)
5. [Formats d'export](#formats-dexport)
6. [Exemples d'utilisation](#exemples-dutilisation)
7. [Personnalisation](#personnalisation)
8. [Considérations techniques](#considérations-techniques)

## Vue d'ensemble

Le module d'états comptables permet de générer les documents comptables officiels conformes aux normes SYSCOHADA, tels que le grand livre, les journaux, la balance générale, la balance âgée et le centralisateur. Ces états peuvent être exportés aux formats Excel et PDF, avec diverses options de filtrage et de formatage.

Les états comptables s'appuient sur les données des écritures comptables, des comptes, des journaux et des exercices déjà présents dans le système. Ils utilisent également les services de calcul pour obtenir les soldes et les totaux nécessaires.

## Architecture

L'implémentation des états comptables suit l'architecture en couches du reste de l'application:

1. **Service (EtatService)**: Contient la logique métier pour générer les états comptables et les exporter dans différents formats.
2. **Contrôleur (EtatController)**: Expose les fonctionnalités du service via l'API REST.
3. **Routes (etats.js)**: Définit les endpoints de l'API pour accéder aux états comptables.

### Dépendances

- **CalculService**: Utilisé pour calculer les soldes, les totaux et les balances.
- **ImportExportService**: Utilisé pour certaines fonctionnalités d'export.
- **XLSX**: Bibliothèque pour générer les fichiers Excel.
- **PDFKit**: Bibliothèque pour générer les fichiers PDF.

## États disponibles

### Grand Livre

Le grand livre présente l'ensemble des mouvements et soldes pour un compte ou tous les comptes sur une période donnée. Il permet de:

- Visualiser toutes les écritures affectant un compte
- Suivre l'évolution du solde progressif
- Voir les informations de lettrage
- Obtenir les totaux débit/crédit et le solde final

### Journal

Le journal présente les écritures comptables chronologiquement pour un journal ou tous les journaux sur une période donnée. Il permet de:

- Visualiser les écritures par ordre chronologique
- Regrouper les écritures par jour si nécessaire
- Vérifier l'équilibre des écritures
- Obtenir les totaux débit/crédit par journal

### Balance Générale

La balance générale présente les totaux débit/crédit et les soldes de tous les comptes sur une période donnée. Elle permet de:

- Vérifier l'équilibre global de la comptabilité
- Filtrer par classe de comptes
- Choisir le niveau de détail (tous, détail, collectif)
- Obtenir les totaux par classe de comptes

### Balance Âgée

La balance âgée (ou balance par antériorité) présente les soldes des comptes clients/fournisseurs ventilés par tranches d'ancienneté. Elle permet de:

- Analyser l'ancienneté des créances et dettes
- Définir des tranches d'ancienneté personnalisées
- Identifier les retards de paiement
- Faciliter le suivi des encaissements et décaissements

### Centralisateur

Le centralisateur présente les totaux débit/crédit de chaque journal sur une période donnée. Il permet de:

- Vérifier l'équilibre global des journaux
- Contrôler les volumes d'écritures par journal
- Faciliter les contrôles de fin de période
- Obtenir une vue synthétique de l'activité comptable

## API REST

Tous les endpoints nécessitent une authentification (JWT) et sont accessibles sous le préfixe `/api/v1/etats/`.

### Grand Livre

```
GET /api/v1/etats/grand-livre/:compteNumero?
```

**Paramètres URL:**
- `compteNumero` (optionnel): Numéro du compte. Si non fourni, génère le grand livre pour tous les comptes.

**Paramètres Query:**
- `dateDebut` (obligatoire): Date de début (format YYYY-MM-DD)
- `dateFin` (obligatoire): Date de fin (format YYYY-MM-DD)
- `format` (optionnel): Format d'export ('excel' ou 'pdf', défaut: 'excel')
- `exerciceId` (optionnel): ID de l'exercice comptable
- `includeNonValidees` (optionnel): Inclure les écritures non validées (true/false, défaut: false)
- `detailLettrage` (optionnel): Inclure les détails de lettrage (true/false, défaut: false)
- `triParDate` (optionnel): Trier par date (true) ou par journal puis date (false) (défaut: true)

### Journal

```
GET /api/v1/etats/journal/:journalCode?
```

**Paramètres URL:**
- `journalCode` (optionnel): Code du journal. Si non fourni, génère le journal pour tous les journaux.

**Paramètres Query:**
- `dateDebut` (obligatoire): Date de début (format YYYY-MM-DD)
- `dateFin` (obligatoire): Date de fin (format YYYY-MM-DD)
- `format` (optionnel): Format d'export ('excel' ou 'pdf', défaut: 'excel')
- `exerciceId` (optionnel): ID de l'exercice comptable
- `includeNonValidees` (optionnel): Inclure les écritures non validées (true/false, défaut: false)
- `groupeParJour` (optionnel): Regrouper les écritures par jour (true/false, défaut: false)

### Balance Générale

```
GET /api/v1/etats/balance
```

**Paramètres Query:**
- `dateDebut` (obligatoire): Date de début (format YYYY-MM-DD)
- `dateFin` (obligatoire): Date de fin (format YYYY-MM-DD)
- `format` (optionnel): Format d'export ('excel' ou 'pdf', défaut: 'excel')
- `exerciceId` (optionnel): ID de l'exercice comptable
- `niveauDetail` (optionnel): Niveau de détail ('TOUS', 'DETAIL', 'COLLECTIF', défaut: 'TOUS')
- `classeComptes` (optionnel): Classes de comptes à inclure (ex: '1,2,3')
- `seulementAvecMouvement` (optionnel): Uniquement les comptes avec mouvement (true/false, défaut: false)
- `includeNonValidees` (optionnel): Inclure les écritures non validées (true/false, défaut: false)

### Balance Âgée

```
GET /api/v1/etats/balance-agee
```

**Paramètres Query:**
- `dateReference` (obligatoire): Date de référence (format YYYY-MM-DD)
- `format` (optionnel): Format d'export ('excel' ou 'pdf', défaut: 'excel')
- `exerciceId` (optionnel): ID de l'exercice comptable
- `tranches` (optionnel): Tranches d'ancienneté en jours (ex: '30,60,90,180', défaut: '30,60,90,180')
- `compteDebut` (optionnel): Compte de début (défaut: '4')
- `compteFin` (optionnel): Compte de fin (défaut: '4')
- `seulementAvecSolde` (optionnel): Uniquement les comptes avec solde (true/false, défaut: true)

### Centralisateur

```
GET /api/v1/etats/centralisateur
```

**Paramètres Query:**
- `dateDebut` (obligatoire): Date de début (format YYYY-MM-DD)
- `dateFin` (obligatoire): Date de fin (format YYYY-MM-DD)
- `format` (optionnel): Format d'export ('excel' ou 'pdf', défaut: 'excel')
- `exerciceId` (optionnel): ID de l'exercice comptable
- `includeNonValidees` (optionnel): Inclure les écritures non validées (true/false, défaut: false)
- `groupeParMois` (optionnel): Regrouper par mois (true/false, défaut: false)

## Formats d'export

### Excel

Les exports Excel utilisent la bibliothèque `xlsx` et offrent:
- Plusieurs feuilles par document (informations, données, totaux)
- Formatage des cellules (largeurs de colonnes, alignement)
- En-têtes et pieds de page
- Totaux et sous-totaux

### PDF

Les exports PDF utilisent la bibliothèque `pdfkit` et offrent:
- Mise en page professionnelle
- En-têtes et pieds de page
- Pagination automatique
- Tableaux formatés
- Totaux et sous-totaux

## Exemples d'utilisation

### Exemple 1: Grand livre d'un compte client

```javascript
// Requête
fetch('/api/v1/etats/grand-livre/411000?dateDebut=2025-01-01&dateFin=2025-01-31&format=pdf', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer votre_token_jwt'
  }
})
.then(response => response.blob())
.then(blob => {
  // Télécharger le fichier PDF
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'grand_livre_client.pdf';
  document.body.appendChild(a);
  a.click();
  a.remove();
});
```

### Exemple 2: Balance générale pour les comptes de tiers

```javascript
// Requête
fetch('/api/v1/etats/balance?dateDebut=2025-01-01&dateFin=2025-01-31&classeComptes=4&niveauDetail=DETAIL', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer votre_token_jwt'
  }
})
.then(response => response.blob())
.then(blob => {
  // Télécharger le fichier Excel
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'balance_tiers.xlsx';
  document.body.appendChild(a);
  a.click();
  a.remove();
});
```

## Personnalisation

Les états comptables peuvent être personnalisés de plusieurs façons:

1. **Filtrage**: Utiliser les paramètres de requête pour filtrer les données (période, comptes, journaux, etc.)
2. **Format**: Choisir entre Excel et PDF selon les besoins
3. **Options d'affichage**: Configurer le niveau de détail, le regroupement, l'inclusion des écritures non validées, etc.
4. **Extension du service**: Le service `EtatService` peut être étendu pour ajouter de nouveaux états ou formats d'export

## Considérations techniques

### Performance

- Les états comptables peuvent contenir un grand nombre de lignes, surtout pour les grands livres et les journaux.
- Pour les gros volumes, l'export Excel est généralement plus performant que le PDF.
- Le service utilise des requêtes optimisées et des jointures efficaces pour minimiser l'impact sur la base de données.
- Les fichiers générés sont stockés temporairement dans le dossier `temp/` et doivent être nettoyés régulièrement.

### Sécurité

- Tous les endpoints nécessitent une authentification.
- Les utilisateurs ne peuvent accéder qu'aux données de leur société (filtrage par `societeId`).
- Les fichiers générés ne contiennent pas d'informations sensibles autres que les données comptables.

### Conformité SYSCOHADA

Les états générés sont conformes aux exigences du Système Comptable OHADA (SYSCOHADA), notamment:
- Format et présentation des états
- Calcul des soldes et totaux
- Regroupement et classification des comptes
- Terminologie comptable

### Tests

Des tests unitaires et d'intégration sont disponibles dans `src/tests/etats.test.js` pour vérifier le bon fonctionnement des états comptables.