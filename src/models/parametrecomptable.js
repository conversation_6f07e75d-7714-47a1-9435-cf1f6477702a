'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class ParametreComptable extends Model {
    /**
     * Associations du modèle ParametreComptable
     */
    static associate(models) {
      // Un paramètre appartient à une société
      ParametreComptable.belongsTo(models.Societe, {
        foreignKey: 'societeId',
        as: 'societe'
      });
    }

    /**
     * Méthodes d'instance
     */
    toJSON() {
      const values = { ...this.get() };
      
      // Convertir la valeur selon le type
      if (values.valeur !== null) {
        switch (values.type) {
          case 'NUMBER':
            values.valeurTypee = parseFloat(values.valeur);
            break;
          case 'BOOLEAN':
            values.valeurTypee = values.valeur === 'true' || values.valeur === '1';
            break;
          case 'JSON':
            try {
              values.valeurTypee = JSON.parse(values.valeur);
            } catch (e) {
              values.valeurTypee = values.valeur;
            }
            break;
          default:
            values.valeurTypee = values.valeur;
        }
      }
      
      return values;
    }

    /**
     * Obtient la valeur typée du paramètre
     */
    getValeurTypee() {
      if (this.valeur === null) return null;
      
      switch (this.type) {
        case 'NUMBER':
          return parseFloat(this.valeur);
        case 'BOOLEAN':
          return this.valeur === 'true' || this.valeur === '1';
        case 'JSON':
          try {
            return JSON.parse(this.valeur);
          } catch (e) {
            return this.valeur;
          }
        default:
          return this.valeur;
      }
    }

    /**
     * Définit la valeur du paramètre avec conversion automatique
     */
    setValeurTypee(valeur) {
      if (valeur === null || valeur === undefined) {
        this.valeur = null;
        return;
      }

      switch (this.type) {
        case 'NUMBER':
          this.valeur = valeur.toString();
          break;
        case 'BOOLEAN':
          this.valeur = valeur ? 'true' : 'false';
          break;
        case 'JSON':
          this.valeur = JSON.stringify(valeur);
          break;
        default:
          this.valeur = valeur.toString();
      }
    }

    /**
     * Valide la valeur selon le type
     */
    validateValeur() {
      if (this.valeur === null) return true;

      switch (this.type) {
        case 'NUMBER':
          if (isNaN(parseFloat(this.valeur))) {
            throw new Error(`La valeur "${this.valeur}" n'est pas un nombre valide`);
          }
          break;
        case 'BOOLEAN':
          if (!['true', 'false', '1', '0'].includes(this.valeur)) {
            throw new Error(`La valeur "${this.valeur}" n'est pas un booléen valide`);
          }
          break;
        case 'JSON':
          try {
            JSON.parse(this.valeur);
          } catch (e) {
            throw new Error(`La valeur "${this.valeur}" n'est pas un JSON valide`);
          }
          break;
      }
      return true;
    }

    /**
     * Méthodes statiques pour les paramètres par défaut
     */
    static getParametresDefaut() {
      return {
        // Paramètres TVA
        'tva_taux_normal': { valeur: '18', type: 'NUMBER', categorie: 'TVA', description: 'Taux de TVA normal (%)' },
        'tva_taux_reduit': { valeur: '9', type: 'NUMBER', categorie: 'TVA', description: 'Taux de TVA réduit (%)' },
        'tva_compte_collectee': { valeur: '4431', type: 'STRING', categorie: 'TVA', description: 'Compte TVA collectée' },
        'tva_compte_deductible': { valeur: '4455', type: 'STRING', categorie: 'TVA', description: 'Compte TVA déductible' },
        
        // Paramètres fiscaux
        'is_taux': { valeur: '25', type: 'NUMBER', categorie: 'FISCAL', description: 'Taux d\'impôt sur les sociétés (%)' },
        'taxe_professionnelle_taux': { valeur: '2', type: 'NUMBER', categorie: 'FISCAL', description: 'Taux taxe professionnelle (%)' },
        'seuil_ca_tva': { valeur: '50000000', type: 'NUMBER', categorie: 'FISCAL', description: 'Seuil CA pour assujettissement TVA' },
        
        // Paramètres comptables
        'compte_resultat_benefice': { valeur: '130', type: 'STRING', categorie: 'COMPTES', description: 'Compte résultat bénéficiaire' },
        'compte_resultat_perte': { valeur: '139', type: 'STRING', categorie: 'COMPTES', description: 'Compte résultat déficitaire' },
        'compte_report_nouveau': { valeur: '110', type: 'STRING', categorie: 'COMPTES', description: 'Compte report à nouveau' },
        
        // Paramètres de reporting
        'devise_presentation': { valeur: 'XOF', type: 'STRING', categorie: 'REPORTING', description: 'Devise de présentation des états' },
        'arrondi_montants': { valeur: 'true', type: 'BOOLEAN', categorie: 'REPORTING', description: 'Arrondir les montants' },
        'decimales_montants': { valeur: '2', type: 'NUMBER', categorie: 'REPORTING', description: 'Nombre de décimales pour les montants' },
        
        // Paramètres système
        'exercice_duree_max': { valeur: '366', type: 'NUMBER', categorie: 'SYSTEME', description: 'Durée maximale d\'un exercice (jours)' },
        'cloture_auto_ecritures': { valeur: 'true', type: 'BOOLEAN', categorie: 'SYSTEME', description: 'Validation automatique des écritures à la clôture' },
        'lettrage_obligatoire': { valeur: '["411", "401", "421"]', type: 'JSON', categorie: 'SYSTEME', description: 'Comptes à lettrage obligatoire' }
      };
    }

    /**
     * Crée les paramètres par défaut pour une société
     */
    static async creerParametresDefaut(societeId, transaction = null) {
      const parametresDefaut = ParametreComptable.getParametresDefaut();
      const parametres = [];

      for (const [cle, config] of Object.entries(parametresDefaut)) {
        parametres.push({
          societeId,
          cle,
          valeur: config.valeur,
          type: config.type,
          categorie: config.categorie,
          description: config.description
        });
      }

      return await ParametreComptable.bulkCreate(parametres, { transaction });
    }

    /**
     * Obtient tous les paramètres d'une société organisés par catégorie
     */
    static async getParametresSociete(societeId, categorie = null) {
      const whereClause = { societeId };
      if (categorie) {
        whereClause.categorie = categorie;
      }

      const parametres = await ParametreComptable.findAll({
        where: whereClause,
        order: [['categorie', 'ASC'], ['cle', 'ASC']]
      });

      // Organiser par catégorie
      const parametresOrganises = {};
      parametres.forEach(param => {
        if (!parametresOrganises[param.categorie]) {
          parametresOrganises[param.categorie] = {};
        }
        parametresOrganises[param.categorie][param.cle] = param.getValeurTypee();
      });

      return parametresOrganises;
    }

    /**
     * Obtient la valeur d'un paramètre spécifique
     */
    static async getValeurParametre(societeId, cle, valeurDefaut = null) {
      const parametre = await ParametreComptable.findOne({
        where: { societeId, cle }
      });

      if (!parametre) {
        return valeurDefaut;
      }

      return parametre.getValeurTypee();
    }

    /**
     * Met à jour ou crée un paramètre
     */
    static async setParametre(societeId, cle, valeur, type = 'STRING', categorie = 'CUSTOM', description = null) {
      const [parametre, created] = await ParametreComptable.findOrCreate({
        where: { societeId, cle },
        defaults: {
          societeId,
          cle,
          valeur: valeur?.toString() || null,
          type,
          categorie,
          description
        }
      });

      if (!created) {
        parametre.setValeurTypee(valeur);
        await parametre.save();
      }

      return parametre;
    }
  }

  ParametreComptable.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    societeId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'societes',
        key: 'id'
      }
    },
    cle: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100],
        is: /^[a-z0-9_]+$/i // Alphanumerique et underscore seulement
      }
    },
    valeur: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    type: {
      type: DataTypes.ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON'),
      allowNull: false,
      defaultValue: 'STRING'
    },
    categorie: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'GENERAL',
      validate: {
        isIn: [['TVA', 'FISCAL', 'COMPTES', 'REPORTING', 'SYSTEME', 'CUSTOM', 'GENERAL']]
      }
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'ParametreComptable',
    tableName: 'parametre_comptables',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['societe_id', 'cle']
      },
      {
        fields: ['societe_id', 'categorie']
      },
      {
        fields: ['categorie']
      }
    ],
    hooks: {
      beforeValidate: (parametre) => {
        if (parametre.valeur !== null) {
          parametre.validateValeur();
        }
      }
    }
  });

  return ParametreComptable;
};
