'use strict';

// const { logger } = require('../config/logger');
const { ValidationError } = require('../middleware/errorHandler');

/**
 * Contrôleur pour les tableaux de bord financiers
 */
class DashboardController {
  constructor() {
    this.models = require('../models');
    this.dashboardService = new (require('../services/dashboardService'))(this.models);
  }

  /**
   * Récupère les KPIs financiers
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async getKPIFinanciers(req, res, next) {
    try {
      const { dateDebut, dateFin } = req.query;
      
      // Valider les paramètres obligatoires
      if (!dateDebut || !dateFin) {
        throw new ValidationError('Les dates de début et de fin sont obligatoires');
      }
      
      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;
      
      // Récupérer l'exercice si fourni
      const exerciceId = req.query.exerciceId;
      
      // Récupérer les KPIs
      const resultat = await this.dashboardService.getKPIFinanciers(
        societeId,
        {
          dateDebut: new Date(dateDebut),
          dateFin: new Date(dateFin)
        },
        { exerciceId }
      );
      
      res.json(resultat);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Récupère l'évolution du chiffre d'affaires
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async getEvolutionChiffreAffaires(req, res, next) {
    try {
      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;
      
      // Récupérer les périodes depuis le corps de la requête
      const { periodes } = req.body;
      
      if (!periodes || !Array.isArray(periodes) || periodes.length === 0) {
        throw new ValidationError('Au moins une période doit être spécifiée');
      }
      
      // Formater les périodes
      const periodesFormatees = periodes.map(periode => ({
        dateDebut: new Date(periode.dateDebut),
        dateFin: new Date(periode.dateFin),
        libelle: periode.libelle
      }));
      
      // Récupérer l'évolution du chiffre d'affaires
      const resultat = await this.dashboardService.getEvolutionChiffreAffaires(
        societeId,
        periodesFormatees
      );
      
      res.json(resultat);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Récupère l'analyse des charges et produits
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async getAnalyseChargesProduits(req, res, next) {
    try {
      const { dateDebut, dateFin, niveauDetail } = req.query;
      
      // Valider les paramètres obligatoires
      if (!dateDebut || !dateFin) {
        throw new ValidationError('Les dates de début et de fin sont obligatoires');
      }
      
      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;
      
      // Récupérer l'exercice si fourni
      const exerciceId = req.query.exerciceId;
      
      // Récupérer l'analyse
      const resultat = await this.dashboardService.getAnalyseChargesProduits(
        societeId,
        {
          dateDebut: new Date(dateDebut),
          dateFin: new Date(dateFin)
        },
        { 
          exerciceId,
          niveauDetail: niveauDetail ? parseInt(niveauDetail) : 2
        }
      );
      
      res.json(resultat);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Récupère les ratios financiers
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async getRatiosFinanciers(req, res, next) {
    try {
      const { dateDebut, dateFin } = req.query;
      
      // Valider les paramètres obligatoires
      if (!dateDebut || !dateFin) {
        throw new ValidationError('Les dates de début et de fin sont obligatoires');
      }
      
      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;
      
      // Récupérer l'exercice si fourni
      const exerciceId = req.query.exerciceId;
      
      // Récupérer les ratios
      const resultat = await this.dashboardService.getRatiosFinanciers(
        societeId,
        {
          dateDebut: new Date(dateDebut),
          dateFin: new Date(dateFin)
        },
        { exerciceId }
      );
      
      res.json(resultat);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Récupère les alertes financières
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async getAlertes(req, res, next) {
    try {
      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;
      
      // Récupérer les alertes
      const resultat = await this.dashboardService.getAlertes(societeId);
      
      res.json(resultat);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new DashboardController();