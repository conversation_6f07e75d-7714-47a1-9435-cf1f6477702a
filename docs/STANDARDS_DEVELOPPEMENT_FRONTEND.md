# Standards de Développement Frontend
## Application Comptabilité SYSCOHADA

**Date** : 7 Août 2025  
**Version** : 1.0  
**Statut** : Proposition  

---

## 📋 Table des Matières

1. [Introduction](#introduction)
2. [Stack Technologique](#stack-technologique)
3. [Architecture et Organisation](#architecture-et-organisation)
4. [Normes de Codage](#normes-de-codage)
5. [Gestion d'État](#gestion-détat)
6. [UI/UX Guidelines](#uiux-guidelines)
7. [Internationalisation](#internationalisation)
8. [Tests et Assurance Qualité](#tests-et-assurance-qualité)
9. [Performance](#performance)
10. [Sécurité](#sécurité)
11. [Accessibilité](#accessibilité)
12. [CI/CD et Déploiement](#cicd-et-déploiement)
13. [Documentation](#documentation)
14. [Bonnes Pratiques](#bonnes-pratiques)

---

## Introduction

Ce document définit les standards de développement pour le frontend de l'application de comptabilité SYSCOHADA. Ces standards visent à garantir la qualité, la maintenabilité et la cohérence du code, tout en optimisant l'expérience utilisateur et les performances.

### Objectifs

- Établir une base technique solide et évolutive
- Assurer la cohérence et la qualité du code
- Optimiser l'expérience utilisateur pour les professionnels de la comptabilité
- Faciliter la maintenance et l'évolution de l'application
- Garantir la sécurité et la performance

### Principes Directeurs

- **Simplicité** : Privilégier des solutions simples et maintenables
- **Cohérence** : Maintenir une approche uniforme dans tout le code
- **Modularité** : Concevoir des composants réutilisables et découplés
- **Performance** : Optimiser pour une expérience fluide même avec de grandes quantités de données
- **Accessibilité** : Assurer l'utilisabilité pour tous les utilisateurs

---

## Stack Technologique

### Technologies de Base

- **Framework** : React 18+
- **Langage** : TypeScript 5+
- **Bundler/Build Tool** : Vite
- **Package Manager** : pnpm

### Bibliothèques Principales

- **Gestion d'État** : Redux Toolkit + RTK Query
- **Routage** : React Router v6+
- **Formulaires** : React Hook Form + Zod
- **UI Components** : MUI (Material-UI) v5+
- **Data Visualization** : Recharts + D3.js
- **Tableaux de Données** : TanStack Table (React Table)
- **Dates et Heures** : date-fns
- **Internationalisation** : i18next
- **HTTP Client** : Axios

### Outils de Développement

- **Linting** : ESLint avec config-airbnb-typescript
- **Formatting** : Prettier
- **Testing** : Vitest + React Testing Library + Playwright
- **Documentation** : Storybook + TSDoc
- **Pre-commit Hooks** : Husky + lint-staged

---

## Architecture et Organisation

### Structure de Répertoires

```
src/
├── assets/            # Ressources statiques (images, fonts, etc.)
├── components/        # Composants React réutilisables
│   ├── common/        # Composants génériques (Button, Input, etc.)
│   ├── layout/        # Composants de mise en page
│   └── features/      # Composants spécifiques aux fonctionnalités
├── config/            # Configuration de l'application
├── constants/         # Constantes et énumérations
├── features/          # Modules fonctionnels
│   ├── auth/          # Authentification
│   ├── societes/      # Gestion des sociétés
│   ├── comptabilite/  # Fonctionnalités comptables
│   └── reporting/     # États et rapports
├── hooks/             # Hooks React personnalisés
├── i18n/              # Internationalisation
├── pages/             # Composants de pages
├── routes/            # Configuration des routes
├── services/          # Services (API, etc.)
├── store/             # Configuration Redux
├── styles/            # Styles globaux
├── types/             # Types TypeScript
├── utils/             # Utilitaires
└── App.tsx            # Composant racine
```

### Architecture des Fonctionnalités

Chaque fonctionnalité (feature) suit une structure cohérente :

```
features/comptabilite/
├── components/        # Composants spécifiques à la fonctionnalité
├── hooks/             # Hooks spécifiques
├── services/          # Services API
├── store/             # Slices Redux et sélecteurs
│   ├── ecrituresSlice.ts
│   ├── journauxSlice.ts
│   └── selectors.ts
├── types/             # Types et interfaces
├── utils/             # Utilitaires spécifiques
└── index.ts           # Point d'entrée exportant les éléments publics
```

### Modèle de Composant

```typescript
// Exemple de structure de composant
import { FC, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useStyles } from './styles';
import { ComponentProps } from './types';

export const MyComponent: FC<ComponentProps> = ({ 
  prop1, 
  prop2,
  children 
}) => {
  // Hooks
  const { t } = useTranslation();
  const styles = useStyles();
  const [state, setState] = useState<string>('');
  
  // Effects
  useEffect(() => {
    // Logic
  }, [prop1]);
  
  // Handlers
  const handleClick = () => {
    // Logic
  };
  
  // Render
  return (
    <div className={styles.container}>
      <h2>{t('component.title')}</h2>
      {children}
    </div>
  );
};
```

---

## Normes de Codage

### TypeScript

- Utiliser TypeScript pour tous les fichiers (`.tsx`, `.ts`)
- Définir des interfaces/types explicites pour toutes les props de composants
- Éviter l'utilisation de `any` - préférer `unknown` si nécessaire
- Utiliser les génériques pour les fonctions/composants réutilisables
- Activer les règles strictes dans `tsconfig.json`

```json
// tsconfig.json (extrait)
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noUncheckedIndexedAccess": true
  }
}
```

### ESLint et Prettier

- Utiliser la configuration Airbnb TypeScript avec personnalisations
- Configurer Prettier pour le formatage automatique
- Exécuter le linting et le formatage en pre-commit

```json
// .eslintrc.json (extrait)
{
  "extends": [
    "airbnb-typescript",
    "plugin:react-hooks/recommended",
    "prettier"
  ],
  "rules": {
    "import/prefer-default-export": "off",
    "react/require-default-props": "off",
    "react/jsx-props-no-spreading": "off"
  }
}
```

### Conventions de Nommage

- **Composants** : PascalCase (`EcritureForm.tsx`)
- **Hooks** : camelCase avec préfixe `use` (`useEcritures.ts`)
- **Fonctions** : camelCase (`formatMontant.ts`)
- **Constantes** : UPPER_SNAKE_CASE (`MAX_MONTANT`)
- **Types/Interfaces** : PascalCase avec préfixe descriptif (`EcritureFormProps`)
- **Fichiers de test** : Même nom que le fichier testé avec suffixe `.test` ou `.spec`

### Imports et Exports

- Préférer les imports nommés aux imports par défaut
- Utiliser les exports nommés pour faciliter le tree-shaking
- Organiser les imports par groupes (React, bibliothèques externes, internes)
- Utiliser les index.ts pour simplifier les chemins d'import

```typescript
// Bonne pratique d'organisation des imports
import { FC, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, TextField } from '@mui/material';

import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { formatMontant } from '@/utils/formatters';
import { EcritureType } from '@/types';
```

---

## Gestion d'État

### Redux Toolkit

- Utiliser Redux Toolkit pour la gestion d'état globale
- Organiser le store par fonctionnalités (slices)
- Utiliser les createSlice et createAsyncThunk pour les actions asynchrones
- Définir des sélecteurs réutilisables et mémoïsés

```typescript
// Exemple de slice Redux
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { EcritureState, Ecriture } from './types';
import { ecrituresApi } from '@/services/api';

export const fetchEcritures = createAsyncThunk(
  'ecritures/fetchEcritures',
  async (societeId: string) => {
    const response = await ecrituresApi.getEcritures(societeId);
    return response.data;
  }
);

const initialState: EcritureState = {
  ecritures: [],
  status: 'idle',
  error: null
};

const ecrituresSlice = createSlice({
  name: 'ecritures',
  initialState,
  reducers: {
    // Reducers synchrones
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchEcritures.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchEcritures.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.ecritures = action.payload;
      })
      .addCase(fetchEcritures.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message || null;
      });
  }
});

export default ecrituresSlice.reducer;
```

### RTK Query

- Utiliser RTK Query pour les requêtes API et la gestion du cache
- Définir des endpoints par domaine fonctionnel
- Configurer la revalidation et l'invalidation du cache

```typescript
// Exemple d'API avec RTK Query
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { Ecriture, EcritureCreateDto } from '@/types';

export const ecrituresApi = createApi({
  reducerPath: 'ecrituresApi',
  baseQuery: fetchBaseQuery({ 
    baseUrl: '/api/v1/',
    prepareHeaders: (headers, { getState }) => {
      // Ajouter le token d'authentification
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    }
  }),
  tagTypes: ['Ecriture'],
  endpoints: (builder) => ({
    getEcritures: builder.query<Ecriture[], string>({
      query: (societeId) => `ecritures?societeId=${societeId}`,
      providesTags: ['Ecriture']
    }),
    createEcriture: builder.mutation<Ecriture, EcritureCreateDto>({
      query: (ecriture) => ({
        url: 'ecritures',
        method: 'POST',
        body: ecriture
      }),
      invalidatesTags: ['Ecriture']
    })
  })
});

export const { 
  useGetEcrituresQuery, 
  useCreateEcritureMutation 
} = ecrituresApi;
```

### État Local

- Utiliser les hooks d'état React (`useState`, `useReducer`) pour l'état local
- Préférer `useReducer` pour les états complexes
- Créer des hooks personnalisés pour la logique d'état réutilisable

```typescript
// Exemple de hook personnalisé pour la gestion d'état local
import { useState, useCallback } from 'react';

export function useFormState<T>(initialState: T) {
  const [formData, setFormData] = useState<T>(initialState);
  
  const handleChange = useCallback((field: keyof T, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);
  
  const resetForm = useCallback(() => {
    setFormData(initialState);
  }, [initialState]);
  
  return { formData, handleChange, resetForm };
}
```

---

## UI/UX Guidelines

### Système de Design

- Utiliser Material-UI (MUI) comme base du système de design
- Créer un thème personnalisé conforme à l'identité visuelle
- Définir des composants de base réutilisables
- Maintenir une bibliothèque de composants dans Storybook

```typescript
// Exemple de thème personnalisé
import { createTheme } from '@mui/material/styles';

export const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0'
    },
    secondary: {
      main: '#388e3c',
      light: '#4caf50',
      dark: '#2e7d32'
    },
    error: {
      main: '#d32f2f'
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff'
    }
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2rem',
      fontWeight: 500
    },
    // Autres styles typographiques
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 4,
          textTransform: 'none'
        }
      }
    }
    // Autres personnalisations de composants
  }
});
```

### Responsive Design

- Adopter une approche "mobile-first"
- Utiliser les breakpoints MUI pour la responsivité
- Tester sur différentes tailles d'écran
- Optimiser pour les tablettes (usage courant en comptabilité)

```typescript
// Exemple d'utilisation des breakpoints
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';

export function useResponsive() {
  const theme = useTheme();
  
  return {
    isMobile: useMediaQuery(theme.breakpoints.down('sm')),
    isTablet: useMediaQuery(theme.breakpoints.between('sm', 'lg')),
    isDesktop: useMediaQuery(theme.breakpoints.up('lg'))
  };
}
```

### Composants Spécialisés pour la Comptabilité

Développer des composants spécialisés pour les besoins comptables :

- **MontantInput** : Saisie de montants avec formatage automatique
- **CompteSelect** : Sélection de comptes avec recherche et validation
- **JournalTable** : Tableau optimisé pour les écritures comptables
- **BalanceDisplay** : Affichage des soldes avec code couleur
- **DateComptableInput** : Sélection de dates avec validation d'exercice

```typescript
// Exemple de composant spécialisé
import { FC } from 'react';
import { TextField, InputAdornment } from '@mui/material';
import { formatMontant, parseMontant } from '@/utils/formatters';

interface MontantInputProps {
  value: number;
  onChange: (value: number) => void;
  devise?: string;
  label?: string;
  error?: boolean;
  helperText?: string;
}

export const MontantInput: FC<MontantInputProps> = ({
  value,
  onChange,
  devise = 'XOF',
  label = 'Montant',
  error,
  helperText
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const parsed = parseMontant(e.target.value);
    if (!isNaN(parsed)) {
      onChange(parsed);
    }
  };

  return (
    <TextField
      label={label}
      value={formatMontant(value, false)}
      onChange={handleChange}
      InputProps={{
        endAdornment: <InputAdornment position="end">{devise}</InputAdornment>
      }}
      error={error}
      helperText={helperText}
      inputMode="decimal"
    />
  );
};
```

### Patterns d'Interface

- **Tableaux de Données** : Utiliser TanStack Table pour les grands ensembles de données
- **Formulaires** : Structurer avec React Hook Form + Zod pour la validation
- **Navigation** : Menu latéral + onglets pour les sous-sections
- **Modales et Dialogues** : Pour les actions nécessitant confirmation
- **Notifications** : Système toast pour les feedbacks utilisateur

---

## Internationalisation

### Configuration i18next

- Configurer i18next pour le support multilingue
- Prioriser le français et l'anglais (langues principales SYSCOHADA)
- Structurer les traductions par domaines fonctionnels
- Supporter la pluralisation et le formatage

```typescript
// Configuration i18next
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: 'fr',
    supportedLngs: ['fr', 'en'],
    ns: ['common', 'auth', 'comptabilite', 'reporting'],
    defaultNS: 'common',
    interpolation: {
      escapeValue: false
    }
  });

export default i18n;
```

### Formatage Localisé

- Utiliser des formateurs spécifiques pour :
  - Montants (avec séparateurs de milliers et décimales)
  - Dates (format local)
  - Numéros de compte
  - Pourcentages

```typescript
// Exemple de formatage localisé
import { format } from 'date-fns';
import { fr, enUS } from 'date-fns/locale';

const locales = {
  fr,
  en: enUS
};

export const formatDate = (date: Date, locale = 'fr', formatStr = 'dd/MM/yyyy') => {
  return format(date, formatStr, {
    locale: locales[locale as keyof typeof locales]
  });
};

export const formatMontant = (
  montant: number,
  includeSymbol = true,
  locale = 'fr-FR',
  devise = 'XOF'
) => {
  const formatted = new Intl.NumberFormat(locale, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(montant);
  
  return includeSymbol ? `${formatted} ${devise}` : formatted;
};
```

---

## Tests et Assurance Qualité

### Types de Tests

- **Tests Unitaires** : Composants, hooks, utilitaires
- **Tests d'Intégration** : Interactions entre composants
- **Tests E2E** : Flux utilisateur complets
- **Tests de Performance** : Chargement et rendu des données volumineuses
- **Tests d'Accessibilité** : Conformité aux standards WCAG

### Configuration des Tests

- Utiliser Vitest pour les tests unitaires et d'intégration
- Configurer React Testing Library pour les tests de composants
- Utiliser Playwright pour les tests E2E
- Mettre en place des tests automatisés dans le pipeline CI

```typescript
// Exemple de test unitaire avec Vitest et RTL
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { MontantInput } from './MontantInput';

describe('MontantInput', () => {
  it('should format the value correctly', () => {
    const onChange = vi.fn();
    render(<MontantInput value={1000.5} onChange={onChange} />);
    
    const input = screen.getByRole('textbox');
    expect(input).toHaveValue('1 000,50');
  });
  
  it('should call onChange with parsed value', () => {
    const onChange = vi.fn();
    render(<MontantInput value={0} onChange={onChange} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '1 234,56' } });
    
    expect(onChange).toHaveBeenCalledWith(1234.56);
  });
});
```

### Couverture de Tests

- Viser une couverture de code > 80% pour les composants critiques
- Tester en priorité :
  - Composants de saisie financière
  - Logique de validation comptable
  - Calculs et formatage des montants
  - Flux d'authentification et d'autorisation

---

## Performance

### Optimisations React

- Utiliser React.memo pour les composants purs
- Optimiser les re-rendus avec useCallback et useMemo
- Implémenter le chargement paresseux (lazy loading) des composants
- Utiliser la virtualisation pour les listes longues

```typescript
// Exemple d'optimisation avec React.memo et useCallback
import { FC, memo, useCallback } from 'react';

interface ItemProps {
  id: string;
  title: string;
  onSelect: (id: string) => void;
}

const ListItem: FC<ItemProps> = memo(({ id, title, onSelect }) => {
  const handleClick = useCallback(() => {
    onSelect(id);
  }, [id, onSelect]);
  
  return (
    <div onClick={handleClick}>
      {title}
    </div>
  );
});
```

### Optimisation des Bundles

- Configurer le code splitting par routes et fonctionnalités
- Optimiser les dépendances avec des imports dynamiques
- Analyser et réduire la taille des bundles

```typescript
// Exemple de code splitting avec React.lazy
import { lazy, Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import LoadingSpinner from '@/components/common/LoadingSpinner';

const Dashboard = lazy(() => import('@/pages/Dashboard'));
const Ecritures = lazy(() => import('@/pages/Ecritures'));
const Reporting = lazy(() => import('@/pages/Reporting'));

export const AppRoutes = () => (
  <Suspense fallback={<LoadingSpinner />}>
    <Routes>
      <Route path="/" element={<Dashboard />} />
      <Route path="/ecritures/*" element={<Ecritures />} />
      <Route path="/reporting/*" element={<Reporting />} />
    </Routes>
  </Suspense>
);
```

### Optimisation des Données

- Mettre en cache les résultats API avec RTK Query
- Implémenter la pagination côté serveur pour les grands ensembles de données
- Utiliser des requêtes optimisées (filtrage côté serveur)
- Mettre en place des stratégies de préchargement pour les données fréquemment utilisées

---

## Sécurité

### Bonnes Pratiques

- Valider toutes les entrées utilisateur côté client et serveur
- Échapper les données affichées pour prévenir les attaques XSS
- Implémenter le CSRF protection
- Utiliser HTTPS pour toutes les communications
- Suivre les recommandations OWASP

### Authentification et Autorisation

- Stocker les tokens JWT de manière sécurisée
- Implémenter l'expiration et le rafraîchissement des tokens
- Vérifier les permissions avant d'afficher les fonctionnalités sensibles
- Gérer la déconnexion et l'expiration de session

```typescript
// Exemple de hook d'autorisation
import { useAppSelector } from '@/hooks/redux';
import { selectCurrentUser } from '@/features/auth/store/selectors';

export function useHasPermission(permission: string): boolean {
  const user = useAppSelector(selectCurrentUser);
  
  if (!user || !user.permissions) {
    return false;
  }
  
  return user.permissions.includes(permission);
}

// Utilisation dans un composant
const ProtectedButton = () => {
  const canValidate = useHasPermission('ECRITURE_VALIDER');
  
  if (!canValidate) {
    return null;
  }
  
  return <Button>Valider l'écriture</Button>;
};
```

### Protection des Données Sensibles

- Ne pas stocker d'informations sensibles dans localStorage/sessionStorage
- Masquer les données financières sensibles dans l'interface
- Implémenter des timeouts pour les sessions inactives
- Nettoyer les données sensibles lors de la déconnexion

---

## Accessibilité

### Standards WCAG

- Viser la conformité WCAG 2.1 niveau AA
- Assurer la navigation au clavier
- Fournir des textes alternatifs pour les éléments visuels
- Maintenir un contraste suffisant pour la lisibilité

### Implémentation

- Utiliser des éléments sémantiques HTML5
- Implémenter correctement ARIA lorsque nécessaire
- Tester avec des lecteurs d'écran
- Vérifier l'accessibilité avec des outils automatisés

```typescript
// Exemple de composant accessible
import { FC } from 'react';

interface TabProps {
  id: string;
  label: string;
  selected: boolean;
  onSelect: () => void;
}

export const AccessibleTab: FC<TabProps> = ({
  id,
  label,
  selected,
  onSelect
}) => {
  return (
    <button
      role="tab"
      id={`tab-${id}`}
      aria-selected={selected}
      aria-controls={`panel-${id}`}
      tabIndex={selected ? 0 : -1}
      onClick={onSelect}
    >
      {label}
    </button>
  );
};
```

---

## CI/CD et Déploiement

### Pipeline CI

- Exécuter les tests automatisés à chaque pull request
- Vérifier le linting et le formatage
- Analyser la qualité du code
- Générer des rapports de couverture de tests

### Pipeline CD

- Construire les bundles optimisés pour la production
- Déployer automatiquement sur les environnements de test
- Promouvoir vers la production après validation
- Mettre en place des rollbacks automatiques en cas d'échec

### Environnements

- **Développement** : Pour le développement local
- **Test** : Pour les tests automatisés et manuels
- **Staging** : Environnement de pré-production
- **Production** : Environnement final

### Configuration par Environnement

- Utiliser des variables d'environnement pour la configuration
- Séparer la configuration par environnement
- Masquer les informations sensibles

```typescript
// Exemple de configuration par environnement
const config = {
  apiUrl: import.meta.env.VITE_API_URL,
  environment: import.meta.env.MODE,
  features: {
    enableAnalytics: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
    enableMockApi: import.meta.env.VITE_ENABLE_MOCK_API === 'true'
  }
};

export default config;
```

---

## Documentation

### Documentation du Code

- Utiliser TSDoc pour documenter les fonctions et composants
- Documenter les props des composants
- Expliquer les algorithmes complexes
- Maintenir des exemples d'utilisation

```typescript
/**
 * Composant pour afficher et éditer un montant comptable.
 * 
 * @param value - La valeur numérique du montant
 * @param onChange - Callback appelé lorsque la valeur change
 * @param devise - Code de la devise (défaut: XOF)
 * @param label - Libellé du champ
 * @param error - Indique si le champ contient une erreur
 * @param helperText - Texte d'aide ou message d'erreur
 * 
 * @example
 * ```tsx
 * <MontantInput
 *   value={1000.50}
 *   onChange={handleChange}
 *   devise="XOF"
 *   label="Montant HT"
 * />
 * ```
 */
export const MontantInput: FC<MontantInputProps> = ({...});
```

### Storybook

- Documenter tous les composants réutilisables dans Storybook
- Inclure des exemples d'utilisation et des variantes
- Documenter les props et leurs effets
- Organiser les stories par catégories fonctionnelles

```typescript
// Exemple de story
import type { Meta, StoryObj } from '@storybook/react';
import { MontantInput } from './MontantInput';

const meta: Meta<typeof MontantInput> = {
  component: MontantInput,
  title: 'Comptabilité/Inputs/MontantInput',
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onChange: { action: 'changed' }
  }
};

export default meta;
type Story = StoryObj<typeof MontantInput>;

export const Default: Story = {
  args: {
    value: 1000.5,
    label: 'Montant',
    devise: 'XOF'
  }
};

export const WithError: Story = {
  args: {
    value: 0,
    label: 'Montant',
    devise: 'XOF',