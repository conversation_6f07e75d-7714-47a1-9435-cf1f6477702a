'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Fonction helper pour ajouter un index en gérant les erreurs
    const addIndexSafely = async (table, columns, options) => {
      try {
        await queryInterface.addIndex(table, columns, options);
        console.log(`Index ${options.name} créé avec succès`);
      } catch (error) {
        console.log(`Index ${options.name} existe déjà ou erreur: ${error.message}`);
      }
    };

    // Index pour la recherche textuelle sur les écritures
    await addIndexSafely('ecriture_comptables', ['libelle'], {
      name: 'idx_ecriture_libelle_search'
    });

    await addIndexSafely('ecriture_comptables', ['reference'], {
      name: 'idx_ecriture_reference_search'
    });

    await addIndexSafely('ecriture_comptables', ['numero_ecriture'], {
      name: 'idx_ecriture_numero_search'
    });

    await addIndexSafely('ecriture_comptables', ['piece_justificative'], {
      name: 'idx_ecriture_piece_search'
    });

    // Index pour la recherche textuelle sur les lignes d'écriture
    await addIndexSafely('ligne_ecritures', ['libelle'], {
      name: 'idx_ligne_libelle_search'
    });

    // Index composites pour les recherches fréquentes
    await addIndexSafely('ecriture_comptables', ['societe_id', 'date_ecriture'], {
      name: 'idx_ecriture_societe_date'
    });

    await addIndexSafely('ecriture_comptables', ['exercice_id', 'statut'], {
      name: 'idx_ecriture_exercice_statut'
    });

    await addIndexSafely('ecriture_comptables', ['journal_code', 'date_ecriture'], {
      name: 'idx_ecriture_journal_date'
    });

    // Index pour les recherches par montant (noms de colonnes corrects)
    try {
      await queryInterface.addIndex('ligne_ecritures', ['debit'], {
        name: 'idx_ligne_debit'
      });
    } catch (error) {
      console.log('Index debit existe déjà');
    }

    try {
      await queryInterface.addIndex('ligne_ecritures', ['credit'], {
        name: 'idx_ligne_credit'
      });
    } catch (error) {
      console.log('Index credit existe déjà');
    }

    // Index pour les recherches par compte
    await addIndexSafely('ligne_ecritures', ['compte_numero', 'ecriture_id'], {
      name: 'idx_ligne_compte_ecriture_new'
    });

    // Index pour les recherches par période avec statut
    await addIndexSafely('ecriture_comptables', ['date_ecriture', 'statut'], {
      name: 'idx_ecriture_date_statut'
    });

    // Index pour les recherches par utilisateur
    await addIndexSafely('ecriture_comptables', ['utilisateur_creation'], {
      name: 'idx_ecriture_utilisateur_creation'
    });

    await addIndexSafely('ecriture_comptables', ['utilisateur_validation'], {
      name: 'idx_ecriture_utilisateur_validation'
    });

    // Index pour les recherches par date de création
    await addIndexSafely('ecriture_comptables', ['created_at'], {
      name: 'idx_ecriture_created_at'
    });

    // Index pour optimiser les jointures
    await addIndexSafely('ligne_ecritures', ['ecriture_id'], {
      name: 'idx_ligne_ecriture_id_new'
    });

    // Index pour les recherches full-text (PostgreSQL)
    if (queryInterface.sequelize.getDialect() === 'postgres') {
      // Créer un index GIN pour la recherche textuelle avancée
      await queryInterface.sequelize.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ecriture_fulltext_search 
        ON ecriture_comptables 
        USING gin(to_tsvector('french', coalesce(libelle, '') || ' ' || coalesce(reference, '') || ' ' || coalesce(numero_ecriture, '')))
      `);

      await queryInterface.sequelize.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ligne_fulltext_search 
        ON ligne_ecritures 
        USING gin(to_tsvector('french', coalesce(libelle, '')))
      `);
    }

    // Index partiels pour optimiser les requêtes sur les écritures validées
    await addIndexSafely('ecriture_comptables', ['date_ecriture'], {
      name: 'idx_ecriture_date_validee',
      where: {
        statut: 'VALIDEE'
      }
    });

    // Index pour les recherches de lettrage
    await addIndexSafely('ligne_ecritures', ['lettrage'], {
      name: 'idx_ligne_lettrage',
      where: {
        lettrage: { [Sequelize.Op.ne]: null }
      }
    });

    // Index composite pour les recherches complexes
    await addIndexSafely('ecriture_comptables',
      ['societe_id', 'exercice_id', 'date_ecriture', 'statut'], {
      name: 'idx_ecriture_recherche_complexe'
    });

    console.log('Index de recherche créés avec succès');
  },

  async down(queryInterface, Sequelize) {
    // Supprimer tous les index créés
    const indexes = [
      'idx_ecriture_libelle_search',
      'idx_ecriture_reference_search',
      'idx_ecriture_numero_search',
      'idx_ecriture_piece_search',
      'idx_ligne_libelle_search',
      'idx_ecriture_societe_date',
      'idx_ecriture_exercice_statut',
      'idx_ecriture_journal_date',
      'idx_ligne_montant_debit',
      'idx_ligne_montant_credit',
      'idx_ligne_compte_ecriture',
      'idx_ecriture_date_statut',
      'idx_ecriture_utilisateur_creation',
      'idx_ecriture_utilisateur_validation',
      'idx_ecriture_created_at',
      'idx_ligne_ecriture_id',
      'idx_ecriture_date_validee',
      'idx_ligne_lettrage',
      'idx_ecriture_recherche_complexe'
    ];

    for (const indexName of indexes) {
      try {
        await queryInterface.removeIndex('ecriture_comptables', indexName);
      } catch (error) {
        // Ignorer les erreurs si l'index n'existe pas
        console.log(`Index ${indexName} non trouvé sur ecriture_comptables`);
      }
      
      try {
        await queryInterface.removeIndex('ligne_ecritures', indexName);
      } catch (error) {
        // Ignorer les erreurs si l'index n'existe pas
        console.log(`Index ${indexName} non trouvé sur ligne_ecritures`);
      }
    }

    // Supprimer les index full-text PostgreSQL
    if (queryInterface.sequelize.getDialect() === 'postgres') {
      try {
        await queryInterface.sequelize.query('DROP INDEX IF EXISTS idx_ecriture_fulltext_search');
        await queryInterface.sequelize.query('DROP INDEX IF EXISTS idx_ligne_fulltext_search');
      } catch (error) {
        console.log('Erreur lors de la suppression des index full-text:', error.message);
      }
    }

    console.log('Index de recherche supprimés');
  }
};
