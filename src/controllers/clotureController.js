'use strict';

const { logger } = require('../config/logger');
const { ValidationError } = require('../middleware/errorHandler');

/**
 * Contrôleur pour la gestion des clôtures d'exercices comptables
 */
class ClotureController {
  constructor(clotureService) {
    this.clotureService = clotureService;
  }

  /**
   * Vérifie la complétude des saisies pour un exercice
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async verifierCompletudeSaisies(req, res, next) {
    try {
      const { exerciceId } = req.params;
      
      logger.info('Demande de vérification de complétude des saisies', {
        exerciceId,
        utilisateurId: req.user?.id
      });
      
      const resultat = await this.clotureService.verifierCompletudeSaisies(exerciceId);
      
      return res.status(200).json({
        success: true,
        data: resultat
      });
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * Génère les écritures de clôture pour un exercice
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async genererEcrituresCloture(req, res, next) {
    try {
      const { exerciceId } = req.params;
      const utilisateurId = req.user?.id || 'system';
      
      logger.info('Demande de génération des écritures de clôture', {
        exerciceId,
        utilisateurId
      });
      
      const resultat = await this.clotureService.genererEcrituresCloture(exerciceId, utilisateurId);
      
      return res.status(200).json({
        success: true,
        data: resultat
      });
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * Calcule le résultat de l'exercice
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async calculerResultatExercice(req, res, next) {
    try {
      const { exerciceId } = req.params;
      
      logger.info('Demande de calcul du résultat d\'exercice', {
        exerciceId,
        utilisateurId: req.user?.id
      });
      
      const resultatExercice = await this.clotureService.calculerResultatExercice(exerciceId);
      
      return res.status(200).json({
        success: true,
        data: {
          exerciceId,
          resultatExercice
        }
      });
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * Clôture un exercice comptable
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async cloturerExercice(req, res, next) {
    try {
      const { exerciceId } = req.params;
      const { commentaire, genererEcritures = true } = req.body;
      const utilisateurId = req.user?.id || 'system';
      
      logger.info('Demande de clôture d\'exercice', {
        exerciceId,
        utilisateurId,
        genererEcritures
      });
      
      const options = {
        commentaire,
        genererEcritures
      };
      
      const resultat = await this.clotureService.cloturerExercice(exerciceId, utilisateurId, options);
      
      return res.status(200).json({
        success: true,
        data: resultat
      });
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * Génère les écritures d'à-nouveaux pour l'exercice suivant
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async genererANouveaux(req, res, next) {
    try {
      const { exerciceId } = req.params;
      const { nouvelExerciceId } = req.body;
      const utilisateurId = req.user?.id || 'system';
      
      if (!nouvelExerciceId) {
        throw new ValidationError('L\'ID du nouvel exercice est requis');
      }
      
      logger.info('Demande de génération des à-nouveaux', {
        exerciceId,
        nouvelExerciceId,
        utilisateurId
      });
      
      const resultat = await this.clotureService.genererANouveaux(exerciceId, nouvelExerciceId, utilisateurId);
      
      return res.status(200).json({
        success: true,
        data: resultat
      });
      
    } catch (error) {
      next(error);
    }
  }
}

module.exports = ClotureController;