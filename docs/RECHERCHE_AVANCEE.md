# Module Recherche Avancée - API Comptabilité SYSCOHADA

## 📋 Vue d'ensemble

Le module de recherche avancée offre des capacités de recherche puissantes et flexibles pour les écritures comptables. Il permet aux utilisateurs de trouver rapidement des informations spécifiques dans de gros volumes de données comptables en utilisant des critères multiples et des algorithmes optimisés.

## 🎯 Fonctionnalités

### ✅ Recherche Multi-Critères
- Recherche combinée par société, exercice, journal, statut
- Filtres temporels (date d'écriture, date de création)
- Filtres de montant avec tolérance
- Filtres par compte avec hiérarchie
- Critères utilisateur (créateur, validateur)
- Critères de lettrage

### ✅ Recherche Textuelle Globale
- Recherche full-text dans tous les champs textuels
- Recherche dans les libellés, références, numéros d'écriture
- Recherche dans les lignes d'écriture
- Options : casse sensible, mot exact, champs spécifiques
- Surlignage des termes trouvés

### ✅ Recherches Spécialisées
- **Par Mont<PERSON>** : Recherche avec tolérance, par type (débit/crédit)
- **Par Compte** : Recherche avec hiérarchie, calcul des totaux
- **Par Période** : Analyses temporelles, groupements par mois/journal

### ✅ Optimisations Performance
- Index de base de données optimisés
- Requêtes paginées pour gros volumes
- Cache pour requêtes fréquentes
- Index full-text PostgreSQL

## 🏗️ Architecture

### Service RechercheService

Le service principal qui gère toutes les opérations de recherche :

```javascript
class RechercheService {
  // Recherche multi-critères
  async rechercheAvancee(criteres)
  
  // Recherche textuelle globale
  async rechercheTextuelle(texte, options)
  
  // Recherches spécialisées
  async rechercheParMontant(montantMin, montantMax, options)
  async rechercheParCompte(compteNumero, options)
  async rechercheParPeriode(dateDebut, dateFin, options)
  
  // Méthodes utilitaires
  async calculerStatistiques(ecritures)
  surlignerTermes(ecritures, terme, champs)
  calculerTotauxCompte(compteNumero, options)
}
```

### Index de Performance

Index créés pour optimiser les recherches :

```sql
-- Index textuels
CREATE INDEX idx_ecriture_libelle_search ON ecriture_comptables(libelle);
CREATE INDEX idx_ecriture_reference_search ON ecriture_comptables(reference);

-- Index composites
CREATE INDEX idx_ecriture_societe_date ON ecriture_comptables(societe_id, date_ecriture);
CREATE INDEX idx_ecriture_recherche_complexe ON ecriture_comptables(societe_id, exercice_id, date_ecriture, statut);

-- Index full-text PostgreSQL
CREATE INDEX idx_ecriture_fulltext_search ON ecriture_comptables 
USING gin(to_tsvector('french', libelle || ' ' || reference));
```

## 🔌 API REST

### Endpoints Disponibles

#### POST /api/v1/ecritures/recherche
Recherche avancée multi-critères.

**Paramètres :**
```json
{
  "societeId": "uuid",
  "exerciceId": "uuid",
  "journalCode": "VT",
  "statut": "VALIDEE",
  "dateDebut": "2024-01-01",
  "dateFin": "2024-12-31",
  "montantMin": 100.00,
  "montantMax": 5000.00,
  "compteNumero": "411000",
  "libelle": "client",
  "reference": "FACT",
  "lettrage": "AA001",
  "statutLettrage": "NON_LETTRE",
  "operateurLogique": "ET",
  "page": 1,
  "limit": 50,
  "orderBy": "dateEcriture",
  "orderDirection": "DESC"
}
```

**Réponse :**
```json
{
  "success": true,
  "data": {
    "ecritures": [...],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 150,
      "pages": 3
    },
    "statistiques": {
      "nombreEcritures": 150,
      "nombreLignes": 450,
      "totalDebit": 125000.00,
      "totalCredit": 125000.00,
      "repartitionParJournal": {...},
      "repartitionParStatut": {...}
    },
    "criteres": {...}
  }
}
```

#### GET /api/v1/ecritures/recherche-textuelle
Recherche textuelle globale.

**Paramètres :**
- `texte` (requis) : Texte à rechercher (min 2 caractères)
- `societeId` : ID de la société
- `champs` : Champs à rechercher (séparés par virgule)
- `rechercherDansLignes` : Rechercher dans les lignes (défaut: true)
- `caseSensitive` : Sensible à la casse (défaut: false)
- `motExact` : Recherche de mot exact (défaut: false)

**Exemple :**
```
GET /api/v1/ecritures/recherche-textuelle?texte=client ABC&rechercherDansLignes=true&caseSensitive=false
```

#### GET /api/v1/ecritures/recherche-montant
Recherche par montant avec tolérance.

**Paramètres :**
- `montantMin` : Montant minimum
- `montantMax` : Montant maximum
- `tolerance` : Tolérance (défaut: 0)
- `typeMontant` : DEBIT, CREDIT ou TOUS (défaut: TOUS)

**Exemple :**
```
GET /api/v1/ecritures/recherche-montant?montantMin=1000&montantMax=5000&tolerance=0.01&typeMontant=DEBIT
```

#### GET /api/v1/ecritures/recherche-compte/:compteNumero
Recherche par compte avec hiérarchie.

**Paramètres :**
- `compteNumero` (requis) : Numéro du compte
- `includeHierarchie` : Inclure la hiérarchie (défaut: false)
- `dateDebut`, `dateFin` : Période de recherche
- `statut` : Statut des écritures (défaut: VALIDEE)

**Exemple :**
```
GET /api/v1/ecritures/recherche-compte/411000?includeHierarchie=true&dateDebut=2024-01-01
```

#### GET /api/v1/ecritures/recherche-periode
Recherche par période avec analyses.

**Paramètres :**
- `dateDebut`, `dateFin` (requis) : Période de recherche
- `groupeParJournal` : Grouper par journal (défaut: false)
- `groupeParMois` : Grouper par mois (défaut: false)
- `includeAnalyses` : Inclure les analyses (défaut: true)

**Exemple :**
```
GET /api/v1/ecritures/recherche-periode?dateDebut=2024-01-01&dateFin=2024-03-31&groupeParMois=true
```

## 💡 Utilisation Pratique

### Exemple 1 : Recherche Multi-Critères

```javascript
// Rechercher toutes les ventes du premier trimestre
const criteres = {
  societeId: "uuid-societe",
  journalCode: "VT",
  dateDebut: "2024-01-01",
  dateFin: "2024-03-31",
  statut: "VALIDEE",
  libelle: "vente",
  montantMin: 100.00
};

const response = await fetch('/api/v1/ecritures/recherche', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(criteres)
});

const resultats = await response.json();
console.log(`${resultats.data.pagination.total} écritures trouvées`);
```

### Exemple 2 : Recherche Textuelle

```javascript
// Rechercher "client ABC" dans tous les champs
const response = await fetch('/api/v1/ecritures/recherche-textuelle?' + 
  new URLSearchParams({
    texte: 'client ABC',
    societeId: 'uuid-societe',
    rechercherDansLignes: true,
    caseSensitive: false
  })
);

const resultats = await response.json();
// Les termes trouvés sont surlignés avec <mark>
```

### Exemple 3 : Recherche par Compte avec Hiérarchie

```javascript
// Rechercher toutes les opérations sur les comptes clients (411xxx)
const response = await fetch('/api/v1/ecritures/recherche-compte/411000?' + 
  new URLSearchParams({
    includeHierarchie: true,
    dateDebut: '2024-01-01',
    dateFin: '2024-12-31'
  })
);

const resultats = await response.json();
console.log('Totaux:', resultats.data.totaux);
console.log('Solde:', resultats.data.totaux.solde);
```

### Exemple 4 : Analyse de Période

```javascript
// Analyser les ventes par mois
const response = await fetch('/api/v1/ecritures/recherche-periode?' + 
  new URLSearchParams({
    dateDebut: '2024-01-01',
    dateFin: '2024-12-31',
    journalCode: 'VT',
    groupeParMois: true,
    includeAnalyses: true
  })
);

const resultats = await response.json();
console.log('Groupement par mois:', resultats.data.groupements.parMois);
console.log('Analyses:', resultats.data.analyses);
```

## 🔧 Configuration et Optimisation

### Paramètres de Performance

```javascript
// Configuration recommandée pour gros volumes
const criteres = {
  // Limiter les résultats
  limit: 50,
  page: 1,
  
  // Optimiser les requêtes
  orderBy: 'dateEcriture',
  orderDirection: 'DESC',
  
  // Filtrer efficacement
  societeId: 'uuid', // Toujours spécifier
  statut: 'VALIDEE'  // Filtrer par statut
};
```

### Index Recommandés

Pour optimiser les performances, assurez-vous que ces index sont créés :

```sql
-- Index essentiels
CREATE INDEX idx_ecriture_societe_date ON ecriture_comptables(societe_id, date_ecriture);
CREATE INDEX idx_ligne_compte_ecriture ON ligne_ecritures(compte_numero, ecriture_id);

-- Index pour recherche textuelle
CREATE INDEX idx_ecriture_libelle_search ON ecriture_comptables(libelle);
CREATE INDEX idx_ecriture_reference_search ON ecriture_comptables(reference);
```

## 📊 Statistiques et Analyses

### Statistiques Automatiques

Chaque recherche retourne des statistiques détaillées :

```json
{
  "statistiques": {
    "nombreEcritures": 150,
    "nombreLignes": 450,
    "totalDebit": 125000.00,
    "totalCredit": 125000.00,
    "repartitionParJournal": {
      "VT": 80,
      "AC": 45,
      "BQ": 25
    },
    "repartitionParStatut": {
      "VALIDEE": 130,
      "BROUILLARD": 20
    },
    "repartitionParMois": {
      "2024-01": 50,
      "2024-02": 60,
      "2024-03": 40
    }
  }
}
```

### Groupements Avancés

```json
{
  "groupements": {
    "parJournal": {
      "VT": {
        "journal": {...},
        "ecritures": [...],
        "total": 80
      }
    },
    "parMois": {
      "2024-01": {
        "mois": "2024-01",
        "ecritures": [...],
        "total": 50
      }
    }
  }
}
```

## 🧪 Tests

### Exécution des Tests

```bash
# Tests unitaires du service
npm test -- --grep "RechercheService"

# Tests d'intégration API
npm test -- --grep "API Recherche"

# Tests complets du module
npm test src/tests/recherche.test.js
```

### Couverture de Tests

- ✅ Recherche multi-critères avec tous les filtres
- ✅ Recherche textuelle avec options avancées
- ✅ Recherche par montant avec tolérance
- ✅ Recherche par compte avec hiérarchie
- ✅ Recherche par période avec analyses
- ✅ Calcul des statistiques et totaux
- ✅ API REST complète avec validation
- ✅ Gestion des erreurs et cas limites

## 🚀 Prochaines Évolutions

- [ ] Recherche par IA/ML pour suggestions intelligentes
- [ ] Sauvegarde et partage de recherches favorites
- [ ] Export des résultats de recherche
- [ ] Recherche géospatiale pour les adresses
- [ ] Recherche par similarité de montants
- [ ] Interface graphique de construction de requêtes
- [ ] Alertes automatiques sur critères de recherche

---

*Ce module offre des capacités de recherche de niveau entreprise pour optimiser la productivité des utilisateurs comptables.*
