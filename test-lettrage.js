#!/usr/bin/env node
'use strict';

/**
 * Script de test pour le module de lettrage
 * Usage: node test-lettrage.js
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api/v1';

// Configuration de test
const timestamp = Date.now();
const testData = {
  societe: {
    nom: 'Société Test Lettrage',
    sigle: 'STL',
    adresse: 'Test Address',
    telephone: '123456789',
    email: `test-${timestamp}@lettrage.com`,
    formeJuridique: 'SARL',
    capital: 1000000,
    numeroRccm: `RCCM-TEST-${timestamp}`,
    numeroIfu: `IFU-TEST-${timestamp}`,
    exerciceDebut: '2024-01-01',
    exerciceFin: '2024-12-31'
  },
  exercice: {
    libelle: 'Exercice Test 2024',
    dateDebut: '2024-01-01',
    dateFin: '2024-12-31',
    statut: 'OUVERT'
  },
  journal: {
    code: `BQ${timestamp.toString().slice(-4)}`,
    libelle: 'Journal Banque Test',
    type: 'TRESORERIE'
  }
};

let societeId, exerciceId, journalCode;
let ecritureIds = [];
let ligneIds = [];

async function log(message, data = null) {
  console.log(`\n📋 ${message}`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
}

async function createTestData() {
  try {
    log('Création des données de test...');

    // 1. Créer une société
    const societeResponse = await axios.post(`${API_BASE}/societes`, testData.societe);
    societeId = societeResponse.data.data.id;
    log('✅ Société créée', { id: societeId });

    // 2. Créer un exercice
    const exerciceData = { ...testData.exercice, societeId };
    const exerciceResponse = await axios.post(`${API_BASE}/exercices`, exerciceData);
    exerciceId = exerciceResponse.data.data.id;
    log('✅ Exercice créé', { id: exerciceId });

    // 3. Créer un journal
    const journalData = { ...testData.journal, societeId };
    const journalResponse = await axios.post(`${API_BASE}/journaux`, journalData);
    journalCode = journalResponse.data.data.code;
    log('✅ Journal créé', { code: journalCode });

    // 4. Créer des écritures de test
    await createTestEcritures();

  } catch (error) {
    console.error('❌ Erreur lors de la création des données de test:', error.response?.data || error.message);
    throw error;
  }
}

async function createTestEcritures() {
  try {
    log('Création des écritures de test...');

    // Écriture 1: Facture client
    const ecriture1 = {
      donnees: {
        societeId,
        exerciceId,
        journalCode,
        dateEcriture: '2024-06-01',
        libelle: 'Facture client 001',
        reference: 'FACT-001'
      },
      lignes: [
        {
          compteNumero: '411000',
          libelle: 'Facture client 001',
          debit: 1000.00,
          credit: 0.00
        },
        {
          compteNumero: '701000',
          libelle: 'Vente marchandises',
          debit: 0.00,
          credit: 1000.00
        }
      ]
    };

    const response1 = await axios.post(`${API_BASE}/ecritures`, ecriture1);
    ecritureIds.push(response1.data.data.ecriture.id);
    ligneIds.push(response1.data.data.ecriture.lignes[0].id); // Ligne client
    log('✅ Écriture 1 créée (Facture)', { id: response1.data.data.ecriture.id });

    // Écriture 2: Règlement client
    const ecriture2 = {
      donnees: {
        societeId,
        exerciceId,
        journalCode,
        dateEcriture: '2024-06-15',
        libelle: 'Règlement client 001',
        reference: 'REG-001'
      },
      lignes: [
        {
          compteNumero: '411000',
          libelle: 'Règlement client 001',
          debit: 0.00,
          credit: 1000.00
        },
        {
          compteNumero: '521000',
          libelle: 'Banque',
          debit: 1000.00,
          credit: 0.00
        }
      ]
    };

    const response2 = await axios.post(`${API_BASE}/ecritures`, ecriture2);
    ecritureIds.push(response2.data.data.ecriture.id);
    ligneIds.push(response2.data.data.ecriture.lignes[0].id); // Ligne client
    log('✅ Écriture 2 créée (Règlement)', { id: response2.data.data.ecriture.id });

    // Valider les écritures
    for (const ecritureId of ecritureIds) {
      await axios.post(`${API_BASE}/ecritures/${ecritureId}/valider`);
    }
    log('✅ Écritures validées');

  } catch (error) {
    console.error('❌ Erreur lors de la création des écritures:', error.response?.data || error.message);
    throw error;
  }
}

async function testLettrage() {
  try {
    log('=== TESTS DU MODULE LETTRAGE ===');

    // 1. Récupérer les lignes à lettrer
    log('1. Récupération des lignes à lettrer...');
    const lignesResponse = await axios.get(`${API_BASE}/lettrage/lignes/411000`);
    log('✅ Lignes récupérées', {
      total: lignesResponse.data.data.pagination.total,
      lignes: lignesResponse.data.data.lignes.length
    });

    // 2. Calculer les soldes avant lettrage
    log('2. Calcul des soldes avant lettrage...');
    const soldesAvant = await axios.get(`${API_BASE}/lettrage/soldes/411000`);
    log('✅ Soldes avant lettrage', soldesAvant.data.data.soldes);

    // 3. Effectuer un lettrage manuel
    log('3. Lettrage manuel...');
    const lettrageData = {
      compteNumero: '411000',
      ligneIds: ligneIds
    };
    const lettrageResponse = await axios.post(`${API_BASE}/lettrage/manuel`, lettrageData);
    log('✅ Lettrage manuel effectué', lettrageResponse.data.data);

    // 4. Calculer les soldes après lettrage
    log('4. Calcul des soldes après lettrage...');
    const soldesApres = await axios.get(`${API_BASE}/lettrage/soldes/411000`);
    log('✅ Soldes après lettrage', soldesApres.data.data.soldes);

    // 5. Obtenir les statistiques
    log('5. Statistiques de lettrage...');
    const statsResponse = await axios.get(`${API_BASE}/lettrage/statistiques/411000`);
    log('✅ Statistiques', statsResponse.data.data.statistiques);

    // 6. Tester le délettrage
    log('6. Test du délettrage...');
    const delettrageData = {
      ligneIds: ligneIds
    };
    const delettrageResponse = await axios.post(`${API_BASE}/lettrage/delettrage`, delettrageData);
    log('✅ Délettrage effectué', delettrageResponse.data.data);

    // 7. Vérifier les soldes après délettrage
    log('7. Soldes après délettrage...');
    const soldesFinaux = await axios.get(`${API_BASE}/lettrage/soldes/411000`);
    log('✅ Soldes finaux', soldesFinaux.data.data.soldes);

    // 8. Test du lettrage automatique
    log('8. Test du lettrage automatique...');
    const lettrageAutoData = {
      compteNumero: '411000',
      criteres: {
        dateDebut: '2024-01-01',
        dateFin: '2024-12-31',
        toleranceMontant: 0.01,
        lettrerParMontant: true,
        lettrerParReference: true
      }
    };
    const lettrageAutoResponse = await axios.post(`${API_BASE}/lettrage/automatique`, lettrageAutoData);
    log('✅ Lettrage automatique terminé', lettrageAutoResponse.data.data);

    log('🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS !');

  } catch (error) {
    console.error('❌ Erreur lors des tests de lettrage:', error.response?.data || error.message);
    throw error;
  }
}

async function cleanup() {
  try {
    log('Nettoyage des données de test...');
    
    // Supprimer les écritures
    for (const ecritureId of ecritureIds) {
      try {
        await axios.delete(`${API_BASE}/ecritures/${ecritureId}`);
      } catch (e) {
        // Ignorer les erreurs de suppression
      }
    }

    // Supprimer l'exercice
    if (exerciceId) {
      try {
        await axios.delete(`${API_BASE}/exercices/${exerciceId}`);
      } catch (e) {
        // Ignorer les erreurs de suppression
      }
    }

    // Supprimer le journal
    if (journalCode) {
      try {
        await axios.delete(`${API_BASE}/journaux/${journalCode}`);
      } catch (e) {
        // Ignorer les erreurs de suppression
      }
    }

    // Supprimer la société
    if (societeId) {
      try {
        await axios.delete(`${API_BASE}/societes/${societeId}`);
      } catch (e) {
        // Ignorer les erreurs de suppression
      }
    }

    log('✅ Nettoyage terminé');
  } catch (error) {
    console.error('⚠️ Erreur lors du nettoyage:', error.message);
  }
}

async function main() {
  try {
    console.log('🚀 Démarrage des tests du module de lettrage...');
    
    await createTestData();
    await testLettrage();
    
  } catch (error) {
    console.error('💥 Échec des tests:', error.message);
    process.exit(1);
  } finally {
    await cleanup();
  }
}

// Exécuter les tests si le script est appelé directement
if (require.main === module) {
  main();
}

module.exports = { main, createTestData, testLettrage, cleanup };
