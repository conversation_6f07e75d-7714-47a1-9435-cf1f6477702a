'use strict';

const { logger } = require('../config/logger');
const ParametreService = require('../services/parametreService');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Controller pour la gestion des paramètres comptables
 */
class ParametreController {
  constructor(models) {
    this.models = models;
    this.parametreService = new ParametreService(models);
  }

  /**
   * Obtient tous les paramètres d'une société
   */
  async getParametresSociete(req, res, next) {
    try {
      const { societeId } = req.params;
      const { categorie, useCache = 'true' } = req.query;

      // Vérifier que la société existe
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      const parametres = await this.parametreService.getParametresSociete(
        societeId, 
        categorie, 
        useCache === 'true'
      );

      res.json({
        success: true,
        data: parametres,
        meta: {
          societe: {
            id: societe.id,
            nom: societe.nom
          },
          categorie: categorie || 'toutes',
          nombreCategories: Object.keys(parametres).length,
          nombreParametres: Object.values(parametres).reduce((total, cat) => total + Object.keys(cat).length, 0)
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des paramètres', {
        societeId: req.params.societeId,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Obtient la configuration TVA d'une société
   */
  async getConfigurationTVA(req, res, next) {
    try {
      const { societeId } = req.params;

      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      const configTVA = await this.parametreService.getConfigurationTVA(societeId);

      res.json({
        success: true,
        data: configTVA,
        meta: {
          societe: {
            id: societe.id,
            nom: societe.nom
          }
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération de la configuration TVA', {
        societeId: req.params.societeId,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Obtient la configuration fiscale d'une société
   */
  async getConfigurationFiscale(req, res, next) {
    try {
      const { societeId } = req.params;

      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      const configFiscale = await this.parametreService.getConfigurationFiscale(societeId);

      res.json({
        success: true,
        data: configFiscale,
        meta: {
          societe: {
            id: societe.id,
            nom: societe.nom
          }
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération de la configuration fiscale', {
        societeId: req.params.societeId,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Met à jour les paramètres d'une société
   */
  async mettreAJourParametres(req, res, next) {
    try {
      const { societeId } = req.params;
      const parametres = req.body;

      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      const parametresMisAJour = await this.parametreService.mettreAJourParametres(
        societeId, 
        parametres
      );

      res.json({
        success: true,
        data: parametresMisAJour,
        message: `${parametresMisAJour.length} paramètre(s) mis à jour avec succès`
      });

    } catch (error) {
      logger.error('Erreur lors de la mise à jour des paramètres', {
        societeId: req.params.societeId,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Valide la cohérence des paramètres
   */
  async validerParametres(req, res, next) {
    try {
      const { societeId } = req.params;

      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      const validation = await this.parametreService.validerCoherenceParametres(societeId);

      res.json({
        success: true,
        data: validation,
        meta: {
          societe: {
            id: societe.id,
            nom: societe.nom
          }
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la validation des paramètres', {
        societeId: req.params.societeId,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Initialise les paramètres par défaut
   */
  async initialiserParametresDefaut(req, res, next) {
    try {
      const { societeId } = req.params;
      const { pays = 'CI' } = req.body;

      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      const parametres = await this.parametreService.initialiserParametresDefaut(societeId, pays);

      res.json({
        success: true,
        data: parametres,
        message: `${parametres.length} paramètres par défaut initialisés pour ${pays}`
      });

    } catch (error) {
      logger.error('Erreur lors de l\'initialisation des paramètres', {
        societeId: req.params.societeId,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Obtient un paramètre spécifique
   */
  async getParametre(req, res, next) {
    try {
      const { societeId, cle } = req.params;

      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      const parametre = await this.models.ParametreComptable.findOne({
        where: { societeId, cle }
      });

      if (!parametre) {
        throw new NotFoundError(`Paramètre '${cle}'`);
      }

      res.json({
        success: true,
        data: {
          cle: parametre.cle,
          valeur: parametre.getValeurTypee(),
          valeurBrute: parametre.valeur,
          type: parametre.type,
          categorie: parametre.categorie,
          description: parametre.description
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération du paramètre', {
        societeId: req.params.societeId,
        cle: req.params.cle,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Met à jour un paramètre spécifique
   */
  async mettreAJourParametre(req, res, next) {
    try {
      const { societeId, cle } = req.params;
      const { valeur, type = 'STRING', categorie = 'CUSTOM', description } = req.body;

      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      const parametre = await this.models.ParametreComptable.setParametre(
        societeId, 
        cle, 
        valeur, 
        type, 
        categorie, 
        description
      );

      // Invalider le cache
      this.parametreService.invaliderCacheSociete(societeId);

      res.json({
        success: true,
        data: {
          cle: parametre.cle,
          valeur: parametre.getValeurTypee(),
          type: parametre.type,
          categorie: parametre.categorie,
          description: parametre.description
        },
        message: `Paramètre '${cle}' mis à jour avec succès`
      });

    } catch (error) {
      logger.error('Erreur lors de la mise à jour du paramètre', {
        societeId: req.params.societeId,
        cle: req.params.cle,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Supprime un paramètre
   */
  async supprimerParametre(req, res, next) {
    try {
      const { societeId, cle } = req.params;

      const parametre = await this.models.ParametreComptable.findOne({
        where: { societeId, cle }
      });

      if (!parametre) {
        throw new NotFoundError(`Paramètre '${cle}'`);
      }

      // Vérifier que ce n'est pas un paramètre système critique
      const parametresCritiques = ['tva_taux_normal', 'compte_resultat_benefice', 'devise_presentation'];
      if (parametresCritiques.includes(cle)) {
        throw new ValidationError(`Le paramètre '${cle}' est critique et ne peut être supprimé`);
      }

      await parametre.destroy();

      // Invalider le cache
      this.parametreService.invaliderCacheSociete(societeId);

      res.json({
        success: true,
        message: `Paramètre '${cle}' supprimé avec succès`
      });

    } catch (error) {
      logger.error('Erreur lors de la suppression du paramètre', {
        societeId: req.params.societeId,
        cle: req.params.cle,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Vide le cache des paramètres
   */
  async viderCache(req, res, next) {
    try {
      this.parametreService.viderCache();

      res.json({
        success: true,
        message: 'Cache des paramètres vidé avec succès'
      });

    } catch (error) {
      logger.error('Erreur lors du vidage du cache', {
        error: error.message
      });
      next(error);
    }
  }
}

module.exports = ParametreController;
