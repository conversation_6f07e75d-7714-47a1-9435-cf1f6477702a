'use strict';

const logger = require('../config/logger');
const { ValidationError, NotFoundError } = require('../middleware/errorHandler');

/**
 * Service pour les calculs comptables et gestion des soldes
 * Conforme aux normes SYSCOHADA
 */
class CalculService {
  constructor(models) {
    this.models = models || require('../models');
    this.cache = new Map(); // Cache simple pour les calculs fréquents
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  // ==========================================
  // CALCULS DE SOLDES DE COMPTES
  // ==========================================

  /**
   * Calcule le solde d'un compte à une date donnée
   * @param {string} compteNumero - Numéro du compte
   * @param {Date} dateDebut - Date de début (optionnelle)
   * @param {Date} dateFin - Date de fin
   * @param {Object} options - Options de calcul
   * @returns {Promise<Object>} Solde détaillé
   */
  async calculerSoldeCompte(compteNumero, dateDebut = null, dateFin, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        includeNonValidees = false,
        useCache = true
      } = options;

      // Vérifier que le compte existe
      const compte = await this.models.CompteComptable.findOne({
        where: { numero: compteNumero, ...(societeId && { societeId }) }
      });

      if (!compte) {
        throw new NotFoundError(`Compte ${compteNumero}`);
      }

      // Clé de cache
      const cacheKey = `solde_${compteNumero}_${dateDebut}_${dateFin}_${societeId}_${exerciceId}_${includeNonValidees}`;
      
      if (useCache && this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.data;
        }
      }

      // Construire les conditions WHERE
      const whereEcriture = {
        dateEcriture: {
          [this.models.sequelize.Op.lte]: dateFin
        }
      };

      if (dateDebut) {
        whereEcriture.dateEcriture[this.models.sequelize.Op.gte] = dateDebut;
      }

      if (!includeNonValidees) {
        whereEcriture.statut = 'VALIDEE';
      }

      if (societeId) {
        whereEcriture.societeId = societeId;
      }

      if (exerciceId) {
        whereEcriture.exerciceId = exerciceId;
      }

      // Requête optimisée avec agrégation
      const result = await this.models.LigneEcriture.findOne({
        where: { compteNumero },
        include: [{
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: whereEcriture,
          attributes: []
        }],
        attributes: [
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('debit')), 'totalDebit'],
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('credit')), 'totalCredit'],
          [this.models.sequelize.fn('COUNT', this.models.sequelize.col('ligne_ecritures.id')), 'nombreLignes']
        ],
        raw: true
      });

      const totalDebit = parseFloat(result?.totalDebit || 0);
      const totalCredit = parseFloat(result?.totalCredit || 0);
      const solde = totalDebit - totalCredit;

      // Déterminer le sens du solde selon la nature du compte
      let sensNaturel = compte.sens;
      let soldeNaturel = sensNaturel === 'DEBIT' ? solde : -solde;
      let sensActuel = solde >= 0 ? 'DEBIT' : 'CREDIT';

      const resultat = {
        compteNumero,
        libelle: compte.libelle,
        sensNaturel,
        totalDebit,
        totalCredit,
        solde: Math.abs(solde),
        sensActuel,
        soldeNaturel,
        nombreLignes: parseInt(result?.nombreLignes || 0),
        dateCalcul: new Date(),
        periode: {
          dateDebut,
          dateFin
        }
      };

      // Mettre en cache
      if (useCache) {
        this.cache.set(cacheKey, {
          data: resultat,
          timestamp: Date.now()
        });
      }

      logger.debug('Solde calculé', {
        compteNumero,
        solde: resultat.solde,
        sensActuel: resultat.sensActuel
      });

      return resultat;

    } catch (error) {
      logger.error('Erreur lors du calcul du solde', {
        compteNumero,
        dateDebut,
        dateFin,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Calcule les soldes progressifs d'un compte sur une période
   * @param {string} compteNumero - Numéro du compte
   * @param {Date} dateDebut - Date de début
   * @param {Date} dateFin - Date de fin
   * @param {Object} options - Options de calcul
   * @returns {Promise<Array>} Soldes progressifs par date
   */
  async calculerSoldesProgressifs(compteNumero, dateDebut, dateFin, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        includeNonValidees = false,
        groupeParMois = false
      } = options;

      // Vérifier que le compte existe
      const compte = await this.models.CompteComptable.findOne({
        where: { numero: compteNumero, ...(societeId && { societeId }) }
      });

      if (!compte) {
        throw new NotFoundError(`Compte ${compteNumero}`);
      }

      // Construire les conditions WHERE
      const whereEcriture = {
        dateEcriture: {
          [this.models.sequelize.Op.between]: [dateDebut, dateFin]
        }
      };

      if (!includeNonValidees) {
        whereEcriture.statut = 'VALIDEE';
      }

      if (societeId) {
        whereEcriture.societeId = societeId;
      }

      if (exerciceId) {
        whereEcriture.exerciceId = exerciceId;
      }

      // Groupement par date ou par mois
      const dateFormat = groupeParMois ? 
        this.models.sequelize.fn('DATE_TRUNC', 'month', this.models.sequelize.col('ecriture.date_ecriture')) :
        this.models.sequelize.col('ecriture.date_ecriture');

      // Requête avec groupement par date
      const resultats = await this.models.LigneEcriture.findAll({
        where: { compteNumero },
        include: [{
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: whereEcriture,
          attributes: []
        }],
        attributes: [
          [dateFormat, 'date'],
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('debit')), 'totalDebit'],
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('credit')), 'totalCredit'],
          [this.models.sequelize.fn('COUNT', this.models.sequelize.col('ligne_ecritures.id')), 'nombreLignes']
        ],
        group: [dateFormat],
        order: [[dateFormat, 'ASC']],
        raw: true
      });

      // Calculer les soldes progressifs
      let soldeProgressif = 0;
      const soldesProgressifs = [];

      for (const resultat of resultats) {
        const totalDebit = parseFloat(resultat.totalDebit || 0);
        const totalCredit = parseFloat(resultat.totalCredit || 0);
        const mouvementNet = totalDebit - totalCredit;
        
        soldeProgressif += mouvementNet;

        soldesProgressifs.push({
          date: resultat.date,
          totalDebit,
          totalCredit,
          mouvementNet,
          soldeProgressif: Math.abs(soldeProgressif),
          sensProgressif: soldeProgressif >= 0 ? 'DEBIT' : 'CREDIT',
          nombreLignes: parseInt(resultat.nombreLignes || 0)
        });
      }

      logger.info('Soldes progressifs calculés', {
        compteNumero,
        nombrePeriodes: soldesProgressifs.length,
        dateDebut,
        dateFin
      });

      return {
        compteNumero,
        libelle: compte.libelle,
        sensNaturel: compte.sens,
        periode: { dateDebut, dateFin },
        soldesProgressifs
      };

    } catch (error) {
      logger.error('Erreur lors du calcul des soldes progressifs', {
        compteNumero,
        dateDebut,
        dateFin,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Calcule les totaux d'un journal sur une période
   * @param {string} journalCode - Code du journal
   * @param {Date} dateDebut - Date de début
   * @param {Date} dateFin - Date de fin
   * @param {Object} options - Options de calcul
   * @returns {Promise<Object>} Totaux du journal
   */
  async calculerTotauxJournal(journalCode, dateDebut, dateFin, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        includeNonValidees = false,
        groupeParCompte = false
      } = options;

      // Vérifier que le journal existe
      const journal = await this.models.Journal.findOne({
        where: { code: journalCode, ...(societeId && { societeId }) }
      });

      if (!journal) {
        throw new NotFoundError(`Journal ${journalCode}`);
      }

      // Construire les conditions WHERE
      const whereEcriture = {
        journalCode,
        dateEcriture: {
          [this.models.sequelize.Op.between]: [dateDebut, dateFin]
        }
      };

      if (!includeNonValidees) {
        whereEcriture.statut = 'VALIDEE';
      }

      if (societeId) {
        whereEcriture.societeId = societeId;
      }

      if (exerciceId) {
        whereEcriture.exerciceId = exerciceId;
      }

      // Totaux généraux du journal
      const totauxGeneraux = await this.models.LigneEcriture.findOne({
        include: [{
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: whereEcriture,
          attributes: []
        }],
        attributes: [
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('debit')), 'totalDebit'],
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('credit')), 'totalCredit'],
          [this.models.sequelize.fn('COUNT', this.models.sequelize.col('ligne_ecritures.id')), 'nombreLignes'],
          [this.models.sequelize.fn('COUNT', this.models.sequelize.fn('DISTINCT', this.models.sequelize.col('ecriture.id'))), 'nombreEcritures']
        ],
        raw: true
      });

      const totalDebit = parseFloat(totauxGeneraux?.totalDebit || 0);
      const totalCredit = parseFloat(totauxGeneraux?.totalCredit || 0);
      const difference = Math.abs(totalDebit - totalCredit);

      let detailsParCompte = [];

      // Si demandé, calculer les détails par compte
      if (groupeParCompte) {
        detailsParCompte = await this.models.LigneEcriture.findAll({
          where: { compteNumero: { [this.models.sequelize.Op.ne]: null } },
          include: [
            {
              model: this.models.EcritureComptable,
              as: 'ecriture',
              where: whereEcriture,
              attributes: []
            },
            {
              model: this.models.CompteComptable,
              as: 'compte',
              attributes: ['libelle', 'sens']
            }
          ],
          attributes: [
            'compteNumero',
            [this.models.sequelize.fn('SUM', this.models.sequelize.col('debit')), 'totalDebit'],
            [this.models.sequelize.fn('SUM', this.models.sequelize.col('credit')), 'totalCredit'],
            [this.models.sequelize.fn('COUNT', this.models.sequelize.col('ligne_ecritures.id')), 'nombreLignes']
          ],
          group: ['compteNumero', 'compte.numero', 'compte.libelle', 'compte.sens'],
          order: [['compteNumero', 'ASC']],
          raw: false
        });

        detailsParCompte = detailsParCompte.map(detail => ({
          compteNumero: detail.compteNumero,
          libelle: detail.compte?.libelle || 'Compte inconnu',
          sensNaturel: detail.compte?.sens || 'DEBIT',
          totalDebit: parseFloat(detail.dataValues.totalDebit || 0),
          totalCredit: parseFloat(detail.dataValues.totalCredit || 0),
          solde: parseFloat(detail.dataValues.totalDebit || 0) - parseFloat(detail.dataValues.totalCredit || 0),
          nombreLignes: parseInt(detail.dataValues.nombreLignes || 0)
        }));
      }

      const resultat = {
        journalCode,
        libelle: journal.libelle,
        type: journal.type,
        periode: { dateDebut, dateFin },
        totaux: {
          totalDebit,
          totalCredit,
          difference,
          equilibre: difference < 0.01,
          nombreLignes: parseInt(totauxGeneraux?.nombreLignes || 0),
          nombreEcritures: parseInt(totauxGeneraux?.nombreEcritures || 0)
        },
        detailsParCompte,
        dateCalcul: new Date()
      };

      logger.info('Totaux journal calculés', {
        journalCode,
        totalDebit,
        totalCredit,
        nombreEcritures: resultat.totaux.nombreEcritures
      });

      return resultat;

    } catch (error) {
      logger.error('Erreur lors du calcul des totaux journal', {
        journalCode,
        dateDebut,
        dateFin,
        error: error.message
      });
      throw error;
    }
  }

  // ==========================================
  // CALCULS DE BALANCES
  // ==========================================

  /**
   * Calcule la balance générale
   * @param {Date} dateDebut - Date de début
   * @param {Date} dateFin - Date de fin
   * @param {Object} options - Options de calcul
   * @returns {Promise<Object>} Balance générale
   */
  async calculerBalanceGenerale(dateDebut, dateFin, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        includeNonValidees = false,
        niveauDetail = 'TOUS', // TOUS, DETAIL, COLLECTIF
        classeComptes = null, // Filtrer par classe (1,2,3,4,5,6,7,8)
        seulementAvecMouvement = false
      } = options;

      // Construire les conditions WHERE pour les écritures
      const whereEcriture = {
        dateEcriture: {
          [this.models.sequelize.Op.between]: [dateDebut, dateFin]
        }
      };

      if (!includeNonValidees) {
        whereEcriture.statut = 'VALIDEE';
      }

      if (societeId) {
        whereEcriture.societeId = societeId;
      }

      if (exerciceId) {
        whereEcriture.exerciceId = exerciceId;
      }

      // Conditions pour les comptes
      const whereCompte = {};
      if (societeId) {
        whereCompte.societeId = societeId;
      }

      if (classeComptes) {
        const classes = Array.isArray(classeComptes) ? classeComptes : [classeComptes];
        whereCompte.classe = {
          [this.models.sequelize.Op.in]: classes
        };
      }

      if (niveauDetail === 'DETAIL') {
        whereCompte.nature = 'DETAIL';
      } else if (niveauDetail === 'COLLECTIF') {
        whereCompte.nature = 'COLLECTIF';
      }

      // Requête principale pour obtenir les soldes par compte
      const soldesComptes = await this.models.LigneEcriture.findAll({
        include: [
          {
            model: this.models.EcritureComptable,
            as: 'ecriture',
            where: whereEcriture,
            attributes: []
          },
          {
            model: this.models.CompteComptable,
            as: 'compte',
            where: whereCompte,
            attributes: ['numero', 'libelle', 'classe', 'nature', 'sens', 'niveau']
          }
        ],
        attributes: [
          'compteNumero',
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('debit')), 'totalDebit'],
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('credit')), 'totalCredit'],
          [this.models.sequelize.fn('COUNT', this.models.sequelize.col('ligne_ecritures.id')), 'nombreLignes']
        ],
        group: [
          'compteNumero',
          'compte.numero',
          'compte.libelle',
          'compte.classe',
          'compte.nature',
          'compte.sens',
          'compte.niveau'
        ],
        order: [['compteNumero', 'ASC']],
        raw: false
      });

      // Traiter les résultats
      const lignesBalance = [];
      let totauxGeneraux = {
        totalDebit: 0,
        totalCredit: 0,
        nombreComptes: 0,
        nombreLignes: 0
      };

      for (const solde of soldesComptes) {
        const totalDebit = parseFloat(solde.dataValues.totalDebit || 0);
        const totalCredit = parseFloat(solde.dataValues.totalCredit || 0);
        const soldeNet = totalDebit - totalCredit;

        // Filtrer les comptes sans mouvement si demandé
        if (seulementAvecMouvement && totalDebit === 0 && totalCredit === 0) {
          continue;
        }

        const ligneBalance = {
          compteNumero: solde.compteNumero,
          libelle: solde.compte?.libelle || 'Compte inconnu',
          classe: solde.compte?.classe || 0,
          nature: solde.compte?.nature || 'DETAIL',
          sensNaturel: solde.compte?.sens || 'DEBIT',
          niveau: solde.compte?.niveau || 1,
          totalDebit,
          totalCredit,
          solde: Math.abs(soldeNet),
          sensActuel: soldeNet >= 0 ? 'DEBIT' : 'CREDIT',
          nombreLignes: parseInt(solde.dataValues.nombreLignes || 0)
        };

        lignesBalance.push(ligneBalance);

        // Cumuler les totaux
        totauxGeneraux.totalDebit += totalDebit;
        totauxGeneraux.totalCredit += totalCredit;
        totauxGeneraux.nombreComptes++;
        totauxGeneraux.nombreLignes += ligneBalance.nombreLignes;
      }

      // Calculer les totaux par classe
      const totauxParClasse = {};
      lignesBalance.forEach(ligne => {
        const classe = ligne.classe;
        if (!totauxParClasse[classe]) {
          totauxParClasse[classe] = {
            classe,
            libelle: this.getLibelleClasse(classe),
            totalDebit: 0,
            totalCredit: 0,
            nombreComptes: 0
          };
        }
        totauxParClasse[classe].totalDebit += ligne.totalDebit;
        totauxParClasse[classe].totalCredit += ligne.totalCredit;
        totauxParClasse[classe].nombreComptes++;
      });

      const resultat = {
        periode: { dateDebut, dateFin },
        options: {
          niveauDetail,
          classeComptes,
          seulementAvecMouvement,
          includeNonValidees
        },
        totauxGeneraux: {
          ...totauxGeneraux,
          equilibre: Math.abs(totauxGeneraux.totalDebit - totauxGeneraux.totalCredit) < 0.01
        },
        totauxParClasse: Object.values(totauxParClasse),
        lignesBalance,
        dateCalcul: new Date()
      };

      logger.info('Balance générale calculée', {
        nombreComptes: totauxGeneraux.nombreComptes,
        totalDebit: totauxGeneraux.totalDebit,
        totalCredit: totauxGeneraux.totalCredit,
        equilibre: resultat.totauxGeneraux.equilibre
      });

      return resultat;

    } catch (error) {
      logger.error('Erreur lors du calcul de la balance générale', {
        dateDebut,
        dateFin,
        options,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Calcule la balance auxiliaire pour un compte collectif
   * @param {string} compteCollectif - Numéro du compte collectif
   * @param {Date} dateDebut - Date de début
   * @param {Date} dateFin - Date de fin
   * @param {Object} options - Options de calcul
   * @returns {Promise<Object>} Balance auxiliaire
   */
  async calculerBalanceAuxiliaire(compteCollectif, dateDebut, dateFin, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        includeNonValidees = false,
        seulementAvecMouvement = false
      } = options;

      // Vérifier que le compte collectif existe
      const compte = await this.models.CompteComptable.findOne({
        where: {
          numero: compteCollectif,
          nature: 'COLLECTIF',
          ...(societeId && { societeId })
        }
      });

      if (!compte) {
        throw new NotFoundError(`Compte collectif ${compteCollectif}`);
      }

      // Trouver tous les comptes auxiliaires (comptes qui commencent par le numéro du collectif)
      const comptesAuxiliaires = await this.models.CompteComptable.findAll({
        where: {
          numero: {
            [this.models.sequelize.Op.like]: `${compteCollectif}%`
          },
          nature: 'DETAIL',
          ...(societeId && { societeId })
        },
        order: [['numero', 'ASC']]
      });

      if (comptesAuxiliaires.length === 0) {
        return {
          compteCollectif,
          libelle: compte.libelle,
          periode: { dateDebut, dateFin },
          comptesAuxiliaires: [],
          totaux: {
            totalDebit: 0,
            totalCredit: 0,
            nombreComptes: 0
          }
        };
      }

      const numerosAuxiliaires = comptesAuxiliaires.map(c => c.numero);

      // Construire les conditions WHERE pour les écritures
      const whereEcriture = {
        dateEcriture: {
          [this.models.sequelize.Op.between]: [dateDebut, dateFin]
        }
      };

      if (!includeNonValidees) {
        whereEcriture.statut = 'VALIDEE';
      }

      if (societeId) {
        whereEcriture.societeId = societeId;
      }

      if (exerciceId) {
        whereEcriture.exerciceId = exerciceId;
      }

      // Calculer les soldes pour chaque compte auxiliaire
      const soldesAuxiliaires = await this.models.LigneEcriture.findAll({
        where: {
          compteNumero: {
            [this.models.sequelize.Op.in]: numerosAuxiliaires
          }
        },
        include: [
          {
            model: this.models.EcritureComptable,
            as: 'ecriture',
            where: whereEcriture,
            attributes: []
          },
          {
            model: this.models.CompteComptable,
            as: 'compte',
            attributes: ['libelle', 'sens']
          }
        ],
        attributes: [
          'compteNumero',
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('debit')), 'totalDebit'],
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('credit')), 'totalCredit'],
          [this.models.sequelize.fn('COUNT', this.models.sequelize.col('ligne_ecritures.id')), 'nombreLignes']
        ],
        group: ['compteNumero', 'compte.numero', 'compte.libelle', 'compte.sens'],
        order: [['compteNumero', 'ASC']],
        raw: false
      });

      // Traiter les résultats
      const lignesBalance = [];
      let totaux = {
        totalDebit: 0,
        totalCredit: 0,
        nombreComptes: 0,
        nombreLignes: 0
      };

      for (const solde of soldesAuxiliaires) {
        const totalDebit = parseFloat(solde.dataValues.totalDebit || 0);
        const totalCredit = parseFloat(solde.dataValues.totalCredit || 0);
        const soldeNet = totalDebit - totalCredit;

        // Filtrer les comptes sans mouvement si demandé
        if (seulementAvecMouvement && totalDebit === 0 && totalCredit === 0) {
          continue;
        }

        const ligneBalance = {
          compteNumero: solde.compteNumero,
          libelle: solde.compte?.libelle || 'Compte inconnu',
          sensNaturel: solde.compte?.sens || compte.sens,
          totalDebit,
          totalCredit,
          solde: Math.abs(soldeNet),
          sensActuel: soldeNet >= 0 ? 'DEBIT' : 'CREDIT',
          nombreLignes: parseInt(solde.dataValues.nombreLignes || 0)
        };

        lignesBalance.push(ligneBalance);

        // Cumuler les totaux
        totaux.totalDebit += totalDebit;
        totaux.totalCredit += totalCredit;
        totaux.nombreComptes++;
        totaux.nombreLignes += ligneBalance.nombreLignes;
      }

      const resultat = {
        compteCollectif,
        libelle: compte.libelle,
        sensNaturel: compte.sens,
        periode: { dateDebut, dateFin },
        options: {
          seulementAvecMouvement,
          includeNonValidees
        },
        totaux: {
          ...totaux,
          equilibre: Math.abs(totaux.totalDebit - totaux.totalCredit) < 0.01
        },
        comptesAuxiliaires: lignesBalance,
        dateCalcul: new Date()
      };

      logger.info('Balance auxiliaire calculée', {
        compteCollectif,
        nombreComptes: totaux.nombreComptes,
        totalDebit: totaux.totalDebit,
        totalCredit: totaux.totalCredit
      });

      return resultat;

    } catch (error) {
      logger.error('Erreur lors du calcul de la balance auxiliaire', {
        compteCollectif,
        dateDebut,
        dateFin,
        error: error.message
      });
      throw error;
    }
  }

  // ==========================================
  // MÉTHODES UTILITAIRES ET OPTIMISATIONS
  // ==========================================

  /**
   * Calcule les statistiques générales d'une société
   * @param {string} societeId - ID de la société
   * @param {Date} dateDebut - Date de début
   * @param {Date} dateFin - Date de fin
   * @returns {Promise<Object>} Statistiques générales
   */
  async calculerStatistiquesGenerales(societeId, dateDebut, dateFin) {
    try {
      const whereEcriture = {
        societeId,
        dateEcriture: {
          [this.models.sequelize.Op.between]: [dateDebut, dateFin]
        },
        statut: 'VALIDEE'
      };

      // Statistiques des écritures
      const statsEcritures = await this.models.EcritureComptable.findOne({
        where: whereEcriture,
        attributes: [
          [this.models.sequelize.fn('COUNT', this.models.sequelize.col('id')), 'nombreEcritures'],
          [this.models.sequelize.fn('MIN', this.models.sequelize.col('date_ecriture')), 'premiereEcriture'],
          [this.models.sequelize.fn('MAX', this.models.sequelize.col('date_ecriture')), 'derniereEcriture']
        ],
        raw: true
      });

      // Statistiques des lignes
      const statsLignes = await this.models.LigneEcriture.findOne({
        include: [{
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: whereEcriture,
          attributes: []
        }],
        attributes: [
          [this.models.sequelize.fn('COUNT', this.models.sequelize.col('ligne_ecritures.id')), 'nombreLignes'],
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('debit')), 'totalDebit'],
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('credit')), 'totalCredit'],
          [this.models.sequelize.fn('COUNT', this.models.sequelize.fn('DISTINCT', this.models.sequelize.col('compte_numero'))), 'comptesUtilises']
        ],
        raw: true
      });

      // Statistiques par journal
      const statsJournaux = await this.models.EcritureComptable.findAll({
        where: whereEcriture,
        include: [{
          model: this.models.Journal,
          as: 'journal',
          attributes: ['libelle', 'type']
        }],
        attributes: [
          'journalCode',
          [this.models.sequelize.fn('COUNT', this.models.sequelize.col('ecriture_comptables.id')), 'nombreEcritures']
        ],
        group: ['journalCode', 'journal.code', 'journal.libelle', 'journal.type'],
        order: [[this.models.sequelize.fn('COUNT', this.models.sequelize.col('ecriture_comptables.id')), 'DESC']],
        limit: 10,
        raw: false
      });

      return {
        periode: { dateDebut, dateFin },
        ecritures: {
          nombre: parseInt(statsEcritures?.nombreEcritures || 0),
          premiereDate: statsEcritures?.premiereEcriture,
          derniereDate: statsEcritures?.derniereEcriture
        },
        lignes: {
          nombre: parseInt(statsLignes?.nombreLignes || 0),
          totalDebit: parseFloat(statsLignes?.totalDebit || 0),
          totalCredit: parseFloat(statsLignes?.totalCredit || 0),
          comptesUtilises: parseInt(statsLignes?.comptesUtilises || 0)
        },
        journauxActifs: statsJournaux.map(stat => ({
          code: stat.journalCode,
          libelle: stat.journal?.libelle || 'Journal inconnu',
          type: stat.journal?.type || 'AUTRE',
          nombreEcritures: parseInt(stat.dataValues.nombreEcritures || 0)
        })),
        dateCalcul: new Date()
      };

    } catch (error) {
      logger.error('Erreur lors du calcul des statistiques générales', {
        societeId,
        dateDebut,
        dateFin,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Vide le cache des calculs
   */
  viderCache() {
    this.cache.clear();
    logger.info('Cache des calculs vidé');
  }

  /**
   * Obtient les statistiques du cache
   * @returns {Object} Statistiques du cache
   */
  getStatistiquesCache() {
    const maintenant = Date.now();
    let entreeValides = 0;
    let entreeExpirees = 0;

    for (const [key, value] of this.cache.entries()) {
      if (maintenant - value.timestamp < this.cacheTimeout) {
        entreeValides++;
      } else {
        entreeExpirees++;
      }
    }

    return {
      totalEntrees: this.cache.size,
      entreeValides,
      entreeExpirees,
      tauxHit: this.cache.size > 0 ? (entreeValides / this.cache.size * 100).toFixed(2) : 0
    };
  }

  /**
   * Nettoie les entrées expirées du cache
   */
  nettoyerCache() {
    const maintenant = Date.now();
    let entreesSupprimees = 0;

    for (const [key, value] of this.cache.entries()) {
      if (maintenant - value.timestamp >= this.cacheTimeout) {
        this.cache.delete(key);
        entreesSupprimees++;
      }
    }

    if (entreesSupprimees > 0) {
      logger.debug(`Cache nettoyé: ${entreesSupprimees} entrées supprimées`);
    }

    return entreesSupprimees;
  }

  /**
   * Obtient le libellé d'une classe de compte
   * @param {number} classe - Numéro de classe
   * @returns {string} Libellé de la classe
   */
  getLibelleClasse(classe) {
    const libelles = {
      1: 'Comptes de capitaux',
      2: 'Comptes d\'immobilisations',
      3: 'Comptes de stocks',
      4: 'Comptes de tiers',
      5: 'Comptes de trésorerie',
      6: 'Comptes de charges',
      7: 'Comptes de produits',
      8: 'Comptes spéciaux'
    };

    return libelles[classe] || `Classe ${classe}`;
  }

  /**
   * Valide les paramètres de période
   * @param {Date} dateDebut - Date de début
   * @param {Date} dateFin - Date de fin
   * @throws {ValidationError} Si les dates sont invalides
   */
  validerPeriode(dateDebut, dateFin) {
    if (!dateDebut || !dateFin) {
      throw new ValidationError('Les dates de début et de fin sont obligatoires');
    }

    if (dateDebut > dateFin) {
      throw new ValidationError('La date de début doit être antérieure à la date de fin');
    }

    const diffJours = (dateFin - dateDebut) / (1000 * 60 * 60 * 24);
    if (diffJours > 366) {
      throw new ValidationError('La période ne peut pas dépasser 366 jours');
    }
  }

  /**
   * Formate un montant pour l'affichage
   * @param {number} montant - Montant à formater
   * @param {string} devise - Code devise (défaut: XOF)
   * @returns {string} Montant formaté
   */
  formaterMontant(montant, devise = 'XOF') {
    if (typeof montant !== 'number' || isNaN(montant)) {
      return '0,00';
    }

    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: devise,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(montant);
  }
}

// Programmer le nettoyage automatique du cache toutes les 10 minutes
setInterval(() => {
  const service = new CalculService();
  service.nettoyerCache();
}, 10 * 60 * 1000);

module.exports = CalculService;
