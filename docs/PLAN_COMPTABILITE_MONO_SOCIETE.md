# Plan Comptabilité - Architecture Mono-Société

## Architecture Clarifiée

**Principe :** 1 instance Lotus Web = 1 société = Sa comptabilité uniquement

### Implications :
- ❌ **Pas de CRUD sociétés** (la société = configuration de l'instance)
- ✅ **Focus sur les fonctionnalités comptables pures**
- ✅ **Configuration société dans settings/admin**
- ✅ **API calls avec société_id fixe**

## Modules à Implémenter

### 1. Configuration Société (Settings)
- **Objectif :** Configurer les infos de la société dans l'admin/settings
- **Stockage :** Variables d'environnement ou modèle de configuration
- **API :** Utiliser l'ID société configuré pour tous les appels

### 2. Dashboard Comptable
- **KPIs financiers** de la société
- **Graphiques** évolution CA, résultat
- **Alertes** financières
- **Résumé** dernières écritures

### 3. Plan Comptable
- **Hiérarchie** des comptes SYSCOHADA
- **Recherche** et filtres par classe
- **Gestion** comptes (CRUD)
- **Import/Export** plan comptable

### 4. Écritures Comptables
- **Saisie** multi-lignes avec validation
- **Liste** avec filtres avancés
- **Validation** et lettrage
- **Templates** d'écritures

### 5. États Comptables
- **Balance** générale
- **Grand livre** par compte
- **Bilan** comptable
- **Compte de résultat**
- **Export** Excel/PDF

### 6. Journaux Comptables
- **Gestion** des journaux
- **Numérotation** automatique
- **Consultation** par journal

## Structure Technique

### Configuration API
```python
# settings/base.py
COMPTABILITE_API_URL = 'http://localhost:3000/api/v1'
COMPTABILITE_API_KEY = 'sk_your_key'
COMPTABILITE_SOCIETE_ID = 'uuid-de-cette-societe'  # ID fixe pour cette instance
```

### Service API adapté
```python
# apps/accounting/services/api_client.py
class ComptabiliteAPIClient:
    def __init__(self):
        self.base_url = settings.COMPTABILITE_API_URL
        self.api_key = settings.COMPTABILITE_API_KEY
        self.societe_id = settings.COMPTABILITE_SOCIETE_ID  # ID fixe
        
    def get_comptes(self):
        # GET /comptes?societeId={self.societe_id}
        
    def create_ecriture(self, data):
        # POST /ecritures avec societeId automatique
        data['societeId'] = self.societe_id
```

### Navigation simplifiée
```html
<!-- Sidebar - Section Comptabilité -->
<div class="accordion-item border-0">
    <h2 class="accordion-header" id="accountingHeading">
        <button class="accordion-button collapsed">
            <i class="bi bi-calculator fs-16 me-2"></i>
            <span>Comptabilité</span>
        </button>
    </h2>
    <div id="accountingCollapse" class="accordion-collapse collapse">
        <div class="accordion-body p-0">
            <ul class="list-unstyled">
                <li><a href="{% url 'accounting:dashboard' %}">
                    <i class="bi bi-speedometer2 fs-16 me-2"></i>Dashboard
                </a></li>
                <li><a href="{% url 'accounting:plan_comptable' %}">
                    <i class="bi bi-list-nested fs-16 me-2"></i>Plan comptable
                </a></li>
                <li><a href="{% url 'accounting:ecritures' %}">
                    <i class="bi bi-journal-text fs-16 me-2"></i>Écritures
                </a></li>
                <li><a href="{% url 'accounting:journaux' %}">
                    <i class="bi bi-book fs-16 me-2"></i>Journaux
                </a></li>
                <li><a href="{% url 'accounting:etats' %}">
                    <i class="bi bi-graph-up fs-16 me-2"></i>États comptables
                </a></li>
                <li><a href="{% url 'accounting:lettrage' %}">
                    <i class="bi bi-link-45deg fs-16 me-2"></i>Lettrage
                </a></li>
            </ul>
        </div>
    </div>
</div>
```

## Chronogramme Adapté (5 jours)

| Jour | Durée | Focus Principal | Livrables |
|------|-------|----------------|-----------|
| **MARDI** | 8h | Configuration + Dashboard | API Client + Dashboard KPIs |
| **MERCREDI** | 8h | Plan Comptable | Hiérarchie + Gestion comptes |
| **JEUDI** | 8h | Écritures Comptables | Saisie + Liste + Validation |
| **VENDREDI** | 8h | États + Journaux | Balance, bilan + Journaux |
| **SAMEDI** | 4h | Lettrage + Tests | Module complet |

## Détail par Jour

### MARDI - Configuration + Dashboard (8h)

**Matin (4h)**
- [ ] Configuration API avec société_id fixe
- [ ] Service API client adapté
- [ ] Structure apps/accounting/ complète
- [ ] Navigation sidebar intégrée

**Après-midi (4h)**
- [ ] Dashboard comptable avec KPIs
- [ ] Graphiques financiers (Chart.js)
- [ ] Widgets résumé (CA, résultat, etc.)
- [ ] Alertes financières

### MERCREDI - Plan Comptable (8h)

**Matin (4h)**
- [ ] Interface hiérarchique des comptes
- [ ] Arbre expand/collapse (JavaScript)
- [ ] Filtres par classe comptable (1-8)
- [ ] Recherche de comptes

**Après-midi (4h)**
- [ ] Formulaire création/modification compte
- [ ] Validation SYSCOHADA
- [ ] Import/Export plan comptable
- [ ] Tests et optimisations

### JEUDI - Écritures Comptables (8h)

**Matin (4h)**
- [ ] Formulaire saisie multi-lignes
- [ ] JavaScript dynamique (ajouter/supprimer lignes)
- [ ] Auto-complétion comptes et tiers
- [ ] Validation équilibrage temps réel

**Après-midi (4h)**
- [ ] Liste écritures avec filtres
- [ ] Détail écriture + actions
- [ ] Validation et modification écritures
- [ ] Templates d'écritures

### VENDREDI - États + Journaux (8h)

**Matin (4h)**
- [ ] Balance générale avec paramètres
- [ ] Grand livre par compte
- [ ] Bilan comptable SYSCOHADA
- [ ] Compte de résultat

**Après-midi (4h)**
- [ ] Gestion journaux comptables
- [ ] Consultation par journal
- [ ] Export Excel/PDF des états
- [ ] Paramètres d'export

### SAMEDI - Lettrage + Finalisation (4h)

**Matin uniquement (4h)**
- [ ] Interface lettrage
- [ ] Lettrage automatique
- [ ] Tests d'intégration complets
- [ ] Corrections et optimisations

## URLs Finales

```python
# apps/accounting/urls.py
app_name = 'accounting'
urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('plan-comptable/', views.plan_comptable, name='plan_comptable'),
    path('comptes/', views.compte_list, name='compte_list'),
    path('comptes/create/', views.compte_create, name='compte_create'),
    path('ecritures/', views.ecriture_list, name='ecriture_list'),
    path('ecritures/create/', views.ecriture_create, name='ecriture_create'),
    path('ecritures/<uuid:pk>/', views.ecriture_detail, name='ecriture_detail'),
    path('journaux/', views.journal_list, name='journal_list'),
    path('etats/balance/', views.balance_generale, name='balance'),
    path('etats/bilan/', views.bilan, name='bilan'),
    path('etats/grand-livre/', views.grand_livre, name='grand_livre'),
    path('lettrage/', views.lettrage, name='lettrage'),
]
```

## Configuration Société

### Option 1 : Variables d'environnement
```bash
# .env
COMPTABILITE_API_URL=http://localhost:3000/api/v1
COMPTABILITE_API_KEY=sk_your_key
COMPTABILITE_SOCIETE_ID=uuid-de-cette-societe
COMPTABILITE_SOCIETE_NOM="Ma Société SARL"
```

### Option 2 : Modèle de configuration
```python
# apps/accounting/models.py
class CompanyConfig(models.Model):
    """Configuration de la société pour cette instance Lotus"""
    api_societe_id = models.UUIDField(unique=True)
    nom = models.CharField(max_length=255)
    api_key = models.CharField(max_length=255)
    api_url = models.URLField()
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Configuration Société"
```

## Avantages de cette Architecture

### ✅ Simplicité
- Pas de gestion multi-sociétés
- Configuration unique par instance
- Focus sur les fonctionnalités comptables

### ✅ Sécurité
- Chaque instance accède uniquement à ses données
- Isolation naturelle des données
- Clés API spécifiques par instance

### ✅ Performance
- Pas de filtrage société côté frontend
- Requêtes API optimisées
- Cache possible par instance

### ✅ Maintenance
- Déploiement indépendant par société
- Configuration simple
- Évolutivité horizontale

## Livrables Finaux

### Module comptabilité intégré :
- ✅ **Dashboard** avec KPIs de la société
- ✅ **Plan comptable** hiérarchique SYSCOHADA
- ✅ **Saisie écritures** multi-lignes avec validation
- ✅ **États comptables** (balance, bilan, grand livre)
- ✅ **Journaux** comptables avec numérotation
- ✅ **Lettrage** manuel et automatique
- ✅ **Export** Excel/PDF des rapports
- ✅ **Navigation** intégrée dans Lotus
- ✅ **Style** cohérent avec l'existant

**Résultat :** Comptabilité complète pour UNE société par instance ! 🎯
