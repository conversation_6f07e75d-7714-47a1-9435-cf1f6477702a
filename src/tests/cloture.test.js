'use strict';

const { models, sequelize } = require('../models');
const ClotureService = require('../services/clotureService');
const { ValidationError } = require('../middleware/errorHandler');

// Mock des modèles et des méthodes
jest.mock('../models', () => {
  const SequelizeMock = require('sequelize-mock');
  const dbMock = new SequelizeMock();

  // Mock du modèle ExerciceComptable
  const ExerciceComptableMock = dbMock.define('ExerciceComptable', {
    id: 'exercice-test-id',
    libelle: 'Exercice 2025',
    dateDebut: '2025-01-01',
    dateFin: '2025-12-31',
    statut: 'OUVERT',
    societeId: 'societe-test-id',
    reportANouveau: 0,
    dateOuverture: new Date(),
    dateCloture: null,
    utilisateurCloture: null,
    commentaireCloture: null
  }, {
    instanceMethods: {
      update: jest.fn().mockResolvedValue(true)
    }
  });

  // Mock du modèle Societe
  const SocieteMock = dbMock.define('Societe', {
    id: 'societe-test-id',
    nom: 'Société Test',
    siret: '12345678901234',
    adresse: 'Adresse test',
    codePostal: '75000',
    ville: 'Paris',
    pays: 'France',
    telephone: '0123456789',
    email: '<EMAIL>'
  });

  // Mock du modèle EcritureComptable
  const EcritureComptableMock = dbMock.define('EcritureComptable', {
    id: 'ecriture-test-id',
    numeroEcriture: 'ECR-2025-001',
    dateEcriture: '2025-01-15',
    journalCode: 'OD',
    libelle: 'Écriture test',
    exerciceId: 'exercice-test-id',
    statut: 'VALIDE',
    societeId: 'societe-test-id'
  }, {
    instanceMethods: {
      getResume: jest.fn().mockResolvedValue({
        id: 'ecriture-test-id',
        numeroEcriture: 'ECR-2025-001',
        dateEcriture: '2025-01-15',
        libelle: 'Écriture test',
        totalDebit: 1000,
        totalCredit: 1000,
        equilibree: true
      })
    }
  });

  // Mock du modèle LigneEcriture
  const LigneEcritureMock = dbMock.define('LigneEcriture', {
    id: 'ligne-test-id',
    ecritureId: 'ecriture-test-id',
    compteNumero: '401000',
    libelle: 'Ligne test',
    montantDebit: 1000,
    montantCredit: 0,
    lettrage: null,
    dateLettrage: null,
    utilisateurLettrage: null
  });

  // Mock du modèle CompteComptable
  const CompteComptableMock = dbMock.define('CompteComptable', {
    numero: '401000',
    libelle: 'Fournisseurs',
    classe: 4,
    nature: 'PASSIF',
    sens: 'CREDIT',
    niveau: 3,
    societeId: 'societe-test-id',
    obligatoireLettrage: true
  });

  // Mock du modèle Journal
  const JournalMock = dbMock.define('Journal', {
    code: 'OD',
    libelle: 'Opérations Diverses',
    type: 'OD',
    societeId: 'societe-test-id'
  });

  // Surcharger les méthodes de recherche
  ExerciceComptableMock.findByPk = jest.fn().mockImplementation((id) => {
    if (id === 'exercice-test-id') {
      const exercice = ExerciceComptableMock.build();
      exercice.societe = SocieteMock.build();
      return Promise.resolve(exercice);
    }
    if (id === 'exercice-cloture-id') {
      const exercice = ExerciceComptableMock.build({
        id: 'exercice-cloture-id',
        statut: 'CLOTURE'
      });
      exercice.societe = SocieteMock.build();
      return Promise.resolve(exercice);
    }
    return Promise.resolve(null);
  });

  EcritureComptableMock.findAll = jest.fn().mockResolvedValue([]);
  EcritureComptableMock.count = jest.fn().mockResolvedValue(0);
  EcritureComptableMock.update = jest.fn().mockResolvedValue([1]);
  EcritureComptableMock.create = jest.fn().mockImplementation(() => {
    const ecriture = EcritureComptableMock.build();
    return Promise.resolve(ecriture);
  });

  LigneEcritureMock.findAll = jest.fn().mockImplementation((options) => {
    if (options && options.attributes && options.attributes[0] && options.attributes[0][0] === dbMock.fn('SUM')) {
      return Promise.resolve([{ resultatExercice: 5000, totalDebit: 10000, totalCredit: 5000 }]);
    }
    return Promise.resolve([]);
  });
  LigneEcritureMock.count = jest.fn().mockResolvedValue(0);
  LigneEcritureMock.create = jest.fn().mockResolvedValue(LigneEcritureMock.build());

  CompteComptableMock.findAll = jest.fn().mockResolvedValue([]);
  CompteComptableMock.count = jest.fn().mockResolvedValue(0);
  CompteComptableMock.findOne = jest.fn().mockImplementation((options) => {
    if (options && options.where && options.where.numero === '401000') {
      return Promise.resolve(CompteComptableMock.build());
    }
    return Promise.resolve(null);
  });

  JournalMock.findOne = jest.fn().mockImplementation((options) => {
    if (options && options.where && options.where.type === 'OD') {
      return Promise.resolve(JournalMock.build());
    }
    return Promise.resolve(null);
  });

  // Mock de la transaction Sequelize
  const transactionMock = {
    commit: jest.fn().mockResolvedValue(true),
    rollback: jest.fn().mockResolvedValue(true)
  };

  return {
    sequelize: {
      transaction: jest.fn().mockResolvedValue(transactionMock),
      literal: jest.fn().mockImplementation((str) => str),
      fn: jest.fn().mockImplementation((fn, col) => [fn, col]),
      col: jest.fn().mockImplementation((col) => col),
      Op: {
        in: Symbol('in'),
        notIn: Symbol('notIn'),
        like: Symbol('like'),
        between: Symbol('between'),
        lte: Symbol('lte'),
        gte: Symbol('gte'),
        gt: Symbol('gt'),
        lt: Symbol('lt'),
        ne: Symbol('ne'),
        or: Symbol('or'),
        and: Symbol('and')
      }
    },
    models: {
      ExerciceComptable: ExerciceComptableMock,
      Societe: SocieteMock,
      EcritureComptable: EcritureComptableMock,
      LigneEcriture: LigneEcritureMock,
      CompteComptable: CompteComptableMock,
      Journal: JournalMock,
      sequelize: {
        transaction: jest.fn().mockResolvedValue(transactionMock),
        literal: jest.fn().mockImplementation((str) => str),
        fn: jest.fn().mockImplementation((fn, col) => [fn, col]),
        col: jest.fn().mockImplementation((col) => col),
        Op: {
          in: Symbol('in'),
          notIn: Symbol('notIn'),
          like: Symbol('like'),
          between: Symbol('between'),
          lte: Symbol('lte'),
          gte: Symbol('gte'),
          gt: Symbol('gt'),
          lt: Symbol('lt'),
          ne: Symbol('ne'),
          or: Symbol('or'),
          and: Symbol('and')
        }
      }
    }
  };
});

// Mock du logger
jest.mock('../config/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

describe('ClotureService', () => {
  let clotureService;

  beforeEach(() => {
    clotureService = new ClotureService(models);
    jest.clearAllMocks();
  });

  describe('verifierCompletudeSaisies', () => {
    it('devrait vérifier la complétude des saisies', async () => {
      const resultat = await clotureService.verifierCompletudeSaisies('exercice-test-id');
      
      expect(resultat).toBeDefined();
      expect(resultat.complet).toBe(true);
      expect(resultat.anomalies).toBeInstanceOf(Array);
      expect(models.ExerciceComptable.findByPk).toHaveBeenCalledWith('exercice-test-id', expect.any(Object));
    });

    it('devrait gérer les exercices non trouvés', async () => {
      models.ExerciceComptable.findByPk.mockResolvedValueOnce(null);
      
      await expect(clotureService.verifierCompletudeSaisies('exercice-inexistant')).rejects.toThrow(ValidationError);
    });
  });

  describe('calculerResultatExercice', () => {
    it('devrait calculer le résultat de l\'exercice', async () => {
      const resultat = await clotureService.calculerResultatExercice('exercice-test-id');
      
      expect(resultat).toBe(5000);
      expect(models.LigneEcriture.findAll).toHaveBeenCalled();
    });
  });

  describe('genererEcrituresCloture', () => {
    it('devrait générer les écritures de clôture', async () => {
      const resultat = await clotureService.genererEcrituresCloture('exercice-test-id', 'user-test-id');
      
      expect(resultat).toBeDefined();
      expect(resultat.success).toBe(true);
      expect(resultat.ecritures).toBeInstanceOf(Array);
      expect(models.EcritureComptable.create).toHaveBeenCalledTimes(2);
    });

    it('devrait rejeter si l\'exercice est déjà clôturé', async () => {
      models.ExerciceComptable.findByPk.mockResolvedValueOnce({
        id: 'exercice-test-id',
        statut: 'CLOTURE',
        societe: { nom: 'Société Test' }
      });
      
      await expect(clotureService.genererEcrituresCloture('exercice-test-id', 'user-test-id')).rejects.toThrow(ValidationError);
    });
  });

  describe('cloturerExercice', () => {
    it('devrait clôturer un exercice', async () => {
      const resultat = await clotureService.cloturerExercice('exercice-test-id', 'user-test-id');
      
      expect(resultat).toBeDefined();
      expect(resultat.success).toBe(true);
      expect(models.ExerciceComptable.findByPk).toHaveBeenCalled();
      expect(models.EcritureComptable.update).toHaveBeenCalled();
    });
  });

  describe('genererANouveaux', () => {
    it('devrait générer les écritures d\'à-nouveaux', async () => {
      models.ExerciceComptable.findByPk
        .mockResolvedValueOnce({ id: 'exercice-cloture-id', statut: 'CLOTURE', societeId: 'societe-test-id' })
        .mockResolvedValueOnce({ 
          id: 'exercice-test-id', 
          statut: 'OUVERT', 
          societeId: 'societe-test-id',
          dateDebut: '2026-01-01',
          societe: { nom: 'Société Test' }
        });
      
      const resultat = await clotureService.genererANouveaux('exercice-cloture-id', 'exercice-test-id', 'user-test-id');
      
      expect(resultat).toBeDefined();
      expect(resultat.success).toBe(true);
      expect(models.EcritureComptable.create).toHaveBeenCalled();
    });

    it('devrait rejeter si l\'exercice source n\'est pas clôturé', async () => {
      models.ExerciceComptable.findByPk
        .mockResolvedValueOnce({ id: 'exercice-test-id', statut: 'OUVERT' });
      
      await expect(clotureService.genererANouveaux('exercice-test-id', 'exercice-nouveau-id', 'user-test-id')).rejects.toThrow(ValidationError);
    });
  });
});