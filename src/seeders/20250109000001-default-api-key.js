/**
 * Seeder pour créer une clé API par défaut
 * API Comptabilité SYSCOHADA
 */

'use strict';

const ApiKeyService = require('../services/apiKeyService');

module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      console.log('🔑 Création de la clé API par défaut...');

      // Créer une clé API par défaut avec tous les droits
      const defaultApiKey = await ApiKeyService.createApiKey({
        name: 'Clé API par défaut',
        permissions: ['admin'],
        createdBy: 'seeder',
        metadata: {
          description: 'Clé API créée automatiquement lors de l\'initialisation',
          environment: process.env.NODE_ENV || 'development'
        }
      });

      console.log('✅ Clé API par défaut créée avec succès!');
      console.log(`   Nom: ${defaultApiKey.name}`);
      console.log(`   Préfixe: ${defaultApiKey.prefix}`);
      console.log(`   Permissions: ${defaultApiKey.permissions.join(', ')}`);
      console.log('');
      console.log('🔐 Clé API (à conserver précieusement):');
      console.log(`   ${defaultApiKey.key}`);
      console.log('');
      console.log('⚠️  IMPORTANT: Notez cette clé, elle ne sera plus jamais affichée!');
      console.log('');

    } catch (error) {
      console.error('❌ Erreur lors de la création de la clé API par défaut:', error.message);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    // Supprimer toutes les clés API créées par le seeder
    await queryInterface.bulkDelete('api_keys', {
      createdBy: 'seeder'
    });
  }
};