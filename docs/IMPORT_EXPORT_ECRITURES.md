# 📁 Module Import/Export Écritures Comptables

## 📋 Vue d'ensemble

Le module d'import/export d'écritures comptables permet de gérer l'importation et l'exportation des données comptables dans différents formats (Excel, CSV, FEC). Il respecte les normes SYSCOHADA et offre une validation robuste des données.

## 🎯 Fonctionnalités principales

### Import
- ✅ **Import Excel** (.xlsx, .xls) avec validation complète
- ✅ **Import CSV** avec séparateurs personnalisables
- ✅ **Validation d'équilibre** automatique des écritures
- ✅ **Mode validation seule** sans import effectif
- ✅ **Gestion des doublons** avec option de remplacement
- ✅ **Traçabilité des erreurs** ligne par ligne

### Export
- ✅ **Export Excel** avec feuilles multiples (écritures + lignes + statistiques)
- ✅ **Export CSV** avec options de formatage
- ✅ **Export FEC** (Fichier des Écritures Comptables) conforme aux normes
- ✅ **Filtres avancés** par société, exercice, journal, période, statut
- ✅ **Options de formatage** des dates et séparateurs

## 🏗️ Architecture

### Services
- **ImportExportService** : Logique métier principale
- **Middleware upload** : Gestion des fichiers avec Multer
- **Contrôleur** : Endpoints API REST
- **Routes** : Configuration des endpoints avec validation

### Modèles impliqués
- `EcritureComptable` : Écritures principales
- `LigneEcriture` : Lignes d'écriture détaillées
- `CompteComptable` : Validation des comptes
- `Journal` : Validation des journaux
- `ExerciceComptable` : Validation des exercices

## 📊 Formats de données

### Structure d'import (Excel/CSV)

#### Colonnes obligatoires
| Colonne | Description | Format | Exemple |
|---------|-------------|--------|---------|
| `Numéro Écriture` | Identifiant unique | Texte (max 20 car.) | `VT001` |
| `Date Écriture` | Date de l'écriture | YYYY-MM-DD | `2024-01-15` |
| `Journal` | Code journal | Texte (max 10 car.) | `VT` |
| `Libellé Écriture` | Description | Texte | `Vente marchandises` |
| `Compte` | Numéro de compte | Texte | `411000` |
| `Libellé Ligne` | Description ligne | Texte | `Client ABC` |
| `Débit` | Montant débit | Décimal | `1200.00` |
| `Crédit` | Montant crédit | Décimal | `0.00` |

#### Colonnes optionnelles
| Colonne | Description | Exemple |
|---------|-------------|---------|
| `Référence` | Référence écriture | `FACT001` |
| `Pièce Justificative` | Document joint | `FACT001.pdf` |
| `Référence Ligne` | Référence ligne | `L001` |

### Exemple de fichier d'import

```csv
Numéro Écriture;Date Écriture;Journal;Libellé Écriture;Compte;Libellé Ligne;Débit;Crédit;Référence
VT001;2024-01-15;VT;Vente marchandises client ABC;411000;Client ABC;1200.00;0.00;FACT001
VT001;2024-01-15;VT;Vente marchandises client ABC;701000;Ventes de marchandises;0.00;1000.00;FACT001
VT001;2024-01-15;VT;Vente marchandises client ABC;445700;TVA collectée;0.00;200.00;FACT001
```

## 🔧 API Endpoints

### Import

#### Import Excel
```http
POST /api/v1/import-export/excel
Content-Type: multipart/form-data
Authorization: Bearer {token}

Form Data:
- fichier: [fichier Excel]
- societeId: {uuid}
- exerciceId: {uuid} (optionnel)
- remplacerExistants: true/false (défaut: false)
- validerUniquement: true/false (défaut: false)
- validerEcritures: true/false (défaut: true)
```

#### Import CSV
```http
POST /api/v1/import-export/csv
Content-Type: multipart/form-data
Authorization: Bearer {token}

Form Data:
- fichier: [fichier CSV]
- societeId: {uuid}
- exerciceId: {uuid} (optionnel)
- separateur: ; (défaut: ;)
- remplacerExistants: true/false
- validerUniquement: true/false
- validerEcritures: true/false
```

### Export

#### Export Excel
```http
GET /api/v1/import-export/excel?societeId={uuid}&exerciceId={uuid}&includeDetails=true
Authorization: Bearer {token}

Paramètres optionnels:
- societeId: Filtrer par société
- exerciceId: Filtrer par exercice
- journalCode: Filtrer par journal
- statut: BROUILLARD|VALIDEE|CLOTUREE
- dateDebut: Date début (ISO 8601)
- dateFin: Date fin (ISO 8601)
- includeDetails: Inclure lignes détaillées
- includeStatistiques: Inclure statistiques
- formatDate: DD/MM/YYYY|YYYY-MM-DD|YYYYMMDD
```

#### Export CSV
```http
GET /api/v1/import-export/csv?societeId={uuid}&separateur=;
Authorization: Bearer {token}

Paramètres similaires à Excel + :
- separateur: Caractère séparateur
```

#### Export FEC
```http
GET /api/v1/import-export/fec/{exerciceId}
Authorization: Bearer {token}

Paramètres optionnels:
- separateur: | (défaut: |)
- encodage: utf8|latin1|ascii
- includeEnTetes: true/false
```

### Utilitaires

#### Formats supportés
```http
GET /api/v1/import-export/formats
Authorization: Bearer {token}
```

#### Templates d'import
```http
GET /api/v1/import-export/template/{format}
Authorization: Bearer {token}

Formats: excel|csv
```

## 📝 Exemples d'utilisation

### Import d'écritures Excel

```javascript
const formData = new FormData();
formData.append('fichier', fileInput.files[0]);
formData.append('societeId', 'uuid-societe');
formData.append('exerciceId', 'uuid-exercice');
formData.append('validerEcritures', 'true');

const response = await fetch('/api/v1/import-export/excel', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

const result = await response.json();
console.log(`${result.data.ecrituresCreees} écritures importées`);
```

### Export Excel avec filtres

```javascript
const params = new URLSearchParams({
  societeId: 'uuid-societe',
  exerciceId: 'uuid-exercice',
  dateDebut: '2024-01-01',
  dateFin: '2024-12-31',
  includeDetails: 'true',
  formatDate: 'DD/MM/YYYY'
});

const response = await fetch(`/api/v1/import-export/excel?${params}`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// Télécharger le fichier
const blob = await response.blob();
const url = window.URL.createObjectURL(blob);
const a = document.createElement('a');
a.href = url;
a.download = 'export-ecritures.xlsx';
a.click();
```

### Validation avant import

```javascript
const formData = new FormData();
formData.append('fichier', fileInput.files[0]);
formData.append('societeId', 'uuid-societe');
formData.append('validerUniquement', 'true');

const response = await fetch('/api/v1/import-export/excel', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

const result = await response.json();
if (result.data.validation.valide) {
  console.log('Fichier valide, prêt pour import');
} else {
  console.log('Erreurs:', result.data.validation.erreurs);
}
```

## ⚠️ Règles de validation

### Validation des écritures
1. **Équilibre obligatoire** : Total débit = Total crédit pour chaque écriture
2. **Comptes existants** : Tous les comptes doivent exister dans le plan comptable
3. **Journaux valides** : Les codes journaux doivent être configurés
4. **Dates cohérentes** : Les dates doivent être dans l'exercice comptable
5. **Montants positifs** : Débit et crédit doivent être ≥ 0
6. **Exclusivité** : Une ligne ne peut avoir à la fois débit ET crédit

### Validation des fichiers
1. **Formats acceptés** : .xlsx, .xls, .csv uniquement
2. **Taille limite** : 50 MB maximum
3. **Colonnes obligatoires** : Toutes les colonnes requises doivent être présentes
4. **Encodage** : UTF-8 recommandé pour les CSV

## 🔍 Gestion des erreurs

### Types d'erreurs d'import
- **Erreurs de format** : Fichier corrompu, colonnes manquantes
- **Erreurs de validation** : Comptes inexistants, écritures déséquilibrées
- **Erreurs métier** : Exercice fermé, journal inactif
- **Erreurs techniques** : Problème de base de données, fichier trop volumineux

### Format des réponses d'erreur

```json
{
  "success": false,
  "message": "Erreur lors de l'import",
  "data": {
    "validation": {
      "valide": false,
      "erreurs": [
        {
          "ligne": 3,
          "numeroEcriture": "VT001",
          "erreurs": [
            "Compte comptable 999999 introuvable",
            "Écriture non équilibrée: Débit 1000 ≠ Crédit 1200"
          ]
        }
      ]
    }
  }
}
```

## 🚀 Optimisations et performances

### Import
- **Transactions** : Utilisation de transactions pour la cohérence
- **Validation en lot** : Validation de toutes les données avant import
- **Index optimisés** : Index sur les champs de recherche fréquents
- **Nettoyage automatique** : Suppression des fichiers temporaires

### Export
- **Streaming** : Export en streaming pour les gros volumes
- **Cache** : Mise en cache des requêtes fréquentes
- **Compression** : Compression des fichiers volumineux
- **Index composites** : Optimisation des requêtes de filtrage

## 📈 Monitoring et logs

### Événements loggés
- Import/export réussis avec statistiques
- Erreurs de validation détaillées
- Performances (temps d'exécution, nombre de lignes)
- Utilisation des fonctionnalités

### Métriques importantes
- Nombre d'imports/exports par jour
- Taux de succès des imports
- Taille moyenne des fichiers traités
- Temps moyen de traitement

## 🔐 Sécurité

### Authentification
- **Token JWT** obligatoire pour tous les endpoints
- **Validation des permissions** par société
- **Audit trail** des opérations d'import/export

### Validation des fichiers
- **Type MIME** vérifié
- **Extension** validée
- **Taille** limitée
- **Contenu** scanné pour détecter les anomalies

### Protection des données
- **Fichiers temporaires** supprimés automatiquement
- **Données sensibles** non loggées
- **Accès restreint** aux fichiers d'export

## 🛠️ Guide technique pour développeurs

### Structure du code

```
src/
├── services/
│   └── importExportService.js     # Logique métier principale
├── controllers/
│   └── importExportController.js  # Endpoints API
├── middleware/
│   └── upload.js                  # Gestion upload fichiers
├── routes/
│   └── importExport.js           # Configuration routes
└── tests/
    └── importExport.test.js      # Tests unitaires
```

### Méthodes principales du service

#### Import
```javascript
// Import Excel
await importExportService.importerEcrituresExcel(filePath, options);

// Import CSV
await importExportService.importerEcrituresCSV(filePath, options);

// Validation seule
await importExportService.validerDonneesEcritures(donnees, societeId, exerciceId);
```

#### Export
```javascript
// Export Excel
await importExportService.exporterEcrituresExcel(filtres, options);

// Export CSV
await importExportService.exporterEcrituresCSV(filtres, options);

// Export FEC
await importExportService.exporterFEC(exerciceId, options);
```

### Configuration Multer

```javascript
const upload = multer({
  storage: multer.diskStorage({
    destination: 'temp/',
    filename: (req, file, cb) => {
      const uniqueName = `${Date.now()}-${Math.round(Math.random() * 1E9)}`;
      cb(null, `${path.basename(file.originalname, path.extname(file.originalname))}-${uniqueName}${path.extname(file.originalname)}`);
    }
  }),
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'];
    cb(null, allowedTypes.includes(file.mimetype));
  },
  limits: { fileSize: 50 * 1024 * 1024 } // 50MB
});
```

### Exemple d'extension

Pour ajouter un nouveau format d'export :

```javascript
// Dans ImportExportService
async exporterEcrituresJSON(filtres = {}, options = {}) {
  try {
    const ecritures = await this.obtenirEcrituresPourExport(filtres);
    const donneesExport = await this.preparerDonneesExportEcritures(ecritures, options);

    const nomFichier = `export_ecritures_${Date.now()}.json`;
    const cheminFichier = path.join(process.cwd(), 'temp', nomFichier);

    fs.writeFileSync(cheminFichier, JSON.stringify(donneesExport, null, 2));

    return {
      success: true,
      cheminFichier,
      nomFichier,
      format: 'json'
    };
  } catch (error) {
    logger.error('Erreur export JSON', { error: error.message });
    throw error;
  }
}
```

## 📚 Ressources complémentaires

### Documentation liée
- [Plan de développement backend](./PLAN_DEVELOPPEMENT_BACKEND.md)
- [Lettrage et rapprochements](./LETTRAGE_RAPPROCHEMENTS.md)
- [Recherche avancée](./RECHERCHE_AVANCEE.md)

### Standards SYSCOHADA
- Format FEC conforme aux normes comptables
- Plan comptable OHADA respecté
- Validation selon les règles comptables

### Dépendances
- `xlsx` : Lecture/écriture fichiers Excel
- `csv-parser` : Parsing fichiers CSV
- `multer` : Upload de fichiers
- `sequelize` : ORM base de données
