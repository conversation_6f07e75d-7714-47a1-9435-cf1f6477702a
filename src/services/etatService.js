'use strict';

const XLSX = require('xlsx');
const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');
const { logger } = require('../config/logger');
const {
  ValidationError,
  NotFoundError
} = require('../middleware/errorHandler');

/**
 * Service pour la génération des états comptables conformes aux normes SYSCOHADA
 */
class EtatService {
  constructor(models) {
    this.models = models || require('../models');
    const CalculService = require('./calculService');
    const ImportExportService = require('./importExportService');
    this.calculService = new CalculService(this.models);
    this.importExportService = new ImportExportService(this.models);
  }

  /**
   * Génère le grand livre pour un compte ou tous les comptes
   * @param {string} compteNumero - Numéro du compte (optionnel, tous les comptes si null)
   * @param {Date} dateDebut - Date de début
   * @param {Date} dateFin - Date de fin
   * @param {Object} options - Options de génération
   * @returns {Promise<Object>} Informations sur le grand livre généré
   */
  async genererGrandLivre(
    compteNumero = null,
    dateDebut,
    dateFin,
    options = {}
  ) {
    try {
      const {
        societeId,
        exerciceId,
        format = 'excel', // 'excel' ou 'pdf'
        includeNonValidees = false,
        detailLettrage = false,
        triParDate = true
      } = options;

      // Valider les paramètres
      this.calculService.validerPeriode(dateDebut, dateFin);

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Récupérer l'exercice si fourni
      let exercice = null;
      if (exerciceId) {
        exercice = await this.models.ExerciceComptable.findByPk(exerciceId);
        if (!exercice) {
          throw new NotFoundError('Exercice comptable');
        }
      }

      // Construire les conditions WHERE pour les écritures
      const whereEcriture = {
        dateEcriture: {
          [this.models.sequelize.Op.between]: [dateDebut, dateFin]
        },
        societeId
      };

      if (exerciceId) {
        whereEcriture.exerciceId = exerciceId;
      }

      if (!includeNonValidees) {
        whereEcriture.statut = 'VALIDEE';
      }

      // Construire les conditions WHERE pour les comptes
      const whereCompte = { societeId };
      if (compteNumero) {
        whereCompte.numero = compteNumero;
      }

      // Récupérer les comptes concernés
      const comptes = await this.models.CompteComptable.findAll({
        where: whereCompte,
        order: [['numero', 'ASC']]
      });

      if (comptes.length === 0) {
        throw new ValidationError(
          'Aucun compte trouvé avec les critères spécifiés'
        );
      }

      // Récupérer les écritures pour chaque compte
      const donneesGrandLivre = [];
      const comptesTraites = [];

      for (const compte of comptes) {
        // Récupérer les lignes d'écritures pour ce compte
        const lignes = await this.models.LigneEcriture.findAll({
          where: { compteNumero: compte.numero },
          include: [
            {
              model: this.models.EcritureComptable,
              as: 'ecriture',
              where: whereEcriture,
              include: [
                {
                  model: this.models.Journal,
                  as: 'journal'
                }
              ]
            }
          ],
          order: triParDate
            ? [
                [
                  { model: this.models.EcritureComptable, as: 'ecriture' },
                  'dateEcriture',
                  'ASC'
                ],
                [
                  { model: this.models.EcritureComptable, as: 'ecriture' },
                  'numeroEcriture',
                  'ASC'
                ]
              ]
            : [
                [
                  { model: this.models.EcritureComptable, as: 'ecriture' },
                  'journalCode',
                  'ASC'
                ],
                [
                  { model: this.models.EcritureComptable, as: 'ecriture' },
                  'dateEcriture',
                  'ASC'
                ]
              ]
        });

        if (lignes.length === 0) {
          continue; // Passer au compte suivant s'il n'y a pas d'écritures
        }

        // Calculer le solde initial (à la date de début - 1 jour)
        const dateDebutMoinsUnJour = new Date(dateDebut);
        dateDebutMoinsUnJour.setDate(dateDebutMoinsUnJour.getDate() - 1);

        const soldeInitial = await this.calculService.calculerSoldeCompte(
          compte.numero,
          null,
          dateDebutMoinsUnJour,
          { societeId, exerciceId, includeNonValidees }
        );

        // Préparer les données du compte
        const lignesCompte = [];
        let soldeProgressif =
          soldeInitial.solde * (soldeInitial.sensActuel === 'DEBIT' ? 1 : -1);

        // Ajouter la ligne de solde initial
        lignesCompte.push({
          date: dateDebut,
          journal: '',
          numeroEcriture: '',
          libelle: 'SOLDE INITIAL',
          reference: '',
          debit: soldeInitial.sensActuel === 'DEBIT' ? soldeInitial.solde : 0,
          credit: soldeInitial.sensActuel === 'CREDIT' ? soldeInitial.solde : 0,
          soldeProgressif: Math.abs(soldeProgressif),
          sensProgressif: soldeProgressif >= 0 ? 'DEBIT' : 'CREDIT',
          lettrage: '',
          dateLettrage: ''
        });

        // Ajouter les lignes d'écritures
        for (const ligne of lignes) {
          const debit = parseFloat(ligne.debit || 0);
          const credit = parseFloat(ligne.credit || 0);
          soldeProgressif += debit - credit;

          lignesCompte.push({
            date: ligne.ecriture.dateEcriture,
            journal: ligne.ecriture.journalCode,
            journalLibelle: ligne.ecriture.journal
              ? ligne.ecriture.journal.libelle
              : '',
            numeroEcriture: ligne.ecriture.numeroEcriture,
            libelle: ligne.libelle,
            reference: ligne.reference || ligne.ecriture.reference || '',
            pieceJustificative: ligne.ecriture.pieceJustificative || '',
            debit,
            credit,
            soldeProgressif: Math.abs(soldeProgressif),
            sensProgressif: soldeProgressif >= 0 ? 'DEBIT' : 'CREDIT',
            lettrage: ligne.lettrage || '',
            dateLettrage: ligne.dateLettrage || ''
          });
        }

        // Calculer les totaux
        const totauxCompte = {
          totalDebit: lignesCompte.reduce((sum, l) => sum + (l.debit || 0), 0),
          totalCredit: lignesCompte.reduce(
            (sum, l) => sum + (l.credit || 0),
            0
          ),
          soldeDebiteur: soldeProgressif > 0 ? Math.abs(soldeProgressif) : 0,
          soldeCrediteur: soldeProgressif < 0 ? Math.abs(soldeProgressif) : 0
        };

        // Ajouter les données du compte au grand livre
        donneesGrandLivre.push({
          compte: {
            numero: compte.numero,
            libelle: compte.libelle,
            sensNaturel: compte.sens
          },
          lignes: lignesCompte,
          totaux: totauxCompte
        });

        comptesTraites.push(compte.numero);
      }

      // Générer le fichier selon le format demandé
      const nomFichier = `grand_livre_${compteNumero || 'tous'}_${Date.now()}.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
      const cheminFichier = path.join(process.cwd(), 'temp', nomFichier);

      // Créer le dossier temp s'il n'existe pas
      const dossierTemp = path.dirname(cheminFichier);
      if (!fs.existsSync(dossierTemp)) {
        fs.mkdirSync(dossierTemp, { recursive: true });
      }

      if (format === 'excel') {
        await this.genererGrandLivreExcel(donneesGrandLivre, cheminFichier, {
          societe,
          exercice,
          periode: { dateDebut, dateFin }
        });
      } else {
        await this.genererGrandLivrePDF(donneesGrandLivre, cheminFichier, {
          societe,
          exercice,
          periode: { dateDebut, dateFin }
        });
      }

      logger.info('Grand livre généré', {
        compteNumero: compteNumero || 'tous',
        format,
        nombreComptes: comptesTraites.length,
        cheminFichier
      });

      return {
        success: true,
        cheminFichier,
        nomFichier: path.basename(cheminFichier),
        format,
        nombreComptes: comptesTraites.length,
        comptesTraites,
        tailleFichier: fs.statSync(cheminFichier).size,
        periode: { dateDebut, dateFin }
      };
    } catch (error) {
      logger.error('Erreur lors de la génération du grand livre', {
        compteNumero,
        dateDebut,
        dateFin,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Génère le journal comptable
   * @param {string} journalCode - Code du journal (optionnel, tous les journaux si null)
   * @param {Date} dateDebut - Date de début
   * @param {Date} dateFin - Date de fin
   * @param {Object} options - Options de génération
   * @returns {Promise<Object>} Informations sur le journal généré
   */
  async genererJournal(journalCode = null, dateDebut, dateFin, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        format = 'excel', // 'excel' ou 'pdf'
        includeNonValidees = false,
        groupeParJour = false
      } = options;

      // Valider les paramètres
      this.calculService.validerPeriode(dateDebut, dateFin);

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Récupérer l'exercice si fourni
      let exercice = null;
      if (exerciceId) {
        exercice = await this.models.ExerciceComptable.findByPk(exerciceId);
        if (!exercice) {
          throw new NotFoundError('Exercice comptable');
        }
      }

      // Construire les conditions WHERE pour les écritures
      const whereEcriture = {
        dateEcriture: {
          [this.models.sequelize.Op.between]: [dateDebut, dateFin]
        },
        societeId
      };

      if (exerciceId) {
        whereEcriture.exerciceId = exerciceId;
      }

      if (!includeNonValidees) {
        whereEcriture.statut = 'VALIDEE';
      }

      if (journalCode) {
        whereEcriture.journalCode = journalCode;
      }

      // Récupérer les journaux concernés
      const journaux = journalCode
        ? [
            await this.models.Journal.findOne({
              where: { code: journalCode, societeId }
            })
          ]
        : await this.models.Journal.findAll({
            where: { societeId },
            order: [['code', 'ASC']]
          });

      if (!journaux[0]) {
        throw new NotFoundError('Journal');
      }

      // Récupérer les écritures pour chaque journal
      const donneesJournaux = [];
      const journauxTraites = [];

      for (const journal of journaux) {
        if (!journal) continue;

        const whereJournal = { ...whereEcriture, journalCode: journal.code };

        // Récupérer les écritures du journal
        const ecritures = await this.models.EcritureComptable.findAll({
          where: whereJournal,
          include: [
            {
              model: this.models.LigneEcriture,
              as: 'lignes',
              include: [
                {
                  model: this.models.CompteComptable,
                  as: 'compte'
                }
              ]
            }
          ],
          order: [
            ['dateEcriture', 'ASC'],
            ['numeroEcriture', 'ASC'],
            [{ model: this.models.LigneEcriture, as: 'lignes' }, 'id', 'ASC']
          ]
        });

        if (ecritures.length === 0) {
          continue; // Passer au journal suivant s'il n'y a pas d'écritures
        }

        // Préparer les données du journal
        const lignesJournal = [];
        let totalDebit = 0;
        let totalCredit = 0;

        // Grouper par jour si demandé
        if (groupeParJour) {
          const ecrituresParJour = {};

          // Regrouper les écritures par jour
          for (const ecriture of ecritures) {
            const dateKey = ecriture.dateEcriture.toISOString().split('T')[0];
            if (!ecrituresParJour[dateKey]) {
              ecrituresParJour[dateKey] = [];
            }
            ecrituresParJour[dateKey].push(ecriture);
          }

          // Traiter chaque jour
          for (const [dateKey, ecrituresJour] of Object.entries(
            ecrituresParJour
          )) {
            const date = new Date(dateKey);
            let totalDebitJour = 0;
            let totalCreditJour = 0;

            // Ajouter l'en-tête du jour
            lignesJournal.push({
              type: 'entete-jour',
              date,
              libelle: `JOURNÉE DU ${date.toLocaleDateString('fr-FR')}`,
              debit: 0,
              credit: 0
            });

            // Ajouter les écritures du jour
            for (const ecriture of ecrituresJour) {
              // Ajouter l'en-tête de l'écriture
              lignesJournal.push({
                type: 'entete-ecriture',
                date: ecriture.dateEcriture,
                numeroEcriture: ecriture.numeroEcriture,
                libelle: ecriture.libelle,
                reference: ecriture.reference || '',
                pieceJustificative: ecriture.pieceJustificative || ''
              });

              // Ajouter les lignes de l'écriture
              for (const ligne of ecriture.lignes) {
                const debit = parseFloat(ligne.debit || 0);
                const credit = parseFloat(ligne.credit || 0);

                lignesJournal.push({
                  type: 'ligne',
                  compteNumero: ligne.compteNumero,
                  compteLibelle: ligne.compte ? ligne.compte.libelle : '',
                  libelle: ligne.libelle,
                  debit,
                  credit
                });

                totalDebitJour += debit;
                totalCreditJour += credit;
                totalDebit += debit;
                totalCredit += credit;
              }
            }

            // Ajouter le total du jour
            lignesJournal.push({
              type: 'total-jour',
              libelle: `TOTAL JOURNÉE DU ${date.toLocaleDateString('fr-FR')}`,
              debit: totalDebitJour,
              credit: totalCreditJour
            });
          }
        } else {
          // Traitement standard (sans groupement par jour)
          for (const ecriture of ecritures) {
            // Ajouter l'en-tête de l'écriture
            lignesJournal.push({
              type: 'entete-ecriture',
              date: ecriture.dateEcriture,
              numeroEcriture: ecriture.numeroEcriture,
              libelle: ecriture.libelle,
              reference: ecriture.reference || '',
              pieceJustificative: ecriture.pieceJustificative || ''
            });

            // Ajouter les lignes de l'écriture
            for (const ligne of ecriture.lignes) {
              const debit = parseFloat(ligne.debit || 0);
              const credit = parseFloat(ligne.credit || 0);

              lignesJournal.push({
                type: 'ligne',
                compteNumero: ligne.compteNumero,
                compteLibelle: ligne.compte ? ligne.compte.libelle : '',
                libelle: ligne.libelle,
                debit,
                credit
              });

              totalDebit += debit;
              totalCredit += credit;
            }
          }
        }

        // Ajouter les données du journal
        donneesJournaux.push({
          journal: {
            code: journal.code,
            libelle: journal.libelle,
            type: journal.type
          },
          lignes: lignesJournal,
          totaux: {
            totalDebit,
            totalCredit,
            equilibre: Math.abs(totalDebit - totalCredit) < 0.01
          }
        });

        journauxTraites.push(journal.code);
      }

      // Générer le fichier selon le format demandé
      const nomFichier = `journal_${journalCode || 'tous'}_${Date.now()}.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
      const cheminFichier = path.join(process.cwd(), 'temp', nomFichier);

      // Créer le dossier temp s'il n'existe pas
      const dossierTemp = path.dirname(cheminFichier);
      if (!fs.existsSync(dossierTemp)) {
        fs.mkdirSync(dossierTemp, { recursive: true });
      }

      if (format === 'excel') {
        await this.genererJournalExcel(donneesJournaux, cheminFichier, {
          societe,
          exercice,
          periode: { dateDebut, dateFin }
        });
      } else {
        await this.genererJournalPDF(donneesJournaux, cheminFichier, {
          societe,
          exercice,
          periode: { dateDebut, dateFin }
        });
      }

      logger.info('Journal généré', {
        journalCode: journalCode || 'tous',
        format,
        nombreJournaux: journauxTraites.length,
        cheminFichier
      });

      return {
        success: true,
        cheminFichier,
        nomFichier: path.basename(cheminFichier),
        format,
        nombreJournaux: journauxTraites.length,
        journauxTraites,
        tailleFichier: fs.statSync(cheminFichier).size,
        periode: { dateDebut, dateFin }
      };
    } catch (error) {
      logger.error('Erreur lors de la génération du journal', {
        journalCode,
        dateDebut,
        dateFin,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Génère la balance générale
   * @param {Date} dateDebut - Date de début
   * @param {Date} dateFin - Date de fin
   * @param {Object} options - Options de génération
   * @returns {Promise<Object>} Informations sur la balance générée
   */
  async genererBalanceGenerale(dateDebut, dateFin, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        format = 'excel', // 'excel' ou 'pdf'
        niveauDetail = 'TOUS', // TOUS, DETAIL, COLLECTIF
        classeComptes = null, // Filtrer par classe (1,2,3,4,5,6,7,8)
        seulementAvecMouvement = false,
        includeNonValidees = false
      } = options;

      // Valider les paramètres
      this.calculService.validerPeriode(dateDebut, dateFin);

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Récupérer l'exercice si fourni
      let exercice = null;
      if (exerciceId) {
        exercice = await this.models.ExerciceComptable.findByPk(exerciceId);
        if (!exercice) {
          throw new NotFoundError('Exercice comptable');
        }
      }

      // Utiliser le service de calcul pour obtenir la balance
      const balance = await this.calculService.calculerBalanceGenerale(
        dateDebut,
        dateFin,
        {
          societeId,
          exerciceId,
          niveauDetail,
          classeComptes,
          seulementAvecMouvement,
          includeNonValidees
        }
      );

      // Générer le fichier selon le format demandé
      const nomFichier = `balance_generale_${Date.now()}.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
      const cheminFichier = path.join(process.cwd(), 'temp', nomFichier);

      // Créer le dossier temp s'il n'existe pas
      const dossierTemp = path.dirname(cheminFichier);
      if (!fs.existsSync(dossierTemp)) {
        fs.mkdirSync(dossierTemp, { recursive: true });
      }

      if (format === 'excel') {
        await this.genererBalanceExcel(balance, cheminFichier, {
          societe,
          exercice,
          periode: { dateDebut, dateFin }
        });
      } else {
        await this.genererBalancePDF(balance, cheminFichier, {
          societe,
          exercice,
          periode: { dateDebut, dateFin }
        });
      }

      logger.info('Balance générale générée', {
        format,
        nombreComptes: balance.lignesBalance.length,
        cheminFichier
      });

      return {
        success: true,
        cheminFichier,
        nomFichier: path.basename(cheminFichier),
        format,
        nombreComptes: balance.lignesBalance.length,
        tailleFichier: fs.statSync(cheminFichier).size,
        periode: { dateDebut, dateFin }
      };
    } catch (error) {
      logger.error('Erreur lors de la génération de la balance générale', {
        dateDebut,
        dateFin,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Génère la balance âgée (balance par antériorité)
   * @param {Date} dateReference - Date de référence
   * @param {Object} options - Options de génération
   * @returns {Promise<Object>} Informations sur la balance âgée générée
   */
  async genererBalanceAgee(dateReference, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        format = 'excel', // 'excel' ou 'pdf'
        tranches = [30, 60, 90, 180], // Tranches d'antériorité en jours
        compteDebut = '4', // Comptes clients/fournisseurs par défaut
        compteFin = '4',
        seulementAvecSolde = true
      } = options;

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Récupérer l'exercice si fourni
      let exercice = null;
      if (exerciceId) {
        exercice = await this.models.ExerciceComptable.findByPk(exerciceId);
        if (!exercice) {
          throw new NotFoundError('Exercice comptable');
        }
      }

      // Récupérer les comptes concernés
      const comptes = await this.models.CompteComptable.findAll({
        where: {
          societeId,
          numero: {
            [this.models.sequelize.Op.between]: [compteDebut, compteFin + 'Z']
          },
          nature: 'DETAIL'
        },
        order: [['numero', 'ASC']]
      });

      if (comptes.length === 0) {
        throw new ValidationError(
          'Aucun compte trouvé avec les critères spécifiés'
        );
      }

      // Préparer les dates pour chaque tranche
      const dates = [];
      for (const tranche of tranches) {
        const date = new Date(dateReference);
        date.setDate(date.getDate() - tranche);
        dates.push({
          tranche,
          date
        });
      }
      dates.sort((a, b) => a.tranche - b.tranche);

      // Calculer les soldes pour chaque compte et chaque tranche
      const lignesBalance = [];
      let totalGeneral = 0;
      let totalParTranche = Array(tranches.length + 1).fill(0);

      for (const compte of comptes) {
        // Calculer le solde à la date de référence
        const soldeReference = await this.calculService.calculerSoldeCompte(
          compte.numero,
          null,
          dateReference,
          { societeId, exerciceId }
        );

        // Si le compte n'a pas de solde et qu'on filtre, passer au suivant
        if (seulementAvecSolde && soldeReference.solde === 0) {
          continue;
        }

        // Calculer les soldes pour chaque tranche
        const soldesParTranche = [];
        let soldeRestant =
          soldeReference.solde *
          (soldeReference.sensActuel === 'DEBIT' ? 1 : -1);

        for (let i = 0; i < dates.length; i++) {
          const dateDebut = i > 0 ? dates[i - 1].date : null;
          const dateFin = dates[i].date;

          // Calculer le solde pour cette tranche
          const solde = await this.calculService.calculerSoldeCompte(
            compte.numero,
            dateDebut,
            dateFin,
            { societeId, exerciceId }
          );

          const montantTranche =
            solde.solde * (solde.sensActuel === 'DEBIT' ? 1 : -1);
          soldesParTranche.push(Math.abs(montantTranche));
          soldeRestant -= montantTranche;

          // Mettre à jour les totaux par tranche
          totalParTranche[i] += Math.abs(montantTranche);
        }

        // Ajouter le solde restant (plus ancien que la dernière tranche)
        soldesParTranche.push(Math.abs(soldeRestant));
        totalParTranche[tranches.length] += Math.abs(soldeRestant);

        // Ajouter la ligne à la balance
        lignesBalance.push({
          compteNumero: compte.numero,
          libelle: compte.libelle,
          soldeTotal: soldeReference.solde,
          sensActuel: soldeReference.sensActuel,
          soldesParTranche
        });

        totalGeneral += soldeReference.solde;
      }

      // Générer le fichier selon le format demandé
      const nomFichier = `balance_agee_${Date.now()}.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
      const cheminFichier = path.join(process.cwd(), 'temp', nomFichier);

      // Créer le dossier temp s'il n'existe pas
      const dossierTemp = path.dirname(cheminFichier);
      if (!fs.existsSync(dossierTemp)) {
        fs.mkdirSync(dossierTemp, { recursive: true });
      }

      const donneesBalance = {
        dateReference,
        tranches,
        lignesBalance,
        totalGeneral,
        totalParTranche
      };

      if (format === 'excel') {
        await this.genererBalanceAgeeExcel(donneesBalance, cheminFichier, {
          societe,
          exercice
        });
      } else {
        await this.genererBalanceAgeePDF(donneesBalance, cheminFichier, {
          societe,
          exercice
        });
      }

      logger.info('Balance âgée générée', {
        format,
        nombreComptes: lignesBalance.length,
        cheminFichier
      });

      return {
        success: true,
        cheminFichier,
        nomFichier: path.basename(cheminFichier),
        format,
        nombreComptes: lignesBalance.length,
        tailleFichier: fs.statSync(cheminFichier).size,
        dateReference
      };
    } catch (error) {
      logger.error('Erreur lors de la génération de la balance âgée', {
        dateReference: dateReference,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Génère le centralisateur des journaux
   * @param {Date} dateDebut - Date de début
   * @param {Date} dateFin - Date de fin
   * @param {Object} options - Options de génération
   * @returns {Promise<Object>} Informations sur le centralisateur généré
   */
  async genererCentralisateur(dateDebut, dateFin, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        format = 'excel', // 'excel' ou 'pdf'
        includeNonValidees = false,
        groupeParMois = false
      } = options;

      // Valider les paramètres
      this.calculService.validerPeriode(dateDebut, dateFin);

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Récupérer l'exercice si fourni
      let exercice = null;
      if (exerciceId) {
        exercice = await this.models.ExerciceComptable.findByPk(exerciceId);
        if (!exercice) {
          throw new NotFoundError('Exercice comptable');
        }
      }

      // Récupérer tous les journaux de la société
      const journaux = await this.models.Journal.findAll({
        where: { societeId },
        order: [['code', 'ASC']]
      });

      if (journaux.length === 0) {
        throw new ValidationError('Aucun journal trouvé pour cette société');
      }

      // Calculer les totaux pour chaque journal
      const donneesJournaux = [];
      let totalDebitGeneral = 0;
      let totalCreditGeneral = 0;

      for (const journal of journaux) {
        // Calculer les totaux du journal
        const totauxJournal = await this.calculService.calculerTotauxJournal(
          journal.code,
          dateDebut,
          dateFin,
          {
            societeId,
            exerciceId,
            includeNonValidees,
            groupeParCompte: false
          }
        );

        // Si le journal n'a pas d'écritures, passer au suivant
        if (totauxJournal.totaux.nombreEcritures === 0) {
          continue;
        }

        // Ajouter les données du journal
        donneesJournaux.push({
          code: journal.code,
          libelle: journal.libelle,
          type: journal.type,
          totalDebit: totauxJournal.totaux.totalDebit,
          totalCredit: totauxJournal.totaux.totalCredit,
          nombreEcritures: totauxJournal.totaux.nombreEcritures
        });

        totalDebitGeneral += totauxJournal.totaux.totalDebit;
        totalCreditGeneral += totauxJournal.totaux.totalCredit;
      }

      // Préparer les données du centralisateur
      const donneesCentralisateur = {
        periode: { dateDebut, dateFin },
        journaux: donneesJournaux,
        totaux: {
          totalDebit: totalDebitGeneral,
          totalCredit: totalCreditGeneral,
          equilibre: Math.abs(totalDebitGeneral - totalCreditGeneral) < 0.01
        }
      };

      // Générer le fichier selon le format demandé
      const nomFichier = `centralisateur_${Date.now()}.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
      const cheminFichier = path.join(process.cwd(), 'temp', nomFichier);

      // Créer le dossier temp s'il n'existe pas
      const dossierTemp = path.dirname(cheminFichier);
      if (!fs.existsSync(dossierTemp)) {
        fs.mkdirSync(dossierTemp, { recursive: true });
      }

      if (format === 'excel') {
        await this.genererCentralisateurExcel(
          donneesCentralisateur,
          cheminFichier,
          {
            societe,
            exercice
          }
        );
      } else {
        await this.genererCentralisateurPDF(
          donneesCentralisateur,
          cheminFichier,
          {
            societe,
            exercice
          }
        );
      }

      logger.info('Centralisateur généré', {
        format,
        nombreJournaux: donneesJournaux.length,
        cheminFichier
      });

      return {
        success: true,
        cheminFichier,
        nomFichier: path.basename(cheminFichier),
        format,
        nombreJournaux: donneesJournaux.length,
        tailleFichier: fs.statSync(cheminFichier).size,
        periode: { dateDebut, dateFin }
      };
    } catch (error) {
      logger.error('Erreur lors de la génération du centralisateur', {
        dateDebut,
        dateFin,
        error: error.message
      });
      throw error;
    }
  }

  // ==========================================
  // MÉTHODES DE GÉNÉRATION DE FICHIERS EXCEL
  // ==========================================

  /**
   * Génère un fichier Excel pour le grand livre
   * @param {Array} donneesGrandLivre - Données du grand livre
   * @param {string} cheminFichier - Chemin du fichier à générer
   * @param {Object} metadata - Métadonnées (société, exercice, période)
   */
  async genererGrandLivreExcel(donneesGrandLivre, cheminFichier, metadata) {
    const workbook = XLSX.utils.book_new();

    // Feuille d'informations générales
    const infoSheet = XLSX.utils.aoa_to_sheet([
      ['GRAND LIVRE COMPTABLE'],
      [''],
      ['Société:', metadata.societe.raisonSociale],
      ['SIRET:', metadata.societe.siret || ''],
      ['Exercice:', metadata.exercice ? metadata.exercice.annee : ''],
      [
        'Période:',
        `Du ${this.formatDate(metadata.periode.dateDebut)} au ${this.formatDate(metadata.periode.dateFin)}`
      ],
      ["Date d'édition:", this.formatDate(new Date())],
      ['']
    ]);
    XLSX.utils.book_append_sheet(workbook, infoSheet, 'Informations');

    // Pour chaque compte, créer une feuille
    for (const donneeCompte of donneesGrandLivre) {
      const compte = donneeCompte.compte;
      const sheetName =
        compte.numero.length > 31
          ? compte.numero.substring(0, 31)
          : compte.numero;

      // En-tête avec informations du compte
      const headers = [
        [`GRAND LIVRE - COMPTE ${compte.numero} - ${compte.libelle}`],
        [''],
        [
          'Date',
          'Journal',
          'N° Pièce',
          'Libellé',
          'Référence',
          'Débit',
          'Crédit',
          'Solde',
          'Sens',
          'Lettrage'
        ]
      ];

      // Lignes du compte
      const rows = donneeCompte.lignes.map(ligne => [
        this.formatDate(ligne.date),
        ligne.journal || '',
        ligne.numeroEcriture || '',
        ligne.libelle || '',
        ligne.reference || '',
        ligne.debit || 0,
        ligne.credit || 0,
        ligne.soldeProgressif || 0,
        ligne.sensProgressif || '',
        ligne.lettrage || ''
      ]);

      // Ligne de total
      rows.push([
        '',
        '',
        '',
        'TOTAUX',
        '',
        donneeCompte.totaux.totalDebit,
        donneeCompte.totaux.totalCredit,
        '',
        '',
        ''
      ]);

      // Ligne de solde final
      rows.push([
        '',
        '',
        '',
        'SOLDE FINAL',
        '',
        donneeCompte.totaux.soldeDebiteur || 0,
        donneeCompte.totaux.soldeCrediteur || 0,
        '',
        '',
        ''
      ]);

      // Créer la feuille
      const worksheet = XLSX.utils.aoa_to_sheet([...headers, ...rows]);

      // Définir les largeurs de colonnes
      const colWidths = [
        { wch: 12 }, // Date
        { wch: 8 }, // Journal
        { wch: 12 }, // N° Pièce
        { wch: 40 }, // Libellé
        { wch: 15 }, // Référence
        { wch: 12 }, // Débit
        { wch: 12 }, // Crédit
        { wch: 12 }, // Solde
        { wch: 8 }, // Sens
        { wch: 10 } // Lettrage
      ];
      worksheet['!cols'] = colWidths;

      // Ajouter la feuille au classeur
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    }

    // Écrire le fichier
    XLSX.writeFile(workbook, cheminFichier);
  }

  /**
   * Génère un fichier Excel pour le journal
   * @param {Array} donneesJournaux - Données des journaux
   * @param {string} cheminFichier - Chemin du fichier à générer
   * @param {Object} metadata - Métadonnées (société, exercice, période)
   */
  async genererJournalExcel(donneesJournaux, cheminFichier, metadata) {
    const workbook = XLSX.utils.book_new();

    // Feuille d'informations générales
    const infoSheet = XLSX.utils.aoa_to_sheet([
      ['JOURNAL COMPTABLE'],
      [''],
      ['Société:', metadata.societe.raisonSociale],
      ['SIRET:', metadata.societe.siret || ''],
      ['Exercice:', metadata.exercice ? metadata.exercice.annee : ''],
      [
        'Période:',
        `Du ${this.formatDate(metadata.periode.dateDebut)} au ${this.formatDate(metadata.periode.dateFin)}`
      ],
      ["Date d'édition:", this.formatDate(new Date())],
      ['']
    ]);
    XLSX.utils.book_append_sheet(workbook, infoSheet, 'Informations');

    // Pour chaque journal, créer une feuille
    for (const donneeJournal of donneesJournaux) {
      const journal = donneeJournal.journal;
      const sheetName =
        journal.code.length > 31 ? journal.code.substring(0, 31) : journal.code;

      // En-tête avec informations du journal
      const headers = [
        [`JOURNAL ${journal.code} - ${journal.libelle}`],
        [''],
        [
          'Date',
          'N° Pièce',
          'Compte',
          'Libellé',
          'Référence',
          'Débit',
          'Crédit'
        ]
      ];

      // Lignes du journal
      const rows = [];
      for (const ligne of donneeJournal.lignes) {
        if (ligne.type === 'entete-jour') {
          rows.push([
            this.formatDate(ligne.date),
            '',
            '',
            ligne.libelle,
            '',
            '',
            ''
          ]);
        } else if (ligne.type === 'entete-ecriture') {
          rows.push([
            this.formatDate(ligne.date),
            ligne.numeroEcriture,
            '',
            ligne.libelle,
            ligne.reference || '',
            '',
            ''
          ]);
        } else if (ligne.type === 'ligne') {
          rows.push([
            '',
            '',
            ligne.compteNumero,
            ligne.libelle,
            '',
            ligne.debit || 0,
            ligne.credit || 0
          ]);
        } else if (ligne.type === 'total-jour') {
          rows.push([
            '',
            '',
            '',
            ligne.libelle,
            '',
            ligne.debit || 0,
            ligne.credit || 0
          ]);
        }
      }

      // Ligne de total
      rows.push([
        '',
        '',
        '',
        'TOTAUX',
        '',
        donneeJournal.totaux.totalDebit,
        donneeJournal.totaux.totalCredit
      ]);

      // Créer la feuille
      const worksheet = XLSX.utils.aoa_to_sheet([...headers, ...rows]);

      // Définir les largeurs de colonnes
      const colWidths = [
        { wch: 12 }, // Date
        { wch: 12 }, // N° Pièce
        { wch: 10 }, // Compte
        { wch: 40 }, // Libellé
        { wch: 15 }, // Référence
        { wch: 12 }, // Débit
        { wch: 12 } // Crédit
      ];
      worksheet['!cols'] = colWidths;

      // Ajouter la feuille au classeur
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    }

    // Écrire le fichier
    XLSX.writeFile(workbook, cheminFichier);
  }

  /**
   * Génère un fichier Excel pour la balance générale
   * @param {Object} balance - Données de la balance
   * @param {string} cheminFichier - Chemin du fichier à générer
   * @param {Object} metadata - Métadonnées (société, exercice, période)
   */
  async genererBalanceExcel(balance, cheminFichier, metadata) {
    const workbook = XLSX.utils.book_new();

    // Feuille d'informations générales
    const infoSheet = XLSX.utils.aoa_to_sheet([
      ['BALANCE GÉNÉRALE'],
      [''],
      ['Société:', metadata.societe.raisonSociale],
      ['SIRET:', metadata.societe.siret || ''],
      ['Exercice:', metadata.exercice ? metadata.exercice.annee : ''],
      [
        'Période:',
        `Du ${this.formatDate(metadata.periode.dateDebut)} au ${this.formatDate(metadata.periode.dateFin)}`
      ],
      ["Date d'édition:", this.formatDate(new Date())],
      ['']
    ]);
    XLSX.utils.book_append_sheet(workbook, infoSheet, 'Informations');

    // Feuille de la balance
    const headers = [
      [
        'N° Compte',
        'Libellé',
        'Débit',
        'Crédit',
        'Solde Débiteur',
        'Solde Créditeur'
      ]
    ];

    // Lignes de la balance
    const rows = balance.lignesBalance.map(ligne => {
      const soldeDebiteur = ligne.sensActuel === 'DEBIT' ? ligne.solde : 0;
      const soldeCrediteur = ligne.sensActuel === 'CREDIT' ? ligne.solde : 0;

      return [
        ligne.compteNumero,
        ligne.libelle,
        ligne.totalDebit,
        ligne.totalCredit,
        soldeDebiteur,
        soldeCrediteur
      ];
    });

    // Ligne de total
    rows.push([
      '',
      'TOTAUX',
      balance.totauxGeneraux.totalDebit,
      balance.totauxGeneraux.totalCredit,
      balance.lignesBalance.reduce(
        (sum, l) => sum + (l.sensActuel === 'DEBIT' ? l.solde : 0),
        0
      ),
      balance.lignesBalance.reduce(
        (sum, l) => sum + (l.sensActuel === 'CREDIT' ? l.solde : 0),
        0
      )
    ]);

    // Créer la feuille
    const worksheet = XLSX.utils.aoa_to_sheet([...headers, ...rows]);

    // Définir les largeurs de colonnes
    const colWidths = [
      { wch: 10 }, // N° Compte
      { wch: 40 }, // Libellé
      { wch: 15 }, // Débit
      { wch: 15 }, // Crédit
      { wch: 15 }, // Solde Débiteur
      { wch: 15 } // Solde Créditeur
    ];
    worksheet['!cols'] = colWidths;

    // Ajouter la feuille au classeur
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Balance');

    // Feuille des totaux par classe
    const headersClasse = [
      ['Classe', 'Libellé', 'Débit', 'Crédit', 'Nombre de comptes']
    ];

    const rowsClasse = balance.totauxParClasse.map(classe => [
      classe.classe,
      classe.libelle,
      classe.totalDebit,
      classe.totalCredit,
      classe.nombreComptes
    ]);

    const worksheetClasse = XLSX.utils.aoa_to_sheet([
      ...headersClasse,
      ...rowsClasse
    ]);
    XLSX.utils.book_append_sheet(
      workbook,
      worksheetClasse,
      'Totaux par classe'
    );

    // Écrire le fichier
    XLSX.writeFile(workbook, cheminFichier);
  }

  /**
   * Génère un fichier Excel pour la balance âgée
   * @param {Object} balance - Données de la balance âgée
   * @param {string} cheminFichier - Chemin du fichier à générer
   * @param {Object} metadata - Métadonnées (société, exercice)
   */
  async genererBalanceAgeeExcel(balance, cheminFichier, metadata) {
    const workbook = XLSX.utils.book_new();

    // Feuille d'informations générales
    const infoSheet = XLSX.utils.aoa_to_sheet([
      ['BALANCE ÂGÉE'],
      [''],
      ['Société:', metadata.societe.raisonSociale],
      ['SIRET:', metadata.societe.siret || ''],
      ['Exercice:', metadata.exercice ? metadata.exercice.annee : ''],
      ['Date de référence:', this.formatDate(balance.dateReference)],
      ["Date d'édition:", this.formatDate(new Date())],
      ['']
    ]);
    XLSX.utils.book_append_sheet(workbook, infoSheet, 'Informations');

    // Préparer les en-têtes avec les tranches
    const headers = [['N° Compte', 'Libellé', 'Solde Total']];

    // Ajouter les tranches aux en-têtes
    for (let i = 0; i < balance.tranches.length; i++) {
      headers[0].push(`< ${balance.tranches[i]} jours`);
    }
    headers[0].push('Antérieur');

    // Lignes de la balance
    const rows = balance.lignesBalance.map(ligne => {
      const row = [ligne.compteNumero, ligne.libelle, ligne.soldeTotal];

      // Ajouter les montants par tranche
      for (let i = 0; i < ligne.soldesParTranche.length; i++) {
        row.push(ligne.soldesParTranche[i]);
      }

      return row;
    });

    // Ligne de total
    const totalRow = ['', 'TOTAUX', balance.totalGeneral];
    for (let i = 0; i < balance.totalParTranche.length; i++) {
      totalRow.push(balance.totalParTranche[i]);
    }
    rows.push(totalRow);

    // Créer la feuille
    const worksheet = XLSX.utils.aoa_to_sheet([...headers, ...rows]);

    // Définir les largeurs de colonnes
    const colWidths = [
      { wch: 10 }, // N° Compte
      { wch: 40 }, // Libellé
      { wch: 15 } // Solde Total
    ];

    // Ajouter les largeurs pour les tranches
    for (let i = 0; i < balance.tranches.length + 1; i++) {
      colWidths.push({ wch: 15 });
    }

    worksheet['!cols'] = colWidths;

    // Ajouter la feuille au classeur
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Balance Âgée');

    // Écrire le fichier
    XLSX.writeFile(workbook, cheminFichier);
  }

  /**
   * Génère un fichier Excel pour le centralisateur
   * @param {Object} centralisateur - Données du centralisateur
   * @param {string} cheminFichier - Chemin du fichier à générer
   * @param {Object} metadata - Métadonnées (société, exercice)
   */
  async genererCentralisateurExcel(centralisateur, cheminFichier, metadata) {
    const workbook = XLSX.utils.book_new();

    // Feuille d'informations générales
    const infoSheet = XLSX.utils.aoa_to_sheet([
      ['CENTRALISATEUR DES JOURNAUX'],
      [''],
      ['Société:', metadata.societe.raisonSociale],
      ['SIRET:', metadata.societe.siret || ''],
      ['Exercice:', metadata.exercice ? metadata.exercice.annee : ''],
      [
        'Période:',
        `Du ${this.formatDate(centralisateur.periode.dateDebut)} au ${this.formatDate(centralisateur.periode.dateFin)}`
      ],
      ["Date d'édition:", this.formatDate(new Date())],
      ['']
    ]);
    XLSX.utils.book_append_sheet(workbook, infoSheet, 'Informations');

    // Feuille du centralisateur
    const headers = [
      ['Code', 'Libellé', 'Type', 'Débit', 'Crédit', "Nombre d'écritures"]
    ];

    // Lignes du centralisateur
    const rows = centralisateur.journaux.map(journal => [
      journal.code,
      journal.libelle,
      journal.type,
      journal.totalDebit,
      journal.totalCredit,
      journal.nombreEcritures
    ]);

    // Ligne de total
    rows.push([
      '',
      'TOTAUX',
      '',
      centralisateur.totaux.totalDebit,
      centralisateur.totaux.totalCredit,
      centralisateur.journaux.reduce((sum, j) => sum + j.nombreEcritures, 0)
    ]);

    // Créer la feuille
    const worksheet = XLSX.utils.aoa_to_sheet([...headers, ...rows]);

    // Définir les largeurs de colonnes
    const colWidths = [
      { wch: 8 }, // Code
      { wch: 40 }, // Libellé
      { wch: 15 }, // Type
      { wch: 15 }, // Débit
      { wch: 15 }, // Crédit
      { wch: 15 } // Nombre d'écritures
    ];
    worksheet['!cols'] = colWidths;

    // Ajouter la feuille au classeur
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Centralisateur');

    // Écrire le fichier
    XLSX.writeFile(workbook, cheminFichier);
  }

  // ==========================================
  // MÉTHODES DE GÉNÉRATION DE FICHIERS PDF
  // ==========================================

  /**
   * Génère un fichier PDF pour le grand livre
   * @param {Array} donneesGrandLivre - Données du grand livre
   * @param {string} cheminFichier - Chemin du fichier à générer
   * @param {Object} metadata - Métadonnées (société, exercice, période)
   */
  async genererGrandLivrePDF(donneesGrandLivre, cheminFichier, metadata) {
    const doc = new PDFDocument({
      margin: 30,
      size: 'A4',
      layout: 'landscape'
    });
    const stream = fs.createWriteStream(cheminFichier);

    doc.pipe(stream);

    // En-tête général
    doc.fontSize(16).text('GRAND LIVRE COMPTABLE', { align: 'center' });
    doc.moveDown();
    doc.fontSize(10).text(`Société: ${metadata.societe.raisonSociale}`);
    doc.text(`SIRET: ${metadata.societe.siret || ''}`);
    doc.text(`Exercice: ${metadata.exercice ? metadata.exercice.annee : ''}`);
    doc.text(
      `Période: Du ${this.formatDate(metadata.periode.dateDebut)} au ${this.formatDate(metadata.periode.dateFin)}`
    );
    doc.text(`Date d'édition: ${this.formatDate(new Date())}`);
    doc.moveDown();

    // Pour chaque compte
    for (const donneeCompte of donneesGrandLivre) {
      const compte = donneeCompte.compte;

      // Nouvelle page pour chaque compte
      if (doc.page.pageNumber > 1) {
        doc.addPage();
      }

      // En-tête du compte
      doc
        .fontSize(14)
        .text(`COMPTE ${compte.numero} - ${compte.libelle}`, {
          align: 'center'
        });
      doc.moveDown();

      // Tableau des écritures
      const tableTop = doc.y;
      const tableLeft = 30;
      const colWidths = [70, 40, 60, 150, 60, 60, 60, 60, 40, 40]; // Largeurs des colonnes
      const rowHeight = 20;

      // En-têtes du tableau
      doc.fontSize(8);
      doc.text('Date', tableLeft, tableTop);
      doc.text('Journal', tableLeft + colWidths[0], tableTop);
      doc.text('N° Pièce', tableLeft + colWidths[0] + colWidths[1], tableTop);
      doc.text(
        'Libellé',
        tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
        tableTop
      );
      doc.text(
        'Référence',
        tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3],
        tableTop
      );
      doc.text(
        'Débit',
        tableLeft +
          colWidths[0] +
          colWidths[1] +
          colWidths[2] +
          colWidths[3] +
          colWidths[4],
        tableTop
      );
      doc.text(
        'Crédit',
        tableLeft +
          colWidths[0] +
          colWidths[1] +
          colWidths[2] +
          colWidths[3] +
          colWidths[4] +
          colWidths[5],
        tableTop
      );
      doc.text(
        'Solde',
        tableLeft +
          colWidths[0] +
          colWidths[1] +
          colWidths[2] +
          colWidths[3] +
          colWidths[4] +
          colWidths[5] +
          colWidths[6],
        tableTop
      );
      doc.text(
        'Sens',
        tableLeft +
          colWidths[0] +
          colWidths[1] +
          colWidths[2] +
          colWidths[3] +
          colWidths[4] +
          colWidths[5] +
          colWidths[6] +
          colWidths[7],
        tableTop
      );
      doc.text(
        'Lettrage',
        tableLeft +
          colWidths[0] +
          colWidths[1] +
          colWidths[2] +
          colWidths[3] +
          colWidths[4] +
          colWidths[5] +
          colWidths[6] +
          colWidths[7] +
          colWidths[8],
        tableTop
      );

      // Ligne horizontale après les en-têtes
      doc
        .moveTo(tableLeft, tableTop + rowHeight)
        .lineTo(
          tableLeft + colWidths.reduce((a, b) => a + b, 0),
          tableTop + rowHeight
        )
        .stroke();

      // Lignes du compte
      let y = tableTop + rowHeight;

      for (const ligne of donneeCompte.lignes) {
        // Vérifier s'il faut ajouter une nouvelle page
        if (y + rowHeight > doc.page.height - 50) {
          doc.addPage();
          y = 50;

          // Répéter les en-têtes sur la nouvelle page
          doc
            .fontSize(14)
            .text(`COMPTE ${compte.numero} - ${compte.libelle} (suite)`, {
              align: 'center'
            });
          doc.moveDown();

          doc.fontSize(8);
          doc.text('Date', tableLeft, y);
          doc.text('Journal', tableLeft + colWidths[0], y);
          doc.text('N° Pièce', tableLeft + colWidths[0] + colWidths[1], y);
          doc.text(
            'Libellé',
            tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
            y
          );
          doc.text(
            'Référence',
            tableLeft +
              colWidths[0] +
              colWidths[1] +
              colWidths[2] +
              colWidths[3],
            y
          );
          doc.text(
            'Débit',
            tableLeft +
              colWidths[0] +
              colWidths[1] +
              colWidths[2] +
              colWidths[3] +
              colWidths[4],
            y
          );
          doc.text(
            'Crédit',
            tableLeft +
              colWidths[0] +
              colWidths[1] +
              colWidths[2] +
              colWidths[3] +
              colWidths[4] +
              colWidths[5],
            y
          );
          doc.text(
            'Solde',
            tableLeft +
              colWidths[0] +
              colWidths[1] +
              colWidths[2] +
              colWidths[3] +
              colWidths[4] +
              colWidths[5] +
              colWidths[6],
            y
          );
          doc.text(
            'Sens',
            tableLeft +
              colWidths[0] +
              colWidths[1] +
              colWidths[2] +
              colWidths[3] +
              colWidths[4] +
              colWidths[5] +
              colWidths[6] +
              colWidths[7],
            y
          );
          doc.text(
            'Lettrage',
            tableLeft +
              colWidths[0] +
              colWidths[1] +
              colWidths[2] +
              colWidths[3] +
              colWidths[4] +
              colWidths[5] +
              colWidths[6] +
              colWidths[7] +
              colWidths[8],
            y
          );

          // Ligne horizontale après les en-têtes
          doc
            .moveTo(tableLeft, y + rowHeight)
            .lineTo(
              tableLeft + colWidths.reduce((a, b) => a + b, 0),
              y + rowHeight
            )
            .stroke();

          y += rowHeight;
        }

        // Écrire les données de la ligne
        doc.fontSize(8);
        doc.text(this.formatDate(ligne.date), tableLeft, y);
        doc.text(ligne.journal || '', tableLeft + colWidths[0], y);
        doc.text(
          ligne.numeroEcriture || '',
          tableLeft + colWidths[0] + colWidths[1],
          y
        );
        doc.text(
          ligne.libelle || '',
          tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
          y,
          { width: colWidths[3] }
        );
        doc.text(
          ligne.reference || '',
          tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3],
          y
        );
        doc.text(
          ligne.debit ? ligne.debit.toFixed(2) : '0.00',
          tableLeft +
            colWidths[0] +
            colWidths[1] +
            colWidths[2] +
            colWidths[3] +
            colWidths[4],
          y,
          { align: 'right' }
        );
        doc.text(
          ligne.credit ? ligne.credit.toFixed(2) : '0.00',
          tableLeft +
            colWidths[0] +
            colWidths[1] +
            colWidths[2] +
            colWidths[3] +
            colWidths[4] +
            colWidths[5],
          y,
          { align: 'right' }
        );
        doc.text(
          ligne.soldeProgressif ? ligne.soldeProgressif.toFixed(2) : '0.00',
          tableLeft +
            colWidths[0] +
            colWidths[1] +
            colWidths[2] +
            colWidths[3] +
            colWidths[4] +
            colWidths[5] +
            colWidths[6],
          y,
          { align: 'right' }
        );
        doc.text(
          ligne.sensProgressif || '',
          tableLeft +
            colWidths[0] +
            colWidths[1] +
            colWidths[2] +
            colWidths[3] +
            colWidths[4] +
            colWidths[5] +
            colWidths[6] +
            colWidths[7],
          y
        );
        doc.text(
          ligne.lettrage || '',
          tableLeft +
            colWidths[0] +
            colWidths[1] +
            colWidths[2] +
            colWidths[3] +
            colWidths[4] +
            colWidths[5] +
            colWidths[6] +
            colWidths[7] +
            colWidths[8],
          y
        );

        // Ligne horizontale après chaque ligne
        doc
          .moveTo(tableLeft, y + rowHeight)
          .lineTo(
            tableLeft + colWidths.reduce((a, b) => a + b, 0),
            y + rowHeight
          )
          .stroke();

        y += rowHeight;
      }

      // Ligne de total
      doc
        .fontSize(9)
        .text(
          'TOTAUX',
          tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
          y
        );
      doc.text(
        donneeCompte.totaux.totalDebit.toFixed(2),
        tableLeft +
          colWidths[0] +
          colWidths[1] +
          colWidths[2] +
          colWidths[3] +
          colWidths[4],
        y,
        { align: 'right' }
      );
      doc.text(
        donneeCompte.totaux.totalCredit.toFixed(2),
        tableLeft +
          colWidths[0] +
          colWidths[1] +
          colWidths[2] +
          colWidths[3] +
          colWidths[4] +
          colWidths[5],
        y,
        { align: 'right' }
      );

      y += rowHeight;

      // Ligne de solde final
      doc
        .fontSize(9)
        .text(
          'SOLDE FINAL',
          tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
          y
        );
      doc.text(
        (donneeCompte.totaux.soldeDebiteur || 0).toFixed(2),
        tableLeft +
          colWidths[0] +
          colWidths[1] +
          colWidths[2] +
          colWidths[3] +
          colWidths[4],
        y,
        { align: 'right' }
      );
      doc.text(
        (donneeCompte.totaux.soldeCrediteur || 0).toFixed(2),
        tableLeft +
          colWidths[0] +
          colWidths[1] +
          colWidths[2] +
          colWidths[3] +
          colWidths[4] +
          colWidths[5],
        y,
        { align: 'right' }
      );
    }

    // Finaliser le document
    doc.end();

    return new Promise((resolve, reject) => {
      stream.on('finish', resolve);
      stream.on('error', reject);
    });
  }

  /**
   * Génère un fichier PDF pour le journal
   * @param {Array} donneesJournaux - Données des journaux
   * @param {string} cheminFichier - Chemin du fichier à générer
   * @param {Object} metadata - Métadonnées (société, exercice, période)
   */
  async genererJournalPDF(donneesJournaux, cheminFichier, metadata) {
    const doc = new PDFDocument({ margin: 30, size: 'A4', layout: 'portrait' });
    const stream = fs.createWriteStream(cheminFichier);

    doc.pipe(stream);

    // En-tête général
    doc.fontSize(16).text('JOURNAL COMPTABLE', { align: 'center' });
    doc.moveDown();
    doc.fontSize(10).text(`Société: ${metadata.societe.raisonSociale}`);
    doc.text(`SIRET: ${metadata.societe.siret || ''}`);
    doc.text(`Exercice: ${metadata.exercice ? metadata.exercice.annee : ''}`);
    doc.text(
      `Période: Du ${this.formatDate(metadata.periode.dateDebut)} au ${this.formatDate(metadata.periode.dateFin)}`
    );
    doc.text(`Date d'édition: ${this.formatDate(new Date())}`);
    doc.moveDown();

    // Pour chaque journal
    for (const donneeJournal of donneesJournaux) {
      const journal = donneeJournal.journal;

      // Nouvelle page pour chaque journal
      if (doc.page.pageNumber > 1) {
        doc.addPage();
      }

      // En-tête du journal
      doc
        .fontSize(14)
        .text(`JOURNAL ${journal.code} - ${journal.libelle}`, {
          align: 'center'
        });
      doc.moveDown();

      // Tableau des écritures
      const tableTop = doc.y;
      const tableLeft = 30;
      const colWidths = [70, 60, 60, 200, 60, 60]; // Largeurs des colonnes
      const rowHeight = 20;

      // En-têtes du tableau
      doc.fontSize(8);
      doc.text('Date', tableLeft, tableTop);
      doc.text('N° Pièce', tableLeft + colWidths[0], tableTop);
      doc.text('Compte', tableLeft + colWidths[0] + colWidths[1], tableTop);
      doc.text(
        'Libellé',
        tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
        tableTop
      );
      doc.text(
        'Débit',
        tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3],
        tableTop
      );
      doc.text(
        'Crédit',
        tableLeft +
          colWidths[0] +
          colWidths[1] +
          colWidths[2] +
          colWidths[3] +
          colWidths[4],
        tableTop
      );

      // Ligne horizontale après les en-têtes
      doc
        .moveTo(tableLeft, tableTop + rowHeight)
        .lineTo(
          tableLeft + colWidths.reduce((a, b) => a + b, 0),
          tableTop + rowHeight
        )
        .stroke();

      // Lignes du journal
      let y = tableTop + rowHeight;

      for (const ligne of donneeJournal.lignes) {
        // Vérifier s'il faut ajouter une nouvelle page
        if (y + rowHeight > doc.page.height - 50) {
          doc.addPage();
          y = 50;

          // Répéter les en-têtes sur la nouvelle page
          doc
            .fontSize(14)
            .text(`JOURNAL ${journal.code} - ${journal.libelle} (suite)`, {
              align: 'center'
            });
          doc.moveDown();

          doc.fontSize(8);
          doc.text('Date', tableLeft, y);
          doc.text('N° Pièce', tableLeft + colWidths[0], y);
          doc.text('Compte', tableLeft + colWidths[0] + colWidths[1], y);
          doc.text(
            'Libellé',
            tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
            y
          );
          doc.text(
            'Débit',
            tableLeft +
              colWidths[0] +
              colWidths[1] +
              colWidths[2] +
              colWidths[3],
            y
          );
          doc.text(
            'Crédit',
            tableLeft +
              colWidths[0] +
              colWidths[1] +
              colWidths[2] +
              colWidths[3] +
              colWidths[4],
            y
          );

          // Ligne horizontale après les en-têtes
          doc
            .moveTo(tableLeft, y + rowHeight)
            .lineTo(
              tableLeft + colWidths.reduce((a, b) => a + b, 0),
              y + rowHeight
            )
            .stroke();

          y += rowHeight;
        }

        // Écrire les données selon le type de ligne
        doc.fontSize(8);

        if (ligne.type === 'entete-jour') {
          doc
            .font('Helvetica-Bold')
            .text(this.formatDate(ligne.date), tableLeft, y);
          doc.text(
            ligne.libelle,
            tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
            y
          );
          doc.font('Helvetica');
        } else if (ligne.type === 'entete-ecriture') {
          doc.text(this.formatDate(ligne.date), tableLeft, y);
          doc.text(ligne.numeroEcriture || '', tableLeft + colWidths[0], y);
          doc
            .font('Helvetica-Bold')
            .text(
              ligne.libelle,
              tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
              y
            );
          doc.font('Helvetica');
        } else if (ligne.type === 'ligne') {
          doc.text('', tableLeft, y);
          doc.text('', tableLeft + colWidths[0], y);
          doc.text(
            ligne.compteNumero,
            tableLeft + colWidths[0] + colWidths[1],
            y
          );
          doc.text(
            ligne.libelle,
            tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
            y,
            { width: colWidths[3] }
          );
          doc.text(
            ligne.debit ? ligne.debit.toFixed(2) : '',
            tableLeft +
              colWidths[0] +
              colWidths[1] +
              colWidths[2] +
              colWidths[3],
            y,
            { align: 'right' }
          );
          doc.text(
            ligne.credit ? ligne.credit.toFixed(2) : '',
            tableLeft +
              colWidths[0] +
              colWidths[1] +
              colWidths[2] +
              colWidths[3] +
              colWidths[4],
            y,
            { align: 'right' }
          );
        } else if (ligne.type === 'total-jour') {
          doc.font('Helvetica-Bold');
          doc.text(
            ligne.libelle,
            tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
            y
          );
          doc.text(
            ligne.debit.toFixed(2),
            tableLeft +
              colWidths[0] +
              colWidths[1] +
              colWidths[2] +
              colWidths[3],
            y,
            { align: 'right' }
          );
          doc.text(
            ligne.credit.toFixed(2),
            tableLeft +
              colWidths[0] +
              colWidths[1] +
              colWidths[2] +
              colWidths[3] +
              colWidths[4],
            y,
            { align: 'right' }
          );
          doc.font('Helvetica');
        }

        // Ligne horizontale après chaque ligne
        doc
          .moveTo(tableLeft, y + rowHeight)
          .lineTo(
            tableLeft + colWidths.reduce((a, b) => a + b, 0),
            y + rowHeight
          )
          .stroke();

        y += rowHeight;
      }

      // Ligne de total
      doc.fontSize(9).font('Helvetica-Bold');
      doc.text(
        'TOTAUX',
        tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
        y
      );
      doc.text(
        donneeJournal.totaux.totalDebit.toFixed(2),
        tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3],
        y,
        { align: 'right' }
      );
      doc.text(
        donneeJournal.totaux.totalCredit.toFixed(2),
        tableLeft +
          colWidths[0] +
          colWidths[1] +
          colWidths[2] +
          colWidths[3] +
          colWidths[4],
        y,
        { align: 'right' }
      );
      doc.font('Helvetica');
    }

    // Finaliser le document
    doc.end();

    return new Promise((resolve, reject) => {
      stream.on('finish', resolve);
      stream.on('error', reject);
    });
  }

  /**
   * Génère un fichier PDF pour la balance générale
   * @param {Object} balance - Données de la balance
   * @param {string} cheminFichier - Chemin du fichier à générer
   * @param {Object} metadata - Métadonnées (société, exercice, période)
   */
  async genererBalancePDF(balance, cheminFichier, metadata) {
    const doc = new PDFDocument({
      margin: 30,
      size: 'A4',
      layout: 'landscape'
    });
    const stream = fs.createWriteStream(cheminFichier);

    doc.pipe(stream);

    // En-tête général
    doc.fontSize(16).text('BALANCE GÉNÉRALE', { align: 'center' });
    doc.moveDown();
    doc.fontSize(10).text(`Société: ${metadata.societe.raisonSociale}`);
    doc.text(`SIRET: ${metadata.societe.siret || ''}`);
    doc.text(`Exercice: ${metadata.exercice ? metadata.exercice.annee : ''}`);
    doc.text(
      `Période: Du ${this.formatDate(metadata.periode.dateDebut)} au ${this.formatDate(metadata.periode.dateFin)}`
    );
    doc.text(`Date d'édition: ${this.formatDate(new Date())}`);
    doc.moveDown();

    // Tableau de la balance
    const tableTop = doc.y;
    const tableLeft = 30;
    const colWidths = [80, 200, 100, 100, 100, 100]; // Largeurs des colonnes
    const rowHeight = 20;

    // En-têtes du tableau
    doc.fontSize(8);
    doc.text('N° Compte', tableLeft, tableTop);
    doc.text('Libellé', tableLeft + colWidths[0], tableTop);
    doc.text('Débit', tableLeft + colWidths[0] + colWidths[1], tableTop);
    doc.text(
      'Crédit',
      tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
      tableTop
    );
    doc.text(
      'Solde Débiteur',
      tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3],
      tableTop
    );
    doc.text(
      'Solde Créditeur',
      tableLeft +
        colWidths[0] +
        colWidths[1] +
        colWidths[2] +
        colWidths[3] +
        colWidths[4],
      tableTop
    );

    // Ligne horizontale après les en-têtes
    doc
      .moveTo(tableLeft, tableTop + rowHeight)
      .lineTo(
        tableLeft + colWidths.reduce((a, b) => a + b, 0),
        tableTop + rowHeight
      )
      .stroke();

    // Lignes de la balance
    let y = tableTop + rowHeight;
    let classeActuelle = '';

    for (const ligne of balance.lignesBalance) {
      // Vérifier s'il faut ajouter une nouvelle page
      if (y + rowHeight > doc.page.height - 50) {
        doc.addPage();
        y = 50;

        // Répéter les en-têtes sur la nouvelle page
        doc.fontSize(14).text('BALANCE GÉNÉRALE (suite)', { align: 'center' });
        doc.moveDown();

        doc.fontSize(8);
        doc.text('N° Compte', tableLeft, y);
        doc.text('Libellé', tableLeft + colWidths[0], y);
        doc.text('Débit', tableLeft + colWidths[0] + colWidths[1], y);
        doc.text(
          'Crédit',
          tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
          y
        );
        doc.text(
          'Solde Débiteur',
          tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3],
          y
        );
        doc.text(
          'Solde Créditeur',
          tableLeft +
            colWidths[0] +
            colWidths[1] +
            colWidths[2] +
            colWidths[3] +
            colWidths[4],
          y
        );

        // Ligne horizontale après les en-têtes
        doc
          .moveTo(tableLeft, y + rowHeight)
          .lineTo(
            tableLeft + colWidths.reduce((a, b) => a + b, 0),
            y + rowHeight
          )
          .stroke();

        y += rowHeight;
      }

      // Ajouter un en-tête de classe si on change de classe
      const classeCompte = ligne.compteNumero.charAt(0);
      if (classeCompte !== classeActuelle) {
        classeActuelle = classeCompte;
        const classeInfo = balance.totauxParClasse.find(
          c => c.classe.toString() === classeActuelle
        );

        if (classeInfo) {
          doc.font('Helvetica-Bold').fontSize(9);
          doc.text(
            `CLASSE ${classeInfo.classe} - ${classeInfo.libelle}`,
            tableLeft,
            y
          );
          doc.font('Helvetica').fontSize(8);

          // Ligne horizontale après l'en-tête de classe
          doc
            .moveTo(tableLeft, y + rowHeight)
            .lineTo(
              tableLeft + colWidths.reduce((a, b) => a + b, 0),
              y + rowHeight
            )
            .stroke();

          y += rowHeight;
        }
      }

      // Écrire les données de la ligne
      const soldeDebiteur = ligne.sensActuel === 'DEBIT' ? ligne.solde : 0;
      const soldeCrediteur = ligne.sensActuel === 'CREDIT' ? ligne.solde : 0;

      doc.fontSize(8);
      doc.text(ligne.compteNumero, tableLeft, y);
      doc.text(ligne.libelle, tableLeft + colWidths[0], y, {
        width: colWidths[1]
      });
      doc.text(
        ligne.totalDebit.toFixed(2),
        tableLeft + colWidths[0] + colWidths[1],
        y,
        { align: 'right' }
      );
      doc.text(
        ligne.totalCredit.toFixed(2),
        tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
        y,
        { align: 'right' }
      );
      doc.text(
        soldeDebiteur.toFixed(2),
        tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3],
        y,
        { align: 'right' }
      );
      doc.text(
        soldeCrediteur.toFixed(2),
        tableLeft +
          colWidths[0] +
          colWidths[1] +
          colWidths[2] +
          colWidths[3] +
          colWidths[4],
        y,
        { align: 'right' }
      );

      // Ligne horizontale après chaque ligne
      doc
        .moveTo(tableLeft, y + rowHeight)
        .lineTo(tableLeft + colWidths.reduce((a, b) => a + b, 0), y + rowHeight)
        .stroke();

      y += rowHeight;
    }

    // Ligne de total
    const totalSoldeDebiteur = balance.lignesBalance.reduce(
      (sum, l) => sum + (l.sensActuel === 'DEBIT' ? l.solde : 0),
      0
    );
    const totalSoldeCrediteur = balance.lignesBalance.reduce(
      (sum, l) => sum + (l.sensActuel === 'CREDIT' ? l.solde : 0),
      0
    );

    doc.fontSize(9).font('Helvetica-Bold');
    doc.text('TOTAUX', tableLeft + colWidths[0], y);
    doc.text(
      balance.totauxGeneraux.totalDebit.toFixed(2),
      tableLeft + colWidths[0] + colWidths[1],
      y,
      { align: 'right' }
    );
    doc.text(
      balance.totauxGeneraux.totalCredit.toFixed(2),
      tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
      y,
      { align: 'right' }
    );
    doc.text(
      totalSoldeDebiteur.toFixed(2),
      tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3],
      y,
      { align: 'right' }
    );
    doc.text(
      totalSoldeCrediteur.toFixed(2),
      tableLeft +
        colWidths[0] +
        colWidths[1] +
        colWidths[2] +
        colWidths[3] +
        colWidths[4],
      y,
      { align: 'right' }
    );
    doc.font('Helvetica');

    // Finaliser le document
    doc.end();

    return new Promise((resolve, reject) => {
      stream.on('finish', resolve);
      stream.on('error', reject);
    });
  }

  /**
   * Génère un fichier PDF pour la balance âgée
   * @param {Object} balance - Données de la balance âgée
   * @param {string} cheminFichier - Chemin du fichier à générer
   * @param {Object} metadata - Métadonnées (société, exercice)
   */
  async genererBalanceAgeePDF(balance, cheminFichier, metadata) {
    const doc = new PDFDocument({
      margin: 30,
      size: 'A4',
      layout: 'landscape'
    });
    const stream = fs.createWriteStream(cheminFichier);

    doc.pipe(stream);

    // En-tête général
    doc.fontSize(16).text('BALANCE ÂGÉE', { align: 'center' });
    doc.moveDown();
    doc.fontSize(10).text(`Société: ${metadata.societe.raisonSociale}`);
    doc.text(`SIRET: ${metadata.societe.siret || ''}`);
    doc.text(`Exercice: ${metadata.exercice ? metadata.exercice.annee : ''}`);
    doc.text(`Date de référence: ${this.formatDate(balance.dateReference)}`);
    doc.text(`Date d'édition: ${this.formatDate(new Date())}`);
    doc.moveDown();

    // Tableau de la balance
    const tableTop = doc.y;
    const tableLeft = 30;

    // Calculer les largeurs de colonnes en fonction du nombre de tranches
    const colWidthCompte = 80;
    const colWidthLibelle = 180;
    const colWidthSolde = 80;
    const colWidthTranche =
      (doc.page.width - 60 - colWidthCompte - colWidthLibelle - colWidthSolde) /
      (balance.tranches.length + 1);

    // En-têtes du tableau
    doc.fontSize(8);
    doc.text('N° Compte', tableLeft, tableTop);
    doc.text('Libellé', tableLeft + colWidthCompte, tableTop);
    doc.text(
      'Solde Total',
      tableLeft + colWidthCompte + colWidthLibelle,
      tableTop
    );

    // En-têtes des tranches
    let xTranche = tableLeft + colWidthCompte + colWidthLibelle + colWidthSolde;
    for (let i = 0; i < balance.tranches.length; i++) {
      doc.text(`< ${balance.tranches[i]} jours`, xTranche, tableTop);
      xTranche += colWidthTranche;
    }
    doc.text('Antérieur', xTranche, tableTop);

    // Ligne horizontale après les en-têtes
    doc
      .moveTo(tableLeft, tableTop + 20)
      .lineTo(
        tableLeft +
          colWidthCompte +
          colWidthLibelle +
          colWidthSolde +
          colWidthTranche * (balance.tranches.length + 1),
        tableTop + 20
      )
      .stroke();

    // Lignes de la balance
    let y = tableTop + 20;

    for (const ligne of balance.lignesBalance) {
      // Vérifier s'il faut ajouter une nouvelle page
      if (y + 20 > doc.page.height - 50) {
        doc.addPage();
        y = 50;

        // Répéter les en-têtes sur la nouvelle page
        doc.fontSize(14).text('BALANCE ÂGÉE (suite)', { align: 'center' });
        doc.moveDown();

        doc.fontSize(8);
        doc.text('N° Compte', tableLeft, y);
        doc.text('Libellé', tableLeft + colWidthCompte, y);
        doc.text(
          'Solde Total',
          tableLeft + colWidthCompte + colWidthLibelle,
          y
        );

        // En-têtes des tranches
        xTranche = tableLeft + colWidthCompte + colWidthLibelle + colWidthSolde;
        for (let i = 0; i < balance.tranches.length; i++) {
          doc.text(`< ${balance.tranches[i]} jours`, xTranche, y);
          xTranche += colWidthTranche;
        }
        doc.text('Antérieur', xTranche, y);

        // Ligne horizontale après les en-têtes
        doc
          .moveTo(tableLeft, y + 20)
          .lineTo(
            tableLeft +
              colWidthCompte +
              colWidthLibelle +
              colWidthSolde +
              colWidthTranche * (balance.tranches.length + 1),
            y + 20
          )
          .stroke();

        y += 20;
      }

      // Écrire les données de la ligne
      doc.fontSize(8);
      doc.text(ligne.compteNumero, tableLeft, y);
      doc.text(ligne.libelle, tableLeft + colWidthCompte, y, {
        width: colWidthLibelle
      });
      doc.text(
        ligne.soldeTotal.toFixed(2),
        tableLeft + colWidthCompte + colWidthLibelle,
        y,
        { align: 'right' }
      );

      // Écrire les montants par tranche
      xTranche = tableLeft + colWidthCompte + colWidthLibelle + colWidthSolde;
      for (let i = 0; i < ligne.soldesParTranche.length; i++) {
        doc.text(ligne.soldesParTranche[i].toFixed(2), xTranche, y, {
          align: 'right'
        });
        xTranche += colWidthTranche;
      }

      // Ligne horizontale après chaque ligne
      doc
        .moveTo(tableLeft, y + 20)
        .lineTo(
          tableLeft +
            colWidthCompte +
            colWidthLibelle +
            colWidthSolde +
            colWidthTranche * (balance.tranches.length + 1),
          y + 20
        )
        .stroke();

      y += 20;
    }

    // Ligne de total
    doc.fontSize(9).font('Helvetica-Bold');
    doc.text('TOTAUX', tableLeft + colWidthCompte, y);
    doc.text(
      balance.totalGeneral.toFixed(2),
      tableLeft + colWidthCompte + colWidthLibelle,
      y,
      { align: 'right' }
    );

    // Totaux par tranche
    xTranche = tableLeft + colWidthCompte + colWidthLibelle + colWidthSolde;
    for (let i = 0; i < balance.totalParTranche.length; i++) {
      doc.text(balance.totalParTranche[i].toFixed(2), xTranche, y, {
        align: 'right'
      });
      xTranche += colWidthTranche;
    }
    doc.font('Helvetica');

    // Finaliser le document
    doc.end();

    return new Promise((resolve, reject) => {
      stream.on('finish', resolve);
      stream.on('error', reject);
    });
  }

  /**
   * Génère un fichier PDF pour le centralisateur
   * @param {Object} centralisateur - Données du centralisateur
   * @param {string} cheminFichier - Chemin du fichier à générer
   * @param {Object} metadata - Métadonnées (société, exercice)
   */
  async genererCentralisateurPDF(centralisateur, cheminFichier, metadata) {
    const doc = new PDFDocument({ margin: 30, size: 'A4', layout: 'portrait' });
    const stream = fs.createWriteStream(cheminFichier);

    doc.pipe(stream);

    // En-tête général
    doc.fontSize(16).text('CENTRALISATEUR DES JOURNAUX', { align: 'center' });
    doc.moveDown();
    doc.fontSize(10).text(`Société: ${metadata.societe.raisonSociale}`);
    doc.text(`SIRET: ${metadata.societe.siret || ''}`);
    doc.text(`Exercice: ${metadata.exercice ? metadata.exercice.annee : ''}`);
    doc.text(
      `Période: Du ${this.formatDate(centralisateur.periode.dateDebut)} au ${this.formatDate(centralisateur.periode.dateFin)}`
    );
    doc.text(`Date d'édition: ${this.formatDate(new Date())}`);
    doc.moveDown();

    // Tableau du centralisateur
    const tableTop = doc.y;
    const tableLeft = 30;
    const colWidths = [50, 200, 80, 100, 100]; // Largeurs des colonnes
    const rowHeight = 20;

    // En-têtes du tableau
    doc.fontSize(8);
    doc.text('Code', tableLeft, tableTop);
    doc.text('Libellé', tableLeft + colWidths[0], tableTop);
    doc.text('Type', tableLeft + colWidths[0] + colWidths[1], tableTop);
    doc.text(
      'Débit',
      tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
      tableTop
    );
    doc.text(
      'Crédit',
      tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3],
      tableTop
    );

    // Ligne horizontale après les en-têtes
    doc
      .moveTo(tableLeft, tableTop + rowHeight)
      .lineTo(
        tableLeft + colWidths.reduce((a, b) => a + b, 0),
        tableTop + rowHeight
      )
      .stroke();

    // Lignes du centralisateur
    let y = tableTop + rowHeight;

    for (const journal of centralisateur.journaux) {
      // Vérifier s'il faut ajouter une nouvelle page
      if (y + rowHeight > doc.page.height - 50) {
        doc.addPage();
        y = 50;

        // Répéter les en-têtes sur la nouvelle page
        doc
          .fontSize(14)
          .text('CENTRALISATEUR DES JOURNAUX (suite)', { align: 'center' });
        doc.moveDown();

        doc.fontSize(8);
        doc.text('Code', tableLeft, y);
        doc.text('Libellé', tableLeft + colWidths[0], y);
        doc.text('Type', tableLeft + colWidths[0] + colWidths[1], y);
        doc.text(
          'Débit',
          tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
          y
        );
        doc.text(
          'Crédit',
          tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3],
          y
        );

        // Ligne horizontale après les en-têtes
        doc
          .moveTo(tableLeft, y + rowHeight)
          .lineTo(
            tableLeft + colWidths.reduce((a, b) => a + b, 0),
            y + rowHeight
          )
          .stroke();

        y += rowHeight;
      }

      // Écrire les données du journal
      doc.fontSize(8);
      doc.text(journal.code, tableLeft, y);
      doc.text(journal.libelle, tableLeft + colWidths[0], y, {
        width: colWidths[1]
      });
      doc.text(journal.type, tableLeft + colWidths[0] + colWidths[1], y);
      doc.text(
        journal.totalDebit.toFixed(2),
        tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
        y,
        { align: 'right' }
      );
      doc.text(
        journal.totalCredit.toFixed(2),
        tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3],
        y,
        { align: 'right' }
      );

      // Ligne horizontale après chaque ligne
      doc
        .moveTo(tableLeft, y + rowHeight)
        .lineTo(tableLeft + colWidths.reduce((a, b) => a + b, 0), y + rowHeight)
        .stroke();

      y += rowHeight;
    }

    // Ligne de total
    doc.fontSize(9).font('Helvetica-Bold');
    doc.text('TOTAUX', tableLeft + colWidths[0], y);
    doc.text(
      centralisateur.totaux.totalDebit.toFixed(2),
      tableLeft + colWidths[0] + colWidths[1] + colWidths[2],
      y,
      { align: 'right' }
    );
    doc.text(
      centralisateur.totaux.totalCredit.toFixed(2),
      tableLeft + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3],
      y,
      { align: 'right' }
    );
    doc.font('Helvetica');

    // Finaliser le document
    doc.end();

    return new Promise((resolve, reject) => {
      stream.on('finish', resolve);
      stream.on('error', reject);
    });
  }

  /**
   * Formate une date au format DD/MM/YYYY
   * @param {Date} date - Date à formater
   * @returns {string} Date formatée
   */
  formatDate(date) {
    if (!date) return '';

    const d = new Date(date);
    const jour = d.getDate().toString().padStart(2, '0');
    const mois = (d.getMonth() + 1).toString().padStart(2, '0');
    const annee = d.getFullYear();

    return `${jour}/${mois}/${annee}`;
  }
}

module.exports = EtatService;
