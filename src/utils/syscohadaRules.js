'use strict';

/**
 * R<PERSON>gles et constantes SYSCOHADA pour validation comptable
 */

/**
 * Classes de comptes SYSCOHADA avec leurs règles
 */
const CLASSES_COMPTES = {
  '1': {
    nom: 'Comptes de ressources durables',
    description: 'Capital, réserves, emprunts',
    nature: 'PASSIF',
    sensNormal: 'CREDIT',
    sousClasses: {
      '10': 'Capital et réserves',
      '11': 'Report à nouveau',
      '12': 'Résultat de l\'exercice',
      '13': 'Subventions d\'investissement',
      '14': 'Provisions réglementées',
      '15': 'Provisions pour risques et charges',
      '16': 'Emprunts et dettes assimilées',
      '17': 'Dettes rattachées à des participations',
      '18': 'Comptes de liaison des établissements',
      '19': 'Provisions pour dépréciation des immobilisations'
    }
  },
  '2': {
    nom: 'Comptes d\'actif immobilisé',
    description: 'Immobilisations incorporelles, corporelles et financières',
    nature: 'ACTIF',
    sensNormal: 'DEBIT',
    sousClasses: {
      '20': 'Charges immobilisées',
      '21': 'Immobilisations incorporelles',
      '22': 'Terrains',
      '23': 'Bâtiments, installations techniques',
      '24': 'Matériel',
      '25': 'Avances et acomptes versés sur immobilisations',
      '26': 'Participations et créances rattachées',
      '27': 'Autres immobilisations financières',
      '28': 'Amortissements',
      '29': 'Provisions pour dépréciation'
    }
  },
  '3': {
    nom: 'Comptes de stocks',
    description: 'Marchandises, matières premières, produits',
    nature: 'ACTIF',
    sensNormal: 'DEBIT',
    sousClasses: {
      '31': 'Matières premières',
      '32': 'Autres approvisionnements',
      '33': 'En-cours de production',
      '34': 'Produits intermédiaires et finis',
      '35': 'Stocks provenant d\'immobilisations',
      '36': 'Produits d\'origine animale ou végétale',
      '37': 'Stocks de marchandises',
      '38': 'Stocks en cours de route',
      '39': 'Provisions pour dépréciation des stocks'
    }
  },
  '4': {
    nom: 'Comptes de tiers',
    description: 'Clients, fournisseurs, personnel, organismes sociaux',
    nature: 'MIXTE',
    sensNormal: 'VARIABLE',
    sousClasses: {
      '40': 'Fournisseurs et comptes rattachés',
      '41': 'Clients et comptes rattachés',
      '42': 'Personnel',
      '43': 'Organismes sociaux',
      '44': 'État et collectivités publiques',
      '45': 'Organismes internationaux',
      '46': 'Associés et groupe',
      '47': 'Débiteurs et créditeurs divers',
      '48': 'Créances et dettes d\'exploitation',
      '49': 'Provisions pour dépréciation des comptes de tiers'
    }
  },
  '5': {
    nom: 'Comptes de trésorerie',
    description: 'Banques, caisses, valeurs mobilières',
    nature: 'ACTIF',
    sensNormal: 'DEBIT',
    sousClasses: {
      '50': 'Valeurs mobilières de placement',
      '51': 'Banques, établissements financiers',
      '52': 'Banques',
      '53': 'Établissements financiers',
      '54': 'Instruments de trésorerie',
      '56': 'Banques, crédit de trésorerie',
      '57': 'Caisse',
      '58': 'Virements internes',
      '59': 'Provisions pour dépréciation'
    }
  },
  '6': {
    nom: 'Comptes de charges',
    description: 'Achats, services extérieurs, charges de personnel',
    nature: 'CHARGE',
    sensNormal: 'DEBIT',
    sousClasses: {
      '60': 'Achats et variations de stocks',
      '61': 'Transports',
      '62': 'Services extérieurs A',
      '63': 'Services extérieurs B',
      '64': 'Impôts et taxes',
      '65': 'Autres charges',
      '66': 'Charges de personnel',
      '67': 'Frais financiers',
      '68': 'Dotations aux amortissements',
      '69': 'Dotations aux provisions'
    }
  },
  '7': {
    nom: 'Comptes de produits',
    description: 'Ventes, prestations, produits financiers',
    nature: 'PRODUIT',
    sensNormal: 'CREDIT',
    sousClasses: {
      '70': 'Ventes',
      '71': 'Subventions d\'exploitation',
      '72': 'Production immobilisée',
      '73': 'Variations des stocks de produits',
      '74': 'Autres produits',
      '75': 'Autres produits',
      '76': 'Produits financiers',
      '77': 'Revenus des cessions d\'immobilisations',
      '78': 'Reprises de provisions',
      '79': 'Transferts de charges'
    }
  },
  '8': {
    nom: 'Comptes spéciaux',
    description: 'Engagements, résultats en instance',
    nature: 'SPECIAL',
    sensNormal: 'VARIABLE',
    sousClasses: {
      '80': 'Comptes spéciaux',
      '81': 'Valeurs à l\'encaissement',
      '82': 'Engagements à recevoir et à donner',
      '83': 'Engagements sur opérations de change',
      '84': 'Instruments de trésorerie en devises',
      '85': 'Virements de fonds',
      '86': 'Biens et valeurs en dépôt',
      '87': 'Biens et valeurs à l\'encaissement',
      '88': 'Résultats en instance d\'affectation',
      '89': 'Bilan d\'ouverture et de clôture'
    }
  }
};

/**
 * Types de journaux SYSCOHADA obligatoires
 */
const JOURNAUX_OBLIGATOIRES = {
  'AC': {
    nom: 'Journal des achats',
    description: 'Enregistrement des factures d\'achats',
    comptesObligatoires: ['6'], // Charges
    comptesAutorises: ['4', '5', '6'], // Fournisseurs, trésorerie, charges
    comptesInterdits: ['7'], // Pas de produits
    controles: ['presence_fournisseur', 'coherence_tva']
  },
  'VT': {
    nom: 'Journal des ventes',
    description: 'Enregistrement des factures de ventes',
    comptesObligatoires: ['7'], // Produits
    comptesAutorises: ['4', '5', '7'], // Clients, trésorerie, produits
    comptesInterdits: ['6'], // Pas de charges
    controles: ['presence_client', 'coherence_tva']
  },
  'BQ': {
    nom: 'Journal de banque',
    description: 'Mouvements des comptes bancaires',
    comptesObligatoires: ['52'], // Banques
    comptesAutorises: ['1', '2', '3', '4', '5', '6', '7'], // Tous comptes
    comptesInterdits: [],
    controles: ['presence_banque', 'coherence_solde']
  },
  'CA': {
    nom: 'Journal de caisse',
    description: 'Mouvements de la caisse',
    comptesObligatoires: ['57'], // Caisse
    comptesAutorises: ['1', '2', '3', '4', '5', '6', '7'], // Tous comptes
    comptesInterdits: [],
    controles: ['presence_caisse', 'solde_positif']
  },
  'OD': {
    nom: 'Journal des opérations diverses',
    description: 'Autres opérations comptables',
    comptesObligatoires: [],
    comptesAutorises: ['1', '2', '3', '4', '5', '6', '7', '8'], // Tous comptes
    comptesInterdits: [],
    controles: ['equilibre_strict']
  }
};

/**
 * Taux de TVA par pays SYSCOHADA
 */
const TAUX_TVA = {
  'BJ': { normal: 18, reduit: 0 }, // Bénin
  'BF': { normal: 18, reduit: 0 }, // Burkina Faso
  'CM': { normal: 19.25, reduit: 5.5 }, // Cameroun
  'CF': { normal: 19, reduit: 5 }, // Centrafrique
  'KM': { normal: 10, reduit: 0 }, // Comores
  'CG': { normal: 18, reduit: 0 }, // Congo
  'CI': { normal: 18, reduit: 9 }, // Côte d'Ivoire
  'GA': { normal: 18, reduit: 0 }, // Gabon
  'GN': { normal: 18, reduit: 0 }, // Guinée
  'GW': { normal: 15, reduit: 0 }, // Guinée-Bissau
  'GQ': { normal: 15, reduit: 0 }, // Guinée Équatoriale
  'ML': { normal: 18, reduit: 0 }, // Mali
  'NE': { normal: 19, reduit: 0 }, // Niger
  'SN': { normal: 18, reduit: 10 }, // Sénégal
  'TD': { normal: 18, reduit: 0 }, // Tchad
  'TG': { normal: 18, reduit: 0 }, // Togo
  'DEFAULT': { normal: 18, reduit: 0 } // Par défaut
};

/**
 * Devises officielles SYSCOHADA
 */
const DEVISES_SYSCOHADA = {
  'XOF': {
    nom: 'Franc CFA BCEAO',
    pays: ['BJ', 'BF', 'CI', 'GW', 'ML', 'NE', 'SN', 'TG'],
    symbole: 'F CFA',
    decimales: 0,
    unite: 'franc'
  },
  'XAF': {
    nom: 'Franc CFA BEAC',
    pays: ['CM', 'CF', 'TD', 'CG', 'GQ', 'GA'],
    symbole: 'F CFA',
    decimales: 0,
    unite: 'franc'
  },
  'KMF': {
    nom: 'Franc Comorien',
    pays: ['KM'],
    symbole: 'CF',
    decimales: 0,
    unite: 'franc'
  },
  'GNF': {
    nom: 'Franc Guinéen',
    pays: ['GN'],
    symbole: 'FG',
    decimales: 0,
    unite: 'franc'
  }
};

/**
 * Règles de validation SYSCOHADA
 */
const REGLES_VALIDATION = {
  // Équilibre comptable
  equilibre: {
    tolerance: 0.01, // 1 centime
    obligatoire: true
  },
  
  // Nombre minimum de lignes
  lignesMinimum: 2,
  
  // Longueurs des champs
  longueurs: {
    libelle: { min: 3, max: 255 },
    reference: { min: 1, max: 50 },
    pieceJustificative: { min: 1, max: 100 },
    numeroCompte: { min: 6, max: 10 }
  },
  
  // Montants
  montants: {
    minimum: 0.01,
    maximum: {
      'XOF': 999999999999.99,
      'XAF': 999999999999.99,
      'EUR': 99999999.99,
      'USD': 99999999.99
    },
    decimales: 2
  },
  
  // Dates
  dates: {
    futurInterdite: true,
    exerciceClos: false
  }
};

/**
 * Messages d'erreur standardisés
 */
const MESSAGES_ERREUR = {
  EQUILIBRE_REQUIS: 'L\'écriture doit être équilibrée (débit = crédit)',
  LIGNES_INSUFFISANTES: 'Une écriture doit avoir au moins 2 lignes',
  DATE_FUTURE: 'La date d\'écriture ne peut pas être dans le futur',
  EXERCICE_FERME: 'L\'exercice comptable est fermé',
  COMPTE_INEXISTANT: 'Le compte comptable n\'existe pas',
  COMPTE_INACTIF: 'Le compte comptable est inactif',
  MONTANT_NEGATIF: 'Les montants ne peuvent pas être négatifs',
  DEBIT_ET_CREDIT: 'Une ligne ne peut pas avoir à la fois un débit et un crédit',
  MONTANT_NUL: 'Une ligne doit avoir soit un débit soit un crédit',
  LIBELLE_REQUIS: 'Le libellé est obligatoire',
  JOURNAL_INACTIF: 'Le journal n\'est pas actif',
  TVA_INCOHERENTE: 'Le montant de TVA semble incohérent'
};

/**
 * Fonctions utilitaires
 */
const SyscohadaUtils = {
  /**
   * Obtient la classe d'un compte
   * @param {string} numeroCompte - Numéro du compte
   * @returns {string} Classe du compte
   */
  getClasseCompte(numeroCompte) {
    return numeroCompte ? numeroCompte.charAt(0) : null;
  },

  /**
   * Obtient les informations d'une classe de compte
   * @param {string} classe - Classe du compte (1-8)
   * @returns {Object} Informations de la classe
   */
  getInfosClasse(classe) {
    return CLASSES_COMPTES[classe] || null;
  },

  /**
   * Vérifie si un compte est de nature débit
   * @param {string} numeroCompte - Numéro du compte
   * @returns {boolean} True si nature débit
   */
  estNatureDebit(numeroCompte) {
    const classe = this.getClasseCompte(numeroCompte);
    const infos = this.getInfosClasse(classe);
    return infos && ['ACTIF', 'CHARGE'].includes(infos.nature);
  },

  /**
   * Vérifie si un compte est de nature crédit
   * @param {string} numeroCompte - Numéro du compte
   * @returns {boolean} True si nature crédit
   */
  estNatureCredit(numeroCompte) {
    const classe = this.getClasseCompte(numeroCompte);
    const infos = this.getInfosClasse(classe);
    return infos && ['PASSIF', 'PRODUIT'].includes(infos.nature);
  },

  /**
   * Obtient le taux de TVA pour un pays
   * @param {string} codePays - Code pays ISO
   * @returns {Object} Taux de TVA
   */
  getTauxTVA(codePays = 'DEFAULT') {
    return TAUX_TVA[codePays] || TAUX_TVA.DEFAULT;
  },

  /**
   * Obtient les informations d'une devise
   * @param {string} codeDevise - Code devise
   * @returns {Object} Informations devise
   */
  getInfosDevise(codeDevise) {
    return DEVISES_SYSCOHADA[codeDevise] || null;
  },

  /**
   * Vérifie si une devise est africaine SYSCOHADA
   * @param {string} codeDevise - Code devise
   * @returns {boolean} True si devise africaine
   */
  estDeviseAfricaine(codeDevise) {
    return ['XOF', 'XAF', 'KMF', 'GNF'].includes(codeDevise);
  },

  /**
   * Formate un montant selon la devise
   * @param {number} montant - Montant à formater
   * @param {string} codeDevise - Code devise
   * @returns {string} Montant formaté
   */
  formaterMontant(montant, codeDevise = 'XOF') {
    const devise = this.getInfosDevise(codeDevise);
    if (!devise) return montant.toString();

    const decimales = devise.decimales;
    const montantFormate = montant.toFixed(decimales);
    
    // Formatage avec séparateurs de milliers
    const parties = montantFormate.split('.');
    parties[0] = parties[0].replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
    
    return parties.join(',') + ' ' + devise.symbole;
  },

  /**
   * Valide le format d'un numéro de compte SYSCOHADA
   * @param {string} numeroCompte - Numéro du compte
   * @returns {Object} Résultat validation
   */
  validerNumeroCompte(numeroCompte) {
    const erreurs = [];

    if (!numeroCompte) {
      erreurs.push('Numéro de compte obligatoire');
      return { valide: false, erreurs };
    }

    // Longueur (6 à 10 caractères)
    if (numeroCompte.length < 6 || numeroCompte.length > 10) {
      erreurs.push('Le numéro de compte doit contenir entre 6 et 10 caractères');
    }

    // Format numérique
    if (!/^\d+$/.test(numeroCompte)) {
      erreurs.push('Le numéro de compte ne peut contenir que des chiffres');
    }

    // Classe valide (1-8)
    const classe = this.getClasseCompte(numeroCompte);
    if (!classe || !CLASSES_COMPTES[classe]) {
      erreurs.push('Classe de compte invalide (doit commencer par 1-8)');
    }

    return {
      valide: erreurs.length === 0,
      erreurs,
      classe,
      infosClasse: this.getInfosClasse(classe)
    };
  }
};

module.exports = {
  CLASSES_COMPTES,
  JOURNAUX_OBLIGATOIRES,
  TAUX_TVA,
  DEVISES_SYSCOHADA,
  REGLES_VALIDATION,
  MESSAGES_ERREUR,
  SyscohadaUtils
};
