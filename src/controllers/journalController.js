/**
 * Controller pour la gestion des journaux comptables
 * API Comptabilité SYSCOHADA
 */

const { Journal, CompteComptable, Societe } = require('../models');
const { AppError, NotFoundError, ValidationError, ConflictError, asyncHandler } = require('../middleware/errorHandler');
const { logger } = require('../config/logger');
const SequenceService = require('../services/sequenceService');

/**
 * Récupérer tous les journaux
 */
const getAllJournaux = asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, type, societeId } = req.query;
  const offset = (page - 1) * limit;
  const where = {};

  if (type) where.type = type;
  if (societeId) where.societeId = societeId;

  const { count, rows: journaux } = await Journal.findAndCountAll({
    where,
    limit: parseInt(limit),
    offset: parseInt(offset),
    include: [
      {
        model: CompteComptable,
        as: 'compteContropartieDetail',
        attributes: ['numero', 'libelle', 'nature']
      },
      {
        model: Societe,
        as: 'societe',
        attributes: ['id', 'nom']
      }
    ],
    order: [['code', 'ASC']]
  });

  // Ajouter des informations calculées
  const journauxData = journaux.map(journal => {
    const data = journal.toJSON();
    data.descriptionType = journal.getDescriptionType();
    data.requiresCompteContropartie = journal.requiresCompteContropartie();
    return data;
  });

  logger.info('Journaux récupérés', {
    count,
    filtres: { type, societeId }
  });

  res.json({
    success: true,
    data: journauxData,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count,
      pages: Math.ceil(count / limit)
    }
  });
});

/**
 * Récupérer un journal par code
 */
const getJournalByCode = asyncHandler(async (req, res) => {
  const { code } = req.params;

  const journal = await Journal.findByPk(code, {
    include: [
      {
        model: CompteComptable,
        as: 'compteContropartieDetail',
        attributes: ['numero', 'libelle', 'nature', 'sens']
      },
      {
        model: Societe,
        as: 'societe',
        attributes: ['id', 'nom', 'devise']
      }
    ]
  });

  if (!journal) {
    throw new NotFoundError('Journal');
  }

  // Ajouter des informations calculées
  const journalData = journal.toJSON();
  journalData.descriptionType = journal.getDescriptionType();
  journalData.requiresCompteContropartie = journal.requiresCompteContropartie();

  logger.info('Journal récupéré', { code });

  res.json({
    success: true,
    data: journalData
  });
});

/**
 * Créer un nouveau journal
 */
const createJournal = asyncHandler(async (req, res) => {
  const journalData = req.body;

  // Vérifier si le code existe déjà
  const existingJournal = await Journal.findByPk(journalData.code);
  if (existingJournal) {
    throw new ConflictError('Un journal avec ce code existe déjà');
  }

  // Vérifier que le compte de contrepartie existe si spécifié
  if (journalData.compteContropartie) {
    const compte = await CompteComptable.findByPk(journalData.compteContropartie);
    if (!compte) {
      throw new ValidationError('Le compte de contrepartie spécifié n\'existe pas');
    }
  }

  // Vérifier que la société existe si spécifiée
  if (journalData.societeId) {
    const societe = await Societe.findByPk(journalData.societeId);
    if (!societe) {
      throw new ValidationError('La société spécifiée n\'existe pas');
    }
  }

  const journal = await Journal.create(journalData);

  logger.info('Journal créé', {
    code: journal.code,
    libelle: journal.libelle,
    type: journal.type
  });

  res.status(201).json({
    success: true,
    message: 'Journal créé avec succès',
    data: journal
  });
});

/**
 * Mettre à jour un journal
 */
const updateJournal = asyncHandler(async (req, res) => {
  const { code } = req.params;
  const updateData = req.body;

  const journal = await Journal.findByPk(code);
  if (!journal) {
    throw new NotFoundError('Journal');
  }

  // Ne pas permettre la modification du code
  if (updateData.code && updateData.code !== code) {
    throw new ValidationError('Le code du journal ne peut pas être modifié');
  }

  // Vérifier le compte de contrepartie si modifié
  if (updateData.compteContropartie) {
    const compte = await CompteComptable.findByPk(updateData.compteContropartie);
    if (!compte) {
      throw new ValidationError('Le compte de contrepartie spécifié n\'existe pas');
    }
  }

  await journal.update(updateData);

  logger.info('Journal mis à jour', {
    code,
    modifications: Object.keys(updateData)
  });

  res.json({
    success: true,
    message: 'Journal mis à jour avec succès',
    data: journal
  });
});

/**
 * Supprimer un journal
 */
const deleteJournal = asyncHandler(async (req, res) => {
  const { code } = req.params;

  const journal = await Journal.findByPk(code);
  if (!journal) {
    throw new NotFoundError('Journal');
  }

  // TODO: Vérifier s'il y a des écritures liées quand le modèle sera disponible

  await journal.destroy();

  logger.warn('Journal supprimé', {
    code,
    libelle: journal.libelle
  });

  res.json({
    success: true,
    message: 'Journal supprimé avec succès'
  });
});

/**
 * Récupérer les journaux par type
 */
const getJournauxByType = asyncHandler(async (req, res) => {
  const { type } = req.params;

  const journaux = await Journal.findAll({
    where: { type: type.toUpperCase() },
    include: [
      {
        model: CompteComptable,
        as: 'compteContropartieDetail',
        attributes: ['numero', 'libelle']
      }
    ],
    order: [['code', 'ASC']]
  });

  res.json({
    success: true,
    data: {
      type: type.toUpperCase(),
      journaux
    }
  });
});

/**
 * Récupérer les types de journaux disponibles
 */
const getTypesJournaux = asyncHandler(async (req, res) => {
  const types = [
    {
      code: 'BANQUE',
      libelle: 'Journal de banque',
      description: 'Opérations bancaires',
      requiresContropartie: true
    },
    {
      code: 'VENTE',
      libelle: 'Journal des ventes',
      description: 'Facturation clients',
      requiresContropartie: true
    },
    {
      code: 'ACHAT',
      libelle: 'Journal des achats',
      description: 'Facturation fournisseurs',
      requiresContropartie: true
    },
    {
      code: 'CAISSE',
      libelle: 'Journal de caisse',
      description: 'Opérations de caisse',
      requiresContropartie: true
    },
    {
      code: 'OD',
      libelle: 'Journal des opérations diverses',
      description: 'Écritures diverses',
      requiresContropartie: false
    }
  ];

  res.json({
    success: true,
    data: types
  });
});

/**
 * Obtenir les statistiques d'un journal
 */
const getJournalStats = asyncHandler(async (req, res) => {
  const { code } = req.params;

  const journal = await Journal.findByPk(code);
  if (!journal) {
    throw new NotFoundError('Journal');
  }

  const stats = {
    // TODO: Ajouter les statistiques réelles quand les modèles d'écritures seront disponibles
    ecritures: 0,
    montantTotal: 0,
    derniereMouvement: null
  };

  res.json({
    success: true,
    data: {
      journal: {
        code: journal.code,
        libelle: journal.libelle,
        type: journal.type
      },
      statistiques: stats
    }
  });
});

/**
 * Générer le prochain numéro pour un journal
 */
const genererProchainNumero = asyncHandler(async (req, res) => {
  const { code } = req.params;

  const sequenceService = new SequenceService(require('../models'));
  const numeroGenere = await sequenceService.genererProchainNumero(code);

  logger.info('Numéro généré via API', { journalCode: code, numero: numeroGenere });

  res.json({
    success: true,
    data: {
      journalCode: code,
      numeroGenere,
      timestamp: new Date()
    }
  });
});

/**
 * Obtenir les statistiques de séquence d'un journal
 */
const getSequenceStats = asyncHandler(async (req, res) => {
  const { code } = req.params;

  const sequenceService = new SequenceService(require('../models'));
  const statistiques = await sequenceService.getStatistiquesSequence(code);

  res.json({
    success: true,
    data: statistiques
  });
});

/**
 * Configurer les paramètres de séquence d'un journal
 */
const configurerSequence = asyncHandler(async (req, res) => {
  const { code } = req.params;
  const { prefixeNumero, longueurNumero, resetSequence } = req.body;

  const sequenceService = new SequenceService(require('../models'));
  const journalMisAJour = await sequenceService.configurerSequence(code, {
    prefixeNumero,
    longueurNumero,
    resetSequence
  });

  logger.info('Configuration de séquence mise à jour', {
    journalCode: code,
    nouveauxParametres: { prefixeNumero, longueurNumero, resetSequence }
  });

  res.json({
    success: true,
    message: 'Configuration de séquence mise à jour avec succès',
    data: journalMisAJour
  });
});

/**
 * Réinitialiser la séquence d'un journal
 */
const resetSequence = asyncHandler(async (req, res) => {
  const { code } = req.params;

  const sequenceService = new SequenceService(require('../models'));
  const resultat = await sequenceService.resetSequence(code);

  logger.info('Séquence réinitialisée via API', { journalCode: code });

  res.json({
    success: true,
    message: 'Séquence réinitialisée avec succès',
    data: resultat
  });
});

/**
 * Obtenir les statistiques de séquences pour tous les journaux d'une société
 */
const getSequenceStatsSociete = asyncHandler(async (req, res) => {
  const { societeId } = req.params;

  const sequenceService = new SequenceService(require('../models'));
  const statistiques = await sequenceService.getStatistiquesSequencesSociete(societeId);

  res.json({
    success: true,
    data: statistiques
  });
});

/**
 * Réinitialiser automatiquement toutes les séquences qui en ont besoin
 */
const resetSequencesAutomatique = asyncHandler(async (req, res) => {
  const { societeId } = req.query;

  const sequenceService = new SequenceService(require('../models'));
  const resultat = await sequenceService.resetSequencesAutomatique(societeId);

  logger.info('Reset automatique des séquences', {
    societeId,
    nombreResets: resultat.nombreResets
  });

  res.json({
    success: true,
    message: `${resultat.nombreResets} séquence(s) réinitialisée(s) automatiquement`,
    data: resultat
  });
});

/**
 * Obtenir les journaux actifs d'une société
 */
const getJournauxActifs = asyncHandler(async (req, res) => {
  const { societeId } = req.params;

  const journaux = await Journal.getJournauxActifs(societeId);

  res.json({
    success: true,
    data: {
      societeId,
      nombreJournaux: journaux.length,
      journaux
    }
  });
});

/**
 * Activer/désactiver un journal
 */
const toggleJournalActif = asyncHandler(async (req, res) => {
  const { code } = req.params;

  const journal = await Journal.findByPk(code);
  if (!journal) {
    throw new NotFoundError('Journal');
  }

  const nouvelEtat = !journal.actif;
  await journal.update({ actif: nouvelEtat });

  logger.info('État du journal modifié', {
    journalCode: code,
    ancienEtat: !nouvelEtat,
    nouvelEtat
  });

  res.json({
    success: true,
    message: `Journal ${nouvelEtat ? 'activé' : 'désactivé'} avec succès`,
    data: {
      code: journal.code,
      libelle: journal.libelle,
      actif: nouvelEtat
    }
  });
});

module.exports = {
  getAllJournaux,
  getJournalByCode,
  createJournal,
  updateJournal,
  deleteJournal,
  getJournauxByType,
  getTypesJournaux,
  getJournalStats,
  // Nouvelles méthodes pour les séquences
  genererProchainNumero,
  getSequenceStats,
  configurerSequence,
  resetSequence,
  getSequenceStatsSociete,
  resetSequencesAutomatique,
  getJournauxActifs,
  toggleJournalActif
};
