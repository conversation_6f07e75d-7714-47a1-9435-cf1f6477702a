const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Mock des dépendances
jest.mock('fs');
jest.mock('axios');
jest.mock('inquirer');
jest.mock('ora');
jest.mock('commander', () => ({
  program: {
    name: jest.fn().mockReturnThis(),
    description: jest.fn().mockReturnThis(),
    version: jest.fn().mockReturnThis(),
    command: jest.fn().mockReturnThis(),
    option: jest.fn().mockReturnThis(),
    action: jest.fn().mockReturnThis(),
    parse: jest.fn(),
    outputHelp: jest.fn()
  }
}));

describe('CLI SYSCOHADA - Tests unitaires', () => {
  const CLI_PATH = path.join(process.cwd(), 'cli.js');
  const CONFIG_DIR = path.join(require('os').homedir(), '.syscohada-cli');
  const CONFIG_FILE = path.join(CONFIG_DIR, 'config.json');

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock de fs
    fs.existsSync.mockReturnValue(false);
    fs.mkdirSync.mockReturnValue(true);
    fs.readFileSync.mockReturnValue(JSON.stringify({
      apiUrl: 'http://localhost:3000/api/v1',
      apiKey: 'sk_test_123456789',
      currentSociete: null
    }));
    fs.writeFileSync.mockReturnValue(true);

    // Mock d'axios
    axios.create.mockReturnValue({
      get: jest.fn(),
      post: jest.fn(),
      defaults: { baseURL: 'http://localhost:3000/api/v1' },
      interceptors: { request: { use: jest.fn() } }
    });
  });

  describe('Configuration CLI base', () => {
    test('devrait valider les chemins de configuration', () => {
      expect(CONFIG_DIR).toContain('.syscohada-cli');
      expect(CONFIG_FILE).toContain('config.json');
    });

    test('devrait créer le dossier de configuration s\'il n\'existe pas', () => {
      fs.existsSync.mockReturnValue(false);
      fs.mkdirSync.mockReturnValue(true);
      
      // Simuler la logique de création du dossier
      if (!fs.existsSync(CONFIG_DIR)) {
        fs.mkdirSync(CONFIG_DIR, { recursive: true });
      }
      
      expect(fs.mkdirSync).toHaveBeenCalledWith(CONFIG_DIR, { recursive: true });
    });

    test('devrait charger la configuration existante', () => {
      fs.existsSync.mockReturnValue(true);
      const mockConfig = {
        apiUrl: 'http://api.test.com/v1',
        apiKey: 'sk_existing_key',
        currentSociete: { id: 1, nom: 'Test Corp' }
      };
      fs.readFileSync.mockReturnValue(JSON.stringify(mockConfig));

      // Simuler le chargement de configuration
      const config = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));

      expect(fs.readFileSync).toHaveBeenCalledWith(CONFIG_FILE, 'utf8');
      expect(config).toEqual(mockConfig);
    });

    test('devrait sauvegarder la configuration correctement', () => {
      const mockConfig = {
        apiUrl: 'http://localhost:3000/api/v1',
        apiKey: 'sk_new_key',
        currentSociete: null
      };

      fs.writeFileSync.mockImplementation((file, data) => {
        expect(file).toBe(CONFIG_FILE);
        expect(JSON.parse(data)).toEqual(mockConfig);
      });

      // Simuler la sauvegarde
      fs.writeFileSync(CONFIG_FILE, JSON.stringify(mockConfig, null, 2));
    });
  });

  describe('Authentification valide', () => {
    test('devrait valider une clé API correcte', async () => {
      const mockApiResponse = {
        data: {
          data: {
            apiKey: {
              id: 1,
              name: 'Test Key',
              prefix: 'sk_test',
              permissions: ['read', 'write']
            }
          }
        }
      };

      const mockApi = {
        get: jest.fn().mockResolvedValue(mockApiResponse)
      };
      axios.create.mockReturnValue(mockApi);

      // Simuler la vérification d'une clé API
      const response = await mockApi.get('/auth/verify');
      
      expect(response.data.data.apiKey).toHaveProperty('name', 'Test Key');
      expect(response.data.data.apiKey.permissions).toContain('read');
    });

    test('devrait stocker la clé API validée', () => {
      const testKey = 'sk_test_valid_key_123';
      global.apiKey = testKey;

      expect(global.apiKey).toBe(testKey);
    });
  });

  describe('Statut API accessible', () => {
    test('devrait vérifier le statut de l\'API', async () => {
      const mockStatusResponse = {
        data: {
          message: 'API Comptabilité SYSCOHADA - Opérationnelle',
          version: '1.0.0',
          environment: 'development'
        }
      };

      const mockApi = {
        get: jest.fn().mockResolvedValue(mockStatusResponse)
      };
      axios.create.mockReturnValue(mockApi);

      const response = await mockApi.get('/');
      
      expect(response.data.message).toContain('SYSCOHADA');
      expect(response.data.version).toBe('1.0.0');
    });
  });

  describe('Liste des clés API', () => {
    test('devrait récupérer la liste des clés API', async () => {
      const mockKeysResponse = {
        data: {
          data: [
            {
              prefix: 'sk_test',
              name: 'Test Key 1',
              permissions: ['read'],
              isActive: true,
              lastUsedAt: '2024-01-01T10:00:00Z',
              expiresAt: null
            },
            {
              prefix: 'sk_prod',
              name: 'Production Key',
              permissions: ['read', 'write', 'admin'],
              isActive: true,
              lastUsedAt: '2024-01-02T15:30:00Z',
              expiresAt: '2024-12-31T23:59:59Z'
            }
          ],
          pagination: {
            page: 1,
            totalPages: 1,
            total: 2
          }
        }
      };

      const mockApi = {
        get: jest.fn().mockResolvedValue(mockKeysResponse)
      };
      axios.create.mockReturnValue(mockApi);

      const response = await mockApi.get('/api-keys?includeInactive=true&limit=20');
      
      expect(response.data.data).toHaveLength(2);
      expect(response.data.data[0]).toHaveProperty('prefix', 'sk_test');
      expect(response.data.data[1].permissions).toContain('admin');
    });
  });

  describe('Création nouvelle clé API', () => {
    test('devrait créer une nouvelle clé API', async () => {
      const mockCreateResponse = {
        data: {
          data: {
            id: 3,
            name: 'New Test Key',
            prefix: 'sk_new',
            permissions: ['read', 'write'],
            key: 'sk_new_complete_key_token_123456789',
            expiresAt: null
          }
        }
      };

      const mockApi = {
        post: jest.fn().mockResolvedValue(mockCreateResponse)
      };
      axios.create.mockReturnValue(mockApi);

      const payload = {
        name: 'New Test Key',
        permissions: ['read', 'write'],
        metadata: {}
      };

      const response = await mockApi.post('/api-keys', payload);
      
      expect(response.data.data).toHaveProperty('name', 'New Test Key');
      expect(response.data.data.key).toMatch(/^sk_new_/);
      expect(response.data.data.permissions).toEqual(['read', 'write']);
    });
  });

  describe('Clé API invalide', () => {
    test('devrait rejeter une clé API malformée', () => {
      const invalidKeys = [
        '',
        'invalid_key',
        'sk_',
        'api_key_without_prefix',
        null,
        undefined
      ];

      invalidKeys.forEach(key => {
        const isValid = key && key.startsWith('sk_') && key.length > 3;
        expect(isValid).toBeFalsy();
      });
    });

    test('devrait gérer l\'erreur d\'authentification', async () => {
      const mockApi = {
        get: jest.fn().mockRejectedValue({
          response: {
            status: 401,
            data: { error: 'Clé API invalide' }
          }
        })
      };
      axios.create.mockReturnValue(mockApi);

      try {
        await mockApi.get('/auth/verify');
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.error).toBe('Clé API invalide');
      }
    });
  });

  describe('URL API inexistante', () => {
    test('devrait gérer l\'erreur de connexion', async () => {
      const mockApi = {
        get: jest.fn().mockRejectedValue({
          code: 'ECONNREFUSED',
          message: 'connect ECONNREFUSED 127.0.0.1:3000'
        })
      };
      axios.create.mockReturnValue(mockApi);

      try {
        await mockApi.get('/');
      } catch (error) {
        expect(error.code).toBe('ECONNREFUSED');
        expect(error.message).toContain('ECONNREFUSED');
      }
    });

    test('devrait gérer l\'erreur de timeout', async () => {
      const mockApi = {
        get: jest.fn().mockRejectedValue({
          code: 'ECONNABORTED',
          message: 'timeout of 10000ms exceeded'
        })
      };
      axios.create.mockReturnValue(mockApi);

      try {
        await mockApi.get('/');
      } catch (error) {
        expect(error.code).toBe('ECONNABORTED');
        expect(error.message).toContain('timeout');
      }
    });
  });

  describe('Permissions insuffisantes admin', () => {
    test('devrait rejeter l\'accès aux fonctions admin', async () => {
      const mockApi = {
        get: jest.fn().mockRejectedValue({
          response: {
            status: 403,
            data: { error: 'Permission admin requise' }
          }
        })
      };
      axios.create.mockReturnValue(mockApi);

      try {
        await mockApi.get('/api-keys');
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data.error).toBe('Permission admin requise');
      }
    });

    test('devrait rejeter la création de clé sans permission', async () => {
      const mockApi = {
        post: jest.fn().mockRejectedValue({
          response: {
            status: 403,
            data: { error: 'Permission admin requise pour cette commande' }
          }
        })
      };
      axios.create.mockReturnValue(mockApi);

      try {
        await mockApi.post('/api-keys', {
          name: 'Test Key',
          permissions: ['read']
        });
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data.error).toContain('admin requise');
      }
    });
  });

  describe('Tests d\'intégration CLI', () => {
    test('devrait définir les bonnes métadonnées CLI', () => {
      const expectedCommands = [
        'config',
        'auth:setup',
        'auth:verify',
        'auth:clear',
        'status',
        'apikeys:list',
        'apikeys:create',
        'societes:list',
        'ecritures:list',
        'etats:generer'
      ];

      // Simuler que toutes les commandes attendues sont présentes
      expectedCommands.forEach(command => {
        expect(command).toBeTruthy();
        expect(command.length).toBeGreaterThan(0);
      });
    });

    test('devrait avoir une structure CLI cohérente', () => {
      const cliInfo = {
        name: 'syscohada-cli',
        description: 'CLI pour interagir avec l\'API Comptabilité SYSCOHADA',
        version: '1.0.0'
      };

      expect(cliInfo.name).toBe('syscohada-cli');
      expect(cliInfo.description).toContain('SYSCOHADA');
      expect(cliInfo.version).toBe('1.0.0');
    });
  });

  describe('Utilitaires CLI', () => {
    test('devrait valider le middleware requireAuth', () => {
      const mockCallback = jest.fn();
      global.apiKey = null;

      const requireAuth = (callback) => {
        return (...args) => {
          if (!global.apiKey) {
            return false; // Simulation de l'erreur
          }
          callback(...args);
        };
      };

      const wrappedCallback = requireAuth(mockCallback);
      const result = wrappedCallback();

      expect(result).toBeFalsy();
      expect(mockCallback).not.toHaveBeenCalled();
    });

    test('devrait exécuter le callback avec une clé API valide', () => {
      const mockCallback = jest.fn();
      global.apiKey = 'sk_test_valid';

      const requireAuth = (callback) => {
        return (...args) => {
          if (!global.apiKey) {
            return false;
          }
          callback(...args);
          return true;
        };
      };

      const wrappedCallback = requireAuth(mockCallback);
      const result = wrappedCallback('arg1', 'arg2');

      expect(result).toBeTruthy();
      expect(mockCallback).toHaveBeenCalledWith('arg1', 'arg2');
    });
  });
});