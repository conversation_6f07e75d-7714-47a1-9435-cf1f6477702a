/**
 * Middleware de validation des données avec Joi
 * API Comptabilité SYSCOHADA
 */

const Joi = require('joi');
const { logger } = require('../config/logger');
const { validationResult } = require('express-validator');

/**
 * Middleware de validation générique
 * @param {Object} schema - Schéma Joi de validation
 * @param {string} source - Source des données à valider ('body', 'params', 'query')
 */
const validate = (schema, source = 'body') => {
  return (req, res, next) => {
    const dataToValidate = req[source];
    
    const { error, value } = schema.validate(dataToValidate, {
      abortEarly: false, // Retourner toutes les erreurs
      stripUnknown: true, // Supprimer les champs non définis
      convert: true // Convertir les types automatiquement
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      logger.warn('Validation Error', {
        source: source,
        errors: errorDetails,
        originalData: dataToValidate
      });

      return res.status(400).json({
        error: 'Erreur de validation',
        details: errorDetails
      });
    }

    // Remplacer les données par les valeurs validées et nettoyées
    req[source] = value;
    next();
  };
};

/**
 * Schémas de validation communs
 */
const commonSchemas = {
  // Validation d'un ID UUID
  id: Joi.string().uuid().required().messages({
    'string.guid': 'L\'ID doit être un UUID valide',
    'any.required': 'L\'ID est requis'
  }),

  // Validation d'une pagination
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('asc')
  }),

  // Validation d'une date
  date: Joi.date().iso().messages({
    'date.format': 'La date doit être au format ISO (YYYY-MM-DD)',
    'date.base': 'Une date valide est requise'
  }),

  // Validation d'un montant comptable
  montant: Joi.number().precision(2).positive().messages({
    'number.positive': 'Le montant doit être positif',
    'number.precision': 'Le montant ne peut avoir plus de 2 décimales'
  }),

  // Validation d'un numéro de compte SYSCOHADA
  numeroCompte: Joi.string().pattern(/^[1-8]\d{2,9}$/).messages({
    'string.pattern.base': 'Le numéro de compte doit commencer par un chiffre de 1 à 8 et contenir 3 à 10 chiffres'
  }),

  // Validation d'un code journal
  codeJournal: Joi.string().length(2).uppercase().messages({
    'string.length': 'Le code journal doit contenir exactement 2 caractères',
    'string.uppercase': 'Le code journal doit être en majuscules'
  }),

  // Validation d'un exercice comptable
  exercice: Joi.number().integer().min(2000).max(2100).messages({
    'number.min': 'L\'exercice doit être supérieur à 2000',
    'number.max': 'L\'exercice doit être inférieur à 2100'
  })
};

/**
 * Schémas de validation pour les entités métier
 */
const schemas = {
  // Validation d'une société
  societe: {
    create: Joi.object({
      nom: Joi.string().min(2).max(100).required().messages({
        'string.min': 'Le nom doit contenir au moins 2 caractères',
        'string.max': 'Le nom ne peut dépasser 100 caractères',
        'any.required': 'Le nom de la société est requis'
      }),
      adresse: Joi.string().max(500).optional(),
      telephone: Joi.string().pattern(/^[\d\s\-+()]+$/).max(20).optional().messages({
        'string.pattern.base': 'Le téléphone contient des caractères invalides'
      }),
      email: Joi.string().email().max(50).optional().messages({
        'string.email': 'L\'email doit être valide'
      }),
      numeroContribuable: Joi.string().max(50).optional(),
      formeJuridique: Joi.string().max(50).optional(),
      capital: commonSchemas.montant.optional(),
      exerciceDebut: commonSchemas.date.required(),
      exerciceFin: commonSchemas.date.required(),
      devise: Joi.string().length(3).uppercase().default('XOF')
    }),
    
    update: Joi.object({
      nom: Joi.string().min(2).max(100).optional(),
      adresse: Joi.string().max(500).optional(),
      telephone: Joi.string().pattern(/^[\d\s\-+()]+$/).max(20).optional(),
      email: Joi.string().email().max(50).optional(),
      numeroContribuable: Joi.string().max(50).optional(),
      formeJuridique: Joi.string().max(50).optional(),
      capital: commonSchemas.montant.optional(),
      exerciceDebut: commonSchemas.date.optional(),
      exerciceFin: commonSchemas.date.optional(),
      devise: Joi.string().length(3).uppercase().optional()
    })
  },

  // Validation d'un compte comptable
  compte: {
    create: Joi.object({
      numero: commonSchemas.numeroCompte.required(),
      libelle: Joi.string().min(2).max(100).required(),
      classe: Joi.number().integer().min(1).max(8).required(),
      nature: Joi.string().valid('ACTIF', 'PASSIF', 'CHARGE', 'PRODUIT').required(),
      sens: Joi.string().valid('DEBIT', 'CREDIT').required(),
      niveau: Joi.number().integer().min(1).max(5).required(),
      compteParent: commonSchemas.numeroCompte.optional()
    })
  },

  // Validation d'une écriture comptable
  ecriture: {
    create: Joi.object({
      dateEcriture: commonSchemas.date.required(),
      journalCode: commonSchemas.codeJournal.required(),
      libelle: Joi.string().min(2).max(200).required(),
      reference: Joi.string().max(50).optional(),
      lignes: Joi.array().min(2).items(
        Joi.object({
          compteNumero: commonSchemas.numeroCompte.required(),
          libelle: Joi.string().max(200).optional(),
          montantDebit: commonSchemas.montant.default(0),
          montantCredit: commonSchemas.montant.default(0)
        })
      ).required()
    })
  }
};

/**
 * Validation personnalisée pour l'équilibre des écritures
 */
const validateEquilibreEcriture = (req, res, next) => {
  const { lignes } = req.body;
  
  if (!lignes || !Array.isArray(lignes)) {
    return next();
  }

  const totalDebit = lignes.reduce((sum, ligne) => sum + (parseFloat(ligne.montantDebit) || 0), 0);
  const totalCredit = lignes.reduce((sum, ligne) => sum + (parseFloat(ligne.montantCredit) || 0), 0);

  if (Math.abs(totalDebit - totalCredit) > 0.01) {
    logger.warn('Écriture non équilibrée', {
      totalDebit,
      totalCredit,
      difference: totalDebit - totalCredit
    });

    return res.status(400).json({
      error: 'Écriture non équilibrée',
      details: {
        totalDebit: totalDebit.toFixed(2),
        totalCredit: totalCredit.toFixed(2),
        difference: (totalDebit - totalCredit).toFixed(2)
      }
    });
  }

  next();
};

/**
 * Middleware pour gérer les erreurs de validation d'express-validator
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorDetails = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value
    }));

    logger.warn('Validation Error (express-validator)', {
      errors: errorDetails,
      path: req.path
    });

    return res.status(400).json({
      success: false,
      message: 'Erreur de validation des données',
      errors: errorDetails
    });
  }
  
  next();
};

module.exports = {
  validate,
  schemas,
  commonSchemas,
  validateEquilibreEcriture,
  handleValidationErrors
};
