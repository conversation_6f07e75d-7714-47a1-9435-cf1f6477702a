'use strict';

const ImportExportService = require('../services/importExportService');
const logger = require('../config/logger');
const { ValidationError, NotFoundError } = require('../middleware/errorHandler');

/**
 * Contrôleur pour la gestion des imports/exports d'écritures comptables
 */
class ImportExportController {
  constructor() {
    this.importExportService = new ImportExportService();
  }

  /**
   * Importe des écritures depuis un fichier Excel
   * POST /api/v1/import/excel
   */
  async importerExcel(req, res, next) {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'Aucun fichier fourni',
          code: 'FICHIER_MANQUANT'
        });
      }

      const { societeId, exerciceId } = req.body;
      const options = {
        societeId,
        exerciceId,
        remplacerExistants: req.body.remplacerExistants === 'true',
        validerUniquement: req.body.validerUniquement === 'true',
        validerEcritures: req.body.validerEcritures !== 'false'
      };

      // Validation des paramètres obligatoires
      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'L\'ID de la société est obligatoire',
          code: 'SOCIETE_MANQUANTE'
        });
      }

      const resultat = await this.importExportService.importerEcrituresExcel(
        req.file.path,
        options
      );

      logger.info('Import Excel réalisé via API', {
        utilisateur: req.user?.id,
        societeId,
        exerciceId,
        fichier: req.file.originalname,
        resultat: resultat.success
      });

      res.json({
        success: true,
        message: resultat.message,
        data: {
          ecrituresCreees: resultat.ecrituresCreees,
          lignesCreees: resultat.lignesCreees,
          validation: resultat.validation,
          erreursImport: resultat.erreursImport
        }
      });

    } catch (error) {
      logger.error('Erreur lors de l\'import Excel via API', {
        utilisateur: req.user?.id,
        fichier: req.file?.originalname,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Importe des écritures depuis un fichier CSV
   * POST /api/v1/import/csv
   */
  async importerCSV(req, res, next) {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'Aucun fichier fourni',
          code: 'FICHIER_MANQUANT'
        });
      }

      const { societeId, exerciceId } = req.body;
      const options = {
        societeId,
        exerciceId,
        remplacerExistants: req.body.remplacerExistants === 'true',
        validerUniquement: req.body.validerUniquement === 'true',
        validerEcritures: req.body.validerEcritures !== 'false',
        separateur: req.body.separateur || ';'
      };

      // Validation des paramètres obligatoires
      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'L\'ID de la société est obligatoire',
          code: 'SOCIETE_MANQUANTE'
        });
      }

      const resultat = await this.importExportService.importerEcrituresCSV(
        req.file.path,
        options
      );

      logger.info('Import CSV réalisé via API', {
        utilisateur: req.user?.id,
        societeId,
        exerciceId,
        fichier: req.file.originalname,
        resultat: resultat.success
      });

      res.json({
        success: true,
        message: resultat.message,
        data: {
          ecrituresCreees: resultat.ecrituresCreees,
          lignesCreees: resultat.lignesCreees,
          validation: resultat.validation,
          erreursImport: resultat.erreursImport
        }
      });

    } catch (error) {
      logger.error('Erreur lors de l\'import CSV via API', {
        utilisateur: req.user?.id,
        fichier: req.file?.originalname,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Exporte des écritures vers Excel
   * GET /api/v1/export/excel
   */
  async exporterExcel(req, res, next) {
    try {
      const filtres = {
        societeId: req.query.societeId,
        exerciceId: req.query.exerciceId,
        journalCode: req.query.journalCode,
        statut: req.query.statut,
        dateDebut: req.query.dateDebut,
        dateFin: req.query.dateFin
      };

      // Filtrer les valeurs undefined
      Object.keys(filtres).forEach(key => {
        if (filtres[key] === undefined || filtres[key] === '') {
          delete filtres[key];
        }
      });

      const options = {
        includeDetails: req.query.includeDetails === 'true',
        includeStatistiques: req.query.includeStatistiques === 'true',
        formatDate: req.query.formatDate || 'DD/MM/YYYY'
      };

      const resultat = await this.importExportService.exporterEcrituresExcel(filtres, options);

      logger.info('Export Excel réalisé via API', {
        utilisateur: req.user?.id,
        filtres,
        nombreEcritures: resultat.nombreEcritures
      });

      // Envoyer le fichier en téléchargement
      res.download(resultat.cheminFichier, resultat.nomFichier, (err) => {
        if (err) {
          logger.error('Erreur lors du téléchargement du fichier Excel', {
            cheminFichier: resultat.cheminFichier,
            error: err.message
          });
        }
        // Nettoyer le fichier temporaire après téléchargement
        const fs = require('fs');
        if (fs.existsSync(resultat.cheminFichier)) {
          fs.unlinkSync(resultat.cheminFichier);
        }
      });

    } catch (error) {
      logger.error('Erreur lors de l\'export Excel via API', {
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Exporte des écritures vers CSV
   * GET /api/v1/export/csv
   */
  async exporterCSV(req, res, next) {
    try {
      const filtres = {
        societeId: req.query.societeId,
        exerciceId: req.query.exerciceId,
        journalCode: req.query.journalCode,
        statut: req.query.statut,
        dateDebut: req.query.dateDebut,
        dateFin: req.query.dateFin
      };

      // Filtrer les valeurs undefined
      Object.keys(filtres).forEach(key => {
        if (filtres[key] === undefined || filtres[key] === '') {
          delete filtres[key];
        }
      });

      const options = {
        separateur: req.query.separateur || ';',
        includeDetails: req.query.includeDetails === 'true',
        formatDate: req.query.formatDate || 'DD/MM/YYYY'
      };

      const resultat = await this.importExportService.exporterEcrituresCSV(filtres, options);

      logger.info('Export CSV réalisé via API', {
        utilisateur: req.user?.id,
        filtres,
        nombreEcritures: resultat.nombreEcritures
      });

      // Envoyer le fichier en téléchargement
      res.download(resultat.cheminFichier, resultat.nomFichier, (err) => {
        if (err) {
          logger.error('Erreur lors du téléchargement du fichier CSV', {
            cheminFichier: resultat.cheminFichier,
            error: err.message
          });
        }
        // Nettoyer le fichier temporaire après téléchargement
        const fs = require('fs');
        if (fs.existsSync(resultat.cheminFichier)) {
          fs.unlinkSync(resultat.cheminFichier);
        }
      });

    } catch (error) {
      logger.error('Erreur lors de l\'export CSV via API', {
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Exporte le Fichier des Écritures Comptables (FEC)
   * GET /api/v1/export/fec/:exerciceId
   */
  async exporterFEC(req, res, next) {
    try {
      const { exerciceId } = req.params;

      if (!exerciceId) {
        return res.status(400).json({
          success: false,
          message: 'L\'ID de l\'exercice est obligatoire',
          code: 'EXERCICE_MANQUANT'
        });
      }

      const options = {
        separateur: req.query.separateur || '|',
        encodage: req.query.encodage || 'utf8',
        includeEnTetes: req.query.includeEnTetes !== 'false'
      };

      const resultat = await this.importExportService.exporterFEC(exerciceId, options);

      logger.info('Export FEC réalisé via API', {
        utilisateur: req.user?.id,
        exerciceId,
        nombreEcritures: resultat.nombreEcritures,
        nombreLignes: resultat.nombreLignes
      });

      // Envoyer le fichier en téléchargement
      res.download(resultat.cheminFichier, resultat.nomFichier, (err) => {
        if (err) {
          logger.error('Erreur lors du téléchargement du fichier FEC', {
            cheminFichier: resultat.cheminFichier,
            error: err.message
          });
        }
        // Nettoyer le fichier temporaire après téléchargement
        const fs = require('fs');
        if (fs.existsSync(resultat.cheminFichier)) {
          fs.unlinkSync(resultat.cheminFichier);
        }
      });

    } catch (error) {
      logger.error('Erreur lors de l\'export FEC via API', {
        utilisateur: req.user?.id,
        exerciceId: req.params.exerciceId,
        error: error.message
      });
      next(error);
    }
  }
}

// Créer une instance du contrôleur
const controller = new ImportExportController();

// Exporter les méthodes liées à l'instance
module.exports = {
  importerExcel: controller.importerExcel.bind(controller),
  importerCSV: controller.importerCSV.bind(controller),
  exporterExcel: controller.exporterExcel.bind(controller),
  exporterCSV: controller.exporterCSV.bind(controller),
  exporterFEC: controller.exporterFEC.bind(controller)
};
