'use strict';

const TemplateService = require('../services/templateService');
const EcritureService = require('../services/ecritureService');
const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Controller pour la gestion des templates d'écritures comptables
 */
class TemplateController {
  constructor() {
    // Les services seront injectés via les middlewares
    this.templateService = null;
    this.ecritureService = null;
  }

  /**
   * Initialise les services
   * @param {Object} models - Modèles Sequelize
   */
  initServices(models) {
    this.templateService = new TemplateService(models);
    this.ecritureService = new EcritureService(models);
  }

  /**
   * Crée un nouveau template
   * POST /api/v1/templates
   */
  async creerTemplate(req, res, next) {
    try {
      const { donnees, lignes } = req.body;

      if (!donnees || !lignes) {
        return res.status(400).json({
          success: false,
          message: 'Les données du template et les lignes sont obligatoires',
          code: 'DONNEES_MANQUANTES'
        });
      }

      // Ajouter l'utilisateur créateur
      donnees.utilisateurCreation = req.user?.id;

      const template = await this.templateService.creerTemplate(donnees, lignes);

      logger.info('Template créé via API', {
        templateId: template.id,
        nom: donnees.nom,
        utilisateur: req.user?.id
      });

      res.status(201).json({
        success: true,
        message: 'Template créé avec succès',
        data: {
          template: template.formater()
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la création du template via API', {
        body: req.body,
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Crée un template à partir d'une écriture existante
   * POST /api/v1/templates/depuis-ecriture/:ecritureId
   */
  async creerTemplateDepuisEcriture(req, res, next) {
    try {
      const { ecritureId } = req.params;
      const donneesTemplate = req.body;

      // Ajouter l'utilisateur créateur
      donneesTemplate.utilisateurCreation = req.user?.id;

      const template = await this.templateService.creerTemplateDepuisEcriture(ecritureId, donneesTemplate);

      logger.info('Template créé depuis écriture via API', {
        templateId: template.id,
        ecritureId,
        utilisateur: req.user?.id
      });

      res.status(201).json({
        success: true,
        message: 'Template créé depuis l\'écriture avec succès',
        data: {
          template: template.formater()
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la création du template depuis écriture via API', {
        ecritureId: req.params.ecritureId,
        body: req.body,
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Obtient les templates avec filtres
   * GET /api/v1/templates
   */
  async getTemplates(req, res, next) {
    try {
      const { societeId } = req.query;

      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'L\'ID de la société est obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      const filtres = {
        ...req.query,
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 50
      };

      const resultats = await this.templateService.getTemplates(societeId, filtres);

      logger.info('Templates récupérés via API', {
        societeId,
        filtres,
        total: resultats.pagination.total,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Templates récupérés avec succès',
        data: resultats
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des templates via API', {
        query: req.query,
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Obtient un template par son ID
   * GET /api/v1/templates/:id
   */
  async getTemplateById(req, res, next) {
    try {
      const { id } = req.params;

      const template = await this.templateService.getTemplateById(id);

      res.json({
        success: true,
        data: {
          template
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération du template via API', {
        templateId: req.params.id,
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Applique un template pour créer une écriture
   * POST /api/v1/templates/:id/appliquer
   */
  async appliquerTemplate(req, res, next) {
    try {
      const { id } = req.params;
      const { parametres = {}, donneesEcriture = {}, creerEcriture = false } = req.body;

      // Ajouter l'utilisateur créateur
      donneesEcriture.utilisateurCreation = req.user?.id;

      const resultatApplication = await this.templateService.appliquerTemplate(id, parametres, donneesEcriture);

      let ecriture = null;
      if (creerEcriture) {
        // Créer directement l'écriture
        ecriture = await this.ecritureService.creerEcriture(
          resultatApplication.donnees,
          resultatApplication.lignes
        );
      }

      logger.info('Template appliqué via API', {
        templateId: id,
        parametres,
        creerEcriture,
        ecritureId: ecriture?.id,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: creerEcriture ? 'Template appliqué et écriture créée' : 'Template appliqué avec succès',
        data: {
          template: resultatApplication.template,
          donnees: resultatApplication.donnees,
          lignes: resultatApplication.lignes,
          ...(ecriture && { ecriture: this.formaterEcriture(ecriture) })
        }
      });

    } catch (error) {
      logger.error('Erreur lors de l\'application du template via API', {
        templateId: req.params.id,
        body: req.body,
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Supprime un template
   * DELETE /api/v1/templates/:id
   */
  async supprimerTemplate(req, res, next) {
    try {
      const { id } = req.params;

      await this.templateService.supprimerTemplate(id);

      logger.info('Template supprimé via API', {
        templateId: id,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Template supprimé avec succès'
      });

    } catch (error) {
      logger.error('Erreur lors de la suppression du template via API', {
        templateId: req.params.id,
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Obtient les catégories de templates disponibles
   * GET /api/v1/templates/categories
   */
  async getCategories(req, res, next) {
    try {
      const categories = [
        { code: 'VENTE', libelle: 'Ventes', description: 'Templates pour les opérations de vente' },
        { code: 'ACHAT', libelle: 'Achats', description: 'Templates pour les opérations d\'achat' },
        { code: 'BANQUE', libelle: 'Banque', description: 'Templates pour les opérations bancaires' },
        { code: 'CAISSE', libelle: 'Caisse', description: 'Templates pour les opérations de caisse' },
        { code: 'PAIE', libelle: 'Paie', description: 'Templates pour les opérations de paie' },
        { code: 'AMORTISSEMENT', libelle: 'Amortissements', description: 'Templates pour les amortissements' },
        { code: 'PROVISION', libelle: 'Provisions', description: 'Templates pour les provisions' },
        { code: 'AUTRE', libelle: 'Autres', description: 'Autres templates' }
      ];

      res.json({
        success: true,
        data: {
          categories
        }
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * Formate une écriture pour l'affichage
   * @param {Object} ecriture - Écriture à formater
   * @returns {Object} Écriture formatée
   */
  formaterEcriture(ecriture) {
    return {
      id: ecriture.id,
      numeroEcriture: ecriture.numeroEcriture,
      dateEcriture: ecriture.dateEcriture,
      libelle: ecriture.libelle,
      journalCode: ecriture.journalCode,
      statut: ecriture.statut,
      reference: ecriture.reference,
      pieceJustificative: ecriture.pieceJustificative,
      createdAt: ecriture.createdAt,
      updatedAt: ecriture.updatedAt
    };
  }
}

module.exports = new TemplateController();
