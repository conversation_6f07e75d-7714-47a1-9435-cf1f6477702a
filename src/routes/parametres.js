'use strict';

const express = require('express');
const router = express.Router();
const { validate } = require('../middleware/validation');
const { protect, optionalAuth } = require('../middleware/auth');
const ParametreController = require('../controllers/parametreController');
const models = require('../models');

// Initialiser le controller
const parametreController = new ParametreController(models);

// Schémas de validation Joi
const Joi = require('joi');

const parametreUpdateSchema = Joi.object({
  valeur: Joi.alternatives().try(
    Joi.string(),
    Joi.number(),
    Joi.boolean(),
    Joi.object()
  ).required()
    .messages({
      'any.required': 'La valeur est obligatoire'
    }),
  type: Joi.string().valid('STRING', 'NUMBER', 'BOOLEAN', 'JSON').default('STRING')
    .messages({
      'any.only': 'Le type doit être STRING, NUMBER, BOOLEAN ou JSON'
    }),
  categorie: Joi.string().valid('TVA', 'FISCAL', 'COMPTES', 'REPORTING', 'SYSTEME', 'CUSTOM', 'GENERAL').default('CUSTOM')
    .messages({
      'any.only': 'Catégorie invalide'
    }),
  description: Joi.string().max(255).optional()
    .messages({
      'string.max': 'La description ne peut dépasser 255 caractères'
    })
});

const parametresBulkUpdateSchema = Joi.object().pattern(
  Joi.string(),
  Joi.object({
    valeur: Joi.alternatives().try(
      Joi.string(),
      Joi.number(),
      Joi.boolean(),
      Joi.object()
    ).required(),
    type: Joi.string().valid('STRING', 'NUMBER', 'BOOLEAN', 'JSON').default('STRING'),
    categorie: Joi.string().valid('TVA', 'FISCAL', 'COMPTES', 'REPORTING', 'SYSTEME', 'CUSTOM', 'GENERAL').default('CUSTOM'),
    description: Joi.string().max(255).optional()
  })
);

const initialisationSchema = Joi.object({
  pays: Joi.string().length(2).default('CI')
    .messages({
      'string.length': 'Le code pays doit faire 2 caractères'
    })
});

/**
 * @swagger
 * /api/v1/parametres/societe/{societeId}:
 *   get:
 *     summary: Obtient tous les paramètres d'une société
 *     tags: [Paramètres]
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: categorie
 *         schema:
 *           type: string
 *           enum: [TVA, FISCAL, COMPTES, REPORTING, SYSTEME, CUSTOM, GENERAL]
 *       - in: query
 *         name: useCache
 *         schema:
 *           type: boolean
 *           default: true
 *     responses:
 *       200:
 *         description: Paramètres de la société
 */
router.get('/societe/:societeId', optionalAuth, (req, res, next) => {
  parametreController.getParametresSociete(req, res, next);
});

/**
 * @swagger
 * /api/v1/parametres/societe/{societeId}/tva:
 *   get:
 *     summary: Obtient la configuration TVA d'une société
 *     tags: [Paramètres]
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Configuration TVA
 */
router.get('/societe/:societeId/tva', optionalAuth, (req, res, next) => {
  parametreController.getConfigurationTVA(req, res, next);
});

/**
 * @swagger
 * /api/v1/parametres/societe/{societeId}/fiscal:
 *   get:
 *     summary: Obtient la configuration fiscale d'une société
 *     tags: [Paramètres]
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Configuration fiscale
 */
router.get('/societe/:societeId/fiscal', optionalAuth, (req, res, next) => {
  parametreController.getConfigurationFiscale(req, res, next);
});

/**
 * @swagger
 * /api/v1/parametres/societe/{societeId}:
 *   put:
 *     summary: Met à jour plusieurs paramètres en lot
 *     tags: [Paramètres]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             additionalProperties:
 *               type: object
 *               properties:
 *                 valeur:
 *                   oneOf:
 *                     - type: string
 *                     - type: number
 *                     - type: boolean
 *                     - type: object
 *                 type:
 *                   type: string
 *                   enum: [STRING, NUMBER, BOOLEAN, JSON]
 *                 categorie:
 *                   type: string
 *                   enum: [TVA, FISCAL, COMPTES, REPORTING, SYSTEME, CUSTOM, GENERAL]
 *                 description:
 *                   type: string
 *     responses:
 *       200:
 *         description: Paramètres mis à jour
 */
router.put('/societe/:societeId', protect, validate(parametresBulkUpdateSchema), (req, res, next) => {
  parametreController.mettreAJourParametres(req, res, next);
});

/**
 * @swagger
 * /api/v1/parametres/societe/{societeId}/valider:
 *   get:
 *     summary: Valide la cohérence des paramètres
 *     tags: [Paramètres]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Résultat de la validation
 */
router.get('/societe/:societeId/valider', protect, (req, res, next) => {
  parametreController.validerParametres(req, res, next);
});

/**
 * @swagger
 * /api/v1/parametres/societe/{societeId}/initialiser:
 *   post:
 *     summary: Initialise les paramètres par défaut
 *     tags: [Paramètres]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               pays:
 *                 type: string
 *                 description: Code pays (CI, SN, BF, CM, etc.)
 *                 default: CI
 *     responses:
 *       200:
 *         description: Paramètres initialisés
 */
router.post('/societe/:societeId/initialiser', protect, validate(initialisationSchema), (req, res, next) => {
  parametreController.initialiserParametresDefaut(req, res, next);
});

/**
 * @swagger
 * /api/v1/parametres/societe/{societeId}/{cle}:
 *   get:
 *     summary: Obtient un paramètre spécifique
 *     tags: [Paramètres]
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: path
 *         name: cle
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Paramètre trouvé
 *       404:
 *         description: Paramètre non trouvé
 */
router.get('/societe/:societeId/:cle', optionalAuth, (req, res, next) => {
  parametreController.getParametre(req, res, next);
});

/**
 * @swagger
 * /api/v1/parametres/societe/{societeId}/{cle}:
 *   put:
 *     summary: Met à jour un paramètre spécifique
 *     tags: [Paramètres]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: path
 *         name: cle
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - valeur
 *             properties:
 *               valeur:
 *                 oneOf:
 *                   - type: string
 *                   - type: number
 *                   - type: boolean
 *                   - type: object
 *               type:
 *                 type: string
 *                 enum: [STRING, NUMBER, BOOLEAN, JSON]
 *               categorie:
 *                 type: string
 *                 enum: [TVA, FISCAL, COMPTES, REPORTING, SYSTEME, CUSTOM, GENERAL]
 *               description:
 *                 type: string
 *     responses:
 *       200:
 *         description: Paramètre mis à jour
 */
router.put('/societe/:societeId/:cle', protect, validate(parametreUpdateSchema), (req, res, next) => {
  parametreController.mettreAJourParametre(req, res, next);
});

/**
 * @swagger
 * /api/v1/parametres/societe/{societeId}/{cle}:
 *   delete:
 *     summary: Supprime un paramètre
 *     tags: [Paramètres]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: path
 *         name: cle
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Paramètre supprimé
 *       404:
 *         description: Paramètre non trouvé
 */
router.delete('/societe/:societeId/:cle', protect, (req, res, next) => {
  parametreController.supprimerParametre(req, res, next);
});

/**
 * @swagger
 * /api/v1/parametres/cache/vider:
 *   post:
 *     summary: Vide le cache des paramètres
 *     tags: [Paramètres]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cache vidé
 */
router.post('/cache/vider', protect, (req, res, next) => {
  parametreController.viderCache(req, res, next);
});

module.exports = router;
