# Système de Clés API - API Comptabilité SYSCOHADA

## Vue d'ensemble

L'API Comptabilité SYSCOHADA utilise un système d'authentification par clés API au lieu des tokens JWT traditionnels. Ce système offre une sécurité robuste et une gestion granulaire des permissions.

## Fonctionnalités

### 🔑 Génération de Clés API
- Génération automatique de clés sécurisées avec préfixe d'identification
- Hachage bcrypt pour le stockage sécurisé
- Support des dates d'expiration
- Métadonnées personnalisables

### 🛡️ Système de Permissions
- **read** : Lecture des données
- **write** : Lecture et écriture des données
- **admin** : Accès complet incluant la gestion des clés API

### 📊 Gestion et Monitoring
- Suivi de la dernière utilisation
- Activation/désactivation des clés
- Nettoyage automatique des clés expirées
- Audit complet des actions

## Installation et Configuration

### 1. Migration de la Base de Données

```bash
# Exécuter les migrations pour créer la table des clés API
npm run db:migrate
```

### 2. Génération de la Première Clé API

```bash
# Utiliser le script CLI pour générer une clé admin
node scripts/generate-api-key.js create \
  --name "Clé API Administrateur" \
  --permissions "admin" \
  --metadata '{"description":"Clé principale pour l'\''administration"}'
```

## Utilisation des Clés API

### Méthodes d'Authentification

#### 1. Header Authorization (Recommandé)
```bash
curl -H "Authorization: Bearer sk_abc123_..." https://api.example.com/api/v1/endpoint
```

#### 2. Header X-API-Key
```bash
curl -H "X-API-Key: sk_abc123_..." https://api.example.com/api/v1/endpoint
```

#### 3. Query Parameter (Moins sécurisé)
```bash
curl "https://api.example.com/api/v1/endpoint?api_key=sk_abc123_..."
```

### Format des Clés API

Les clés API suivent le format : `sk_[prefix]_[key]`
- `sk_` : Préfixe standard pour identification
- `[prefix]` : Identifiant court unique (6 caractères hex)
- `[key]` : Clé cryptographique (64 caractères hex)

Exemple : `sk_a1b2c3_f4e5d6c7b8a9...`

## Gestion des Clés API

### Via l'API REST

#### Créer une Clé API
```bash
POST /api/v1/api-keys
Authorization: Bearer <admin-key>
Content-Type: application/json

{
  "name": "Nom de la clé",
  "permissions": ["read", "write"],
  "expiresAt": "2024-12-31T23:59:59Z",
  "metadata": {
    "description": "Description de la clé",
    "environment": "production"
  }
}
```

#### Lister les Clés API
```bash
GET /api/v1/api-keys?page=1&limit=20&includeInactive=false
Authorization: Bearer <admin-key>
```

#### Mettre à Jour une Clé API
```bash
PUT /api/v1/api-keys/{id}
Authorization: Bearer <admin-key>
Content-Type: application/json

{
  "name": "Nouveau nom",
  "permissions": ["read"],
  "isActive": false
}
```

#### Supprimer une Clé API
```bash
DELETE /api/v1/api-keys/{id}
Authorization: Bearer <admin-key>
```

### Via le Script CLI

#### Créer une Clé API
```bash
node scripts/generate-api-key.js create \
  --name "Nom de la clé" \
  --permissions "read,write" \
  --expires "2024-12-31T23:59:59Z" \
  --metadata '{"env":"prod"}'
```

#### Lister les Clés API
```bash
node scripts/generate-api-key.js list --include-inactive --limit 50
```

#### Désactiver une Clé API
```bash
node scripts/generate-api-key.js deactivate <id>
```

#### Nettoyer les Clés Expirées
```bash
node scripts/generate-api-key.js cleanup
```

## Endpoints d'Authentification

### Vérifier une Clé API
```bash
GET /api/v1/auth/verify
Authorization: Bearer <api-key>

# Réponse
{
  "success": true,
  "message": "Clé API valide",
  "data": {
    "apiKey": {
      "id": "uuid",
      "name": "Nom de la clé",
      "prefix": "sk_abc123",
      "permissions": ["read", "write"]
    }
  }
}
```

### Profil de la Clé API
```bash
GET /api/v1/auth/profile
Authorization: Bearer <api-key>

# Réponse
{
  "success": true,
  "data": {
    "apiKey": {
      "id": "uuid",
      "name": "Nom de la clé",
      "prefix": "sk_abc123",
      "permissions": ["read", "write"]
    }
  }
}
```

## Sécurité

### Bonnes Pratiques

1. **Stockage Sécurisé**
   - Ne jamais stocker les clés en plain text
   - Utiliser des variables d'environnement
   - Rotation régulière des clés

2. **Principe du Moindre Privilège**
   - Accorder uniquement les permissions nécessaires
   - Utiliser des clés spécialisées par service

3. **Monitoring**
   - Surveiller l'utilisation des clés
   - Alertes sur les tentatives d'accès non autorisées
   - Audit régulier des clés actives

### Gestion des Incidents

#### Clé Compromise
```bash
# Désactiver immédiatement la clé
node scripts/generate-api-key.js deactivate <id>

# Ou via l'API
PUT /api/v1/api-keys/{id}
{
  "isActive": false
}
```

#### Rotation des Clés
```bash
# 1. Créer une nouvelle clé
node scripts/generate-api-key.js create --name "Nouvelle clé" --permissions "admin"

# 2. Mettre à jour les applications
# 3. Désactiver l'ancienne clé
node scripts/generate-api-key.js deactivate <old-id>
```

## Exemples d'Intégration

### Node.js
```javascript
const axios = require('axios');

const apiClient = axios.create({
  baseURL: 'https://api.example.com/api/v1',
  headers: {
    'Authorization': `Bearer ${process.env.SYSCOHADA_API_KEY}`
  }
});

// Utilisation
const response = await apiClient.get('/societes');
```

### Python
```python
import requests
import os

headers = {
    'Authorization': f'Bearer {os.getenv("SYSCOHADA_API_KEY")}'
}

response = requests.get('https://api.example.com/api/v1/societes', headers=headers)
```

### cURL
```bash
#!/bin/bash
API_KEY="sk_abc123_..."
BASE_URL="https://api.example.com/api/v1"

curl -H "Authorization: Bearer $API_KEY" "$BASE_URL/societes"
```

## Dépannage

### Erreurs Communes

#### "Clé API manquante"
- Vérifiez que la clé est bien incluse dans les headers
- Utilisez le bon format : `Authorization: Bearer <clé>`

#### "Clé API invalide ou expirée"
- Vérifiez que la clé n'a pas expiré
- Confirmez que la clé est active
- Vérifiez qu'il n'y a pas d'erreurs de copie

#### "Permission requise"
- Vérifiez les permissions de la clé API
- Utilisez une clé avec les permissions appropriées

### Logs et Debugging

Les logs d'authentification sont disponibles dans les fichiers de log de l'application :

```bash
# Voir les logs d'authentification
tail -f logs/app.log | grep "Authentification"

# Voir les tentatives échouées
tail -f logs/app.log | grep "échouée"
```

## Migration depuis JWT

Si vous migrez depuis un système JWT existant :

1. **Générer des clés API** pour remplacer les tokens JWT
2. **Mettre à jour les clients** pour utiliser les clés API
3. **Tester l'authentification** avec les nouvelles clés
4. **Supprimer l'ancien système** JWT une fois la migration terminée

## Support

Pour toute question ou problème :
- Consultez les logs de l'application
- Vérifiez la documentation de l'API
- Utilisez les outils CLI pour diagnostiquer les problèmes