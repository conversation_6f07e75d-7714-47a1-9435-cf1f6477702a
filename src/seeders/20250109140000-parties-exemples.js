'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Récupération des sociétés existantes
    const societes = await queryInterface.sequelize.query(
      'SELECT id FROM societes LIMIT 3',
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (societes.length === 0) {
      console.log('Aucune société trouvée, création de tiers ignorée');
      return;
    }

    const societeId = societes[0].id;

    // Données des tiers exemples
    const parties = [
      // Clients
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        code: 'CLI001',
        nom: 'ENTREPRISE ALPHA SARL',
        type: 'CLIENT',
        civilite: 'SARL',
        adresse: 'Zone Industrielle, Lot 15\nAbidjan, Côte d\'Ivoire',
        ville: 'Abidjan',
        pays: 'Côte d\'Ivoire',
        telephone: '+225 27 20 30 40 50',
        email: '<EMAIL>',
        compte_comptable: '4111',
        conditions_paiement: '30 jours fin de mois',
        plafond_credit: 5000000.00,
        numero_contribuable: 'CI-2023-001234567',
        assujetti_tva: true,
        actif: true,
        societe_id: societeId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        code: 'CLI002',
        nom: 'BETA TRADING SA',
        type: 'CLIENT',
        civilite: 'SA',
        adresse: 'Boulevard de la République\nBouaké, Côte d\'Ivoire',
        ville: 'Bouaké',
        pays: 'Côte d\'Ivoire',
        telephone: '+225 31 63 45 67',
        email: '<EMAIL>',
        compte_comptable: '4112',
        conditions_paiement: '60 jours',
        plafond_credit: 3000000.00,
        numero_contribuable: 'CI-2023-001234568',
        assujetti_tva: true,
        actif: true,
        societe_id: societeId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        code: 'CLI003',
        nom: 'GAMMA SERVICES EURL',
        type: 'CLIENT',
        civilite: 'EURL',
        adresse: 'Rue des Jardins, Résidence Les Palmiers\nYamoussoukro, Côte d\'Ivoire',
        ville: 'Yamoussoukro',
        pays: 'Côte d\'Ivoire',
        telephone: '+225 30 64 25 36',
        email: '<EMAIL>',
        compte_comptable: '411',
        conditions_paiement: 'Comptant',
        plafond_credit: 1000000.00,
        numero_contribuable: 'CI-2023-001234569',
        assujetti_tva: true,
        actif: true,
        societe_id: societeId,
        created_at: new Date(),
        updated_at: new Date()
      },

      // Fournisseurs
      {
        id: '550e8400-e29b-41d4-a716-446655440004',
        code: 'FOU001',
        nom: 'DELTA DISTRIBUTION SARL',
        type: 'FOURNISSEUR',
        civilite: 'SARL',
        adresse: 'Zone Portuaire, Entrepôt 12\nAbidjan, Côte d\'Ivoire',
        ville: 'Abidjan',
        pays: 'Côte d\'Ivoire',
        telephone: '+225 27 21 45 67 89',
        email: '<EMAIL>',
        compte_comptable: '4011',
        conditions_paiement: '30 jours',
        plafond_credit: 10000000.00,
        numero_contribuable: 'CI-2023-001234570',
        assujetti_tva: true,
        actif: true,
        societe_id: societeId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440005',
        code: 'FOU002',
        nom: 'EPSILON LOGISTICS SA',
        type: 'FOURNISSEUR',
        civilite: 'SA',
        adresse: 'Autoroute du Nord, Km 15\nAbidjan, Côte d\'Ivoire',
        ville: 'Abidjan',
        pays: 'Côte d\'Ivoire',
        telephone: '+225 27 22 33 44 55',
        email: '<EMAIL>',
        compte_comptable: '4012',
        conditions_paiement: '45 jours fin de mois',
        plafond_credit: 8000000.00,
        numero_contribuable: 'CI-2023-001234571',
        assujetti_tva: true,
        actif: true,
        societe_id: societeId,
        created_at: new Date(),
        updated_at: new Date()
      },

      // Client-Fournisseur
      {
        id: '550e8400-e29b-41d4-a716-446655440006',
        code: 'CF001',
        nom: 'ZETA INTERNATIONAL SAS',
        type: 'CLIENT_FOURNISSEUR',
        civilite: 'SAS',
        adresse: 'Centre d\'Affaires, Tour B, 8ème étage\nAbidjan, Côte d\'Ivoire',
        ville: 'Abidjan',
        pays: 'Côte d\'Ivoire',
        telephone: '+225 27 20 11 22 33',
        email: '<EMAIL>',
        compte_comptable: '411',
        conditions_paiement: '30 jours',
        plafond_credit: 15000000.00,
        numero_contribuable: 'CI-2023-001234572',
        assujetti_tva: true,
        actif: true,
        societe_id: societeId,
        created_at: new Date(),
        updated_at: new Date()
      },

      // Particuliers
      {
        id: '550e8400-e29b-41d4-a716-446655440007',
        code: 'CLI004',
        nom: 'KOUAME Jean-Baptiste',
        type: 'CLIENT',
        civilite: 'M',
        adresse: 'Cocody, Riviera 3\nAbidjan, Côte d\'Ivoire',
        ville: 'Abidjan',
        pays: 'Côte d\'Ivoire',
        telephone: '+225 07 12 34 56 78',
        email: '<EMAIL>',
        compte_comptable: '411',
        conditions_paiement: 'Comptant',
        plafond_credit: 500000.00,
        numero_contribuable: null,
        assujetti_tva: false,
        actif: true,
        societe_id: societeId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440008',
        code: 'CLI005',
        nom: 'TRAORE Aminata',
        type: 'CLIENT',
        civilite: 'MME',
        adresse: 'Marcory, Zone 4C\nAbidjan, Côte d\'Ivoire',
        ville: 'Abidjan',
        pays: 'Côte d\'Ivoire',
        telephone: '+225 05 67 89 01 23',
        email: '<EMAIL>',
        compte_comptable: '411',
        conditions_paiement: 'Comptant',
        plafond_credit: 300000.00,
        numero_contribuable: null,
        assujetti_tva: false,
        actif: true,
        societe_id: societeId,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // Insertion des tiers
    await queryInterface.bulkInsert('parties', parties);

    console.log(`${parties.length} tiers exemples créés pour la société ${societeId}`);
  },

  async down(queryInterface, Sequelize) {
    // Suppression des tiers exemples
    await queryInterface.bulkDelete('parties', {
      code: {
        [Sequelize.Op.in]: ['CLI001', 'CLI002', 'CLI003', 'CLI004', 'CLI005', 'FOU001', 'FOU002', 'CF001']
      }
    });
  }
};