/**
 * Configuration principale de l'application Express
 * API Comptabilité SYSCOHADA
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');

// Configuration et middleware personnalisés
const { config, validateConfig, displayConfig } = require('./config/env');
const { httpLogger } = require('./config/logger');
const { errorHandler, notFoundHandler, handleUncaughtException, handleUnhandledRejection } = require('./middleware/errorHandler');

// Validation de la configuration au démarrage
try {
  validateConfig();
  if (config.NODE_ENV !== 'test') {
    displayConfig();
  }
} catch (error) {
  console.error('❌ Erreur de configuration:', error.message);
  process.exit(1);
}

// Gestionnaires d'erreurs globaux
handleUncaughtException();
handleUnhandledRejection();

const app = express();

// Middleware de sécurité
app.use(helmet({
  contentSecurityPolicy: config.NODE_ENV === 'production' ? undefined : false
}));

app.use(cors({
  origin: config.cors.origin,
  credentials: config.cors.credentials
}));

app.use(compression());

// Middleware de parsing
app.use(express.json({
  limit: `${config.upload.maxFileSize}b`,
  type: 'application/json'
}));
app.use(express.urlencoded({
  extended: true,
  limit: `${config.upload.maxFileSize}b`
}));

// Logging HTTP
if (config.NODE_ENV !== 'test') {
  app.use(httpLogger);
}

// Route de health check avancée
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: config.NODE_ENV,
    uptime: process.uptime(),
    memory: process.memoryUsage()
  });
});

// Route de base avec informations détaillées
app.get('/', (req, res) => {
  res.json({
    message: 'API Comptabilité SYSCOHADA',
    version: process.env.npm_package_version || '1.0.0',
    environment: config.NODE_ENV,
    documentation: '/api-docs',
    endpoints: {
      health: '/health',
      api: `/api/${config.API_VERSION}`
    }
  });
});

// Initialisation des contrôleurs avec les modèles
const models = require('./models');
const movementController = require('./controllers/movementController');
const reportController = require('./controllers/reportController');
const depreciationController = require('./controllers/depreciationController');
const exerciseController = require('./controllers/exerciseController');

// Initialisation des contrôleurs
movementController.init(models);
reportController.init(models);
depreciationController.init(models);
exerciseController.init(models);

// Routes API
app.use(`/api/${config.API_VERSION}`, require('./routes'));

// Middleware de gestion des routes non trouvées
app.use(notFoundHandler);

// Middleware de gestion d'erreurs global
app.use(errorHandler);

module.exports = app;
