'use strict';

const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const { validate } = require('../middleware/validation');
const { protect } = require('../middleware/auth');
const PlanComptableController = require('../controllers/planComptableController');
const models = require('../models');

// Configuration multer pour l'upload de fichiers
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'temp/uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'import-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.xlsx', '.xls', '.csv'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Type de fichier non supporté. Utilisez Excel (.xlsx, .xls) ou CSV (.csv)'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB max
  }
});

// Initialiser le controller
const planComptableController = new PlanComptableController(models);

// Schémas de validation Joi
const Joi = require('joi');

const comptePersonnaliseSchema = Joi.object({
  numero: Joi.string().pattern(/^[1-8]\d{2,9}$/).required()
    .messages({
      'string.pattern.base': 'Le numéro doit commencer par un chiffre de 1 à 8 et contenir 3 à 10 chiffres',
      'any.required': 'Le numéro de compte est obligatoire'
    }),
  libelle: Joi.string().min(3).max(100).required()
    .messages({
      'string.min': 'Le libellé doit contenir au moins 3 caractères',
      'string.max': 'Le libellé ne peut dépasser 100 caractères',
      'any.required': 'Le libellé est obligatoire'
    }),
  nature: Joi.string().valid('ACTIF', 'PASSIF', 'CHARGE', 'PRODUIT').required()
    .messages({
      'any.only': 'La nature doit être ACTIF, PASSIF, CHARGE ou PRODUIT',
      'any.required': 'La nature est obligatoire'
    }),
  sens: Joi.string().valid('DEBIT', 'CREDIT').required()
    .messages({
      'any.only': 'Le sens doit être DEBIT ou CREDIT',
      'any.required': 'Le sens est obligatoire'
    }),
  compteParent: Joi.string().pattern(/^[1-8]\d{0,8}$/).optional()
    .messages({
      'string.pattern.base': 'Le compte parent doit être un numéro de compte valide'
    }),
  raisonSociale: Joi.string().max(255).optional()
    .messages({
      'string.max': 'La raison sociale ne peut dépasser 255 caractères'
    }),
  typeAnalytique: Joi.string().valid('AUCUN', 'CENTRE_COUT', 'PROJET').default('AUCUN')
    .messages({
      'any.only': 'Le type analytique doit être AUCUN, CENTRE_COUT ou PROJET'
    })
});

const compteUpdateSchema = Joi.object({
  libelle: Joi.string().min(3).max(100).optional(),
  raisonSociale: Joi.string().max(255).optional(),
  actif: Joi.boolean().optional(),
  obligatoireLettrage: Joi.boolean().optional(),
  typeAnalytique: Joi.string().valid('AUCUN', 'CENTRE_COUT', 'PROJET').optional()
});

const toggleActifSchema = Joi.object({
  actif: Joi.boolean().required()
    .messages({
      'any.required': 'L\'état actif est obligatoire'
    })
});

/**
 * @swagger
 * /api/v1/plan-comptable/societe/{societeId}/comptes-personnalises:
 *   get:
 *     summary: Liste les comptes personnalisés d'une société
 *     tags: [Plan Comptable]
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: actif
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: classe
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 8
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Liste des comptes personnalisés
 */
router.get('/societe/:societeId/comptes-personnalises', (req, res, next) => {
  planComptableController.getComptesPersonnalises(req, res, next);
});

/**
 * @swagger
 * /api/v1/plan-comptable/societe/{societeId}/comptes:
 *   post:
 *     summary: Crée un nouveau compte personnalisé
 *     tags: [Plan Comptable]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - numero
 *               - libelle
 *               - nature
 *               - sens
 *             properties:
 *               numero:
 *                 type: string
 *                 pattern: '^[1-8]\d{2,9}$'
 *               libelle:
 *                 type: string
 *               nature:
 *                 type: string
 *                 enum: [DETAIL, TOTAL]
 *               sens:
 *                 type: string
 *                 enum: [DEBITEUR, CREDITEUR]
 *               compteParent:
 *                 type: string
 *               raisonSociale:
 *                 type: string
 *               typeAnalytique:
 *                 type: string
 *                 enum: [AUCUN, CENTRE_COUT, PROJET]
 *     responses:
 *       201:
 *         description: Compte personnalisé créé
 *       400:
 *         description: Données invalides
 */
router.post('/societe/:societeId/comptes', validate(comptePersonnaliseSchema), (req, res, next) => {
  planComptableController.creerComptePersonnalise(req, res, next);
});

/**
 * @swagger
 * /api/v1/plan-comptable/societe/{societeId}/comptes/{numero}:
 *   put:
 *     summary: Modifie un compte personnalisé
 *     tags: [Plan Comptable]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: path
 *         name: numero
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               libelle:
 *                 type: string
 *               raisonSociale:
 *                 type: string
 *               actif:
 *                 type: boolean
 *               obligatoireLettrage:
 *                 type: boolean
 *               typeAnalytique:
 *                 type: string
 *                 enum: [AUCUN, CENTRE_COUT, PROJET]
 *     responses:
 *       200:
 *         description: Compte modifié
 *       404:
 *         description: Compte non trouvé
 */
router.put('/societe/:societeId/comptes/:numero', protect, validate(compteUpdateSchema), (req, res, next) => {
  planComptableController.modifierComptePersonnalise(req, res, next);
});

/**
 * @swagger
 * /api/v1/plan-comptable/societe/{societeId}/comptes/{numero}:
 *   delete:
 *     summary: Supprime un compte personnalisé
 *     tags: [Plan Comptable]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: path
 *         name: numero
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Compte supprimé
 *       404:
 *         description: Compte non trouvé
 */
router.delete('/societe/:societeId/comptes/:numero', protect, (req, res, next) => {
  planComptableController.supprimerComptePersonnalise(req, res, next);
});

/**
 * @swagger
 * /api/v1/plan-comptable/societe/{societeId}/comptes/{numero}/toggle-actif:
 *   patch:
 *     summary: Active ou désactive un compte
 *     tags: [Plan Comptable]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: path
 *         name: numero
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - actif
 *             properties:
 *               actif:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: État du compte modifié
 */
router.patch('/societe/:societeId/comptes/:numero/toggle-actif', protect, validate(toggleActifSchema), (req, res, next) => {
  planComptableController.toggleCompteActif(req, res, next);
});

/**
 * @swagger
 * /api/v1/plan-comptable/societe/{societeId}/statistiques:
 *   get:
 *     summary: Obtient les statistiques de personnalisation
 *     tags: [Plan Comptable]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Statistiques de personnalisation
 */
router.get('/societe/:societeId/statistiques', (req, res, next) => {
  planComptableController.getStatistiquesPersonnalisation(req, res, next);
});

/**
 * @swagger
 * /api/v1/plan-comptable/societe/{societeId}/valider:
 *   get:
 *     summary: Valide la cohérence du plan comptable
 *     tags: [Plan Comptable]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Résultat de la validation
 */
router.get('/societe/:societeId/valider', (req, res, next) => {
  planComptableController.validerCoherencePlan(req, res, next);
});

/**
 * @swagger
 * /api/v1/plan-comptable/societe/{societeId}/import:
 *   post:
 *     summary: Importe un plan comptable depuis un fichier
 *     tags: [Plan Comptable]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               remplacerExistants:
 *                 type: boolean
 *               validerUniquement:
 *                 type: boolean
 *               formatFichier:
 *                 type: string
 *                 enum: [auto, excel, csv]
 *     responses:
 *       200:
 *         description: Import réussi
 */
router.post('/societe/:societeId/import', protect, upload.single('file'), (req, res, next) => {
  planComptableController.importerPlanComptable(req, res, next);
});

/**
 * @swagger
 * /api/v1/plan-comptable/societe/{societeId}/export:
 *   get:
 *     summary: Exporte le plan comptable vers un fichier
 *     tags: [Plan Comptable]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [excel, csv]
 *           default: excel
 *       - in: query
 *         name: includeInactifs
 *         schema:
 *           type: boolean
 *           default: false
 *       - in: query
 *         name: seulementPersonnalises
 *         schema:
 *           type: boolean
 *           default: false
 *       - in: query
 *         name: classes
 *         schema:
 *           type: string
 *           description: Classes séparées par des virgules (ex: 1,2,3)
 *     responses:
 *       200:
 *         description: Fichier d'export
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/societe/:societeId/export', (req, res, next) => {
  planComptableController.exporterPlanComptable(req, res, next);
});

/**
 * @swagger
 * /api/v1/plan-comptable/template-import:
 *   get:
 *     summary: Génère un template Excel pour l'import
 *     tags: [Plan Comptable]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Template Excel
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/template-import', (req, res, next) => {
  planComptableController.genererTemplateImport(req, res, next);
});

module.exports = router;
