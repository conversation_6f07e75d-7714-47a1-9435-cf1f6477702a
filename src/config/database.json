{"development": {"username": "postgres", "password": "adminpass", "database": "compta_syscohada", "host": "localhost", "port": 5432, "dialect": "postgres", "logging": true, "pool": {"max": 10, "min": 0, "acquire": 30000, "idle": 10000}, "define": {"timestamps": true, "underscored": true, "freezeTableName": true}}, "test": {"username": "postgres", "password": "adminpass", "database": "compta_syscohada_test", "host": "localhost", "port": 5432, "dialect": "postgres", "logging": false, "pool": {"max": 5, "min": 0, "acquire": 30000, "idle": 10000}, "define": {"timestamps": true, "underscored": true, "freezeTableName": true}}, "production": {"username": "postgres", "password": "adminpass", "database": "compta_syscohada", "host": "localhost", "port": 5432, "dialect": "postgres", "logging": false, "pool": {"max": 20, "min": 5, "acquire": 30000, "idle": 10000}, "define": {"timestamps": true, "underscored": true, "freezeTableName": true}}}