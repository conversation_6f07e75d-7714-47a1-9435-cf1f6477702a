'use strict';

const express = require('express');
const router = express.Router();
const Joi = require('joi');

const movementController = require('../controllers/movementController');
const { protect } = require('../middleware/auth');
const { validate } = require('../middleware/validation');

/**
 * Schémas de validation Joi pour les mouvements
 */

// Schéma pour les ventes
const venteSchema = Joi.object({
  societeId: Joi.string().uuid().required(),
  clientId: Joi.string().uuid().required(),
  montantHT: Joi.number().positive().required(),
  tauxTva: Joi.number().min(0).max(100).default(18),
  dateVente: Joi.date().iso().required(),
  numeroFacture: Joi.string().min(1).max(50).required(),
  libelle: Joi.string().max(255).optional(),
  compteVente: Joi.string().pattern(/^\d{6}$/).default('701100'),
  journalCode: Joi.string().length(2).default('VT')
});

// Schéma pour les achats
const achatSchema = Joi.object({
  societeId: Joi.string().uuid().required(),
  fournisseurId: Joi.string().uuid().required(),
  montantHT: Joi.number().positive().required(),
  tauxTva: Joi.number().min(0).max(100).default(18),
  dateAchat: Joi.date().iso().required(),
  numeroFacture: Joi.string().min(1).max(50).required(),
  libelle: Joi.string().max(255).optional(),
  compteAchat: Joi.string().pattern(/^\d{6}$/).default('601100'),
  journalCode: Joi.string().length(2).default('AC')
});

// Schéma pour les règlements clients
const reglementClientSchema = Joi.object({
  societeId: Joi.string().uuid().required(),
  clientId: Joi.string().uuid().required(),
  montant: Joi.number().positive().required(),
  dateReglement: Joi.date().iso().required(),
  modeReglement: Joi.string().valid('ESPECES', 'CHEQUE', 'VIREMENT', 'CARTE_BANCAIRE', 'TRAITE', 'BILLET_ORDRE').default('VIREMENT'),
  numeroReglement: Joi.string().max(50).optional(),
  libelle: Joi.string().max(255).optional(),
  compteTresorerie: Joi.string().pattern(/^\d{6}$/).default('512100'),
  journalCode: Joi.string().length(2).default('BQ')
});

// Schéma pour les règlements fournisseurs
const reglementFournisseurSchema = Joi.object({
  societeId: Joi.string().uuid().required(),
  fournisseurId: Joi.string().uuid().required(),
  montant: Joi.number().positive().required(),
  dateReglement: Joi.date().iso().required(),
  modeReglement: Joi.string().valid('ESPECES', 'CHEQUE', 'VIREMENT', 'CARTE_BANCAIRE', 'TRAITE', 'BILLET_ORDRE').default('VIREMENT'),
  numeroReglement: Joi.string().max(50).optional(),
  libelle: Joi.string().max(255).optional(),
  compteTresorerie: Joi.string().pattern(/^\d{6}$/).default('512100'),
  journalCode: Joi.string().length(2).default('BQ')
});

// Schéma pour les amortissements
const amortissementSchema = Joi.object({
  societeId: Joi.string().uuid().required(),
  immobilisationId: Joi.string().uuid().optional(),
  montantAmortissement: Joi.number().positive().required(),
  dateAmortissement: Joi.date().iso().required(),
  libelle: Joi.string().max(255).optional(),
  compteImmobilisation: Joi.string().pattern(/^\d{6}$/).required(),
  compteAmortissement: Joi.string().pattern(/^\d{6}$/).required(),
  compteDotation: Joi.string().pattern(/^\d{6}$/).default('681100'),
  journalCode: Joi.string().length(2).default('OD')
});

// Schéma pour la paie
const paieSchema = Joi.object({
  societeId: Joi.string().uuid().required(),
  periode: Joi.string().pattern(/^\d{4}-\d{2}$/).required(), // Format YYYY-MM
  salaires: Joi.array().items(Joi.object({
    nom: Joi.string().required(),
    salaireBrut: Joi.number().positive().required(),
    cotisationsSalariales: Joi.number().min(0).default(0)
  })).min(1).required(),
  charges: Joi.object().pattern(Joi.string(), Joi.number().min(0)).optional(),
  dateVersement: Joi.date().iso().required(),
  libelle: Joi.string().max(255).optional(),
  journalCode: Joi.string().length(2).default('PA')
});

/**
 * Routes pour les mouvements comptables automatiques
 */

/**
 * @swagger
 * /api/v1/movements/vente:
 *   post:
 *     summary: Génère une écriture de vente automatique
 *     tags: [Mouvements]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/VenteData'
 *     responses:
 *       201:
 *         description: Écriture de vente générée avec succès
 *       400:
 *         description: Données invalides
 *       422:
 *         description: Erreur de validation métier
 */
router.post('/vente',
  protect,
  validate(venteSchema),
  movementController.genererEcritureVente.bind(movementController)
);

/**
 * @swagger
 * /api/v1/movements/achat:
 *   post:
 *     summary: Génère une écriture d'achat automatique
 *     tags: [Mouvements]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AchatData'
 *     responses:
 *       201:
 *         description: Écriture d'achat générée avec succès
 *       400:
 *         description: Données invalides
 *       422:
 *         description: Erreur de validation métier
 */
router.post('/achat',
  protect,
  validate(achatSchema),
  movementController.genererEcritureAchat.bind(movementController)
);

/**
 * @swagger
 * /api/v1/movements/reglement-client:
 *   post:
 *     summary: Génère une écriture de règlement client
 *     tags: [Mouvements]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ReglementClientData'
 *     responses:
 *       201:
 *         description: Écriture de règlement client générée avec succès
 *       400:
 *         description: Données invalides
 *       422:
 *         description: Erreur de validation métier
 */
router.post('/reglement-client',
  protect,
  validate(reglementClientSchema),
  movementController.genererReglementClient.bind(movementController)
);

/**
 * @swagger
 * /api/v1/movements/reglement-fournisseur:
 *   post:
 *     summary: Génère une écriture de règlement fournisseur
 *     tags: [Mouvements]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ReglementFournisseurData'
 *     responses:
 *       201:
 *         description: Écriture de règlement fournisseur générée avec succès
 *       400:
 *         description: Données invalides
 *       422:
 *         description: Erreur de validation métier
 */
router.post('/reglement-fournisseur',
  protect,
  validate(reglementFournisseurSchema),
  movementController.genererReglementFournisseur.bind(movementController)
);

/**
 * @swagger
 * /api/v1/movements/amortissement:
 *   post:
 *     summary: Génère une écriture d'amortissement
 *     tags: [Mouvements]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AmortissementData'
 *     responses:
 *       201:
 *         description: Écriture d'amortissement générée avec succès
 *       400:
 *         description: Données invalides
 *       422:
 *         description: Erreur de validation métier
 */
router.post('/amortissement',
  protect,
  validate(amortissementSchema),
  movementController.genererAmortissement.bind(movementController)
);

/**
 * @swagger
 * /api/v1/movements/paie:
 *   post:
 *     summary: Génère des écritures de paie
 *     tags: [Mouvements]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PaieData'
 *     responses:
 *       201:
 *         description: Écritures de paie générées avec succès
 *       400:
 *         description: Données invalides
 *       422:
 *         description: Erreur de validation métier
 */
router.post('/paie',
  protect,
  validate(paieSchema),
  movementController.genererEcriturePaie.bind(movementController)
);

/**
 * @swagger
 * /api/v1/movements/templates:
 *   get:
 *     summary: Obtient les templates d'écritures disponibles
 *     tags: [Mouvements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *     responses:
 *       200:
 *         description: Templates récupérés avec succès
 *       400:
 *         description: ID société manquant
 */
router.get('/templates',
  protect,
  validate(Joi.object({
    societeId: Joi.string().uuid().required()
  }), 'query'),
  movementController.getTemplatesEcritures.bind(movementController)
);

/**
 * @swagger
 * /api/v1/movements/validate:
 *   post:
 *     summary: Valide un mouvement avant génération
 *     tags: [Mouvements]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - donnees
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [VENTE, ACHAT, REGLEMENT_CLIENT, REGLEMENT_FOURNISSEUR, AMORTISSEMENT, PAIE]
 *                 description: Type de mouvement à valider
 *               donnees:
 *                 type: object
 *                 description: Données du mouvement selon le type
 *     responses:
 *       200:
 *         description: Validation effectuée avec succès
 *       400:
 *         description: Type ou données manquants
 */
router.post('/validate',
  protect,
  validate(Joi.object({
    type: Joi.string().valid('VENTE', 'ACHAT', 'REGLEMENT_CLIENT', 'REGLEMENT_FOURNISSEUR', 'AMORTISSEMENT', 'PAIE').required(),
    donnees: Joi.object().required()
  })),
  movementController.validateMovement.bind(movementController)
);

module.exports = router;