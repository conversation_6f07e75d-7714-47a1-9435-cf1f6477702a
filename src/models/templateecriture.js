'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class TemplateEcriture extends Model {
    /**
     * Associations du modèle TemplateEcriture
     */
    static associate(models) {
      // Un template appartient à une société
      TemplateEcriture.belongsTo(models.Societe, {
        foreignKey: 'societeId',
        as: 'societe'
      });

      // Un template appartient à un journal
      TemplateEcriture.belongsTo(models.Journal, {
        foreignKey: 'journalCode',
        targetKey: 'code',
        as: 'journal'
      });

      // Un template a plusieurs lignes de template
      TemplateEcriture.hasMany(models.TemplateLigneEcriture, {
        foreignKey: 'templateId',
        as: 'lignes',
        onDelete: 'CASCADE'
      });

      // Un template peut être créé par un utilisateur
      // TODO: Association avec User quand le modèle sera disponible
    }

    /**
     * Méthodes statiques
     */

    /**
     * Valide les données d'un template
     * @param {Object} donnees - Données à valider
     * @returns {Object} Résultat de validation
     */
    static validateTemplateData(donnees) {
      const errors = [];

      if (!donnees.nom || donnees.nom.trim().length < 3) {
        errors.push('Le nom du template doit contenir au moins 3 caractères');
      }

      if (!donnees.societeId) {
        errors.push('L\'ID de la société est obligatoire');
      }

      if (!donnees.journalCode) {
        errors.push('Le code journal est obligatoire');
      }

      if (!donnees.categorie) {
        errors.push('La catégorie est obligatoire');
      }

      const categoriesValides = ['VENTE', 'ACHAT', 'BANQUE', 'CAISSE', 'PAIE', 'AMORTISSEMENT', 'PROVISION', 'AUTRE'];
      if (donnees.categorie && !categoriesValides.includes(donnees.categorie)) {
        errors.push('Catégorie invalide');
      }

      return {
        valide: errors.length === 0,
        erreurs: errors
      };
    }

    /**
     * Obtient les templates par catégorie pour une société
     * @param {string} societeId - ID de la société
     * @param {string} categorie - Catégorie (optionnelle)
     * @returns {Promise<Array>} Templates
     */
    static async getTemplatesParCategorie(societeId, categorie = null) {
      const where = { societeId, actif: true };
      if (categorie) {
        where.categorie = categorie;
      }

      return await this.findAll({
        where,
        include: [
          {
            model: this.sequelize.models.TemplateLigneEcriture,
            as: 'lignes',
            order: [['ordre', 'ASC']]
          },
          {
            model: this.sequelize.models.Journal,
            as: 'journal',
            attributes: ['code', 'libelle', 'type']
          }
        ],
        order: [['categorie', 'ASC'], ['nom', 'ASC']]
      });
    }

    /**
     * Recherche des templates par nom ou description
     * @param {string} societeId - ID de la société
     * @param {string} recherche - Terme de recherche
     * @returns {Promise<Array>} Templates trouvés
     */
    static async rechercherTemplates(societeId, recherche) {
      const { Op } = this.sequelize;
      
      return await this.findAll({
        where: {
          societeId,
          actif: true,
          [Op.or]: [
            { nom: { [Op.iLike]: `%${recherche}%` } },
            { description: { [Op.iLike]: `%${recherche}%` } }
          ]
        },
        include: [
          {
            model: this.sequelize.models.Journal,
            as: 'journal',
            attributes: ['code', 'libelle', 'type']
          }
        ],
        order: [['nom', 'ASC']]
      });
    }

    /**
     * Méthodes d'instance
     */

    /**
     * Obtient les paramètres variables du template
     * @returns {Array} Liste des paramètres
     */
    getParametresVariables() {
      const parametres = [];
      const regex = /\{\{(\w+)\}\}/g;
      
      // Rechercher dans le libellé du template
      let match;
      while ((match = regex.exec(this.libelle || '')) !== null) {
        if (!parametres.includes(match[1])) {
          parametres.push(match[1]);
        }
      }

      // Rechercher dans les lignes (sera fait par TemplateLigneEcriture)
      return parametres;
    }

    /**
     * Applique des valeurs aux paramètres variables
     * @param {Object} valeurs - Valeurs des paramètres
     * @returns {Object} Template avec valeurs appliquées
     */
    appliquerParametres(valeurs = {}) {
      let libelleApplique = this.libelle || '';
      
      // Remplacer les paramètres dans le libellé
      Object.keys(valeurs).forEach(param => {
        const regex = new RegExp(`\\{\\{${param}\\}\\}`, 'g');
        libelleApplique = libelleApplique.replace(regex, valeurs[param]);
      });

      return {
        ...this.toJSON(),
        libelle: libelleApplique,
        valeursAppliquees: valeurs
      };
    }

    /**
     * Duplique le template
     * @param {Object} nouvellesDonnees - Nouvelles données pour le template dupliqué
     * @returns {Object} Données du template dupliqué
     */
    dupliquer(nouvellesDonnees = {}) {
      const templateDuplique = {
        nom: nouvellesDonnees.nom || `${this.nom} (Copie)`,
        description: nouvellesDonnees.description || this.description,
        libelle: this.libelle,
        journalCode: nouvellesDonnees.journalCode || this.journalCode,
        categorie: nouvellesDonnees.categorie || this.categorie,
        societeId: nouvellesDonnees.societeId || this.societeId,
        actif: true,
        public: false, // Les copies sont privées par défaut
        utilisateurCreation: nouvellesDonnees.utilisateurCreation
      };

      return templateDuplique;
    }

    /**
     * Obtient les statistiques d'utilisation du template
     * @returns {Promise<Object>} Statistiques
     */
    async getStatistiquesUtilisation() {
      // TODO: Implémenter quand on aura un système de tracking d'utilisation
      return {
        nombreUtilisations: 0,
        derniereUtilisation: null,
        utilisateursUniques: 0
      };
    }

    /**
     * Formate le template pour l'affichage
     * @returns {Object} Template formaté
     */
    formater() {
      return {
        id: this.id,
        nom: this.nom,
        description: this.description,
        libelle: this.libelle,
        categorie: this.categorie,
        journalCode: this.journalCode,
        journal: this.journal ? {
          code: this.journal.code,
          libelle: this.journal.libelle,
          type: this.journal.type
        } : null,
        actif: this.actif,
        public: this.public,
        parametresVariables: this.getParametresVariables(),
        nombreLignes: this.lignes ? this.lignes.length : 0,
        createdAt: this.createdAt,
        updatedAt: this.updatedAt
      };
    }
  }

  TemplateEcriture.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    nom: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [3, 100]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    libelle: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: 'Libellé du template avec paramètres variables {{param}}'
    },
    journalCode: {
      type: DataTypes.STRING(10),
      allowNull: false,
      references: {
        model: 'journaux',
        key: 'code'
      }
    },
    categorie: {
      type: DataTypes.ENUM(
        'VENTE', 'ACHAT', 'BANQUE', 'CAISSE', 'PAIE', 
        'AMORTISSEMENT', 'PROVISION', 'AUTRE'
      ),
      allowNull: false,
      defaultValue: 'AUTRE'
    },
    societeId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'societes',
        key: 'id'
      }
    },
    actif: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    public: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Template partageable entre utilisateurs'
    },
    utilisateurCreation: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: 'ID de l\'utilisateur créateur'
    },
    tagsRecherche: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Tags pour faciliter la recherche'
    }
  }, {
    sequelize,
    modelName: 'TemplateEcriture',
    tableName: 'template_ecritures',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['societe_id', 'actif']
      },
      {
        fields: ['categorie']
      },
      {
        fields: ['journal_code']
      },
      {
        fields: ['nom'],
        type: 'gin',
        operator: 'gin_trgm_ops'
      }
    ]
  });

  return TemplateEcriture;
};
