
# 📊 Logiciel de Comptabilité Générale SYSCOHADA

## 🎯 Vue d'ensemble

Ce projet vise à développer un logiciel de comptabilité générale complet, conforme aux normes du  **Système Comptable Ouest Africain (SYSCOHADA)** . Il s'agit d'une solution moderne, web-based, destinée aux entreprises de la zone UEMOA/CEMAC qui ont besoin d'un outil de gestion comptable robuste, accessible et économique.

### 🌍 Contexte SYSCOHADA

Le SYSCOHADA (Système Comptable Ouest Africain) est le référentiel comptable unifié adopté par 17 pays d'Afrique de l'Ouest et du Centre. Il harmonise les pratiques comptables et facilite les échanges économiques dans cette zone géographique.

**Pays concernés :**

* Bénin, Burkina Faso, Cameroun, Centrafrique, Comores, Congo, Côte d'Ivoire, Gabon, Guinée, Guinée-Bissau, Guinée <PERSON>, Mali, Niger, Sénégal, Tchad, Togo

## 🎯 Objectifs du Projet

### Objectifs Principaux

#### 1. **Digitalisation de la Comptabilité**

* Remplacer les systèmes comptables manuels ou obsolètes
* Offrir une solution moderne accessible via navigateur web
* Réduire les erreurs humaines grâce à l'automatisation
* Faciliter la collaboration entre équipes comptables

#### 2. **Conformité Réglementaire**

* Respect strict des normes SYSCOHADA (classes 1 à 8)
* Génération automatique des états financiers obligatoires
* Support des liasses fiscales SYSCOHADA
* Traçabilité complète des opérations comptables

#### 3. **Accessibilité et Coût**

* Solution abordable pour les PME africaines
* Interface multilingue (Français, Anglais)
* Fonctionnement offline partiel
* Support des devises locales (FCFA, etc.)

#### 4. **Performance et Fiabilité**

* Architecture scalable supportant la croissance
* Sauvegarde automatique et sécurisée des données
* Contrôles d'intégrité automatiques
* Audit trail complet

### Objectifs Secondaires

#### 1. **Formation et Accompagnement**

* Interface intuitive nécessitant peu de formation
* Documentation complète en français
* Tutoriels vidéo intégrés
* Support technique local

#### 2. **Intégration Écosystème**

* API pour connexion avec systèmes tiers
* Import/export vers Excel, PDF
* Intégration potentielle avec banques locales
* Connecteurs e-commerce

#### 3. **Intelligence d'Affaires**

* Tableaux de bord financiers
* Analyses de performance
* Prévisions de trésorerie
* Alertes automatiques

## 🏗️ Architecture et Technologies

### Stack Technique

```
Frontend: React.js + Material-UI
Backend: Node.js + Express.js
Base de données: PostgreSQL
ORM: Sequelize
Authentification: JWT + bcrypt
PDF: PDFKit
Reports: Chart.js + D3.js
```

### Architecture 3-tiers

```
┌─────────────────┐
│   PRESENTATION  │  ← Interface utilisateur (React)
├─────────────────┤
│    LOGIQUE      │  ← API REST (Express.js)
├─────────────────┤
│     DONNÉES     │  ← Base de données (PostgreSQL)
└─────────────────┘
```

## 📋 Fonctionnalités Détaillées

### 1. MODULE FICHIER - Gestion des Données de Base

#### 1.1 Gestion des Sociétés

**Objectif :** Centraliser les informations légales et fiscales de l'entreprise

**Fonctionnalités :**

* Création de fiches société complètes
* Gestion multi-sociétés pour les groupes
* Paramètres fiscaux (TVA, IS, etc.)
* Exercices comptables avec dates personnalisables
* Gestion des devises multiples

**Bénéfices :**

* Conformité légale automatique
* Facilite les audits et contrôles
* Base pour tous les autres modules

#### 1.2 Plan Comptable SYSCOHADA

**Objectif :** Implémenter le plan comptable normalisé SYSCOHADA

**Structure des 8 classes :**

```
Classe 1: Comptes de ressources durables
Classe 2: Comptes d'actif immobilisé  
Classe 3: Comptes de stocks
Classe 4: Comptes de tiers
Classe 5: Comptes de trésorerie
Classe 6: Comptes de charges
Classe 7: Comptes de produits
Classe 8: Comptes des autres charges et produits
```

**Fonctionnalités :**

* Plan comptable pré-configuré SYSCOHADA
* Personnalisation possible par entreprise
* Hiérarchie des comptes (classe > compte > sous-compte)
* Validation automatique des imputations

#### 1.3 Gestion des Journaux

**Objectif :** Organiser les écritures par nature d'opération

**Journaux standards :**

* Journal des ventes (VT)
* Journal des achats (AC)
* Journal de banque (BQ)
* Journal des opérations diverses (OD)

**Bénéfices :**

* Meilleure organisation comptable
* Facilite les contrôles et révisions
* Respect des obligations légales

### 2. MODULE SAISIE - Enregistrement des Opérations

#### 2.1 Saisie d'Écritures

**Objectif :** Permettre l'enregistrement rigoureux des opérations comptables

**Fonctionnalités clés :**

* Saisie guidée par type d'opération
* Contrôle d'équilibre automatique (Débit = Crédit)
* Numérotation automatique des écritures
* Validation par étapes (brouillard → définitif)

**Contrôles qualité :**

* Vérification cohérence des comptes
* Validation des montants
* Contrôle des dates d'exercice
* Détection des doublons

#### 2.2 Mouvements Prédéfinis

**Objectif :** Simplifier les opérations courantes

**Types de mouvements :**

##### Ventes

* Facturation clients automatique
* Calcul TVA selon taux en vigueur
* Gestion des acomptes et soldes
* Écritures multi-devises

##### Achats

* Enregistrement factures fournisseurs
* Gestion TVA déductible
* Suivi des engagements
* Rapprochement commandes/factures

##### Paiements/Encaissements

* Gestion des moyens de paiement
* Rapprochement bancaire automatique
* Lettrage automatique
* Suivi de trésorerie temps réel

### 3. MODULE ÉDITION - États et Rapports

#### 3.1 États Comptables Légaux

##### Journal

**Objectif :** Présenter chronologiquement toutes les écritures d'une période

* Tri par date et numéro d'écriture
* Totaux par journal et période
* Export PDF/Excel
* Signature électronique

##### Grand Livre

**Objectif :** Visualiser tous les mouvements d'un compte spécifique

* Soldes progressifs
* Filtrage par période
* Détail des écritures
* Soldes d'ouverture et de clôture

##### Balance

**Objectif :** Présenter les soldes de tous les comptes utilisés

```
Structure de la Balance :
┌─────────────┬─────────────────┬──────────┬──────────┬──────────┐
│ N° Compte   │ Intitulé        │ Débit    │ Crédit   │ Solde    │
├─────────────┼─────────────────┼──────────┼──────────┼──────────┤
│ 101000      │ Capital         │    0,00  │ 10000,00 │-10000,00 │
│ 411000      │ Clients         │  5000,00 │  2000,00 │  3000,00 │
│ ...         │ ...             │    ...   │    ...   │    ...   │
└─────────────┴─────────────────┴──────────┴──────────┴──────────┘
```

#### 3.2 États Financiers SYSCOHADA

##### Bilan

* **Actif :** Immobilisations + Actif circulant + Trésorerie
* **Passif :** Capitaux propres + Dettes
* Présentation conforme SYSCOHADA
* Comparaison N/N-1
* Ratios financiers automatiques

##### Compte de Résultat

* **Produits :** Ventes + Autres produits
* **Charges :** Achats + Charges + Dotations
* **Résultat :** Bénéfice ou Perte
* Soldes intermédiaires de gestion

### 4. OUTILS AVANCÉS

#### 4.1 Lettrage

**Objectif :** Rapprocher les écritures qui se compensent

**Processus :**

1. Sélection des écritures d'un même compte
2. Vérification de l'équilibre
3. Attribution d'un code lettrage unique
4. Marquage des lignes lettrées

**Bénéfices :**

* Suivi précis des créances/dettes
* Facilite les relances clients
* Optimise la gestion de trésorerie

#### 4.2 Validation Brouillard

**Objectif :** Finaliser les écritures après vérification

**Étapes :**

1. Contrôles d'intégrité
2. Vérification équilibre
3. Validation définitive
4. Verrouillage modification

#### 4.3 Calculs Financiers

##### Amortissements

* **Linéaire :** Dotation constante sur la durée
* **Dégressif :** Dotation décroissante
* Tableau d'amortissement automatique
* Écritures de dotation programmées

##### Emprunts

* Calcul mensualités
* Tableau de remboursement
* Écritures d'intérêts automatiques
* Suivi capital restant dû

## 🎯 Public Cible

### Utilisateurs Primaires

#### 1. **Petites et Moyennes Entreprises (PME)**

* **Taille :** 5 à 250 employés
* **Secteurs :** Commerce, services, industrie
* **Besoin :** Solution complète et abordable
* **Contraintes :** Budget limité, personnel peu formé

#### 2. **Cabinets d'Expertise Comptable**

* **Taille :** 2 à 50 comptables
* **Clients :** Portefeuille PME variées
* **Besoin :** Multi-dossiers, productivité
* **Contraintes :** Conformité réglementaire stricte

#### 3. **Associations et ONG**

* **Taille :** Variable
* **Secteurs :** Humanitaire, développement
* **Besoin :** Traçabilité des fonds
* **Contraintes :** Reporting bailleurs spécifique

### Utilisateurs Secondaires

#### 1. **Grandes Entreprises** (Filiales)

* Module comptabilité auxiliaire
* Consolidation avec ERP principal
* Reporting local SYSCOHADA

#### 2. **Institutions Financières**

* Analyse crédit clients
* Évaluation garanties
* Due diligence acquisitions

## 📈 Bénéfices Attendus

### Bénéfices Économiques

#### Pour les Entreprises

* **Réduction coûts :** -50% vs solutions propriétaires
* **Gain temps :** +40% productivité comptable
* **Réduction erreurs :** -80% erreurs saisie
* **Meilleur contrôle :** Tableaux de bord temps réel

#### Pour les Cabinets

* **Productivité :** +60% dossiers traités
* **Qualité :** Standardisation des procédures
* **Croissance :** Acquisition nouveaux clients
* **Rentabilité :** Optimisation charge de travail

### Bénéfices Opérationnels

#### Conformité

* Respect automatique normes SYSCOHADA
* États légaux générés automatiquement
* Audit trail complet et inaltérable
* Archivage sécurisé long terme

#### Efficacité

* Automatisation tâches répétitives
* Workflow de validation optimisés
* Alertes et notifications proactives
* Intégration écosystème d'affaires

### Bénéfices Stratégiques

#### Prise de Décision

* Indicateurs financiers temps réel
* Analyses prévisionnelles
* Benchmarking sectoriel
* Optimisation fiscale

#### Développement

* Support croissance entreprise
* Facilite levées de fonds
* Améliore rating crédit
* Attire investisseurs

## 🚀 Roadmap de Développement

### Phase 1 : Fondations (Mois 1-3)

**Objectif :** Poser les bases techniques solides

**Livrables :**

* Architecture système complète
* Base de données SYSCOHADA
* Authentification et sécurité
* Module gestion sociétés
* Plan comptable configurable

**Critères de succès :**

* Tests unitaires > 80% couverture
* Performance < 2s pour toute requête
* Sécurité validée audit externe

### Phase 2 : Saisie Comptable (Mois 4-6)

**Objectif :** Permettre l'enregistrement complet des opérations

**Livrables :**

* Module saisie écritures
* Contrôles d'équilibre automatiques
* Mouvements prédéfinis (vente, achat)
* Gestion des journaux
* Interface utilisateur intuitive

**Critères de succès :**

* Saisie < 30s par écriture standard
* 0% écritures déséquilibrées acceptées
* Formation utilisateur < 2h

### Phase 3 : États Comptables (Mois 7-9)

**Objectif :** Générer tous les rapports légaux

**Livrables :**

* Journal, Grand Livre, Balance
* Bilan et Compte de Résultat SYSCOHADA
* Export PDF/Excel professionnel
* Tableaux de bord dynamiques
* Système d'impression

**Critères de succès :**

* États générés < 10s
* Conformité SYSCOHADA 100%
* Export multi-formats

### Phase 4 : Outils Avancés (Mois 10-12)

**Objectif :** Finaliser les fonctionnalités professionnelles

**Livrables :**

* Lettrage automatique et manuel
* Validation brouillard
* Calculs financiers (amortissement, emprunt)
* Clôture d'exercice
* Sauvegarde/restauration

**Critères de succès :**

* Lettrage automatique > 70%
* Clôture exercice < 1h
* Sauvegarde quotidienne automatique

### Phase 5 : Optimisation et Déploiement (Mois 13-15)

**Objectif :** Préparer la mise sur le marché

**Livrables :**

* Tests utilisateurs réels
* Optimisations performances
* Documentation complète
* Formation support client
* Stratégie commerciale

**Critères de succès :**

* Satisfaction utilisateur > 85%
* Performance système optimale
* Support client opérationnel

## 🔧 Installation et Configuration

### Prérequis Système

#### Serveur

* **OS :** Ubuntu 20.04+ ou Windows Server 2019+
* **RAM :** 8GB minimum, 16GB recommandé
* **Stockage :** 100GB SSD minimum
* **Processeur :** 4 cœurs minimum

#### Base de Données

* **PostgreSQL :** Version 13+
* **Espace :** 50GB par société/an
* **Backup :** Quotidien automatique

#### Navigateur Client

* **Chrome :** Version 90+
* **Firefox :** Version 88+
* **Safari :** Version 14+

### Outil CLI

Un outil en ligne de commande est disponible pour interagir avec l'API. Cet outil permet d'effectuer toutes les opérations disponibles via l'API directement depuis le terminal.

#### Installation de l'outil CLI

```bash
# Cloner le dépôt
git clone https://github.com/geekobueno/api-compta-generale.git
cd api-compta-generale

# Installer l'outil CLI
./install-cli.sh
```

#### Utilisation de l'outil CLI

```bash
# Afficher l'aide
syscohada --help

# Configurer l'URL de l'API
syscohada config

# Se connecter
syscohada login

# Lister les sociétés
syscohada societes:list

# Créer une écriture comptable
syscohada ecritures:create
```

Pour plus d'informations sur l'utilisation de l'outil CLI, consultez le fichier [CLI_README.md](CLI_README.md).
* **Edge :** Version 90+

### Installation Rapide

```bash
# 1. Cloner le repository
git clone https://github.com/votre-org/comptabilite-syscohada.git
cd comptabilite-syscohada

# 2. Installation des dépendances
npm install

# 3. Configuration base de données
cp .env.example .env
# Éditer .env avec vos paramètres DB

# 4. Migration base de données
npm run db:migrate
npm run db:seed

# 5. Démarrage application
npm run dev
```

### Configuration Avancée

#### Variables d'Environnement

```bash
# Base de données
DB_HOST=localhost
DB_PORT=5432
DB_NAME=comptabilite_syscohada
DB_USER=comptable
DB_PASSWORD=motdepasse_securise

# JWT
JWT_SECRET=cle_secrete_tres_longue
JWT_EXPIRES_IN=24h

# Email (notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=mot_de_passe_app

# Upload fichiers
UPLOAD_MAX_SIZE=10MB
UPLOAD_ALLOWED_TYPES=pdf,jpg,png,xlsx
```

## 🤝 Contribution

### Comment Contribuer

#### 1. **Développeurs**

```bash
# Fork du projet
git clone https://github.com/VOTRE_USERNAME/comptabilite-syscohada.git

# Créer une branche feature
git checkout -b feature/nouvelle-fonctionnalite

# Développer et tester
npm test

# Commit avec message explicite
git commit -m "feat: ajout calcul amortissement dégressif"

# Push et Pull Request
git push origin feature/nouvelle-fonctionnalite
```

#### 2. **Comptables/Experts**

* Validation conformité SYSCOHADA
* Tests fonctionnels sur cas réels
* Retours d'expérience utilisateur
* Rédaction documentation métier

#### 3. **Traducteurs**

* Localisation interface utilisateur
* Adaptation terminologie locale
* Révision documentation

### Standards de Code

#### JavaScript/Node.js

```javascript
// Utiliser ESLint + Prettier
// Nommage explicite des variables
const calculerAmortissementLineaire = (valeurAchat, dureeVie) => {
  // Toujours valider les paramètres
  if (!valeurAchat || !dureeVie) {
    throw new Error('Paramètres manquants');
  }
  
  return valeurAchat / dureeVie;
};
```

#### Base de Données

```sql
-- Nommage en français pour métier comptable
-- Utiliser des contraintes strictes
CREATE TABLE ecriture_comptable (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  numero_ecriture VARCHAR(20) NOT NULL UNIQUE,
  date_ecriture DATE NOT NULL,
  libelle VARCHAR(200) NOT NULL,
  statut ecriture_statut DEFAULT 'BROUILLARD',
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 📞 Support et Communauté

### Support Technique

* **Email :** <EMAIL>
* **Forum :** https://forum.comptabilite-syscohada.com
* **Chat :** Discord #comptabilite-syscohada
* **Heures :** Lun-Ven 8h-18h GMT+0

### Communauté

* **GitHub Discussions :** Questions techniques
* **LinkedIn :** Réseau professionnel comptable
* **Webinaires :** Formation utilisateur mensuelle
* **Newsletter :** Actualités produit et SYSCOHADA

### Formation

* **Tutoriels vidéo :** YouTube @ComptabiliteSyscohada
* **Documentation :** Wiki complet
* **Certification :** Programme certifiant utilisateur
* **Accompagnement :** Support migration inclus

## 📄 Licence et Conditions

### Licence Open Source

* **Type :** MIT License
* **Usage :** Commercial autorisé
* **Modification :** Libre avec attribution
* **Distribution :** Libre avec licence

### Conditions d'Utilisation

* Respect de la législation locale
* Pas de responsabilité sur décisions fiscales
* Support best-effort communauté
* Support garanti pour version payante

### Roadmap Commerciale

* **Version Community :** Gratuite, fonctions de base
* **Version Professional :** €50/mois, support prioritaire
* **Version Enterprise :** Sur devis, personnalisation

---

## 🎉 Conclusion

Ce logiciel de comptabilité SYSCOHADA ambitionne de démocratiser l'accès à des outils comptables professionnels de qualité pour les entreprises africaines. En combinant les standards internationaux du développement logiciel avec l'expertise comptable locale, nous créons une solution qui répond vraiment aux besoins du terrain.

**Votre participation est précieuse !** Que vous soyez développeur, comptable, entrepreneur ou simple utilisateur, votre contribution aidera à construire l'outil comptable de référence pour l'Afrique de l'Ouest et du Centre.

**Rejoignez-nous dans cette aventure ! 🚀**
