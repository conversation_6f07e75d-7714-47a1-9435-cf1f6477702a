'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Journal extends Model {
    /**
     * Associations du modèle Journal
     */
    static associate(models) {
      // Un journal appartient à une société
      Journal.belongsTo(models.Societe, {
        foreignKey: 'societeId',
        as: 'societe'
      });

      // Un journal peut avoir un compte de contrepartie
      Journal.belongsTo(models.CompteComptable, {
        foreignKey: 'compteContropartie',
        targetKey: 'numero',
        as: 'compteContropartieDetail'
      });

      // Un journal peut avoir plusieurs écritures
      Journal.hasMany(models.EcritureComptable, {
        foreignKey: 'journalCode',
        sourceKey: 'code',
        as: 'ecritures'
      });
    }

    /**
     * Méthodes statiques
     */

    /**
     * Obtient les journaux actifs d'une société
     * @param {string} societeId - ID de la société
     * @returns {Promise<Array>} Liste des journaux actifs
     */
    static async getJournauxActifs(societeId) {
      return await this.findAll({
        where: {
          societeId,
          actif: true
        },
        order: [['code', 'ASC']]
      });
    }

    /**
     * Obtient les types de journaux disponibles avec leurs configurations
     * @returns {Array} Types de journaux
     */
    static getTypesDisponibles() {
      return [
        {
          code: 'BANQUE',
          libelle: 'Journal de banque',
          description: 'Opérations bancaires',
          requiresContropartie: true,
          prefixeDefaut: 'BQ',
          longueurDefaut: 8,
          resetDefaut: 'ANNUEL'
        },
        {
          code: 'VENTE',
          libelle: 'Journal des ventes',
          description: 'Facturation clients',
          requiresContropartie: false,
          prefixeDefaut: 'VT',
          longueurDefaut: 6,
          resetDefaut: 'ANNUEL'
        },
        {
          code: 'ACHAT',
          libelle: 'Journal des achats',
          description: 'Facturation fournisseurs',
          requiresContropartie: false,
          prefixeDefaut: 'AC',
          longueurDefaut: 6,
          resetDefaut: 'ANNUEL'
        },
        {
          code: 'CAISSE',
          libelle: 'Journal de caisse',
          description: 'Opérations de caisse',
          requiresContropartie: true,
          prefixeDefaut: 'CA',
          longueurDefaut: 6,
          resetDefaut: 'MENSUEL'
        },
        {
          code: 'OD',
          libelle: 'Journal des opérations diverses',
          description: 'Écritures diverses',
          requiresContropartie: false,
          prefixeDefaut: 'OD',
          longueurDefaut: 6,
          resetDefaut: 'JAMAIS'
        }
      ];
    }

    /**
     * Valide les paramètres de séquence
     * @param {Object} params - Paramètres à valider
     * @returns {Object} Résultat de validation
     */
    static validateSequenceParams(params) {
      const { prefixeNumero, longueurNumero, resetSequence } = params;
      const errors = [];

      if (prefixeNumero && (prefixeNumero.length < 1 || prefixeNumero.length > 10)) {
        errors.push('Le préfixe doit contenir entre 1 et 10 caractères');
      }

      if (longueurNumero && (longueurNumero < 4 || longueurNumero > 12)) {
        errors.push('La longueur du numéro doit être entre 4 et 12');
      }

      if (resetSequence && !['JAMAIS', 'MENSUEL', 'ANNUEL'].includes(resetSequence)) {
        errors.push('Le type de reset doit être JAMAIS, MENSUEL ou ANNUEL');
      }

      return {
        valide: errors.length === 0,
        erreurs: errors
      };
    }

    /**
     * Méthodes d'instance
     */

    /**
     * Retourne la description du type de journal
     */
    getDescriptionType() {
      const descriptions = {
        'BANQUE': 'Journal de banque - Opérations bancaires',
        'VENTE': 'Journal des ventes - Facturation clients',
        'ACHAT': 'Journal des achats - Facturation fournisseurs',
        'OD': 'Journal des opérations diverses - Écritures diverses'
      };
      return descriptions[this.type] || 'Type de journal inconnu';
    }

    /**
     * Vérifie si le journal nécessite un compte de contrepartie
     */
    requiresCompteContropartie() {
      return ['BANQUE', 'CAISSE'].includes(this.type);
    }

    /**
     * Génère le prochain numéro de séquence pour ce journal
     * @returns {string} Numéro formaté avec préfixe et padding
     */
    genererProchainNumero() {
      const prochainNumero = this.dernierNumero + 1;
      const numeroFormate = prochainNumero.toString().padStart(this.longueurNumero, '0');

      if (this.prefixeNumero) {
        return `${this.prefixeNumero}${numeroFormate}`;
      }

      return numeroFormate;
    }

    /**
     * Vérifie si la séquence doit être réinitialisée
     * @returns {boolean} True si reset nécessaire
     */
    needsSequenceReset() {
      if (this.resetSequence === 'JAMAIS') {
        return false;
      }

      if (!this.dateDernierReset) {
        return true;
      }

      const maintenant = new Date();
      const dernierReset = new Date(this.dateDernierReset);

      if (this.resetSequence === 'MENSUEL') {
        return maintenant.getMonth() !== dernierReset.getMonth() ||
               maintenant.getFullYear() !== dernierReset.getFullYear();
      }

      if (this.resetSequence === 'ANNUEL') {
        return maintenant.getFullYear() !== dernierReset.getFullYear();
      }

      return false;
    }

    /**
     * Formate un numéro selon les paramètres du journal
     * @param {number} numero - Numéro à formater
     * @returns {string} Numéro formaté
     */
    formaterNumero(numero) {
      const numeroFormate = numero.toString().padStart(this.longueurNumero, '0');

      if (this.prefixeNumero) {
        return `${this.prefixeNumero}${numeroFormate}`;
      }

      return numeroFormate;
    }

    /**
     * Vérifie si le journal peut être supprimé
     * @returns {Promise<{canDelete: boolean, reason: string}>}
     */
    async canBeDeleted() {
      // Vérifier s'il y a des écritures associées
      const nombreEcritures = await this.sequelize.models.EcritureComptable.count({
        where: { journalCode: this.code }
      });

      if (nombreEcritures > 0) {
        return {
          canDelete: false,
          reason: `Le journal a ${nombreEcritures} écriture(s) associée(s)`
        };
      }

      // Les journaux standard ne peuvent pas être supprimés
      if (!this.personnalise) {
        return {
          canDelete: false,
          reason: 'Les journaux standard ne peuvent pas être supprimés'
        };
      }

      return { canDelete: true, reason: null };
    }

    /**
     * Obtient les statistiques du journal
     * @returns {Promise<Object>} Statistiques
     */
    async getStatistiques() {
      const stats = {
        nombreEcritures: 0,
        montantTotal: 0,
        derniereEcriture: null,
        prochainNumero: this.genererProchainNumero()
      };

      // TODO: Implémenter les vraies statistiques quand le modèle EcritureComptable sera disponible
      // const ecritures = await this.sequelize.models.EcritureComptable.findAll({
      //   where: { journalCode: this.code }
      // });

      return stats;
    }
  }

  Journal.init({
    code: {
      type: DataTypes.STRING(10),
      primaryKey: true,
      validate: {
        len: [2, 10],
        isUppercase: true
      }
    },
    libelle: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 50]
      }
    },
    type: {
      type: DataTypes.ENUM('BANQUE', 'VENTE', 'ACHAT', 'OD', 'CAISSE'),
      allowNull: false
    },
    compteContropartie: {
      type: DataTypes.STRING(10),
      allowNull: true,
      references: {
        model: 'compte_comptables',
        key: 'numero'
      }
    },
    societeId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'societes',
        key: 'id'
      }
    },
    // Nouveaux champs pour la numérotation automatique
    numeroSequence: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'numero_sequence'
    },
    prefixeNumero: {
      type: DataTypes.STRING(10),
      allowNull: true,
      field: 'prefixe_numero'
    },
    longueurNumero: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 6,
      field: 'longueur_numero',
      validate: {
        min: 4,
        max: 12
      }
    },
    resetSequence: {
      type: DataTypes.ENUM('JAMAIS', 'MENSUEL', 'ANNUEL'),
      allowNull: false,
      defaultValue: 'ANNUEL',
      field: 'reset_sequence'
    },
    dernierNumero: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'dernier_numero'
    },
    personnalise: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    actif: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    controleEquilibre: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'controle_equilibre'
    },
    dateDernierReset: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'date_dernier_reset'
    },
    utilisateurCreation: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'utilisateur_creation'
    }
  }, {
    sequelize,
    modelName: 'Journal',
    tableName: 'journaux',
    timestamps: true,
    underscored: true,
    hooks: {
      beforeValidate: (journal) => {
        // Convertir le code en majuscules
        if (journal.code) {
          journal.code = journal.code.toUpperCase();
        }
      }
    }
  });

  return Journal;
};