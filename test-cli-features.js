#!/usr/bin/env node

/**
 * Script de test pour vérifier les nouvelles fonctionnalités du CLI
 * Ce script teste les commandes ajoutées sans les exécuter réellement
 */

const fs = require('fs');
const path = require('path');

// Lire le fichier CLI
const cliPath = path.join(__dirname, 'cli.js');
const cliContent = fs.readFileSync(cliPath, 'utf8');

// Fonctionnalités attendues
const expectedFeatures = [
  // Journaux
  'journaux:list',
  'journaux:create',
  
  // Templates
  'templates:list',
  'templates:create',
  
  // Lettrage
  'lettrage:comptes',
  'lettrage:auto',
  
  // Tiers
  'tiers:list',
  'tiers:create',
  
  // Import/Export
  'import:ecritures',
  'export:ecritures',
  
  // Rapports
  'rapports:generer',
  
  // Paramètres
  'parametres:list',
  'parametres:set',
  
  // Plan comptable
  'plan:personnalise',
  'plan:personnaliser'
];

console.log('🔍 Vérification des fonctionnalités du CLI...\n');

let allFeaturesPresent = true;

expectedFeatures.forEach(feature => {
  const regex = new RegExp(`\\.command\\('${feature.replace(':', '\\:')}`, 'g');
  const matches = cliContent.match(regex);
  
  if (matches && matches.length > 0) {
    console.log(`✅ ${feature} - Présent`);
  } else {
    console.log(`❌ ${feature} - Manquant`);
    allFeaturesPresent = false;
  }
});

console.log('\n📊 Statistiques:');
console.log(`Total des fonctionnalités testées: ${expectedFeatures.length}`);
console.log(`Fonctionnalités présentes: ${expectedFeatures.filter(feature => {
  const regex = new RegExp(`\\.command\\('${feature.replace(':', '\\:')}`, 'g');
  return cliContent.match(regex);
}).length}`);

// Vérifier les dépendances
console.log('\n🔧 Vérification des dépendances...');

const packageJsonPath = path.join(__dirname, 'cli-package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const requiredDeps = ['form-data', 'axios', 'chalk', 'cli-table3', 'commander', 'inquirer', 'ora'];
  
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep} - ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - Manquant`);
      allFeaturesPresent = false;
    }
  });
} else {
  console.log('❌ cli-package.json non trouvé');
  allFeaturesPresent = false;
}

// Vérifier la structure du code
console.log('\n🏗️ Vérification de la structure du code...');

const structureChecks = [
  { name: 'requireAuth middleware', pattern: /const requireAuth = \(callback\) => {/ },
  { name: 'API configuration', pattern: /const api = axios\.create\({/ },
  { name: 'Config management', pattern: /const saveConfig = \(\) => {/ },
  { name: 'Error handling', pattern: /catch \(error\) {/ },
  { name: 'Spinner usage', pattern: /ora\('.*'\)\.start\(\)/ },
  { name: 'Table display', pattern: /new Table\({/ },
  { name: 'Inquirer prompts', pattern: /inquirer\.prompt\(\[/ }
];

structureChecks.forEach(check => {
  if (cliContent.match(check.pattern)) {
    console.log(`✅ ${check.name} - Présent`);
  } else {
    console.log(`❌ ${check.name} - Manquant`);
  }
});

console.log('\n📝 Résumé:');
if (allFeaturesPresent) {
  console.log('🎉 Toutes les fonctionnalités sont présentes et le CLI est complet!');
  console.log('\n📚 Prochaines étapes:');
  console.log('1. Tester le CLI avec une API en cours d\'exécution');
  console.log('2. Vérifier que toutes les routes API correspondent');
  console.log('3. Tester les fonctionnalités d\'import/export avec des fichiers réels');
  console.log('4. Valider les permissions et la sécurité');
} else {
  console.log('⚠️ Certaines fonctionnalités sont manquantes ou incomplètes.');
  console.log('Veuillez vérifier les éléments marqués comme manquants ci-dessus.');
}

console.log('\n🚀 Pour utiliser le CLI:');
console.log('1. npm install (pour installer les dépendances)');
console.log('2. chmod +x cli.js');
console.log('3. ./cli.js --help (pour voir toutes les commandes)');
console.log('4. ./cli.js config (pour configurer l\'URL de l\'API)');
console.log('5. ./cli.js auth:setup (pour configurer la clé API)');