#!/usr/bin/env node

/**
 * Script de test d'intégration pour le CLI SYSCOHADA
 * Ce script teste toutes les fonctionnalités du CLI de manière automatique
 */

const { exec, spawn } = require('child_process');
const { promisify } = require('util');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

const execAsync = promisify(exec);
const CLI_PATH = path.join(__dirname, 'cli.js');

// Configuration de test
const TEST_CONFIG = {
  apiUrl: process.env.API_URL || 'http://localhost:3000/api/v1',
  testApiKey: process.env.TEST_API_KEY || null,
  timeout: 30000
};

class CLITester {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
  }

  async runCommand(command, timeout = 10000) {
    try {
      console.log(chalk.blue(`🧪 Test: ${command}`));
      const { stdout, stderr } = await execAsync(`node ${CLI_PATH} ${command}`, { timeout });
      return { success: true, stdout, stderr, error: null };
    } catch (error) {
      return { success: false, stdout: error.stdout || '', stderr: error.stderr || '', error: error.message };
    }
  }

  async runInteractiveCommand(command, inputs = [], timeout = 15000) {
    return new Promise((resolve, reject) => {
      console.log(chalk.blue(`🧪 Test interactif: ${command}`));
      
      const child = spawn('node', [CLI_PATH, ...command.split(' ')], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';
      let inputIndex = 0;

      const timer = setTimeout(() => {
        child.kill();
        reject(new Error('Timeout'));
      }, timeout);

      child.stdout.on('data', (data) => {
        stdout += data.toString();
        // Simuler les réponses utilisateur
        if (inputIndex < inputs.length) {
          setTimeout(() => {
            child.stdin.write(inputs[inputIndex] + '\n');
            inputIndex++;
          }, 500);
        }
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        clearTimeout(timer);
        resolve({
          success: code === 0,
          stdout,
          stderr,
          code
        });
      });

      child.on('error', (error) => {
        clearTimeout(timer);
        resolve({
          success: false,
          error: error.message
        });
      });
    });
  }

  logResult(testName, result, description = '') {
    const status = result.success ? '✅' : '❌';
    const message = `${status} ${testName}${description ? ' - ' + description : ''}`;
    
    if (result.success) {
      console.log(chalk.green(message));
    } else {
      console.log(chalk.red(message));
      if (result.error) console.log(chalk.red(`   Erreur: ${result.error}`));
      if (result.stderr) console.log(chalk.red(`   stderr: ${result.stderr}`));
    }

    this.results.push({
      testName,
      success: result.success,
      description,
      error: result.error,
      timestamp: new Date().toISOString()
    });
  }

  async testBasicCommands() {
    console.log(chalk.yellow('\n=== Tests des commandes de base ==='));

    // Test d'aide
    let result = await this.runCommand('--help');
    this.logResult('Aide générale', result, 'Affichage de l\'aide');

    // Test de version
    result = await this.runCommand('--version');
    this.logResult('Version', result, 'Affichage de la version');

    // Test de statut API (sans authentification)
    result = await this.runCommand('status');
    this.logResult('Statut API', result, 'Vérification du statut de l\'API');
  }

  async testConfigurationCommands() {
    console.log(chalk.yellow('\n=== Tests de configuration ==='));

    // Test configuration URL (interactif)
    if (TEST_CONFIG.apiUrl) {
      try {
        const result = await this.runInteractiveCommand('config', [TEST_CONFIG.apiUrl]);
        this.logResult('Configuration URL', result, 'Configuration de l\'URL API');
      } catch (error) {
        this.logResult('Configuration URL', { success: false, error: error.message });
      }
    }
  }

  async testAuthenticationCommands() {
    console.log(chalk.yellow('\n=== Tests d\'authentification ==='));

    // Test configuration clé API (si disponible)
    if (TEST_CONFIG.testApiKey) {
      try {
        const result = await this.runInteractiveCommand('auth:setup', [TEST_CONFIG.testApiKey]);
        this.logResult('Configuration clé API', result, 'Configuration de la clé API de test');

        // Si succès, tester la vérification
        if (result.success) {
          const verifyResult = await this.runCommand('auth:verify');
          this.logResult('Vérification clé API', verifyResult, 'Vérification de la clé API configurée');
        }
      } catch (error) {
        this.logResult('Configuration clé API', { success: false, error: error.message });
      }
    } else {
      console.log(chalk.yellow('⚠️  Aucune clé API de test fournie - tests d\'auth sautés'));
    }

    // Test suppression clé API
    const clearResult = await this.runCommand('auth:clear');
    this.logResult('Suppression clé API', clearResult, 'Suppression de la clé API');
  }

  async testApiKeyManagement() {
    console.log(chalk.yellow('\n=== Tests de gestion des clés API ==='));

    if (!TEST_CONFIG.testApiKey) {
      console.log(chalk.yellow('⚠️  Tests admin nécessitent une clé API - sautés'));
      return;
    }

    // Reconfigurer la clé API pour les tests admin
    await this.runInteractiveCommand('auth:setup', [TEST_CONFIG.testApiKey]);

    // Test liste des clés
    let result = await this.runCommand('apikeys:list');
    this.logResult('Liste clés API', result, 'Liste des clés API (admin)');

    // Test liste avec options
    result = await this.runCommand('apikeys:list --include-inactive --limit 10');
    this.logResult('Liste clés API avec options', result, 'Liste avec filtres');
  }

  async testSocietesManagement() {
    console.log(chalk.yellow('\n=== Tests de gestion des sociétés ==='));

    if (!TEST_CONFIG.testApiKey) {
      console.log(chalk.yellow('⚠️  Tests nécessitent une clé API - sautés'));
      return;
    }

    // Test liste sociétés
    let result = await this.runCommand('societes:list');
    this.logResult('Liste sociétés', result, 'Liste des sociétés');

    // Test sélection société (si des sociétés existent)
    result = await this.runCommand('societes:select');
    this.logResult('Sélection société', result, 'Sélection d\'une société');
  }

  async testExercicesManagement() {
    console.log(chalk.yellow('\n=== Tests de gestion des exercices ==='));

    if (!TEST_CONFIG.testApiKey) {
      console.log(chalk.yellow('⚠️  Tests nécessitent une clé API - sautés'));
      return;
    }

    // Test liste exercices
    let result = await this.runCommand('exercices:list');
    this.logResult('Liste exercices', result, 'Liste des exercices comptables');

    // Test statuts de clôture
    result = await this.runCommand('exercices:vue-clotures');
    this.logResult('Vue clôtures', result, 'Vue d\'ensemble des clôtures');
  }

  async testComptabiliteOperations() {
    console.log(chalk.yellow('\n=== Tests des opérations comptables ==='));

    if (!TEST_CONFIG.testApiKey) {
      console.log(chalk.yellow('⚠️  Tests nécessitent une clé API - sautés'));
      return;
    }

    // Test plan comptable
    let result = await this.runCommand('comptes:list');
    this.logResult('Plan comptable', result, 'Liste des comptes');

    // Test plan comptable personnalisé
    result = await this.runCommand('plan:personnalise');
    this.logResult('Plan personnalisé', result, 'Plan comptable personnalisé');

    // Test journaux
    result = await this.runCommand('journaux:list');
    this.logResult('Liste journaux', result, 'Liste des journaux');

    // Test écritures
    result = await this.runCommand('ecritures:list');
    this.logResult('Liste écritures', result, 'Liste des écritures comptables');

    // Test templates
    result = await this.runCommand('templates:list');
    this.logResult('Liste templates', result, 'Liste des templates d\'écriture');

    // Test tiers
    result = await this.runCommand('tiers:list');
    this.logResult('Liste tiers', result, 'Liste des tiers');

    // Test paramètres
    result = await this.runCommand('parametres:list');
    this.logResult('Liste paramètres', result, 'Liste des paramètres');
  }

  async testReportsAndAnalysis() {
    console.log(chalk.yellow('\n=== Tests des rapports et analyses ==='));

    if (!TEST_CONFIG.testApiKey) {
      console.log(chalk.yellow('⚠️  Tests nécessitent une clé API - sautés'));
      return;
    }

    // Test dashboard
    let result = await this.runCommand('dashboard');
    this.logResult('Dashboard', result, 'Tableau de bord');

    // Test lettrage
    result = await this.runCommand('lettrage:comptes');
    this.logResult('Lettrage comptes', result, 'Lettrage des comptes');

    // Test amortissements
    result = await this.runCommand('amortissements:list');
    this.logResult('Liste amortissements', result, 'Liste des amortissements');
  }

  async testImportExport() {
    console.log(chalk.yellow('\n=== Tests d\'import/export ==='));

    if (!TEST_CONFIG.testApiKey) {
      console.log(chalk.yellow('⚠️  Tests nécessitent une clé API - sautés'));
      return;
    }

    // Test export écritures
    let result = await this.runCommand('export:ecritures');
    this.logResult('Export écritures', result, 'Export des écritures');
  }

  async testErrorHandling() {
    console.log(chalk.yellow('\n=== Tests de gestion d\'erreurs ==='));

    // Test commande inexistante
    let result = await this.runCommand('commande-inexistante');
    this.logResult('Commande inexistante', { success: !result.success }, 'Gestion commande invalide');

    // Test sans clé API configurée (après clear)
    await this.runCommand('auth:clear');
    result = await this.runCommand('societes:list');
    this.logResult('Commande sans auth', { success: !result.success }, 'Gestion absence authentification');
  }

  generateReport() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    
    const totalTests = this.results.length;
    const successTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - successTests;
    const successRate = totalTests > 0 ? (successTests / totalTests * 100).toFixed(1) : 0;

    const report = {
      summary: {
        totalTests,
        successTests,
        failedTests,
        successRate: `${successRate}%`,
        duration: `${duration}ms`,
        timestamp: new Date().toISOString()
      },
      results: this.results,
      environment: {
        apiUrl: TEST_CONFIG.apiUrl,
        hasApiKey: !!TEST_CONFIG.testApiKey,
        nodeVersion: process.version,
        platform: process.platform
      }
    };

    // Sauvegarder le rapport
    const reportFile = path.join(__dirname, 'cli-test-report.json');
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

    console.log(chalk.yellow('\n=== RÉSUMÉ DES TESTS ==='));
    console.log(`📊 Total des tests: ${totalTests}`);
    console.log(`✅ Tests réussis: ${chalk.green(successTests)}`);
    console.log(`❌ Tests échoués: ${chalk.red(failedTests)}`);
    console.log(`📈 Taux de réussite: ${chalk.blue(successRate)}%`);
    console.log(`⏱️  Durée: ${duration}ms`);
    console.log(`📄 Rapport détaillé: ${reportFile}`);

    if (failedTests > 0) {
      console.log(chalk.red('\n❌ TESTS ÉCHOUÉS:'));
      this.results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`   - ${r.testName}: ${r.error || 'Erreur inconnue'}`);
        });
    }

    return report;
  }

  async runAllTests() {
    console.log(chalk.blue('🚀 Démarrage des tests CLI SYSCOHADA\n'));
    
    await this.testBasicCommands();
    await this.testConfigurationCommands();
    await this.testAuthenticationCommands();
    await this.testApiKeyManagement();
    await this.testSocietesManagement();
    await this.testExercicesManagement();
    await this.testComptabiliteOperations();
    await this.testReportsAndAnalysis();
    await this.testImportExport();
    await this.testErrorHandling();

    return this.generateReport();
  }
}

// Exécution des tests si le script est lancé directement
if (require.main === module) {
  const tester = new CLITester();
  
  tester.runAllTests()
    .then(report => {
      console.log(chalk.green('\n✅ Tests terminés'));
      process.exit(report.summary.failedTests > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error(chalk.red('❌ Erreur lors des tests:'), error);
      process.exit(1);
    });
}

module.exports = CLITester;