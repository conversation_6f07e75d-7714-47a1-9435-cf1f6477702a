/**
 * Configuration de la base de données PostgreSQL avec Sequelize
 * API Comptabilité SYSCOHADA
 */

const { Sequelize } = require('sequelize');
const { config } = require('./env');

// Configuration Sequelize avec les paramètres d'environnement
const sequelizeConfig = {
  host: config.database.host,
  port: config.database.port,
  database: config.database.name,
  username: config.database.username,
  password: config.database.password,
  dialect: config.database.dialect,
  logging: config.database.logging ? console.log : false,
  pool: config.database.pool,
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    charset: 'utf8',
    collate: 'utf8_general_ci'
  },
  dialectOptions: {
    charset: 'utf8mb4'
  }
};

// Création de l'instance Sequelize
const sequelize = new Sequelize(sequelizeConfig);

/**
 * Test de la connexion à la base de données
 */
async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log('✅ Connexion à la base de données établie avec succès.');
    return true;
  } catch (error) {
    console.error('❌ Impossible de se connecter à la base de données:', error);
    return false;
  }
}

/**
 * Fermeture propre de la connexion
 */
async function closeConnection() {
  try {
    await sequelize.close();
    console.log('✅ Connexion à la base de données fermée.');
  } catch (error) {
    console.error('❌ Erreur lors de la fermeture de la connexion:', error);
  }
}

module.exports = {
  sequelize,
  testConnection,
  closeConnection,
  config
};
