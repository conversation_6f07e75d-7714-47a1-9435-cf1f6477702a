'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Service pour la gestion des templates d'écritures comptables
 */
class TemplateService {
  constructor(models) {
    this.models = models;
  }

  /**
   * Crée un nouveau template d'écriture
   * @param {Object} donnees - Données du template
   * @param {Array} lignes - Lignes du template
   * @param {Object} transaction - Transaction Sequelize (optionnelle)
   * @returns {Promise<Object>} Template créé
   */
  async creerTemplate(donnees, lignes, transaction = null) {
    const t = transaction || await this.models.sequelize.transaction();
    
    try {
      // Validation des données du template
      const validationTemplate = this.models.TemplateEcriture.validateTemplateData(donnees);
      if (!validationTemplate.valide) {
        throw new ValidationError(validationTemplate.erreurs.join(', '));
      }

      // Validation des lignes
      if (!lignes || lignes.length < 2) {
        throw new ValidationError('Un template doit avoir au moins 2 lignes');
      }

      for (const ligne of lignes) {
        const validationLigne = this.models.TemplateLigneEcriture.validateLigneTemplateData(ligne);
        if (!validationLigne.valide) {
          throw new ValidationError(`Ligne ${ligne.ordre || 'inconnue'}: ${validationLigne.erreurs.join(', ')}`);
        }
      }

      // Vérifier l'unicité du nom pour la société
      const templateExistant = await this.models.TemplateEcriture.findOne({
        where: {
          nom: donnees.nom,
          societeId: donnees.societeId,
          actif: true
        },
        transaction: t
      });

      if (templateExistant) {
        throw new ValidationError('Un template avec ce nom existe déjà pour cette société');
      }

      // Créer le template
      const template = await this.models.TemplateEcriture.create(donnees, { transaction: t });

      // Créer les lignes
      for (let i = 0; i < lignes.length; i++) {
        await this.models.TemplateLigneEcriture.create({
          ...lignes[i],
          templateId: template.id,
          ordre: lignes[i].ordre || (i + 1)
        }, { transaction: t });
      }

      // Recharger le template avec ses lignes
      const templateComplet = await this.models.TemplateEcriture.findByPk(template.id, {
        include: [
          {
            model: this.models.TemplateLigneEcriture,
            as: 'lignes',
            include: [
              {
                model: this.models.CompteComptable,
                as: 'compte',
                attributes: ['numero', 'libelle', 'classe', 'nature']
              }
            ],
            order: [['ordre', 'ASC']]
          },
          {
            model: this.models.Journal,
            as: 'journal',
            attributes: ['code', 'libelle', 'type']
          }
        ],
        transaction: t
      });

      if (!transaction) {
        await t.commit();
      }

      logger.info('Template créé avec succès', {
        templateId: template.id,
        nom: donnees.nom,
        societeId: donnees.societeId,
        nombreLignes: lignes.length
      });

      return templateComplet;

    } catch (error) {
      if (!transaction) {
        await t.rollback();
      }
      
      logger.error('Erreur lors de la création du template', {
        donnees,
        nombreLignes: lignes?.length,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Crée un template à partir d'une écriture existante
   * @param {string} ecritureId - ID de l'écriture source
   * @param {Object} donneesTemplate - Données du nouveau template
   * @returns {Promise<Object>} Template créé
   */
  async creerTemplateDepuisEcriture(ecritureId, donneesTemplate) {
    try {
      // Récupérer l'écriture source
      const ecriture = await this.models.EcritureComptable.findByPk(ecritureId, {
        include: [
          {
            model: this.models.LigneEcriture,
            as: 'lignes',
            include: [
              {
                model: this.models.CompteComptable,
                as: 'compte'
              }
            ],
            order: [['ordre', 'ASC']]
          }
        ]
      });

      if (!ecriture) {
        throw new NotFoundError('Écriture comptable');
      }

      // Préparer les données du template
      const donnees = {
        nom: donneesTemplate.nom,
        description: donneesTemplate.description || `Template créé depuis l'écriture ${ecriture.numeroEcriture}`,
        libelle: donneesTemplate.libelle || ecriture.libelle,
        journalCode: donneesTemplate.journalCode || ecriture.journalCode,
        categorie: donneesTemplate.categorie || 'AUTRE',
        societeId: donneesTemplate.societeId || ecriture.societeId,
        public: donneesTemplate.public || false,
        utilisateurCreation: donneesTemplate.utilisateurCreation
      };

      // Convertir les lignes d'écriture en lignes de template
      const lignes = ecriture.lignes.map((ligne, index) => ({
        compteNumero: ligne.compteNumero,
        libelle: ligne.libelle,
        sens: parseFloat(ligne.debit || 0) > 0 ? 'DEBIT' : 'CREDIT',
        montantFixe: parseFloat(ligne.debit || ligne.credit || 0),
        ordre: index + 1,
        obligatoire: true
      }));

      return await this.creerTemplate(donnees, lignes);

    } catch (error) {
      logger.error('Erreur lors de la création du template depuis écriture', {
        ecritureId,
        donneesTemplate,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Applique un template pour créer une nouvelle écriture
   * @param {string} templateId - ID du template
   * @param {Object} parametres - Valeurs des paramètres variables
   * @param {Object} donneesEcriture - Données spécifiques à l'écriture
   * @returns {Promise<Object>} Données de l'écriture à créer
   */
  async appliquerTemplate(templateId, parametres = {}, donneesEcriture = {}) {
    try {
      // Récupérer le template
      const template = await this.models.TemplateEcriture.findByPk(templateId, {
        include: [
          {
            model: this.models.TemplateLigneEcriture,
            as: 'lignes',
            include: [
              {
                model: this.models.CompteComptable,
                as: 'compte'
              }
            ],
            order: [['ordre', 'ASC']]
          },
          {
            model: this.models.Journal,
            as: 'journal'
          }
        ]
      });

      if (!template) {
        throw new NotFoundError('Template d\'écriture');
      }

      if (!template.actif) {
        throw new ValidationError('Ce template n\'est pas actif');
      }

      // Appliquer les paramètres au template
      const templateApplique = template.appliquerParametres(parametres);

      // Préparer les données de l'écriture
      const donnees = {
        dateEcriture: donneesEcriture.dateEcriture || new Date().toISOString().split('T')[0],
        libelle: donneesEcriture.libelle || templateApplique.libelle,
        journalCode: donneesEcriture.journalCode || template.journalCode,
        societeId: donneesEcriture.societeId || template.societeId,
        reference: donneesEcriture.reference || null,
        pieceJustificative: donneesEcriture.pieceJustificative || null,
        utilisateurCreation: donneesEcriture.utilisateurCreation
      };

      // Convertir les lignes de template en lignes d'écriture
      const lignes = template.lignes.map(ligneTemplate => {
        return ligneTemplate.toLigneEcriture(parametres);
      });

      // Vérifier l'équilibre
      const totalDebit = lignes.reduce((sum, ligne) => sum + parseFloat(ligne.debit || 0), 0);
      const totalCredit = lignes.reduce((sum, ligne) => sum + parseFloat(ligne.credit || 0), 0);
      
      if (Math.abs(totalDebit - totalCredit) > 0.01) {
        throw new ValidationError(`Écriture non équilibrée après application du template: Débit ${totalDebit}, Crédit ${totalCredit}`);
      }

      logger.info('Template appliqué avec succès', {
        templateId,
        templateNom: template.nom,
        parametres,
        totalDebit,
        totalCredit,
        nombreLignes: lignes.length
      });

      return {
        donnees,
        lignes,
        template: {
          id: template.id,
          nom: template.nom,
          categorie: template.categorie
        }
      };

    } catch (error) {
      logger.error('Erreur lors de l\'application du template', {
        templateId,
        parametres,
        donneesEcriture,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Obtient les templates d'une société avec filtres
   * @param {string} societeId - ID de la société
   * @param {Object} filtres - Filtres de recherche
   * @returns {Promise<Object>} Templates avec pagination
   */
  async getTemplates(societeId, filtres = {}) {
    try {
      const {
        categorie,
        journalCode,
        recherche,
        actif = true,
        public: isPublic,
        utilisateurCreation,
        page = 1,
        limit = 50
      } = filtres;

      // Construction des conditions WHERE
      const whereConditions = { societeId };

      if (actif !== undefined) {
        whereConditions.actif = actif;
      }

      if (categorie) {
        whereConditions.categorie = categorie;
      }

      if (journalCode) {
        whereConditions.journalCode = journalCode;
      }

      if (isPublic !== undefined) {
        whereConditions.public = isPublic;
      }

      if (utilisateurCreation) {
        whereConditions.utilisateurCreation = utilisateurCreation;
      }

      // Recherche textuelle
      if (recherche) {
        whereConditions[this.models.Sequelize.Op.or] = [
          { nom: { [this.models.Sequelize.Op.iLike]: `%${recherche}%` } },
          { description: { [this.models.Sequelize.Op.iLike]: `%${recherche}%` } },
          { tagsRecherche: { [this.models.Sequelize.Op.iLike]: `%${recherche}%` } }
        ];
      }

      // Pagination
      const offset = (page - 1) * limit;

      // Requête avec comptage
      const { count, rows } = await this.models.TemplateEcriture.findAndCountAll({
        where: whereConditions,
        include: [
          {
            model: this.models.TemplateLigneEcriture,
            as: 'lignes',
            include: [
              {
                model: this.models.CompteComptable,
                as: 'compte',
                attributes: ['numero', 'libelle', 'classe', 'nature']
              }
            ],
            order: [['ordre', 'ASC']]
          },
          {
            model: this.models.Journal,
            as: 'journal',
            attributes: ['code', 'libelle', 'type']
          }
        ],
        order: [['categorie', 'ASC'], ['nom', 'ASC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      // Formater les templates
      const templates = rows.map(template => template.formater());

      logger.info('Templates récupérés', {
        societeId,
        filtres,
        total: count,
        page,
        limit
      });

      return {
        templates,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        },
        filtres
      };

    } catch (error) {
      logger.error('Erreur lors de la récupération des templates', {
        societeId,
        filtres,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Obtient un template par son ID
   * @param {string} templateId - ID du template
   * @returns {Promise<Object>} Template complet
   */
  async getTemplateById(templateId) {
    try {
      const template = await this.models.TemplateEcriture.findByPk(templateId, {
        include: [
          {
            model: this.models.TemplateLigneEcriture,
            as: 'lignes',
            include: [
              {
                model: this.models.CompteComptable,
                as: 'compte',
                attributes: ['numero', 'libelle', 'classe', 'nature']
              }
            ],
            order: [['ordre', 'ASC']]
          },
          {
            model: this.models.Journal,
            as: 'journal',
            attributes: ['code', 'libelle', 'type']
          },
          {
            model: this.models.Societe,
            as: 'societe',
            attributes: ['id', 'nom']
          }
        ]
      });

      if (!template) {
        throw new NotFoundError('Template d\'écriture');
      }

      return template.formater();

    } catch (error) {
      logger.error('Erreur lors de la récupération du template', {
        templateId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Supprime un template (suppression logique)
   * @param {string} templateId - ID du template
   * @returns {Promise<boolean>} Succès de la suppression
   */
  async supprimerTemplate(templateId) {
    try {
      const template = await this.models.TemplateEcriture.findByPk(templateId);

      if (!template) {
        throw new NotFoundError('Template d\'écriture');
      }

      // Suppression logique
      await template.update({ actif: false });

      logger.info('Template supprimé (logiquement)', {
        templateId,
        nom: template.nom
      });

      return true;

    } catch (error) {
      logger.error('Erreur lors de la suppression du template', {
        templateId,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = TemplateService;
