# Plan de Travail Détaillé - Backend SYSCOHADA

## Analyse Comparative : État Actuel vs Guide SYSCOHADA

### ✅ **DÉJÀ IMPLÉMENTÉ**

#### Modèles de Données
- ✅ **Société** : Modèle `Societe` complet avec exercices comptables
- ✅ **Comptes Comptables** : Mod<PERSON>le `CompteComptable` avec hiérarchie
- ✅ **Journaux** : Modèle `Journal` avec types et codes
- ✅ **Écritures** : Modèles `EcritureComptable` et `LigneEcriture`
- ✅ **Exercices** : Modèle `ExerciceComptable` pour gestion périodes
- ✅ **Paramètres** : Modèle `ParametreComptable` pour configuration
- ✅ **Templates** : Modèles `TemplateEcriture` et `TemplateLigneEcriture`
- ✅ **Devises** : Modèles `Devise` et `TauxChange`
- ✅ **Audit** : Mod<PERSON><PERSON> `AuditLog` pour traçabilité

#### Controllers et Routes
- ✅ **Société** : CRUD complet avec statistiques
- ✅ **Comptes** : Gestion plan comptable
- ✅ **Journaux** : Gestion journaux comptables
- ✅ **Écritures** : Saisie et validation écritures
- ✅ **Lettrage** : Fonctionnalités de lettrage
- ✅ **États** : Génération rapports comptables
- ✅ **Analyses** : Analyses financières
- ✅ **Calculs** : Outils de calcul comptable
- ✅ **Import/Export** : Fonctionnalités d'échange
- ✅ **Dashboard** : Tableaux de bord
- ✅ **Clôture** : Processus de clôture d'exercice

#### Services Métier
- ✅ **Validation** : Service de validation des données
- ✅ **Calculs** : Services de calculs comptables
- ✅ **États** : Génération d'états comptables
- ✅ **Lettrage** : Logique de lettrage automatique
- ✅ **Audit** : Traçabilité des opérations

---

## 🔄 **À AMÉLIORER/COMPLÉTER**

### 1. **MODÈLES DE DONNÉES - Alignement Guide SYSCOHADA**

#### 1.1 Modèle Société → Company (Renommage et Enrichissement)
**Priorité : HAUTE**
- [ ] Renommer `Societe` → `Company` pour cohérence avec guide
- [ ] Ajouter champs manquants :
  - `numero_rccm` (Registre Commerce)
  - `regime_fiscal` (ENUM: REEL_NORMAL, REEL_SIMPLIFIE, SYNTHETIQUE)
  - `statut` (ENUM: ACTIF, SUSPENDU, FERME)
  - `logo_url`
- [ ] Modifier contraintes :
  - `nom` : 255 caractères (actuellement 100)
  - `telephone` : 50 caractères avec validation pattern
  - `email` : 255 caractères (actuellement 50)

#### 1.2 Modèle Tiers/Parties (MANQUANT - CRITIQUE)
**Priorité : CRITIQUE**
- [ ] Créer modèle `Party` pour clients/fournisseurs
- [ ] Champs requis :
  - `code`, `nom`, `type` (CLIENT/FOURNISSEUR/CLIENT_FOURNISSEUR)
  - `civilite`, `adresse`, `ville`, `pays`
  - `compte_comptable`, `conditions_paiement`, `plafond_credit`
  - `numero_contribuable`, `assujetti_tva`
- [ ] Relations avec `EcritureComptable` et `LigneEcriture`

#### 1.3 Modèle Écritures - Enrichissement
**Priorité : HAUTE**
- [ ] Ajouter champs manquants dans `EcritureComptable` :
  - `date_piece`, `date_echeance`
  - `reference_externe`
  - `total_debit`, `total_credit` (contrôle)
  - `user_validation_id`, `date_validation`
  - `lettrage_global`, `lettre` (boolean)
- [ ] Ajouter champs dans `LigneEcriture` :
  - `tiers_id` (référence vers Party)
  - `tiers_nom` (cache)
  - `date_echeance`, `mode_reglement`
  - `section_analytique`
  - `quantite`, `unite`, `prix_unitaire`
  - `devise`, `cours`, `montant_devise`

#### 1.4 Modèle Amortissements (MANQUANT)
**Priorité : MOYENNE**
- [ ] Créer modèle `Depreciation`
- [ ] Champs : immobilisation, comptes, méthodes, durées
- [ ] Logique de calcul automatique

### 2. **CONTROLLERS - Alignement Fonctionnel**

#### 2.1 CompanyController - Enrichissement
**Priorité : HAUTE**
- [ ] Méthode `closeExercise()` complète
- [ ] Initialisation automatique plan comptable SYSCOHADA
- [ ] Création journaux par défaut
- [ ] Validation contraintes métier

#### 2.2 PartyController (NOUVEAU - CRITIQUE)
**Priorité : CRITIQUE**
- [ ] CRUD complet pour tiers
- [ ] Recherche clients/fournisseurs
- [ ] Calcul soldes tiers
- [ ] Gestion comptes auxiliaires automatique

#### 2.3 MovementController (NOUVEAU - HAUTE)
**Priorité : HAUTE**
- [ ] Écritures automatiques de vente avec choix de comptes
- [ ] Écritures automatiques d'achat avec choix de comptes
- [ ] Écritures de paiement/encaissement avec choix de comptes
- [ ] Virements internes
- [ ] Écritures de salaire
- [ ] **Système de configuration à deux niveaux :**
  - [ ] Configuration comptes par défaut (niveau société)
  - [ ] Override comptes par écriture (niveau transaction)
- [ ] **Modes de saisie :**
  - [ ] Mode Simple (comptes automatiques)
  - [ ] Mode Avancé (contrôle total utilisateur)
- [ ] **Interface de sélection comptes intelligente**
- [ ] **Validation temps réel des comptes choisis**

#### 2.4 ReportController - Complétion
**Priorité : MOYENNE**
- [ ] Bilan comptable SYSCOHADA
- [ ] Compte de résultat SYSCOHADA
- [ ] Tableau de flux de trésorerie
- [ ] Grand livre détaillé
- [ ] Balance générale

### 3. **SERVICES MÉTIER - Logiques Avancées**

#### 3.1 EntryValidationService (NOUVEAU - CRITIQUE)
**Priorité : CRITIQUE**
- [ ] Validation équilibre débit/crédit
- [ ] Contrôle existence comptes
- [ ] Validation dates exercice
- [ ] Contrôle numéros pièce uniques
- [ ] Validation spécifique par type mouvement
- [ ] **Validation cohérence comptes choisis** (ex: classe 7 pour ventes)
- [ ] **Suggestions comptes alternatifs** en cas d'erreur

#### 3.2 AccountConfigService (NOUVEAU - HAUTE)
**Priorité : HAUTE**
- [ ] **Gestion configuration comptes par défaut**
  - [ ] Sauvegarde/récupération par société
  - [ ] Configuration par type d'opération (VENTE, ACHAT, TRESORERIE)
  - [ ] Validation cohérence configuration
- [ ] **Service de sélection comptes intelligente**
  - [ ] Recherche comptes par numéro/intitulé
  - [ ] Filtrage par classe comptable
  - [ ] Comptes favoris par utilisateur
  - [ ] Suggestions basées sur historique

#### 3.3 AccountingCalculationService (NOUVEAU - HAUTE)
**Priorité : HAUTE**
- [ ] Calcul soldes comptes avec sens normal
- [ ] Calcul résultat exercice
- [ ] Calcul ratios financiers
- [ ] Calculs de balance générale
- [ ] Calculs de bilan/compte résultat

#### 3.4 ExerciseService (NOUVEAU - MOYENNE)
**Priorité : MOYENNE**
- [ ] Processus complet clôture exercice
- [ ] Écritures de détermination résultat
- [ ] Écritures de clôture/réouverture
- [ ] Validation pré-requis clôture

### 4. **API ENDPOINTS - Complétion Routes**

#### 4.1 Routes Tiers (NOUVELLES - CRITIQUE)
**Priorité : CRITIQUE**
```
GET    /api/parties             # Liste des tiers
POST   /api/parties             # Créer un tiers
GET    /api/parties/:id         # Détail d'un tiers
PUT    /api/parties/:id         # Modifier un tiers
DELETE /api/parties/:id         # Supprimer un tiers
GET    /api/parties/clients     # Liste des clients
GET    /api/parties/fournisseurs # Liste des fournisseurs
GET    /api/parties/:id/balance # Solde d'un tiers
```

#### 4.2 Routes Mouvements Automatiques (NOUVELLES - HAUTE)
**Priorité : HAUTE**
```
POST   /api/movements/sale      # Écriture de vente
POST   /api/movements/purchase  # Écriture d'achat
POST   /api/movements/payment   # Écriture de paiement
POST   /api/movements/receipt   # Écriture d'encaissement
POST   /api/movements/transfer  # Virement interne
POST   /api/movements/salary    # Écriture de salaire
```

#### 4.3 Routes Configuration Comptes (NOUVELLES - HAUTE)
**Priorité : HAUTE**
```
GET    /api/configuration/accounts/defaults/:type    # Récupérer comptes par défaut
PUT    /api/configuration/accounts/defaults/:type    # Sauvegarder comptes par défaut
GET    /api/configuration/accounts/available/:classe # Comptes disponibles par classe
GET    /api/configuration/accounts/search            # Recherche intelligente comptes
POST   /api/configuration/accounts/favorites         # Gérer comptes favoris
GET    /api/configuration/accounts/suggestions       # Suggestions basées historique
```

#### 4.4 Routes Amortissements (NOUVELLES - MOYENNE)
**Priorité : MOYENNE**
```
GET    /api/depreciations                # Liste des amortissements
POST   /api/depreciations                # Créer un amortissement
GET    /api/depreciations/:id            # Détail amortissement
PUT    /api/depreciations/:id            # Modifier amortissement
DELETE /api/depreciations/:id            # Supprimer amortissement
GET    /api/depreciations/:id/plan       # Plan d'amortissement
POST   /api/depreciations/calculate      # Calculer dotations exercice
POST   /api/depreciations/generate-entries # Générer écritures dotations
```

### 5. **CONTRAINTES TECHNIQUES - Sécurité et Performance**

#### 5.1 Contraintes Base de Données (MANQUANTES - HAUTE)
**Priorité : HAUTE**
- [ ] Contrainte équilibre : `CHECK (ABS(total_debit - total_credit) < 0.01)`
- [ ] Contrainte débit/crédit : `CHECK ((debit > 0 AND credit = 0) OR (debit = 0 AND credit > 0))`
- [ ] Contrainte numéro compte/classe : `CHECK (LEFT(numero, 1)::INTEGER = classe)`
- [ ] Index performance sur dates, comptes, sociétés

#### 5.2 Middleware Sécurité (PARTIELLEMENT IMPLÉMENTÉ)
**Priorité : HAUTE**
- [ ] Middleware contrôle accès société
- [ ] Validation JWT avec refresh tokens
- [ ] Audit trail complet
- [ ] Rate limiting par utilisateur

#### 5.3 Cache et Performance (MANQUANT - MOYENNE)
**Priorité : MOYENNE**
- [ ] Cache Redis pour balances
- [ ] Jobs arrière-plan pour rapports lourds
- [ ] Optimisation requêtes avec index
- [ ] Pagination automatique

### 6. **TESTS ET QUALITÉ**

#### 6.1 Tests Unitaires (PARTIELLEMENT IMPLÉMENTÉ)
**Priorité : MOYENNE**
- [ ] Tests modèles avec validations
- [ ] Tests controllers avec mocks
- [ ] Tests services métier
- [ ] Tests middleware sécurité

#### 6.2 Tests d'Intégration (MANQUANT)
**Priorité : MOYENNE**
- [ ] Tests API endpoints complets
- [ ] Tests workflows comptables
- [ ] Tests performance sur gros volumes
- [ ] Tests sécurité et autorisation

---

## 📋 **PLAN D'EXÉCUTION PAR PHASES**

### **PHASE 1 - FONDATIONS CRITIQUES (Semaines 1-2)**
1. Créer modèle `Party` (tiers)
2. Enrichir modèles `Company`, `EcritureComptable`, `LigneEcriture`
3. Créer `PartyController` et routes
4. Implémenter `EntryValidationService`
5. Ajouter contraintes base de données

### **PHASE 2 - LOGIQUES MÉTIER (Semaines 3-4)**
1. Créer `MovementController` pour écritures automatiques
2. Implémenter `AccountingCalculationService`
3. Enrichir `ReportController` avec états SYSCOHADA
4. Compléter middleware sécurité
5. Tests unitaires critiques

### **PHASE 3 - FONCTIONNALITÉS AVANCÉES (Semaines 5-6)**
1. Créer modèle et controller `Depreciation`
2. Implémenter `ExerciseService` pour clôtures
3. Ajouter cache Redis et optimisations
4. Jobs arrière-plan pour rapports
5. Tests d'intégration

### **PHASE 4 - FINALISATION (Semaine 7)**
1. Documentation API complète
2. Tests de performance
3. Audit sécurité
4. Déploiement et monitoring

---

## 🎯 **PRIORITÉS IMMÉDIATES**

### **CRITIQUE (À faire en premier)**
1. **Modèle Party** - Indispensable pour tiers
2. **EntryValidationService** - Sécurité données comptables
3. **Enrichissement modèles** - Conformité SYSCOHADA
4. **MovementController** - Écritures automatiques

### **HAUTE (Semaine suivante)**
1. **AccountingCalculationService** - Calculs comptables
2. **Contraintes BDD** - Intégrité données
3. **Middleware sécurité** - Protection API
4. **Routes mouvements** - API complète

### **MOYENNE (Après fondations)**
1. **Amortissements** - Fonctionnalité avancée
2. **Cache/Performance** - Optimisation
3. **Tests complets** - Qualité
4. **Documentation** - Maintenance

---

*Ce plan assure une progression logique du backend vers la conformité complète SYSCOHADA tout en maintenant la stabilité de l'existant.*
