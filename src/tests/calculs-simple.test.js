'use strict';

// Mock du logger pour éviter les erreurs dans les tests
jest.mock('../config/logger', () => ({
  info: jest.fn(),
  debug: jest.fn(),
  error: jest.fn(),
  warn: jest.fn()
}));

// Mock du middleware errorHandler
jest.mock('../middleware/errorHandler', () => ({
  ValidationError: class ValidationError extends Error {
    constructor(message) {
      super(message);
      this.name = 'ValidationError';
    }
  },
  NotFoundError: class NotFoundError extends Error {
    constructor(message) {
      super(message);
      this.name = 'NotFoundError';
    }
  }
}));

const CalculService = require('../services/calculService');

describe('CalculService - Tests unitaires', () => {
  let calculService;

  beforeAll(() => {
    calculService = new CalculService();
  });

  describe('Méthodes utilitaires', () => {
    test('devrait valider les périodes correctement', () => {
      expect(() => {
        calculService.validerPeriode(null, new Date());
      }).toThrow('obligatoires');

      expect(() => {
        calculService.validerPeriode(new Date('2024-12-31'), new Date('2024-01-01'));
      }).toThrow('antérieure');

      expect(() => {
        calculService.validerPeriode(new Date('2024-01-01'), new Date('2025-12-31'));
      }).toThrow('366 jours');

      // Test valide
      expect(() => {
        calculService.validerPeriode(new Date('2024-01-01'), new Date('2024-12-31'));
      }).not.toThrow();
    });

    test('devrait formater les montants correctement', () => {
      expect(calculService.formaterMontant(1234.56)).toMatch(/1\s*234,56/);
      expect(calculService.formaterMontant(0)).toMatch(/0,00/);
      expect(calculService.formaterMontant(null)).toBe('0,00');
      expect(calculService.formaterMontant(undefined)).toBe('0,00');
      expect(calculService.formaterMontant('invalid')).toBe('0,00');
    });

    test('devrait obtenir les libellés de classes', () => {
      expect(calculService.getLibelleClasse(1)).toBe('Comptes de capitaux');
      expect(calculService.getLibelleClasse(2)).toBe('Comptes d\'immobilisations');
      expect(calculService.getLibelleClasse(3)).toBe('Comptes de stocks');
      expect(calculService.getLibelleClasse(4)).toBe('Comptes de tiers');
      expect(calculService.getLibelleClasse(5)).toBe('Comptes de trésorerie');
      expect(calculService.getLibelleClasse(6)).toBe('Comptes de charges');
      expect(calculService.getLibelleClasse(7)).toBe('Comptes de produits');
      expect(calculService.getLibelleClasse(8)).toBe('Comptes spéciaux');
      expect(calculService.getLibelleClasse(9)).toBe('Classe 9');
    });
  });

  describe('Gestion du cache', () => {
    test('devrait initialiser le cache correctement', () => {
      expect(calculService.cache).toBeDefined();
      expect(calculService.cache instanceof Map).toBe(true);
      expect(calculService.cacheTimeout).toBe(5 * 60 * 1000); // 5 minutes
    });

    test('devrait obtenir les statistiques du cache', () => {
      const stats = calculService.getStatistiquesCache();
      expect(stats).toBeDefined();
      expect(stats.totalEntrees).toBeDefined();
      expect(stats.entreeValides).toBeDefined();
      expect(stats.entreeExpirees).toBeDefined();
      expect(stats.tauxHit).toBeDefined();
    });

    test('devrait vider le cache', () => {
      // Ajouter une entrée fictive
      calculService.cache.set('test', { data: 'test', timestamp: Date.now() });
      expect(calculService.cache.size).toBe(1);

      // Vider le cache
      calculService.viderCache();
      expect(calculService.cache.size).toBe(0);
    });

    test('devrait nettoyer les entrées expirées', () => {
      // Ajouter des entrées avec différents timestamps
      const maintenant = Date.now();
      calculService.cache.set('recent', { data: 'recent', timestamp: maintenant });
      calculService.cache.set('expired', { data: 'expired', timestamp: maintenant - (10 * 60 * 1000) }); // 10 minutes ago

      const entreesSupprimees = calculService.nettoyerCache();
      expect(entreesSupprimees).toBe(1); // Une entrée expirée supprimée
      expect(calculService.cache.has('recent')).toBe(true);
      expect(calculService.cache.has('expired')).toBe(false);
    });
  });

  describe('Validation des paramètres', () => {
    test('devrait valider les dates correctement', () => {
      const dateDebut = new Date('2024-01-01');
      const dateFin = new Date('2024-12-31');

      expect(() => {
        calculService.validerPeriode(dateDebut, dateFin);
      }).not.toThrow();
    });

    test('devrait rejeter les dates invalides', () => {
      expect(() => {
        calculService.validerPeriode(null, new Date());
      }).toThrow('obligatoires');

      expect(() => {
        calculService.validerPeriode(new Date(), null);
      }).toThrow('obligatoires');
    });

    test('devrait rejeter les périodes trop longues', () => {
      const dateDebut = new Date('2024-01-01');
      const dateFin = new Date('2025-12-31'); // Plus d'un an

      expect(() => {
        calculService.validerPeriode(dateDebut, dateFin);
      }).toThrow('366 jours');
    });
  });

  describe('Formatage', () => {
    test('devrait formater les montants avec différentes devises', () => {
      const resultEUR = calculService.formaterMontant(1000, 'EUR');
      const resultUSD = calculService.formaterMontant(1000, 'USD');
      const resultXOF = calculService.formaterMontant(1000);

      expect(resultEUR).toMatch(/€|EUR/);
      expect(resultUSD).toMatch(/\$|USD/);
      expect(resultXOF).toMatch(/XOF|F CFA/);
    });

    test('devrait gérer les montants négatifs', () => {
      const resultat = calculService.formaterMontant(-1234.56);
      expect(resultat).toContain('-');
      expect(resultat).toMatch(/1\s*234,56/);
    });

    test('devrait gérer les très grands nombres', () => {
      const resultat = calculService.formaterMontant(1234567890.12);
      expect(resultat).toMatch(/1\s*234\s*567\s*890,12/);
    });
  });

  describe('Configuration', () => {
    test('devrait avoir les bonnes valeurs par défaut', () => {
      expect(calculService.cacheTimeout).toBe(5 * 60 * 1000);
      expect(calculService.models).toBeDefined();
      expect(calculService.cache).toBeInstanceOf(Map);
    });

    test('devrait accepter des modèles personnalisés', () => {
      const mockModels = { test: 'mock' };
      const serviceAvecModeles = new CalculService(mockModels);
      expect(serviceAvecModeles.models).toBe(mockModels);
    });
  });
});
