'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Ajouter les champs de personnalisation au modèle CompteComptable
    await queryInterface.addColumn('compte_comptables', 'personnalise', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'true si le compte a été créé par l\'utilisateur'
    });

    await queryInterface.addColumn('compte_comptables', 'modifiable', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'false pour les comptes SYSCOHADA standard'
    });

    await queryInterface.addColumn('compte_comptables', 'date_creation', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: 'Date de création du compte personnalisé'
    });

    await queryInterface.addColumn('compte_comptables', 'utilisateur_creation', {
      type: Sequelize.UUID,
      allowNull: true,
      comment: 'ID de l\'utilisateur qui a créé le compte'
    });

    await queryInterface.addColumn('compte_comptables', 'raison_sociale', {
      type: Sequelize.STRING(255),
      allowNull: true,
      comment: 'Raison sociale pour les comptes clients/fournisseurs'
    });

    await queryInterface.addColumn('compte_comptables', 'actif', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Compte actif ou désactivé'
    });

    await queryInterface.addColumn('compte_comptables', 'obligatoire_lettrage', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Lettrage obligatoire pour ce compte'
    });

    await queryInterface.addColumn('compte_comptables', 'type_analytique', {
      type: Sequelize.ENUM('AUCUN', 'CENTRE_COUT', 'PROJET'),
      allowNull: false,
      defaultValue: 'AUCUN',
      comment: 'Type de gestion analytique'
    });

    // Ajouter des index pour améliorer les performances
    await queryInterface.addIndex('compte_comptables', {
      fields: ['societe_id', 'personnalise'],
      name: 'idx_compte_societe_personnalise'
    });

    await queryInterface.addIndex('compte_comptables', {
      fields: ['societe_id', 'actif'],
      name: 'idx_compte_societe_actif'
    });

    await queryInterface.addIndex('compte_comptables', {
      fields: ['obligatoire_lettrage'],
      name: 'idx_compte_lettrage'
    });

    // Mettre à jour les comptes existants comme étant des comptes standard
    await queryInterface.sequelize.query(`
      UPDATE compte_comptables
      SET personnalise = false,
          modifiable = false,
          date_creation = created_at
      WHERE numero IN (
        SELECT DISTINCT numero FROM compte_comptables
        WHERE LENGTH(numero) <= 3 OR numero LIKE '1%' OR numero LIKE '2%'
        OR numero LIKE '3%' OR numero LIKE '4%' OR numero LIKE '5%'
        OR numero LIKE '6%' OR numero LIKE '7%' OR numero LIKE '8%'
      )
    `);
  },

  async down (queryInterface, Sequelize) {
    // Supprimer les index
    await queryInterface.removeIndex('compte_comptables', 'idx_compte_societe_personnalise');
    await queryInterface.removeIndex('compte_comptables', 'idx_compte_societe_actif');
    await queryInterface.removeIndex('compte_comptables', 'idx_compte_lettrage');

    // Supprimer les colonnes
    await queryInterface.removeColumn('compte_comptables', 'personnalise');
    await queryInterface.removeColumn('compte_comptables', 'modifiable');
    await queryInterface.removeColumn('compte_comptables', 'date_creation');
    await queryInterface.removeColumn('compte_comptables', 'utilisateur_creation');
    await queryInterface.removeColumn('compte_comptables', 'raison_sociale');
    await queryInterface.removeColumn('compte_comptables', 'actif');
    await queryInterface.removeColumn('compte_comptables', 'obligatoire_lettrage');
    await queryInterface.removeColumn('compte_comptables', 'type_analytique');
  }
};
