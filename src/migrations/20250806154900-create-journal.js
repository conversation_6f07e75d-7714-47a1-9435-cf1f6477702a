'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('journaux', {
      code: {
        type: Sequelize.STRING(10),
        primaryKey: true,
        allowNull: false
      },
      libelle: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      type: {
        type: Sequelize.ENUM('BANQUE', 'VENTE', 'ACHAT', 'OD', 'CAISSE'),
        allowNull: false
      },
      compte_contropartie: {
        type: Sequelize.STRING(10),
        allowNull: true,
        references: {
          model: 'compte_comptables',
          key: 'numero'
        }
      },
      societe_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'societes',
          key: 'id'
        }
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Index pour améliorer les performances
    await queryInterface.addIndex('journaux', ['type']);
    await queryInterface.addIndex('journaux', ['societe_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('journaux');
  }
};