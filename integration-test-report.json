{"summary": {"totalTests": 31, "successTests": 3, "failedTests": 28, "successRate": "9.7%", "duration": "33s", "timestamp": "2025-08-09T18:20:39.465Z"}, "results": [{"command": "--help", "description": "Affichage de l'aide", "success": true, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  enable debug logging with { debug: true }\nUsage: syscohada-cli [options] [command]\n\nCLI pour interagir avec l'API Comptabilité SYSCOHADA\n\nOptio", "timestamp": "2025-08-09T18:20:09.649Z"}, {"command": "--version", "description": "Affichage de la version", "success": true, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  suppress all logs with { quiet: true }\n1.0.0\n", "timestamp": "2025-08-09T18:20:10.636Z"}, {"command": "status", "description": "Vérification du statut de l'API", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 📡 observe env with Radar: https://dotenvx.com/radar\n\u001b[90m┌───────────────\u001b[39m\u001b[90m┬────────────────────────────┐\u001b[39m\n\u001b[90m│\u001b[39m\u001b[31m <PERSON><PERSON><PERSON><PERSON><PERSON> ", "timestamp": "2025-08-09T18:20:11.588Z"}, {"command": "auth:verify", "description": "Vérification de la clé API", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 🔐 encrypt with Dotenvx: https://dotenvx.com\n\u001b[90m┌─────────────\u001b[39m\u001b[90m┬──────────────────────────────────────┐\u001b[39m\n\u001b[90m│\u001b[39m\u001b[31m <PERSON><PERSON><PERSON><PERSON><PERSON> ", "timestamp": "2025-08-09T18:20:14.096Z"}, {"command": "apikeys:list", "description": "Liste des clés API", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 📡 version env with Radar: https://dotenvx.com/radar\n\u001b[90m┌───────────\u001b[39m\u001b[90m┬────────────────────\u001b[39m\u001b[90m┬─────────────\u001b[39m\u001b[90m┬────────\u001b[3", "timestamp": "2025-08-09T18:20:16.385Z"}, {"command": "apikeys:list --include-inactive", "description": "Liste avec clés inactives", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 📡 auto-backup env with Radar: https://dotenvx.com/radar\n\u001b[90m┌───────────\u001b[39m\u001b[90m┬────────────────────\u001b[39m\u001b[90m┬─────────────\u001b[39m\u001b[90m┬───────", "timestamp": "2025-08-09T18:20:18.768Z"}, {"command": "apikeys:list --limit 5", "description": "Liste avec limite", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 🔐 prevent building .env in docker: https://dotenvx.com/prebuild\n\u001b[90m┌───────────\u001b[39m\u001b[90m┬────────────────────\u001b[39m\u001b[90m┬─────────────\u001b[39m\u001b[90m", "timestamp": "2025-08-09T18:20:21.147Z"}, {"command": "societes:list", "description": "Liste des sociétés", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  enable debug logging with { debug: true }\n\u001b[90m┌─────────────\u001b[39m\u001b[90m┬───────────────────────\u001b[39m\u001b[90m┬──────\u001b[39m\u001b[90m┬───────────────\u001b[39m", "timestamp": "2025-08-09T18:20:22.096Z"}, {"command": "exercices:list", "description": "Liste des exercices", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 🛠️  run anywhere with `dotenvx run -- yourcommand`\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:22.688Z"}, {"command": "exercices:vue-clotures", "description": "Vue des clôtures", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  suppress all logs with { quiet: true }\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:23.420Z"}, {"command": "comptes:list", "description": "Liste des comptes", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 🔐 prevent building .env in docker: https://dotenvx.com/prebuild\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:24.156Z"}, {"command": "comptes:list --classe 1", "description": "Comptes classe 1", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] }\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:24.732Z"}, {"command": "comptes:list --limit 10", "description": "Comptes avec limite", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 📡 observe env with Radar: https://dotenvx.com/radar\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:25.323Z"}, {"command": "plan:personnalise", "description": "Plan comptable personnalisé", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  specify custom .env file path with { path: '/custom/path/.env' }\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:25.916Z"}, {"command": "journaux:list", "description": "Liste des journaux", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  write to custom object with { processEnv: myObject }\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:26.559Z"}, {"command": "ecritures:list", "description": "Liste des écritures", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  specify custom .env file path with { path: '/custom/path/.env' }\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:27.138Z"}, {"command": "ecritures:list --limit 5", "description": "Écritures avec limite", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  specify custom .env file path with { path: '/custom/path/.env' }\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:27.856Z"}, {"command": "ecritures:list --statut BROUILLARD", "description": "Écritures en brouillard", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  write to custom object with { processEnv: myObject }\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:28.475Z"}, {"command": "ecritures:list --statut VALIDEE", "description": "Écritures validées", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 📡 observe env with Radar: https://dotenvx.com/radar\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:29.083Z"}, {"command": "templates:list", "description": "Liste des templates", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 🔐 prevent committing .env to code: https://dotenvx.com/precommit\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:29.673Z"}, {"command": "templates:list --limit 10", "description": "Templates avec limite", "success": false, "error": "Command failed: node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js templates:list --limit 10\nerror: unknown option '--limit'\n", "timestamp": "2025-08-09T18:20:30.237Z"}, {"command": "tiers:list", "description": "Liste des tiers", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 📡 observe env with Radar: https://dotenvx.com/radar\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:30.828Z"}, {"command": "tiers:list --limit 10", "description": "Tiers avec limite", "success": false, "error": "Command failed: node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js tiers:list --limit 10\nerror: unknown option '--limit'\n", "timestamp": "2025-08-09T18:20:31.394Z"}, {"command": "lettrage:comptes", "description": "Comptes à lettrer", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 🔐 encrypt with Dotenvx: https://dotenvx.com\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:32.003Z"}, {"command": "amortissements:list", "description": "Liste des amortissements", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  enable debug logging with { debug: true }\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:32.636Z"}, {"command": "amortissements:calculer-dotations", "description": "Calcul des dotations", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 📡 auto-backup env with Radar: https://dotenvx.com/radar\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:33.218Z"}, {"command": "dashboard", "description": "Tableau de bord", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] }\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:33.818Z"}, {"command": "parametres:list", "description": "Liste des paramètres", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: ⚙️  enable debug logging with { debug: true }\nUtilisez la commande \"societes:select\" pour sélectionner une société\n", "timestamp": "2025-08-09T18:20:34.406Z"}, {"command": "export:ecritures --format json --limit 5", "description": "Export écritures JSON", "success": false, "error": "Command failed: node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js export:ecritures --format json --limit 5\nerror: unknown option '--format'\n", "timestamp": "2025-08-09T18:20:34.984Z"}, {"command": "commande-inexistante", "description": "Commande inexistante", "success": true, "error": "Command failed: node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js commande-inexistante\nerror: unknown command 'commande-inexistante'\n", "timestamp": "2025-08-09T18:20:35.555Z"}, {"command": "ecritures:show 999999", "description": "Écriture inexistante", "success": false, "output": "[dotenv@17.2.1] injecting env (17) from .env -- tip: 🔐 prevent committing .env to code: https://dotenvx.com/precommit\n", "timestamp": "2025-08-09T18:20:39.460Z"}]}