'use strict';

const { logger } = require('../config/logger');
const PersonnalisationPlanService = require('../services/personnalisationPlanService');
const ImportExportService = require('../services/importExportService');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Controller pour la personnalisation du plan comptable
 */
class PlanComptableController {
  constructor(models) {
    this.models = models;
    this.personnalisationService = new PersonnalisationPlanService(models);
    this.importExportService = new ImportExportService(models);
  }

  /**
   * Crée un nouveau compte personnalisé
   */
  async creerComptePersonnalise(req, res, next) {
    try {
      const { societeId } = req.params;
      const compteData = req.body;
      const utilisateurId = req.user?.id || null;

      const compte = await this.personnalisationService.creerComptePersonnalise(
        societeId, 
        compteData, 
        utilisateurId
      );

      res.status(201).json({
        success: true,
        data: compte,
        message: 'Compte personnalisé créé avec succès'
      });

    } catch (error) {
      logger.error('Erreur lors de la création du compte personnalisé', {
        societeId: req.params.societeId,
        body: req.body,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Modifie un compte personnalisé
   */
  async modifierComptePersonnalise(req, res, next) {
    try {
      const { societeId, numero } = req.params;
      const updateData = req.body;
      const utilisateurId = req.user?.id || null;

      const compte = await this.personnalisationService.modifierComptePersonnalise(
        societeId, 
        numero, 
        updateData, 
        utilisateurId
      );

      res.json({
        success: true,
        data: compte,
        message: 'Compte personnalisé modifié avec succès'
      });

    } catch (error) {
      logger.error('Erreur lors de la modification du compte', {
        societeId: req.params.societeId,
        numero: req.params.numero,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Supprime un compte personnalisé
   */
  async supprimerComptePersonnalise(req, res, next) {
    try {
      const { societeId, numero } = req.params;
      const utilisateurId = req.user?.id || null;

      await this.personnalisationService.supprimerComptePersonnalise(
        societeId, 
        numero, 
        utilisateurId
      );

      res.json({
        success: true,
        message: 'Compte personnalisé supprimé avec succès'
      });

    } catch (error) {
      logger.error('Erreur lors de la suppression du compte', {
        societeId: req.params.societeId,
        numero: req.params.numero,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Active ou désactive un compte
   */
  async toggleCompteActif(req, res, next) {
    try {
      const { societeId, numero } = req.params;
      const { actif } = req.body;
      const utilisateurId = req.user?.id || null;

      const compte = await this.personnalisationService.toggleCompteActif(
        societeId, 
        numero, 
        actif, 
        utilisateurId
      );

      res.json({
        success: true,
        data: compte,
        message: `Compte ${actif ? 'activé' : 'désactivé'} avec succès`
      });

    } catch (error) {
      logger.error('Erreur lors de la modification de l\'état du compte', {
        societeId: req.params.societeId,
        numero: req.params.numero,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Obtient les statistiques de personnalisation
   */
  async getStatistiquesPersonnalisation(req, res, next) {
    try {
      const { societeId } = req.params;

      const statistiques = await this.personnalisationService.getStatistiquesPersonnalisation(societeId);

      res.json({
        success: true,
        data: statistiques
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques', {
        societeId: req.params.societeId,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Valide la cohérence du plan comptable
   */
  async validerCoherencePlan(req, res, next) {
    try {
      const { societeId } = req.params;

      const validation = await this.personnalisationService.validerCoherencePlan(societeId);

      res.json({
        success: true,
        data: validation
      });

    } catch (error) {
      logger.error('Erreur lors de la validation du plan comptable', {
        societeId: req.params.societeId,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Importe un plan comptable depuis un fichier
   */
  async importerPlanComptable(req, res, next) {
    try {
      const { societeId } = req.params;
      const utilisateurId = req.user?.id || null;
      
      if (!req.file) {
        throw new ValidationError('Aucun fichier fourni');
      }

      const options = {
        remplacerExistants: req.body.remplacerExistants === 'true',
        validerUniquement: req.body.validerUniquement === 'true',
        formatFichier: req.body.formatFichier || 'auto'
      };

      const resultat = await this.importExportService.importerPlanComptable(
        societeId,
        req.file.path,
        utilisateurId,
        options
      );

      res.json({
        success: true,
        data: resultat,
        message: resultat.message
      });

    } catch (error) {
      logger.error('Erreur lors de l\'import du plan comptable', {
        societeId: req.params.societeId,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Exporte le plan comptable vers un fichier
   */
  async exporterPlanComptable(req, res, next) {
    try {
      const { societeId } = req.params;
      const { 
        format = 'excel',
        includeInactifs = false,
        seulementPersonnalises = false,
        classes
      } = req.query;

      const options = {
        includeInactifs: includeInactifs === 'true',
        seulementPersonnalises: seulementPersonnalises === 'true',
        classes: classes ? classes.split(',').map(c => parseInt(c)) : null
      };

      const resultat = await this.importExportService.exporterPlanComptable(
        societeId,
        format,
        options
      );

      if (resultat.success) {
        // Envoyer le fichier en téléchargement
        res.download(resultat.cheminFichier, resultat.nomFichier, (err) => {
          if (err) {
            logger.error('Erreur lors du téléchargement', { error: err.message });
          }
          // Nettoyer le fichier temporaire après téléchargement
          setTimeout(() => {
            try {
              require('fs').unlinkSync(resultat.cheminFichier);
            } catch (cleanupError) {
              logger.warn('Impossible de supprimer le fichier temporaire', {
                fichier: resultat.cheminFichier,
                error: cleanupError.message
              });
            }
          }, 5000);
        });
      } else {
        throw new AppError('Erreur lors de l\'export');
      }

    } catch (error) {
      logger.error('Erreur lors de l\'export du plan comptable', {
        societeId: req.params.societeId,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Génère un template pour l'import
   */
  async genererTemplateImport(req, res, next) {
    try {
      const resultat = await this.importExportService.genererTemplateImport();

      if (resultat.success) {
        res.download(resultat.cheminFichier, resultat.nomFichier, (err) => {
          if (err) {
            logger.error('Erreur lors du téléchargement du template', { error: err.message });
          }
          // Nettoyer le fichier temporaire
          setTimeout(() => {
            try {
              require('fs').unlinkSync(resultat.cheminFichier);
            } catch (cleanupError) {
              logger.warn('Impossible de supprimer le template temporaire', {
                error: cleanupError.message
              });
            }
          }, 5000);
        });
      } else {
        throw new AppError('Erreur lors de la génération du template');
      }

    } catch (error) {
      logger.error('Erreur lors de la génération du template', {
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Obtient les comptes personnalisés d'une société
   */
  async getComptesPersonnalises(req, res, next) {
    try {
      const { societeId } = req.params;
      const { 
        page = 1, 
        limit = 20, 
        actif, 
        classe,
        search 
      } = req.query;

      const offset = (page - 1) * limit;
      const whereClause = { 
        societeId, 
        personnalise: true 
      };

      if (actif !== undefined) {
        whereClause.actif = actif === 'true';
      }

      if (classe) {
        whereClause.classe = parseInt(classe);
      }

      if (search) {
        whereClause[this.models.sequelize.Op.or] = [
          { numero: { [this.models.sequelize.Op.iLike]: `%${search}%` } },
          { libelle: { [this.models.sequelize.Op.iLike]: `%${search}%` } }
        ];
      }

      const { count, rows: comptes } = await this.models.CompteComptable.findAndCountAll({
        where: whereClause,
        order: [['numero', 'ASC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      res.json({
        success: true,
        data: comptes,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des comptes personnalisés', {
        societeId: req.params.societeId,
        error: error.message
      });
      next(error);
    }
  }
}

module.exports = PlanComptableController;
