# 🧮 Module Calculs et Soldes Comptables

## 📋 Vue d'ensemble

Le module de calculs et soldes fournit un ensemble complet de fonctionnalités pour effectuer tous les calculs comptables nécessaires dans un système SYSCOHADA. Il inclut le calcul de soldes de comptes, les balances générales et auxiliaires, les totaux par journal, et les statistiques comptables avec optimisations de performance.

## 🎯 Fonctionnalités principales

### Calculs de soldes
- ✅ **Solde de compte** à une date donnée avec cache intelligent
- ✅ **Soldes progressifs** sur une période avec groupement par jour/mois
- ✅ **Validation d'équilibre** automatique
- ✅ **Gestion du sens comptable** (débit/crédit) selon les normes SYSCOHADA

### Calculs de journaux
- ✅ **Totaux par journal** avec détails par compte
- ✅ **Vérification d'équilibre** des journaux
- ✅ **Statistiques d'utilisation** des journaux

### Balances comptables
- ✅ **Balance générale** avec filtres par classe, niveau, mouvement
- ✅ **Balance auxiliaire** pour comptes collectifs
- ✅ **Totaux par classe** de comptes
- ✅ **Export formaté** des balances

### Optimisations de performance
- ✅ **Cache intelligent** avec expiration automatique
- ✅ **Index de base de données** optimisés
- ✅ **Vues matérialisées** pour calculs fréquents
- ✅ **Fonctions stockées** PostgreSQL

## 🏗️ Architecture

### Services
- **CalculService** : Service principal pour tous les calculs
- **Cache intégré** : Système de cache avec nettoyage automatique
- **Optimisations DB** : Vues matérialisées et fonctions stockées

### Modèles impliqués
- `EcritureComptable` : Écritures principales
- `LigneEcriture` : Lignes d'écriture détaillées
- `CompteComptable` : Plan comptable
- `Journal` : Journaux comptables
- `ExerciceComptable` : Exercices comptables

## 📊 Types de calculs disponibles

### 1. Soldes de comptes

#### Solde simple
```javascript
const solde = await calculService.calculerSoldeCompte(
  '411000',                    // Numéro de compte
  null,                        // Date début (optionnelle)
  new Date('2024-12-31'),     // Date fin
  {
    societeId: 'uuid-societe',
    exerciceId: 'uuid-exercice',
    includeNonValidees: false,  // Exclure brouillards
    useCache: true             // Utiliser le cache
  }
);

// Résultat
{
  compteNumero: '411000',
  libelle: 'Clients',
  sensNaturel: 'DEBIT',
  totalDebit: 15000.00,
  totalCredit: 8000.00,
  solde: 7000.00,
  sensActuel: 'DEBIT',
  soldeNaturel: 7000.00,
  nombreLignes: 25,
  dateCalcul: '2024-01-15T10:30:00Z',
  periode: {
    dateDebut: null,
    dateFin: '2024-12-31'
  }
}
```

#### Soldes progressifs
```javascript
const soldesProgressifs = await calculService.calculerSoldesProgressifs(
  '411000',
  new Date('2024-01-01'),
  new Date('2024-12-31'),
  {
    societeId: 'uuid-societe',
    groupeParMois: true        // Grouper par mois
  }
);

// Résultat
{
  compteNumero: '411000',
  libelle: 'Clients',
  sensNaturel: 'DEBIT',
  periode: { dateDebut: '2024-01-01', dateFin: '2024-12-31' },
  soldesProgressifs: [
    {
      date: '2024-01-01',
      totalDebit: 5000.00,
      totalCredit: 2000.00,
      mouvementNet: 3000.00,
      soldeProgressif: 3000.00,
      sensProgressif: 'DEBIT',
      nombreLignes: 8
    },
    // ... autres périodes
  ]
}
```

### 2. Totaux de journaux

```javascript
const totauxJournal = await calculService.calculerTotauxJournal(
  'VT',                       // Code journal
  new Date('2024-01-01'),
  new Date('2024-12-31'),
  {
    societeId: 'uuid-societe',
    groupeParCompte: true      // Détails par compte
  }
);

// Résultat
{
  journalCode: 'VT',
  libelle: 'Journal des ventes',
  type: 'VENTE',
  periode: { dateDebut: '2024-01-01', dateFin: '2024-12-31' },
  totaux: {
    totalDebit: 125000.00,
    totalCredit: 125000.00,
    difference: 0.00,
    equilibre: true,
    nombreLignes: 450,
    nombreEcritures: 150
  },
  detailsParCompte: [
    {
      compteNumero: '411000',
      libelle: 'Clients',
      sensNaturel: 'DEBIT',
      totalDebit: 100000.00,
      totalCredit: 0.00,
      solde: 100000.00,
      nombreLignes: 150
    }
    // ... autres comptes
  ]
}
```

### 3. Balance générale

```javascript
const balanceGenerale = await calculService.calculerBalanceGenerale(
  new Date('2024-01-01'),
  new Date('2024-12-31'),
  {
    societeId: 'uuid-societe',
    niveauDetail: 'DETAIL',     // TOUS, DETAIL, COLLECTIF
    classeComptes: [4, 7],      // Classes 4 et 7 uniquement
    seulementAvecMouvement: true
  }
);

// Résultat
{
  periode: { dateDebut: '2024-01-01', dateFin: '2024-12-31' },
  options: { niveauDetail: 'DETAIL', classeComptes: [4, 7] },
  totauxGeneraux: {
    totalDebit: 500000.00,
    totalCredit: 500000.00,
    nombreComptes: 45,
    nombreLignes: 1250,
    equilibre: true
  },
  totauxParClasse: [
    {
      classe: 4,
      libelle: 'Comptes de tiers',
      totalDebit: 300000.00,
      totalCredit: 250000.00,
      nombreComptes: 25
    }
    // ... autres classes
  ],
  lignesBalance: [
    {
      compteNumero: '411000',
      libelle: 'Clients',
      classe: 4,
      nature: 'DETAIL',
      sensNaturel: 'DEBIT',
      niveau: 6,
      totalDebit: 150000.00,
      totalCredit: 80000.00,
      solde: 70000.00,
      sensActuel: 'DEBIT',
      nombreLignes: 85
    }
    // ... autres comptes
  ]
}
```

### 4. Balance auxiliaire

```javascript
const balanceAuxiliaire = await calculService.calculerBalanceAuxiliaire(
  '411',                      // Compte collectif
  new Date('2024-01-01'),
  new Date('2024-12-31'),
  {
    societeId: 'uuid-societe',
    seulementAvecMouvement: true
  }
);

// Résultat
{
  compteCollectif: '411',
  libelle: 'Clients',
  sensNaturel: 'DEBIT',
  periode: { dateDebut: '2024-01-01', dateFin: '2024-12-31' },
  totaux: {
    totalDebit: 250000.00,
    totalCredit: 180000.00,
    nombreComptes: 15,
    nombreLignes: 125,
    equilibre: true
  },
  comptesAuxiliaires: [
    {
      compteNumero: '411001',
      libelle: 'Client ABC SA',
      sensNaturel: 'DEBIT',
      totalDebit: 50000.00,
      totalCredit: 30000.00,
      solde: 20000.00,
      sensActuel: 'DEBIT',
      nombreLignes: 25
    }
    // ... autres comptes auxiliaires
  ]
}
```

## 🔧 API Endpoints

### Calculs de soldes

#### Solde d'un compte
```http
GET /api/v1/calculs/solde/{compteNumero}?dateFin=2024-12-31&societeId={uuid}
Authorization: Bearer {token}

Paramètres:
- compteNumero (obligatoire): Numéro du compte
- dateFin (obligatoire): Date de fin au format ISO 8601
- dateDebut (optionnel): Date de début
- societeId (optionnel): ID de la société
- exerciceId (optionnel): ID de l'exercice
- includeNonValidees (optionnel): Inclure les brouillards
- useCache (optionnel): Utiliser le cache (défaut: true)
```

#### Soldes progressifs
```http
GET /api/v1/calculs/soldes-progressifs/{compteNumero}?dateDebut=2024-01-01&dateFin=2024-12-31
Authorization: Bearer {token}

Paramètres:
- compteNumero (obligatoire): Numéro du compte
- dateDebut (obligatoire): Date de début
- dateFin (obligatoire): Date de fin
- groupeParMois (optionnel): Grouper par mois (défaut: false)
```

### Calculs de journaux

#### Totaux d'un journal
```http
GET /api/v1/calculs/totaux-journal/{journalCode}?dateDebut=2024-01-01&dateFin=2024-12-31
Authorization: Bearer {token}

Paramètres:
- journalCode (obligatoire): Code du journal
- dateDebut (obligatoire): Date de début
- dateFin (obligatoire): Date de fin
- groupeParCompte (optionnel): Détails par compte
```

### Calculs de balances

#### Balance générale
```http
GET /api/v1/calculs/balance?dateDebut=2024-01-01&dateFin=2024-12-31&societeId={uuid}
Authorization: Bearer {token}

Paramètres:
- dateDebut (obligatoire): Date de début
- dateFin (obligatoire): Date de fin
- societeId (optionnel): ID de la société
- niveauDetail (optionnel): TOUS|DETAIL|COLLECTIF
- classeComptes (optionnel): Liste de classes (ex: 1,4,7)
- seulementAvecMouvement (optionnel): Exclure comptes sans mouvement
```

#### Balance auxiliaire
```http
GET /api/v1/calculs/balance-auxiliaire/{compteCollectif}?dateDebut=2024-01-01&dateFin=2024-12-31
Authorization: Bearer {token}

Paramètres:
- compteCollectif (obligatoire): Numéro du compte collectif
- dateDebut (obligatoire): Date de début
- dateFin (obligatoire): Date de fin
- seulementAvecMouvement (optionnel): Exclure comptes sans mouvement
```

### Statistiques et utilitaires

#### Statistiques générales
```http
GET /api/v1/calculs/statistiques?dateDebut=2024-01-01&dateFin=2024-12-31&societeId={uuid}
Authorization: Bearer {token}
```

#### Gestion du cache
```http
GET /api/v1/calculs/cache/stats     # Statistiques du cache
DELETE /api/v1/calculs/cache        # Vider le cache
Authorization: Bearer {token}
```

#### Informations sur les calculs
```http
GET /api/v1/calculs/info
Authorization: Bearer {token}
```

## 📝 Exemples d'utilisation

### Calcul de solde avec cache

```javascript
// Premier appel - calcul complet
const debut1 = Date.now();
const solde1 = await fetch('/api/v1/calculs/solde/411000?dateFin=2024-12-31&useCache=true', {
  headers: { 'Authorization': `Bearer ${token}` }
});
const duree1 = Date.now() - debut1;

// Deuxième appel - depuis le cache (plus rapide)
const debut2 = Date.now();
const solde2 = await fetch('/api/v1/calculs/solde/411000?dateFin=2024-12-31&useCache=true', {
  headers: { 'Authorization': `Bearer ${token}` }
});
const duree2 = Date.now() - debut2;

console.log(`Premier calcul: ${duree1}ms, Depuis cache: ${duree2}ms`);
```

### Balance générale filtrée

```javascript
const params = new URLSearchParams({
  dateDebut: '2024-01-01',
  dateFin: '2024-12-31',
  societeId: 'uuid-societe',
  niveauDetail: 'DETAIL',
  classeComptes: '4,7',  // Comptes de tiers et produits
  seulementAvecMouvement: 'true'
});

const response = await fetch(`/api/v1/calculs/balance?${params}`, {
  headers: { 'Authorization': `Bearer ${token}` }
});

const balance = await response.json();
console.log(`Balance: ${balance.data.balanceGenerale.totauxGeneraux.nombreComptes} comptes`);
```

### Suivi des soldes progressifs

```javascript
const soldesProgressifs = await fetch(
  '/api/v1/calculs/soldes-progressifs/411000?dateDebut=2024-01-01&dateFin=2024-12-31&groupeParMois=true',
  { headers: { 'Authorization': `Bearer ${token}` } }
);

const data = await soldesProgressifs.json();
data.data.soldesProgressifs.soldesProgressifs.forEach(periode => {
  console.log(`${periode.date}: Solde ${periode.soldeProgressif} ${periode.sensProgressif}`);
});
```

## ⚡ Optimisations de performance

### Cache intelligent
- **Durée de vie** : 5 minutes par défaut
- **Clés de cache** : Basées sur les paramètres de calcul
- **Nettoyage automatique** : Toutes les 10 minutes
- **Statistiques** : Taux de hit, nombre d'entrées

### Index de base de données
- **Index composites** : compte + débit/crédit
- **Index par période** : société + date + statut
- **Index par journal** : journal + date + statut
- **Index par classe** : classe + nature + société

### Vues matérialisées
- **vue_soldes_mensuels** : Soldes pré-calculés par mois
- **vue_totaux_journaux_mensuels** : Totaux journaux par mois
- **vue_balance_classes** : Balances par classe

### Fonctions stockées PostgreSQL
- **calculer_solde_compte_rapide()** : Calcul optimisé de solde
- **rafraichir_vues_calculs()** : Rafraîchissement des vues
- **Triggers automatiques** : Mise à jour lors de validation d'écritures

## 🔍 Validation et règles métier

### Validation des périodes
- **Dates obligatoires** : dateDebut et dateFin requises pour la plupart des calculs
- **Ordre chronologique** : dateDebut ≤ dateFin
- **Limite de période** : Maximum 366 jours pour éviter les surcharges
- **Format ISO 8601** : Dates au format YYYY-MM-DD

### Règles comptables SYSCOHADA
- **Sens des comptes** : Respect du sens naturel (débit/crédit)
- **Classes de comptes** : 1-8 selon le plan SYSCOHADA
- **Équilibre obligatoire** : Débit = Crédit pour chaque écriture
- **Validation d'écritures** : Seules les écritures validées dans les calculs par défaut

### Gestion des erreurs
```javascript
// Exemples d'erreurs gérées
{
  "success": false,
  "message": "Compte 999999 introuvable",
  "code": "COMPTE_INEXISTANT"
}

{
  "success": false,
  "message": "La période ne peut pas dépasser 366 jours",
  "code": "PERIODE_TROP_LONGUE"
}

{
  "success": false,
  "message": "La date de début doit être antérieure à la date de fin",
  "code": "DATES_INCOHERENTES"
}
```

## 📊 Monitoring et métriques

### Métriques de performance
- **Temps de calcul** : Durée moyenne par type de calcul
- **Utilisation du cache** : Taux de hit, nombre d'accès
- **Charge base de données** : Nombre de requêtes, temps d'exécution
- **Fréquence d'utilisation** : Calculs les plus demandés

### Logs et audit
```javascript
// Exemples de logs générés
{
  "level": "info",
  "message": "Solde calculé via API",
  "data": {
    "utilisateur": "user-uuid",
    "compteNumero": "411000",
    "solde": 15000.00,
    "sensActuel": "DEBIT",
    "dureeCalcul": 45
  }
}

{
  "level": "info",
  "message": "Balance générale calculée via API",
  "data": {
    "utilisateur": "user-uuid",
    "nombreComptes": 125,
    "equilibre": true,
    "dureeCalcul": 1250
  }
}
```

### Alertes et surveillance
- **Calculs longs** : Alerte si calcul > 5 secondes
- **Cache inefficace** : Alerte si taux de hit < 70%
- **Erreurs fréquentes** : Alerte si taux d'erreur > 5%
- **Charge excessive** : Alerte si > 100 calculs/minute

## 🛠️ Guide technique pour développeurs

### Structure du code

```
src/
├── services/
│   └── calculService.js           # Service principal
├── controllers/
│   └── calculController.js        # Contrôleur API
├── routes/
│   └── calculs.js                 # Routes API
├── migrations/
│   └── 20250807190000-add-calcul-optimizations.js  # Optimisations DB
└── tests/
    ├── calculs.test.js            # Tests d'intégration
    └── calculs-simple.test.js     # Tests unitaires
```

### Extension du service

Pour ajouter un nouveau type de calcul :

```javascript
// Dans CalculService
async calculerNouveauType(parametres, options = {}) {
  try {
    // 1. Validation des paramètres
    this.validerPeriode(parametres.dateDebut, parametres.dateFin);

    // 2. Vérification du cache
    const cacheKey = `nouveau_type_${JSON.stringify(parametres)}`;
    if (options.useCache && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    // 3. Calcul principal
    const resultat = await this.effectuerCalcul(parametres);

    // 4. Mise en cache
    if (options.useCache) {
      this.cache.set(cacheKey, {
        data: resultat,
        timestamp: Date.now()
      });
    }

    // 5. Log et retour
    logger.info('Nouveau calcul effectué', { parametres, resultat });
    return resultat;

  } catch (error) {
    logger.error('Erreur nouveau calcul', { parametres, error: error.message });
    throw error;
  }
}
```

### Ajout d'un endpoint API

```javascript
// Dans calculController.js
async nouveauCalcul(req, res, next) {
  try {
    const { param1, param2 } = req.params;
    const { option1, option2 } = req.query;

    const resultat = await this.calculService.calculerNouveauType(
      { param1, param2 },
      { option1: option1 === 'true', option2 }
    );

    logger.info('Nouveau calcul via API', {
      utilisateur: req.user?.id,
      param1,
      param2
    });

    res.json({
      success: true,
      data: { resultat }
    });

  } catch (error) {
    logger.error('Erreur nouveau calcul via API', {
      utilisateur: req.user?.id,
      error: error.message
    });
    next(error);
  }
}
```

### Configuration des optimisations

```javascript
// Configuration du cache
const calculService = new CalculService();
calculService.cacheTimeout = 10 * 60 * 1000; // 10 minutes

// Nettoyage manuel du cache
calculService.viderCache();

// Statistiques du cache
const stats = calculService.getStatistiquesCache();
console.log(`Taux de hit: ${stats.tauxHit}%`);
```

## 🔐 Sécurité et bonnes pratiques

### Authentification et autorisation
- **Token JWT** obligatoire pour tous les endpoints
- **Validation des permissions** par société
- **Audit trail** de tous les calculs effectués

### Validation des entrées
- **Sanitisation** des paramètres d'entrée
- **Validation des UUID** pour societeId, exerciceId
- **Contrôle des plages de dates** pour éviter les abus
- **Limitation du taux** de requêtes par utilisateur

### Protection contre les abus
- **Timeout** sur les calculs longs (30 secondes max)
- **Limitation de période** (366 jours maximum)
- **Cache obligatoire** pour les calculs répétitifs
- **Monitoring** des utilisations anormales

## 📚 Ressources complémentaires

### Documentation liée
- [Plan de développement backend](./PLAN_DEVELOPPEMENT_BACKEND.md)
- [Import/Export écritures](./IMPORT_EXPORT_ECRITURES.md)
- [Lettrage et rapprochements](./LETTRAGE_RAPPROCHEMENTS.md)
- [Recherche avancée](./RECHERCHE_AVANCEE.md)

### Standards SYSCOHADA
- **Plan comptable OHADA** : Classes 1-8 respectées
- **Règles d'équilibre** : Débit = Crédit obligatoire
- **Sens des comptes** : Selon la nature comptable
- **Périodes comptables** : Exercices et périodes intermédiaires

### Dépendances techniques
- **Sequelize ORM** : Requêtes optimisées avec agrégations
- **PostgreSQL** : Vues matérialisées et fonctions stockées
- **Node.js** : Cache en mémoire avec Map native
- **Express.js** : API REST avec validation express-validator

### Exemples de migration

```sql
-- Exemple de vue matérialisée personnalisée
CREATE MATERIALIZED VIEW vue_soldes_personnalises AS
SELECT
  compte_numero,
  societe_id,
  DATE_TRUNC('week', date_ecriture) as semaine,
  SUM(debit) as total_debit,
  SUM(credit) as total_credit
FROM ligne_ecritures le
JOIN ecriture_comptables ec ON le.ecriture_id = ec.id
WHERE ec.statut = 'VALIDEE'
GROUP BY compte_numero, societe_id, DATE_TRUNC('week', date_ecriture);

-- Index sur la vue
CREATE INDEX idx_vue_soldes_personnalises_compte_semaine
ON vue_soldes_personnalises (compte_numero, semaine);
```
