/**
 * Migration pour corriger les noms de colonnes de la table api_keys
 * API Comptabilité SYSCOHADA
 */

'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Renommer les colonnes pour utiliser snake_case
    await queryInterface.renameColumn('api_keys', 'lastUsedAt', 'last_used_at');
    await queryInterface.renameColumn('api_keys', 'expiresAt', 'expires_at');
    await queryInterface.renameColumn('api_keys', 'isActive', 'is_active');
    await queryInterface.renameColumn('api_keys', 'createdBy', 'created_by');
    await queryInterface.renameColumn('api_keys', 'createdAt', 'created_at');
    await queryInterface.renameColumn('api_keys', 'updatedAt', 'updated_at');

    // Recréer les index avec les nouveaux noms
    await queryInterface.removeIndex('api_keys', 'api_keys_expires_at_idx');
    await queryInterface.removeIndex('api_keys', 'api_keys_is_active_idx');
    
    await queryInterface.addIndex('api_keys', ['expires_at'], {
      name: 'api_keys_expires_at_idx'
    });
    
    await queryInterface.addIndex('api_keys', ['is_active'], {
      name: 'api_keys_is_active_idx'
    });
  },

  async down(queryInterface, Sequelize) {
    // Revenir aux noms originaux
    await queryInterface.renameColumn('api_keys', 'last_used_at', 'lastUsedAt');
    await queryInterface.renameColumn('api_keys', 'expires_at', 'expiresAt');
    await queryInterface.renameColumn('api_keys', 'is_active', 'isActive');
    await queryInterface.renameColumn('api_keys', 'created_by', 'createdBy');
    await queryInterface.renameColumn('api_keys', 'created_at', 'createdAt');
    await queryInterface.renameColumn('api_keys', 'updated_at', 'updatedAt');

    // Recréer les index avec les anciens noms
    await queryInterface.removeIndex('api_keys', 'api_keys_expires_at_idx');
    await queryInterface.removeIndex('api_keys', 'api_keys_is_active_idx');
    
    await queryInterface.addIndex('api_keys', ['expiresAt'], {
      name: 'api_keys_expires_at_idx'
    });
    
    await queryInterface.addIndex('api_keys', ['isActive'], {
      name: 'api_keys_is_active_idx'
    });
  }
};