'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Societe extends Model {
    /**
     * Associations du modèle Societe
     */
    static associate(models) {
      // Une société peut avoir plusieurs journaux
      Societe.hasMany(models.Journal, {
        foreignKey: 'societeId',
        as: 'journaux'
      });

      // Une société peut avoir plusieurs comptes comptables
      Societe.hasMany(models.CompteComptable, {
        foreignKey: 'societeId',
        as: 'comptes'
      });

      // Une société peut avoir plusieurs écritures comptables
      Societe.hasMany(models.EcritureComptable, {
        foreignKey: 'societeId',
        as: 'ecritures'
      });

      // Une société peut avoir plusieurs exercices comptables
      Societe.hasMany(models.ExerciceComptable, {
        foreignKey: 'societeId',
        as: 'exercices'
      });

      // Une société peut avoir plusieurs paramètres comptables
      Societe.hasMany(models.ParametreComptable, {
        foreignKey: 'societeId',
        as: 'parametres'
      });

      // Une société peut avoir plusieurs tiers
      Societe.hasMany(models.Party, {
        foreignKey: 'societeId',
        as: 'tiers'
      });
    }

    /**
     * Méthodes d'instance
     */
    toJSON() {
      const values = { ...this.get() };
      return values;
    }

    /**
     * Validation de l'exercice comptable (pour compatibilité)
     */
    validateExercice() {
      if (this.exerciceDebut >= this.exerciceFin) {
        throw new Error('La date de début d\'exercice doit être antérieure à la date de fin');
      }

      const dureeExercice = (this.exerciceFin - this.exerciceDebut) / (1000 * 60 * 60 * 24);
      if (dureeExercice > 366) {
        throw new Error('Un exercice comptable ne peut dépasser 366 jours');
      }
    }

    /**
     * Obtient l'exercice comptable courant
     */
    async getExerciceCourant() {
      const models = this.sequelize.models;
      return await models.ExerciceComptable.findOne({
        where: {
          societeId: this.id,
          statut: 'OUVERT'
        },
        order: [['dateDebut', 'DESC']]
      });
    }

    /**
     * Obtient tous les exercices de la société
     */
    async getExercices(options = {}) {
      const models = this.sequelize.models;
      return await models.ExerciceComptable.findAll({
        where: {
          societeId: this.id,
          ...(options.statut && { statut: options.statut })
        },
        order: [['dateDebut', 'DESC']],
        ...options
      });
    }

    /**
     * Obtient tous les paramètres comptables de la société
     */
    async getParametres(categorie = null) {
      const models = this.sequelize.models;
      return await models.ParametreComptable.getParametresSociete(this.id, categorie);
    }

    /**
     * Obtient la valeur d'un paramètre spécifique
     */
    async getParametre(cle, valeurDefaut = null) {
      const models = this.sequelize.models;
      return await models.ParametreComptable.getValeurParametre(this.id, cle, valeurDefaut);
    }

    /**
     * Définit la valeur d'un paramètre
     */
    async setParametre(cle, valeur, type = 'STRING', categorie = 'CUSTOM', description = null) {
      const models = this.sequelize.models;
      return await models.ParametreComptable.setParametre(this.id, cle, valeur, type, categorie, description);
    }

    /**
     * Initialise les paramètres par défaut pour la société
     */
    async initialiserParametresDefaut() {
      const models = this.sequelize.models;
      return await models.ParametreComptable.creerParametresDefaut(this.id);
    }

    /**
     * Vérifie si la société est active
     */
    estActive() {
      return this.statut === 'ACTIF';
    }

    /**
     * Vérifie si la société est en régime réel normal
     */
    estRegimeReelNormal() {
      return this.regimeFiscal === 'REEL_NORMAL';
    }

    /**
     * Obtient le nom complet avec la forme juridique
     */
    getNomComplet() {
      return this.formeJuridique ? `${this.nom} ${this.formeJuridique}` : this.nom;
    }

    /**
     * Obtient l'adresse complète formatée
     */
    getAdresseComplete() {
      const elements = [this.adresse, this.ville, this.codePostal, this.pays].filter(Boolean);
      return elements.join(', ');
    }

    /**
     * Valide les données SYSCOHADA
     */
    static validateSyscohadaData(donnees) {
      const errors = [];

      if (!donnees.nom || donnees.nom.trim().length < 2) {
        errors.push('Le nom de la société doit contenir au moins 2 caractères');
      }

      if (donnees.nom && donnees.nom.length > 255) {
        errors.push('Le nom de la société ne peut dépasser 255 caractères');
      }

      if (donnees.numeroRccm && !/^[A-Z]{2}-\d{4}-[A-Z]\d{2}-\d{5}$/.test(donnees.numeroRccm)) {
        errors.push('Le format du numéro RCCM n\'est pas valide (ex: CI-2023-B12-12345)');
      }

      if (donnees.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(donnees.email)) {
        errors.push('L\'adresse email n\'est pas valide');
      }

      if (donnees.capital && parseFloat(donnees.capital) < 0) {
        errors.push('Le capital ne peut pas être négatif');
      }

      if (donnees.exerciceDebut && donnees.exerciceFin) {
        const debut = new Date(donnees.exerciceDebut);
        const fin = new Date(donnees.exerciceFin);
        
        if (debut >= fin) {
          errors.push('La date de début d\'exercice doit être antérieure à la date de fin');
        }

        const dureeExercice = (fin - debut) / (1000 * 60 * 60 * 24);
        if (dureeExercice > 366) {
          errors.push('Un exercice comptable ne peut dépasser 366 jours');
        }
      }

      return {
        valide: errors.length === 0,
        erreurs: errors
      };
    }
  }

  Societe.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    nom: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 255]
      }
    },
    adresse: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    telephone: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        is: /^[\d\s\-+()]*$/
      }
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        isEmail: true
      }
    },
    numeroContribuable: {
      type: DataTypes.STRING(50),
      allowNull: true,
      unique: true
    },
    formeJuridique: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    capital: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    exerciceDebut: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    exerciceFin: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    devise: {
      type: DataTypes.STRING(3),
      allowNull: false,
      defaultValue: 'XOF',
      validate: {
        isIn: [['XOF', 'EUR', 'USD', 'GBP']]
      }
    },
    // Nouveaux champs SYSCOHADA
    numeroRccm: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'numero_rccm',
      validate: {
        is: /^[A-Z]{2}-\d{4}-[A-Z]\d{2}-\d{5}$/
      }
    },
    regimeFiscal: {
      type: DataTypes.ENUM('REEL_NORMAL', 'REEL_SIMPLIFIE', 'SYNTHETIQUE'),
      allowNull: false,
      defaultValue: 'REEL_NORMAL',
      field: 'regime_fiscal'
    },
    statut: {
      type: DataTypes.ENUM('ACTIF', 'SUSPENDU', 'FERME'),
      allowNull: false,
      defaultValue: 'ACTIF'
    },
    logoUrl: {
      type: DataTypes.STRING(500),
      allowNull: true,
      field: 'logo_url',
      validate: {
        isUrl: true
      }
    },
    secteurActivite: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'secteur_activite'
    },
    numeroCnps: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'numero_cnps'
    },
    dateCreation: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'date_creation'
    },
    dateDebutActivite: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'date_debut_activite'
    },
    representantLegal: {
      type: DataTypes.STRING(255),
      allowNull: true,
      field: 'representant_legal'
    },
    fonctionRepresentant: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'fonction_representant'
    },
    siteWeb: {
      type: DataTypes.STRING(255),
      allowNull: true,
      field: 'site_web',
      validate: {
        isUrl: true
      }
    },
    codePostal: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'code_postal'
    },
    ville: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    pays: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: 'Côte d\'Ivoire'
    }
  }, {
    sequelize,
    modelName: 'Societe',
    tableName: 'societes',
    timestamps: true,
    underscored: true,
    hooks: {
      beforeValidate: (societe) => {
        if (societe.exerciceDebut && societe.exerciceFin) {
          societe.validateExercice();
        }
      }
    }
  });

  return Societe;
};