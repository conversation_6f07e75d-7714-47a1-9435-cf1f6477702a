'use strict';

const express = require('express');
const router = express.Router();
const Joi = require('joi');

const depreciationController = require('../controllers/depreciationController');
const { protect } = require('../middleware/auth');
const { validate } = require('../middleware/validation');

/**
 * Schémas de validation Joi pour les amortissements
 */

// Schéma pour créer un amortissement
const createDepreciationSchema = Joi.object({
  societeId: Joi.string().uuid().required(),
  code: Joi.string().min(1).max(20).required(),
  libelle: Joi.string().min(1).max(255).required(),
  description: Joi.string().max(1000).optional(),
  categorie: Joi.string().valid(
    'TERRAIN', 'BATIMENT', 'MATERIEL_TRANSPORT', 'MATERIEL_BUREAU',
    'MATERIEL_INFORMATIQUE', 'MOBILIER', 'INSTALLATION', 'MATERIEL_INDUSTRIEL',
    'BREVETS_LICENCES', 'LOGICIELS', 'AUTRES'
  ).required(),
  compteImmobilisation: Joi.string().pattern(/^2\d{5}$/).required(),
  compteAmortissement: Joi.string().pattern(/^28\d{4}$/).required(),
  compteDotation: Joi.string().pattern(/^681\d{3}$/).default('681100'),
  valeurAcquisition: Joi.number().positive().required(),
  valeurResiduelle: Joi.number().min(0).default(0),
  dateAcquisition: Joi.date().iso().required(),
  dateMiseEnService: Joi.date().iso().required(),
  methodeAmortissement: Joi.string().valid('LINEAIRE', 'DEGRESSIF', 'PROGRESSIF', 'VARIABLE', 'EXCEPTIONNEL').default('LINEAIRE'),
  dureeAmortissement: Joi.number().integer().min(1).max(100).required(),
  tauxAmortissement: Joi.number().min(0).max(100).optional(),
  coefficientDegressif: Joi.number().min(1).max(3).optional(),
  prorataTemporisPremierExercice: Joi.boolean().default(true),
  numeroSerie: Joi.string().max(100).optional(),
  localisation: Joi.string().max(255).optional(),
  fournisseur: Joi.string().max(255).optional(),
  numeroFacture: Joi.string().max(50).optional(),
  regimeFiscal: Joi.string().valid('NORMAL', 'SIMPLIFIE', 'EXONERE').default('NORMAL'),
  deductibleTVA: Joi.boolean().default(true),
  notes: Joi.string().max(1000).optional()
});

// Schéma pour modifier un amortissement
const updateDepreciationSchema = Joi.object({
  libelle: Joi.string().min(1).max(255).optional(),
  description: Joi.string().max(1000).optional(),
  categorie: Joi.string().valid(
    'TERRAIN', 'BATIMENT', 'MATERIEL_TRANSPORT', 'MATERIEL_BUREAU',
    'MATERIEL_INFORMATIQUE', 'MOBILIER', 'INSTALLATION', 'MATERIEL_INDUSTRIEL',
    'BREVETS_LICENCES', 'LOGICIELS', 'AUTRES'
  ).optional(),
  compteImmobilisation: Joi.string().pattern(/^2\d{5}$/).optional(),
  compteAmortissement: Joi.string().pattern(/^28\d{4}$/).optional(),
  compteDotation: Joi.string().pattern(/^681\d{3}$/).optional(),
  valeurAcquisition: Joi.number().positive().optional(),
  valeurResiduelle: Joi.number().min(0).optional(),
  dateAcquisition: Joi.date().iso().optional(),
  dateMiseEnService: Joi.date().iso().optional(),
  methodeAmortissement: Joi.string().valid('LINEAIRE', 'DEGRESSIF', 'PROGRESSIF', 'VARIABLE', 'EXCEPTIONNEL').optional(),
  dureeAmortissement: Joi.number().integer().min(1).max(100).optional(),
  tauxAmortissement: Joi.number().min(0).max(100).optional(),
  coefficientDegressif: Joi.number().min(1).max(3).optional(),
  statut: Joi.string().valid('EN_SERVICE', 'HORS_SERVICE', 'CEDE', 'REFORME', 'TOTALEMENT_AMORTI').optional(),
  numeroSerie: Joi.string().max(100).optional(),
  localisation: Joi.string().max(255).optional(),
  fournisseur: Joi.string().max(255).optional(),
  numeroFacture: Joi.string().max(50).optional(),
  regimeFiscal: Joi.string().valid('NORMAL', 'SIMPLIFIE', 'EXONERE').optional(),
  deductibleTVA: Joi.boolean().optional(),
  notes: Joi.string().max(1000).optional()
});

// Schéma pour calculer les dotations
const calculateDotationsSchema = Joi.object({
  societeId: Joi.string().uuid().required(),
  exercice: Joi.number().integer().min(2000).max(2100).required()
});

// Schéma pour générer les écritures
const generateEntriesSchema = Joi.object({
  societeId: Joi.string().uuid().required(),
  exercice: Joi.number().integer().min(2000).max(2100).required(),
  amortissementIds: Joi.array().items(Joi.string().uuid()).optional()
});

/**
 * Routes pour les amortissements
 */

/**
 * @swagger
 * /api/v1/depreciations:
 *   get:
 *     summary: Liste les amortissements d'une société
 *     tags: [Amortissements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: categorie
 *         schema:
 *           type: string
 *           enum: [TERRAIN, BATIMENT, MATERIEL_TRANSPORT, MATERIEL_BUREAU, MATERIEL_INFORMATIQUE, MOBILIER, INSTALLATION, MATERIEL_INDUSTRIEL, BREVETS_LICENCES, LOGICIELS, AUTRES]
 *         description: Filtrer par catégorie
 *       - in: query
 *         name: statut
 *         schema:
 *           type: string
 *           enum: [EN_SERVICE, HORS_SERVICE, CEDE, REFORME, TOTALEMENT_AMORTI]
 *         description: Filtrer par statut
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Recherche par code ou libellé
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Numéro de page
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Nombre d'éléments par page
 *     responses:
 *       200:
 *         description: Liste des amortissements récupérée avec succès
 *       400:
 *         description: Paramètres invalides
 */
router.get('/',
  protect,
  validate(Joi.object({
    societeId: Joi.string().uuid().required(),
    categorie: Joi.string().optional(),
    statut: Joi.string().optional(),
    search: Joi.string().optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(50)
  }), 'query'),
  depreciationController.listerAmortissements.bind(depreciationController)
);

/**
 * @swagger
 * /api/v1/depreciations:
 *   post:
 *     summary: Crée un nouvel amortissement
 *     tags: [Amortissements]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - societeId
 *               - code
 *               - libelle
 *               - categorie
 *               - compteImmobilisation
 *               - compteAmortissement
 *               - valeurAcquisition
 *               - dateAcquisition
 *               - dateMiseEnService
 *               - dureeAmortissement
 *             properties:
 *               societeId:
 *                 type: string
 *                 format: uuid
 *               code:
 *                 type: string
 *                 maxLength: 20
 *               libelle:
 *                 type: string
 *                 maxLength: 255
 *               categorie:
 *                 type: string
 *                 enum: [TERRAIN, BATIMENT, MATERIEL_TRANSPORT, MATERIEL_BUREAU, MATERIEL_INFORMATIQUE, MOBILIER, INSTALLATION, MATERIEL_INDUSTRIEL, BREVETS_LICENCES, LOGICIELS, AUTRES]
 *               compteImmobilisation:
 *                 type: string
 *                 pattern: '^2\d{5}$'
 *               compteAmortissement:
 *                 type: string
 *                 pattern: '^28\d{4}$'
 *               valeurAcquisition:
 *                 type: number
 *                 minimum: 0
 *               dateAcquisition:
 *                 type: string
 *                 format: date
 *               dateMiseEnService:
 *                 type: string
 *                 format: date
 *               dureeAmortissement:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 100
 *     responses:
 *       201:
 *         description: Amortissement créé avec succès
 *       400:
 *         description: Données invalides
 *       422:
 *         description: Erreur de validation métier
 */
router.post('/',
  protect,
  validate(createDepreciationSchema),
  depreciationController.creerAmortissement.bind(depreciationController)
);

/**
 * @swagger
 * /api/v1/depreciations/{id}:
 *   get:
 *     summary: Récupère un amortissement par ID
 *     tags: [Amortissements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'amortissement
 *     responses:
 *       200:
 *         description: Amortissement récupéré avec succès
 *       404:
 *         description: Amortissement inexistant
 */
router.get('/:id',
  protect,
  validate(Joi.object({
    id: Joi.string().uuid().required()
  }), 'params'),
  depreciationController.obtenirAmortissement.bind(depreciationController)
);

/**
 * @swagger
 * /api/v1/depreciations/{id}:
 *   put:
 *     summary: Met à jour un amortissement
 *     tags: [Amortissements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'amortissement
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               libelle:
 *                 type: string
 *                 maxLength: 255
 *               categorie:
 *                 type: string
 *                 enum: [TERRAIN, BATIMENT, MATERIEL_TRANSPORT, MATERIEL_BUREAU, MATERIEL_INFORMATIQUE, MOBILIER, INSTALLATION, MATERIEL_INDUSTRIEL, BREVETS_LICENCES, LOGICIELS, AUTRES]
 *               statut:
 *                 type: string
 *                 enum: [EN_SERVICE, HORS_SERVICE, CEDE, REFORME, TOTALEMENT_AMORTI]
 *     responses:
 *       200:
 *         description: Amortissement modifié avec succès
 *       400:
 *         description: Données invalides
 *       404:
 *         description: Amortissement inexistant
 *       422:
 *         description: Erreur de validation métier
 */
router.put('/:id',
  protect,
  validate(Joi.object({
    id: Joi.string().uuid().required()
  }), 'params'),
  validate(updateDepreciationSchema),
  depreciationController.modifierAmortissement.bind(depreciationController)
);

/**
 * @swagger
 * /api/v1/depreciations/{id}:
 *   delete:
 *     summary: Supprime un amortissement
 *     tags: [Amortissements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'amortissement
 *     responses:
 *       200:
 *         description: Amortissement supprimé avec succès
 *       404:
 *         description: Amortissement inexistant
 *       422:
 *         description: Impossible de supprimer (contraintes métier)
 */
router.delete('/:id',
  protect,
  validate(Joi.object({
    id: Joi.string().uuid().required()
  }), 'params'),
  depreciationController.supprimerAmortissement.bind(depreciationController)
);

/**
 * @swagger
 * /api/v1/depreciations/{id}/plan:
 *   get:
 *     summary: Récupère le plan d'amortissement
 *     tags: [Amortissements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'amortissement
 *     responses:
 *       200:
 *         description: Plan d'amortissement récupéré avec succès
 *       404:
 *         description: Amortissement inexistant
 */
router.get('/:id/plan',
  protect,
  validate(Joi.object({
    id: Joi.string().uuid().required()
  }), 'params'),
  depreciationController.obtenirPlanAmortissement.bind(depreciationController)
);

/**
 * @swagger
 * /api/v1/depreciations/calculate-dotations:
 *   post:
 *     summary: Calcule les dotations d'un exercice
 *     tags: [Amortissements]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - societeId
 *               - exercice
 *             properties:
 *               societeId:
 *                 type: string
 *                 format: uuid
 *               exercice:
 *                 type: integer
 *                 minimum: 2000
 *                 maximum: 2100
 *     responses:
 *       200:
 *         description: Dotations calculées avec succès
 *       400:
 *         description: Paramètres invalides
 */
router.post('/calculate-dotations',
  protect,
  validate(calculateDotationsSchema),
  depreciationController.calculerDotations.bind(depreciationController)
);

/**
 * @swagger
 * /api/v1/depreciations/generate-entries:
 *   post:
 *     summary: Génère les écritures d'amortissement
 *     tags: [Amortissements]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - societeId
 *               - exercice
 *             properties:
 *               societeId:
 *                 type: string
 *                 format: uuid
 *               exercice:
 *                 type: integer
 *                 minimum: 2000
 *                 maximum: 2100
 *               amortissementIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 description: IDs des amortissements à traiter (tous si non spécifié)
 *     responses:
 *       200:
 *         description: Écritures générées avec succès
 *       400:
 *         description: Paramètres invalides
 *       404:
 *         description: Aucun plan d'amortissement à traiter
 */
router.post('/generate-entries',
  protect,
  validate(generateEntriesSchema),
  depreciationController.genererEcritures.bind(depreciationController)
);

module.exports = router;