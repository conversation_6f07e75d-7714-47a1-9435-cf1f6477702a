# Résultats des Tests Manuels CLI SYSCOHADA

Date: 2025-08-09T17:41:45.818Z

## Instructions
- ✅ pour un test réussi
- ❌ pour un test échoué  
- ⚠️ pour un test partiellement réussi
- 📝 pour des notes

## Résultats

### 1. Configuration de base

#### 1.1 Afficher l'aide générale
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js --help`
**Attendu:** Liste des commandes disponibles
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 1.2 Afficher la version
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js --version`
**Attendu:** Version 1.0.0
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 1.3 Configurer l'URL de l'API
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js config`
**Attendu:** Prompt pour l'URL
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 1.4 Vérifier le statut de l'API
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js status`
**Attendu:** Statut API ou erreur de connexion
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

### 2. Authentification

#### 2.1 Configurer la clé API
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js auth:setup`
**Attendu:** Prompt pour la clé API
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 2.2 Vérifier la clé API
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js auth:verify`
**Attendu:** Informations de la clé API
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 2.3 Supprimer la clé API
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js auth:clear`
**Attendu:** Confirmation de suppression
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

### 3. Gestion des clés API (Admin)

#### 3.1 Lister les clés API
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js apikeys:list`
**Attendu:** Liste des clés ou erreur de permission
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 3.2 Lister avec options
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js apikeys:list --include-inactive --limit 5`
**Attendu:** Liste filtrée des clés
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 3.3 Créer une clé API
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js apikeys:create`
**Attendu:** Prompt de création ou erreur de permission
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

### 4. Gestion des sociétés

#### 4.1 Lister les sociétés
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js societes:list`
**Attendu:** Liste des sociétés
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 4.2 Créer une société
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js societes:create`
**Attendu:** Prompt de création
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 4.3 Sélectionner une société
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js societes:select`
**Attendu:** Liste pour sélection
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

### 5. Gestion des exercices

#### 5.1 Lister les exercices
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js exercices:list`
**Attendu:** Liste des exercices ou message si aucun
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 5.2 Créer un exercice
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js exercices:create`
**Attendu:** Prompt de création
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 5.3 Vue des clôtures
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js exercices:vue-clotures`
**Attendu:** État des clôtures
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

### 6. Plan comptable et comptes

#### 6.1 Lister les comptes
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js comptes:list`
**Attendu:** Plan comptable
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 6.2 Lister par classe
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js comptes:list --classe 1`
**Attendu:** Comptes de la classe 1
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 6.3 Plan comptable personnalisé
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js plan:personnalise`
**Attendu:** Plan avec personnalisations
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

### 7. Journaux et écritures

#### 7.1 Lister les journaux
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js journaux:list`
**Attendu:** Liste des journaux
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 7.2 Lister les écritures
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js ecritures:list`
**Attendu:** Liste des écritures
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 7.3 Écritures en brouillard
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js ecritures:list --statut BROUILLARD`
**Attendu:** Écritures non validées
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 7.4 Lister les templates
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js templates:list`
**Attendu:** Templates d'écriture
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

### 8. États et rapports

#### 8.1 Générer un état
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js etats:generer`
**Attendu:** Sélection du type d'état
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 8.2 Tableau de bord
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js dashboard`
**Attendu:** Indicateurs comptables
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 8.3 Générer une analyse
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js analyses:generer`
**Attendu:** Sélection du type d'analyse
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 8.4 Générer un rapport
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js rapports:generer`
**Attendu:** Sélection du rapport
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

### 9. Amortissements

#### 9.1 Lister les amortissements
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js amortissements:list`
**Attendu:** Liste des amortissements
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 9.2 Calculer les dotations
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js amortissements:calculer-dotations`
**Attendu:** Calcul des dotations
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

### 10. Tiers et lettrage

#### 10.1 Lister les tiers
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js tiers:list`
**Attendu:** Liste des tiers
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

#### 10.2 Lettrage des comptes
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js lettrage:comptes`
**Attendu:** État du lettrage
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

### 11. Import/Export

#### 11.1 Exporter les écritures
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js export:ecritures`
**Attendu:** Sélection du format d'export
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

### 12. Paramètres

#### 12.1 Lister les paramètres
**Commande:** `node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js parametres:list`
**Attendu:** Liste des paramètres
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---

## Tests d'erreurs

### Commande inexistante
**Résultat:** [ ] ✅ / [ ] ❌
**Notes:** 

### Commande sans authentification  
**Résultat:** [ ] ✅ / [ ] ❌
**Notes:**

## Résumé
- Tests réussis: __/__
- Tests échoués: __/__
- Taux de réussite: __%

## Notes générales

