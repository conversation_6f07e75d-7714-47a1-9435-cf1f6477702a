'use strict';

const express = require('express');
const router = express.Router();
const Joi = require('joi');

const ecritureController = require('../controllers/ecritureController');
const { protect } = require('../middleware/auth');
const { validate } = require('../middleware/validation');

/**
 * Schémas de validation Joi pour les écritures
 */

// Schéma pour les données d'écriture
const ecritureSchema = Joi.object({
  dateEcriture: Joi.date().iso().required()
    .messages({
      'date.base': 'La date d\'écriture doit être une date valide',
      'any.required': 'La date d\'écriture est obligatoire'
    }),
  libelle: Joi.string().min(3).max(255).required()
    .messages({
      'string.min': 'Le libellé doit contenir au moins 3 caractères',
      'string.max': 'Le libellé ne peut pas dépasser 255 caractères',
      'any.required': 'Le libellé est obligatoire'
    }),
  reference: Joi.string().max(50).optional().allow(''),
  pieceJustificative: Joi.string().max(100).optional().allow(''),
  journalCode: Joi.string().length(2).required()
    .messages({
      'string.length': 'Le code journal doit contenir exactement 2 caractères',
      'any.required': 'Le code journal est obligatoire'
    }),
  exerciceId: Joi.string().uuid().optional(),
  societeId: Joi.string().uuid().required()
    .messages({
      'string.guid': 'L\'ID société doit être un UUID valide',
      'any.required': 'L\'ID société est obligatoire'
    })
});

// Schéma pour les lignes d'écriture
const ligneSchema = Joi.object({
  compteNumero: Joi.string().min(6).max(10).pattern(/^\d+$/).required()
    .messages({
      'string.pattern.base': 'Le numéro de compte ne peut contenir que des chiffres',
      'string.min': 'Le numéro de compte doit contenir au moins 6 chiffres',
      'string.max': 'Le numéro de compte ne peut pas dépasser 10 chiffres',
      'any.required': 'Le numéro de compte est obligatoire'
    }),
  libelle: Joi.string().min(2).max(255).required()
    .messages({
      'string.min': 'Le libellé de ligne doit contenir au moins 2 caractères',
      'any.required': 'Le libellé de ligne est obligatoire'
    }),
  debit: Joi.number().min(0).precision(2).default(0),
  credit: Joi.number().min(0).precision(2).default(0),
  reference: Joi.string().max(50).optional().allow('')
}).custom((value, helpers) => {
  const { debit, credit } = value;
  
  // Vérifier qu'on a soit débit soit crédit, pas les deux
  if (debit > 0 && credit > 0) {
    return helpers.error('custom.debitEtCredit');
  }
  
  // Vérifier qu'on a au moins un montant
  if (debit === 0 && credit === 0) {
    return helpers.error('custom.montantNul');
  }
  
  return value;
}).messages({
  'custom.debitEtCredit': 'Une ligne ne peut pas avoir à la fois un débit et un crédit',
  'custom.montantNul': 'Une ligne doit avoir soit un débit soit un crédit'
});

// Schéma pour création d'écriture
const creerEcritureSchema = Joi.object({
  donnees: ecritureSchema.required(),
  lignes: Joi.array().items(ligneSchema).min(2).required()
    .messages({
      'array.min': 'Une écriture doit avoir au moins 2 lignes'
    })
});

// Schéma pour modification d'écriture
const modifierEcritureSchema = Joi.object({
  donnees: ecritureSchema.optional(),
  lignes: Joi.array().items(ligneSchema).min(2).optional()
});

// Schéma pour validation SYSCOHADA
const validerSYSCOHADASchema = creerEcritureSchema;

// Schéma pour filtres de recherche
const filtresSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(50),
  societeId: Joi.string().uuid().optional(),
  journalCode: Joi.string().length(2).optional(),
  statut: Joi.string().valid('BROUILLARD', 'VALIDEE', 'CLOTUREE').optional(),
  dateDebut: Joi.date().iso().optional(),
  dateFin: Joi.date().iso().optional(),
  compteNumero: Joi.string().min(6).max(10).pattern(/^\d+$/).optional(),
  recherche: Joi.string().max(100).optional()
});

/**
 * Middleware d'initialisation du controller
 */
router.use((req, res, next) => {
  if (!ecritureController.ecritureService) {
    ecritureController.init(req.app.get('models'));
  }
  next();
});

/**
 * Routes des écritures comptables
 */

/**
 * @swagger
 * /api/v1/ecritures:
 *   post:
 *     summary: Crée une nouvelle écriture comptable
 *     tags: [Écritures]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               donnees:
 *                 type: object
 *                 properties:
 *                   dateEcriture:
 *                     type: string
 *                     format: date
 *                   libelle:
 *                     type: string
 *                   journalCode:
 *                     type: string
 *                   societeId:
 *                     type: string
 *                     format: uuid
 *               lignes:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     compteNumero:
 *                       type: string
 *                     libelle:
 *                       type: string
 *                     debit:
 *                       type: number
 *                     credit:
 *                       type: number
 *     responses:
 *       201:
 *         description: Écriture créée avec succès
 *       400:
 *         description: Données invalides
 *       401:
 *         description: Non authentifié
 */
router.post('/',
  protect,
  validate(creerEcritureSchema),
  ecritureController.creerEcriture.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures:
 *   get:
 *     summary: Obtient la liste des écritures avec filtres
 *     tags: [Écritures]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Numéro de page
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Nombre d'éléments par page
 *       - in: query
 *         name: statut
 *         schema:
 *           type: string
 *           enum: [BROUILLARD, VALIDEE, CLOTUREE]
 *         description: Statut de l'écriture
 *       - in: query
 *         name: journalCode
 *         schema:
 *           type: string
 *         description: Code du journal
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin
 *     responses:
 *       200:
 *         description: Liste des écritures
 */
router.get('/',
  protect,
  validate(filtresSchema, 'query'),
  ecritureController.getEcritures.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/{id}:
 *   get:
 *     summary: Obtient une écriture par son ID
 *     tags: [Écritures]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Détails de l'écriture
 *       404:
 *         description: Écriture non trouvée
 */
router.get('/:id',
  protect,
  ecritureController.getEcritureById.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/{id}:
 *   put:
 *     summary: Met à jour une écriture (seulement si en brouillard)
 *     tags: [Écritures]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               donnees:
 *                 type: object
 *               lignes:
 *                 type: array
 *     responses:
 *       200:
 *         description: Écriture mise à jour
 *       400:
 *         description: Validation échouée
 *       404:
 *         description: Écriture non trouvée
 */
router.put('/:id',
  protect,
  validate(modifierEcritureSchema),
  ecritureController.modifierEcriture.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/{id}:
 *   delete:
 *     summary: Supprime une écriture (seulement si en brouillard)
 *     tags: [Écritures]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Écriture supprimée
 *       400:
 *         description: Suppression interdite
 *       404:
 *         description: Écriture non trouvée
 */
router.delete('/:id',
  protect,
  ecritureController.supprimerEcriture.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/{id}/valider:
 *   post:
 *     summary: Valide une écriture (passage BROUILLARD → VALIDEE)
 *     tags: [Écritures]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Écriture validée avec succès
 *       400:
 *         description: Validation échouée
 *       404:
 *         description: Écriture non trouvée
 */
router.post('/:id/valider',
  protect,
  ecritureController.validerEcriture.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/{id}/dupliquer:
 *   post:
 *     summary: Duplique une écriture pour créer un template
 *     tags: [Écritures]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Template créé avec succès
 *       404:
 *         description: Écriture non trouvée
 */
router.post('/:id/dupliquer',
  protect,
  ecritureController.dupliquerEcriture.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/valider-syscohada:
 *   post:
 *     summary: Valide une écriture selon les règles SYSCOHADA sans la sauvegarder
 *     tags: [Écritures]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               donnees:
 *                 type: object
 *               lignes:
 *                 type: array
 *     responses:
 *       200:
 *         description: Résultat de validation SYSCOHADA
 */
router.post('/valider-syscohada',
  protect,
  validate(validerSYSCOHADASchema),
  ecritureController.validerEcritureSYSCOHADA.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/brouillards:
 *   get:
 *     summary: Obtient les écritures en brouillard d'une société
 *     tags: [Écritures]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Numéro de page
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Nombre d'éléments par page
 *     responses:
 *       200:
 *         description: Liste des écritures en brouillard
 *       400:
 *         description: ID société manquant
 */
router.get('/brouillards',
  protect,
  ecritureController.getBrouillards.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/journal/{code}:
 *   get:
 *     summary: Obtient les écritures d'un journal pour une période
 *     tags: [Écritures]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *         description: Code du journal
 *       - in: query
 *         name: dateDebut
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début
 *       - in: query
 *         name: dateFin
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Numéro de page
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Nombre d'éléments par page
 *     responses:
 *       200:
 *         description: Écritures du journal avec totaux
 *       400:
 *         description: Dates manquantes
 */
router.get('/journal/:code',
  protect,
  ecritureController.getEcrituresJournal.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/statistiques:
 *   get:
 *     summary: Obtient les statistiques des écritures
 *     tags: [Écritures]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début (optionnelle)
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin (optionnelle)
 *     responses:
 *       200:
 *         description: Statistiques des écritures
 *       400:
 *         description: ID société manquant
 */
router.get('/statistiques',
  protect,
  ecritureController.getStatistiques.bind(ecritureController)
);

// ==========================================
// ROUTES GESTION DES BROUILLARDS - JOUR 5
// ==========================================

/**
 * @swagger
 * /api/v1/ecritures/brouillards/avances:
 *   get:
 *     summary: Obtient les brouillards avec filtres avancés et statistiques
 *     tags: [Brouillards]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: journalCode
 *         schema:
 *           type: string
 *         description: Code du journal
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin
 *       - in: query
 *         name: utilisateurCreation
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'utilisateur créateur
 *       - in: query
 *         name: recherche
 *         schema:
 *           type: string
 *         description: Recherche textuelle
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Numéro de page
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Nombre d'éléments par page
 *     responses:
 *       200:
 *         description: Brouillards avec statistiques détaillées
 */
router.get('/brouillards/avances',
  protect,
  ecritureController.getBrouillardsAvances.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/brouillards/statistiques:
 *   get:
 *     summary: Obtient les statistiques détaillées des brouillards
 *     tags: [Brouillards]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin
 *     responses:
 *       200:
 *         description: Statistiques des brouillards par utilisateur et journal
 *       400:
 *         description: ID société manquant
 */
router.get('/brouillards/statistiques',
  protect,
  ecritureController.getStatistiquesBrouillards.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/valider-lot:
 *   post:
 *     summary: Valide plusieurs écritures en lot
 *     tags: [Brouillards]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ecritureIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 description: Liste des IDs d'écritures à valider
 *             required:
 *               - ecritureIds
 *     responses:
 *       200:
 *         description: Résultat de la validation en lot
 *       400:
 *         description: Liste d'IDs manquante ou invalide
 */
router.post('/valider-lot',
  protect,
  validate(Joi.object({
    ecritureIds: Joi.array().items(Joi.string().uuid()).min(1).required()
      .messages({
        'array.min': 'Au moins une écriture doit être sélectionnée',
        'any.required': 'La liste des écritures est obligatoire'
      })
  })),
  ecritureController.validerEcrituresEnLot.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/{id}/permissions:
 *   get:
 *     summary: Vérifie les permissions de modification d'une écriture
 *     tags: [Brouillards]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'écriture
 *       - in: query
 *         name: action
 *         required: true
 *         schema:
 *           type: string
 *           enum: [modifier, supprimer, valider]
 *         description: Action à vérifier
 *     responses:
 *       200:
 *         description: Résultat de vérification des permissions
 *       400:
 *         description: Action invalide
 *       404:
 *         description: Écriture non trouvée
 */
router.get('/:id/permissions',
  protect,
  ecritureController.verifierPermissions.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/{id}/historique:
 *   get:
 *     summary: Obtient l'historique des changements d'état d'une écriture
 *     tags: [Brouillards]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'écriture
 *     responses:
 *       200:
 *         description: Historique des changements d'état
 *       404:
 *         description: Écriture non trouvée
 */
router.get('/:id/historique',
  protect,
  ecritureController.getHistoriqueEtat.bind(ecritureController)
);

// ==========================================
// ROUTES RECHERCHE AVANCÉE - JOUR 8
// ==========================================

/**
 * @swagger
 * /api/v1/ecritures/recherche:
 *   post:
 *     summary: Effectue une recherche avancée multi-critères
 *     tags: [Recherche]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               societeId:
 *                 type: string
 *                 format: uuid
 *               exerciceId:
 *                 type: string
 *                 format: uuid
 *               journalCode:
 *                 type: string
 *               statut:
 *                 type: string
 *                 enum: [BROUILLARD, VALIDEE]
 *               dateDebut:
 *                 type: string
 *                 format: date
 *               dateFin:
 *                 type: string
 *                 format: date
 *               montantMin:
 *                 type: number
 *               montantMax:
 *                 type: number
 *               compteNumero:
 *                 type: string
 *               libelle:
 *                 type: string
 *               reference:
 *                 type: string
 *               page:
 *                 type: integer
 *                 minimum: 1
 *                 default: 1
 *               limit:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 100
 *                 default: 50
 *     responses:
 *       200:
 *         description: Résultats de recherche avec statistiques
 *       400:
 *         description: Critères de recherche manquants
 */
router.post('/recherche',
  protect,
  ecritureController.rechercheAvancee.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/recherche-textuelle:
 *   get:
 *     summary: Effectue une recherche textuelle globale
 *     tags: [Recherche]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: texte
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *         description: Texte à rechercher
 *       - in: query
 *         name: societeId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: exerciceId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice
 *       - in: query
 *         name: champs
 *         schema:
 *           type: string
 *         description: Champs à rechercher (séparés par virgule)
 *       - in: query
 *         name: rechercherDansLignes
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Rechercher aussi dans les lignes d'écriture
 *       - in: query
 *         name: caseSensitive
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Recherche sensible à la casse
 *       - in: query
 *         name: motExact
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Recherche de mot exact
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Numéro de page
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Nombre d'éléments par page
 *     responses:
 *       200:
 *         description: Résultats de recherche textuelle avec surlignage
 *       400:
 *         description: Texte de recherche invalide
 */
router.get('/recherche-textuelle',
  protect,
  ecritureController.rechercheTextuelle.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/recherche-montant:
 *   get:
 *     summary: Effectue une recherche par montant avec tolérance
 *     tags: [Recherche]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: montantMin
 *         schema:
 *           type: number
 *         description: Montant minimum
 *       - in: query
 *         name: montantMax
 *         schema:
 *           type: number
 *         description: Montant maximum
 *       - in: query
 *         name: tolerance
 *         schema:
 *           type: number
 *           default: 0
 *         description: Tolérance de montant
 *       - in: query
 *         name: typeMontant
 *         schema:
 *           type: string
 *           enum: [DEBIT, CREDIT, TOUS]
 *           default: TOUS
 *         description: Type de montant à rechercher
 *       - in: query
 *         name: compteNumero
 *         schema:
 *           type: string
 *         description: Numéro du compte
 *       - in: query
 *         name: societeId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Numéro de page
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Nombre d'éléments par page
 *     responses:
 *       200:
 *         description: Résultats de recherche par montant
 *       400:
 *         description: Montants manquants
 */
router.get('/recherche-montant',
  protect,
  ecritureController.rechercheParMontant.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/recherche-compte/{compteNumero}:
 *   get:
 *     summary: Effectue une recherche par compte avec hiérarchie
 *     tags: [Recherche]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: compteNumero
 *         required: true
 *         schema:
 *           type: string
 *         description: Numéro du compte
 *       - in: query
 *         name: includeHierarchie
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Inclure la hiérarchie des comptes
 *       - in: query
 *         name: societeId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin
 *       - in: query
 *         name: statut
 *         schema:
 *           type: string
 *           enum: [BROUILLARD, VALIDEE]
 *           default: VALIDEE
 *         description: Statut des écritures
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Numéro de page
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Nombre d'éléments par page
 *     responses:
 *       200:
 *         description: Résultats de recherche par compte avec totaux
 *       400:
 *         description: Numéro de compte manquant
 *       404:
 *         description: Compte non trouvé
 */
router.get('/recherche-compte/:compteNumero',
  protect,
  ecritureController.rechercheParCompte.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/recherche-periode:
 *   get:
 *     summary: Effectue une recherche par période avec analyses
 *     tags: [Recherche]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: dateDebut
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début
 *       - in: query
 *         name: dateFin
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin
 *       - in: query
 *         name: societeId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: journalCode
 *         schema:
 *           type: string
 *         description: Code du journal
 *       - in: query
 *         name: groupeParJournal
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Grouper les résultats par journal
 *       - in: query
 *         name: groupeParMois
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Grouper les résultats par mois
 *       - in: query
 *         name: includeAnalyses
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Inclure les analyses de période
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Numéro de page
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Nombre d'éléments par page
 *     responses:
 *       200:
 *         description: Résultats de recherche par période avec analyses et groupements
 *       400:
 *         description: Dates manquantes ou invalides
 */
router.get('/recherche-periode',
  protect,
  ecritureController.rechercheParPeriode.bind(ecritureController)
);

/**
 * @swagger
 * /api/v1/ecritures/validate:
 *   post:
 *     summary: Valide une écriture selon les normes SYSCOHADA
 *     tags: [Écritures]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - donnees
 *               - lignes
 *               - societeId
 *             properties:
 *               donnees:
 *                 $ref: '#/components/schemas/EcritureData'
 *               lignes:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/LigneEcritureData'
 *               societeId:
 *                 type: string
 *                 format: uuid
 *                 description: ID de la société
 *     responses:
 *       200:
 *         description: Écriture conforme aux normes SYSCOHADA
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Écriture conforme aux normes SYSCOHADA"
 *                 data:
 *                   type: object
 *                   properties:
 *                     validation:
 *                       $ref: '#/components/schemas/ValidationResult'
 *                     conformiteSYSCOHADA:
 *                       type: boolean
 *                     niveauValidation:
 *                       type: string
 *                       enum: [CONFORME_SYSCOHADA, CONFORME_AVEC_RESERVES, NON_CONFORME]
 *       422:
 *         description: Écriture non conforme - Corrections requises
 *       400:
 *         description: Données manquantes ou invalides
 */
router.post('/validate',
  protect,
  validate(Joi.object({
    donnees: ecritureSchema.required(),
    lignes: Joi.array().items(ligneSchema).min(2).required()
      .messages({
        'array.min': 'Une écriture doit contenir au moins 2 lignes'
      }),
    societeId: Joi.string().uuid().required()
      .messages({
        'string.guid': 'L\'ID société doit être un UUID valide',
        'any.required': 'L\'ID société est obligatoire'
      })
  })),
  ecritureController.validateEntry.bind(ecritureController)
);

module.exports = router;
