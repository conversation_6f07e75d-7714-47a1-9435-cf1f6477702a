'use strict';

const request = require('supertest');
const app = require('../app');
const { sequelize } = require('../models');
const CalculService = require('../services/calculService');

describe('Module Calculs et Soldes', () => {
  let authToken;
  let societeId;
  let exerciceId;
  let journalCode;
  let calculService;

  beforeAll(async () => {
    // Synchroniser la base de données
    await sequelize.sync({ force: true });
    
    calculService = new CalculService();

    // Créer un utilisateur de test et obtenir un token
    const userResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'TestPassword123!',
        nom: 'Test',
        prenom: 'Calculs'
      });

    const loginResponse = await request(app)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'TestPassword123!'
      });

    authToken = loginResponse.body.data.token;

    // Créer une société de test
    const societeResponse = await request(app)
      .post('/api/v1/societes')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        raisonSociale: 'Société Test Calculs',
        siret: '12345678901234',
        adresse: '123 Rue Test',
        ville: 'Abidjan',
        pays: 'CI',
        telephone: '+225 01 02 03 04',
        email: '<EMAIL>'
      });

    societeId = societeResponse.body.data.societe.id;

    // Créer un exercice de test
    const exerciceResponse = await request(app)
      .post('/api/v1/exercices')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        societeId,
        annee: 2024,
        dateDebut: '2024-01-01',
        dateFin: '2024-12-31',
        statut: 'OUVERT'
      });

    exerciceId = exerciceResponse.body.data.exercice.id;

    // Récupérer un journal existant
    const journauxResponse = await request(app)
      .get('/api/v1/journaux')
      .set('Authorization', `Bearer ${authToken}`)
      .query({ societeId });

    journalCode = journauxResponse.body.data.journaux[0].code;

    // Créer quelques écritures de test
    await creerEcrituresTest();
  });

  afterAll(async () => {
    await sequelize.close();
  });

  // Fonction utilitaire pour créer des écritures de test
  async function creerEcrituresTest() {
    const ecritures = [
      {
        donnees: {
          numeroEcriture: 'CALC001',
          dateEcriture: '2024-01-15',
          journalCode,
          libelle: 'Écriture test calculs 1',
          societeId,
          exerciceId
        },
        lignes: [
          { compteNumero: '411000', libelle: 'Client A', debit: 1000.00, credit: 0.00 },
          { compteNumero: '701000', libelle: 'Ventes', debit: 0.00, credit: 1000.00 }
        ]
      },
      {
        donnees: {
          numeroEcriture: 'CALC002',
          dateEcriture: '2024-01-20',
          journalCode,
          libelle: 'Écriture test calculs 2',
          societeId,
          exerciceId
        },
        lignes: [
          { compteNumero: '411000', libelle: 'Client B', debit: 1500.00, credit: 0.00 },
          { compteNumero: '701000', libelle: 'Ventes', debit: 0.00, credit: 1500.00 }
        ]
      },
      {
        donnees: {
          numeroEcriture: 'CALC003',
          dateEcriture: '2024-02-10',
          journalCode,
          libelle: 'Écriture test calculs 3',
          societeId,
          exerciceId
        },
        lignes: [
          { compteNumero: '512000', libelle: 'Banque', debit: 800.00, credit: 0.00 },
          { compteNumero: '411000', libelle: 'Client A', debit: 0.00, credit: 800.00 }
        ]
      }
    ];

    for (const ecriture of ecritures) {
      await request(app)
        .post('/api/v1/ecritures')
        .set('Authorization', `Bearer ${authToken}`)
        .send(ecriture);
    }

    // Valider les écritures
    const ecrituresResponse = await request(app)
      .get('/api/v1/ecritures')
      .set('Authorization', `Bearer ${authToken}`)
      .query({ societeId });

    for (const ecriture of ecrituresResponse.body.data.ecritures) {
      await request(app)
        .patch(`/api/v1/ecritures/${ecriture.id}/valider`)
        .set('Authorization', `Bearer ${authToken}`);
    }
  }

  describe('Calculs de soldes', () => {
    test('devrait calculer le solde d\'un compte', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/solde/411000')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          dateFin: '2024-12-31',
          societeId,
          exerciceId
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.solde).toBeDefined();
      expect(response.body.data.solde.compteNumero).toBe('411000');
      expect(response.body.data.solde.totalDebit).toBe(2500); // 1000 + 1500
      expect(response.body.data.solde.totalCredit).toBe(800);
      expect(response.body.data.solde.solde).toBe(1700); // 2500 - 800
      expect(response.body.data.solde.sensActuel).toBe('DEBIT');
    });

    test('devrait calculer les soldes progressifs', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/soldes-progressifs/411000')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          dateDebut: '2024-01-01',
          dateFin: '2024-12-31',
          societeId,
          exerciceId
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.soldesProgressifs).toBeDefined();
      expect(response.body.data.soldesProgressifs.soldesProgressifs).toBeInstanceOf(Array);
      expect(response.body.data.soldesProgressifs.soldesProgressifs.length).toBeGreaterThan(0);
    });

    test('devrait rejeter un calcul sans date de fin', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/solde/411000')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          societeId,
          exerciceId
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('date de fin');
    });
  });

  describe('Calculs de journaux', () => {
    test('devrait calculer les totaux d\'un journal', async () => {
      const response = await request(app)
        .get(`/api/v1/calculs/totaux-journal/${journalCode}`)
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          dateDebut: '2024-01-01',
          dateFin: '2024-12-31',
          societeId,
          exerciceId,
          groupeParCompte: 'true'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.totauxJournal).toBeDefined();
      expect(response.body.data.totauxJournal.journalCode).toBe(journalCode);
      expect(response.body.data.totauxJournal.totaux.equilibre).toBe(true);
      expect(response.body.data.totauxJournal.detailsParCompte).toBeInstanceOf(Array);
    });

    test('devrait rejeter un calcul de journal sans dates', async () => {
      const response = await request(app)
        .get(`/api/v1/calculs/totaux-journal/${journalCode}`)
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          societeId,
          exerciceId
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Calculs de balances', () => {
    test('devrait calculer la balance générale', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/balance')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          dateDebut: '2024-01-01',
          dateFin: '2024-12-31',
          societeId,
          exerciceId,
          niveauDetail: 'TOUS',
          seulementAvecMouvement: 'true'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.balanceGenerale).toBeDefined();
      expect(response.body.data.balanceGenerale.totauxGeneraux.equilibre).toBe(true);
      expect(response.body.data.balanceGenerale.lignesBalance).toBeInstanceOf(Array);
      expect(response.body.data.balanceGenerale.totauxParClasse).toBeInstanceOf(Array);
    });

    test('devrait filtrer la balance par classe de comptes', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/balance')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          dateDebut: '2024-01-01',
          dateFin: '2024-12-31',
          societeId,
          exerciceId,
          classeComptes: '4,7', // Comptes de tiers et produits
          seulementAvecMouvement: 'true'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.balanceGenerale.lignesBalance).toBeInstanceOf(Array);
      
      // Vérifier que seules les classes 4 et 7 sont présentes
      const classes = response.body.data.balanceGenerale.lignesBalance.map(ligne => ligne.classe);
      const classesUniques = [...new Set(classes)];
      expect(classesUniques.every(classe => [4, 7].includes(classe))).toBe(true);
    });

    test('devrait calculer la balance auxiliaire', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/balance-auxiliaire/411')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          dateDebut: '2024-01-01',
          dateFin: '2024-12-31',
          societeId,
          exerciceId,
          seulementAvecMouvement: 'true'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.balanceAuxiliaire).toBeDefined();
      expect(response.body.data.balanceAuxiliaire.compteCollectif).toBe('411');
    });
  });

  describe('Statistiques générales', () => {
    test('devrait calculer les statistiques générales', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/statistiques')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          dateDebut: '2024-01-01',
          dateFin: '2024-12-31',
          societeId
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.statistiques).toBeDefined();
      expect(response.body.data.statistiques.ecritures.nombre).toBeGreaterThan(0);
      expect(response.body.data.statistiques.lignes.nombre).toBeGreaterThan(0);
      expect(response.body.data.statistiques.journauxActifs).toBeInstanceOf(Array);
    });

    test('devrait rejeter les statistiques sans société', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/statistiques')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          dateDebut: '2024-01-01',
          dateFin: '2024-12-31'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Gestion du cache', () => {
    test('devrait obtenir les statistiques du cache', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/cache/stats')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.cache).toBeDefined();
      expect(response.body.data.cache.totalEntrees).toBeDefined();
      expect(response.body.data.cache.tauxHit).toBeDefined();
    });

    test('devrait vider le cache', async () => {
      const response = await request(app)
        .delete('/api/v1/calculs/cache')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Cache vidé');
    });
  });

  describe('Routes utilitaires', () => {
    test('devrait retourner les informations sur les calculs', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/info')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.calculsDisponibles).toBeDefined();
      expect(response.body.data.calculsDisponibles.soldes).toBeDefined();
      expect(response.body.data.calculsDisponibles.balanceGenerale).toBeDefined();
      expect(response.body.data.classesComptes).toBeDefined();
    });
  });

  describe('Service CalculService', () => {
    test('devrait calculer un solde avec le service directement', async () => {
      const solde = await calculService.calculerSoldeCompte(
        '411000',
        null,
        new Date('2024-12-31'),
        { societeId, exerciceId }
      );

      expect(solde).toBeDefined();
      expect(solde.compteNumero).toBe('411000');
      expect(solde.totalDebit).toBeGreaterThan(0);
      expect(solde.sensActuel).toBeDefined();
    });

    test('devrait utiliser le cache pour les calculs répétés', async () => {
      // Premier calcul
      const debut1 = Date.now();
      const solde1 = await calculService.calculerSoldeCompte(
        '701000',
        null,
        new Date('2024-12-31'),
        { societeId, exerciceId, useCache: true }
      );
      const duree1 = Date.now() - debut1;

      // Deuxième calcul (devrait être plus rapide grâce au cache)
      const debut2 = Date.now();
      const solde2 = await calculService.calculerSoldeCompte(
        '701000',
        null,
        new Date('2024-12-31'),
        { societeId, exerciceId, useCache: true }
      );
      const duree2 = Date.now() - debut2;

      expect(solde1.solde).toBe(solde2.solde);
      expect(duree2).toBeLessThan(duree1); // Le cache devrait accélérer
    });

    test('devrait valider les périodes correctement', () => {
      expect(() => {
        calculService.validerPeriode(null, new Date());
      }).toThrow('obligatoires');

      expect(() => {
        calculService.validerPeriode(new Date('2024-12-31'), new Date('2024-01-01'));
      }).toThrow('antérieure');

      expect(() => {
        calculService.validerPeriode(new Date('2024-01-01'), new Date('2025-12-31'));
      }).toThrow('366 jours');
    });

    test('devrait formater les montants correctement', () => {
      expect(calculService.formaterMontant(1234.56)).toContain('1 234,56');
      expect(calculService.formaterMontant(0)).toContain('0,00');
      expect(calculService.formaterMontant(null)).toBe('0,00');
    });

    test('devrait obtenir les libellés de classes', () => {
      expect(calculService.getLibelleClasse(1)).toBe('Comptes de capitaux');
      expect(calculService.getLibelleClasse(4)).toBe('Comptes de tiers');
      expect(calculService.getLibelleClasse(7)).toBe('Comptes de produits');
      expect(calculService.getLibelleClasse(9)).toBe('Classe 9');
    });
  });

  describe('Gestion des erreurs', () => {
    test('devrait rejeter une requête sans authentification', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/solde/411000')
        .query({ dateFin: '2024-12-31' });

      expect(response.status).toBe(401);
    });

    test('devrait gérer un compte inexistant', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/solde/999999')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          dateFin: '2024-12-31',
          societeId,
          exerciceId
        });

      expect(response.status).toBe(404);
    });

    test('devrait valider les paramètres de date', async () => {
      const response = await request(app)
        .get('/api/v1/calculs/solde/411000')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          dateFin: 'date-invalide',
          societeId,
          exerciceId
        });

      expect(response.status).toBe(400);
    });
  });
});
