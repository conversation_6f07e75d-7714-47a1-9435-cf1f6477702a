/**
 * Controller pour la gestion des sociétés
 * API Comptabilité SYSCOHADA
 */

const { Societe, Journal, CompteComptable } = require('../models');
const { AppError, NotFoundError, ValidationError, asyncHandler } = require('../middleware/errorHandler');
const { logger } = require('../config/logger');

/**
 * Récupérer toutes les sociétés
 */
const getAllSocietes = asyncHandler(async (req, res) => {
  const { page = 1, limit = 20 } = req.query;
  const offset = (page - 1) * limit;

  const { count, rows: societes } = await Societe.findAndCountAll({
    limit: parseInt(limit),
    offset: parseInt(offset),
    include: [
      {
        model: Journal,
        as: 'journaux',
        attributes: ['code', 'libelle', 'type']
      }
    ],
    order: [['nom', 'ASC']]
  });

  logger.info('Sociétés récupérées', {
    count,
    page: parseInt(page),
    limit: parseInt(limit)
  });

  res.json({
    success: true,
    data: societes,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count,
      pages: Math.ceil(count / limit)
    }
  });
});

/**
 * Récupérer une société par ID
 */
const getSocieteById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const societe = await Societe.findByPk(id, {
    include: [
      {
        model: Journal,
        as: 'journaux',
        attributes: ['code', 'libelle', 'type', 'compteContropartie']
      },
      {
        model: CompteComptable,
        as: 'comptes',
        attributes: ['numero', 'libelle', 'classe', 'nature'],
        limit: 10 // Limiter pour éviter trop de données
      }
    ]
  });

  if (!societe) {
    throw new NotFoundError('Société');
  }

  logger.info('Société récupérée', { societeId: id });

  res.json({
    success: true,
    data: societe
  });
});

/**
 * Créer une nouvelle société
 */
const createSociete = asyncHandler(async (req, res) => {
  const societeData = req.body;

  // Validation des dates d'exercice
  if (new Date(societeData.exerciceDebut) >= new Date(societeData.exerciceFin)) {
    throw new ValidationError('La date de début d\'exercice doit être antérieure à la date de fin');
  }

  const societe = await Societe.create(societeData);

  logger.info('Société créée', {
    societeId: societe.id,
    nom: societe.nom
  });

  res.status(201).json({
    success: true,
    message: 'Société créée avec succès',
    data: societe
  });
});

/**
 * Mettre à jour une société
 */
const updateSociete = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  const societe = await Societe.findByPk(id);
  if (!societe) {
    throw new NotFoundError('Société');
  }

  // Validation des dates si modifiées
  if (updateData.exerciceDebut || updateData.exerciceFin) {
    const debut = updateData.exerciceDebut || societe.exerciceDebut;
    const fin = updateData.exerciceFin || societe.exerciceFin;
    
    if (new Date(debut) >= new Date(fin)) {
      throw new ValidationError('La date de début d\'exercice doit être antérieure à la date de fin');
    }
  }

  await societe.update(updateData);

  logger.info('Société mise à jour', {
    societeId: id,
    modifications: Object.keys(updateData)
  });

  res.json({
    success: true,
    message: 'Société mise à jour avec succès',
    data: societe
  });
});

/**
 * Supprimer une société
 */
const deleteSociete = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const societe = await Societe.findByPk(id);
  if (!societe) {
    throw new NotFoundError('Société');
  }

  // Vérifier s'il y a des données liées
  const journauxCount = await Journal.count({ where: { societeId: id } });
  const comptesCount = await CompteComptable.count({ where: { societeId: id } });

  if (journauxCount > 0 || comptesCount > 0) {
    throw new ValidationError('Impossible de supprimer une société qui contient des journaux ou des comptes');
  }

  await societe.destroy();

  logger.warn('Société supprimée', {
    societeId: id,
    nom: societe.nom
  });

  res.json({
    success: true,
    message: 'Société supprimée avec succès'
  });
});

/**
 * Obtenir les statistiques d'une société
 */
const getSocieteStats = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const societe = await Societe.findByPk(id);
  if (!societe) {
    throw new NotFoundError('Société');
  }

  const stats = {
    journaux: await Journal.count({ where: { societeId: id } }),
    comptes: await CompteComptable.count({ where: { societeId: id } }),
    // Ajouter d'autres statistiques quand les modèles seront disponibles
  };

  res.json({
    success: true,
    data: {
      societe: {
        id: societe.id,
        nom: societe.nom,
        devise: societe.devise
      },
      statistiques: stats
    }
  });
});

module.exports = {
  getAllSocietes,
  getSocieteById,
  createSociete,
  updateSociete,
  deleteSociete,
  getSocieteStats
};
