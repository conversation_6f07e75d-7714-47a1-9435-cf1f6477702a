'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Créer le type ENUM pour reset_sequence s'il n'existe pas
    await queryInterface.sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE enum_journaux_reset_sequence AS ENUM ('JAMAIS', 'MENSUEL', 'ANNUEL');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryInterface.addColumn('journaux', 'numero_sequence', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Compteur de séquence pour numérotation automatique'
    });

    await queryInterface.addColumn('journaux', 'prefixe_numero', {
      type: Sequelize.STRING(10),
      allowNull: true,
      comment: 'Préfixe pour la numérotation (ex: VT, AC, BQ)'
    });

    await queryInterface.addColumn('journaux', 'longueur_numero', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 6,
      comment: 'Longueur totale du numéro généré (padding avec zéros)'
    });

    await queryInterface.addColumn('journaux', 'reset_sequence', {
      type: 'enum_journaux_reset_sequence',
      allowNull: false,
      defaultValue: 'ANNUEL',
      comment: 'Fréquence de remise à zéro de la séquence'
    });

    await queryInterface.addColumn('journaux', 'dernier_numero', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Dernier numéro généré pour ce journal'
    });

    await queryInterface.addColumn('journaux', 'personnalise', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Indique si le journal a été créé par l\'utilisateur'
    });

    await queryInterface.addColumn('journaux', 'actif', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Indique si le journal est actif'
    });

    await queryInterface.addColumn('journaux', 'controle_equilibre', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Vérification automatique équilibre débit/crédit'
    });

    await queryInterface.addColumn('journaux', 'date_dernier_reset', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: 'Date du dernier reset de séquence'
    });

    await queryInterface.addColumn('journaux', 'utilisateur_creation', {
      type: Sequelize.UUID,
      allowNull: true,
      comment: 'Utilisateur ayant créé le journal personnalisé'
    });

    // Créer des index pour optimiser les performances
    await queryInterface.addIndex('journaux', ['societe_id', 'actif'], {
      name: 'idx_journal_societe_actif'
    });

    await queryInterface.addIndex('journaux', ['societe_id', 'personnalise'], {
      name: 'idx_journal_societe_personnalise'
    });

    await queryInterface.addIndex('journaux', ['type', 'actif'], {
      name: 'idx_journal_type_actif'
    });

    // Mettre à jour les journaux existants avec des valeurs par défaut intelligentes
    await queryInterface.sequelize.query(`
      UPDATE journaux
      SET
        prefixe_numero = code,
        personnalise = false,
        actif = true,
        controle_equilibre = true,
        longueur_numero = CASE
          WHEN type = 'BANQUE' THEN 8
          WHEN type = 'VENTE' THEN 6
          WHEN type = 'ACHAT' THEN 6
          ELSE 6
        END,
        reset_sequence = CASE
          WHEN type = 'OD' THEN 'JAMAIS'::enum_journaux_reset_sequence
          ELSE 'ANNUEL'::enum_journaux_reset_sequence
        END
      WHERE prefixe_numero IS NULL
    `);
  },

  async down(queryInterface, Sequelize) {
    // Supprimer les index
    await queryInterface.removeIndex('journaux', 'idx_journal_societe_actif');
    await queryInterface.removeIndex('journaux', 'idx_journal_societe_personnalise');
    await queryInterface.removeIndex('journaux', 'idx_journal_type_actif');

    // Supprimer les colonnes
    await queryInterface.removeColumn('journaux', 'numero_sequence');
    await queryInterface.removeColumn('journaux', 'prefixe_numero');
    await queryInterface.removeColumn('journaux', 'longueur_numero');
    await queryInterface.removeColumn('journaux', 'reset_sequence');
    await queryInterface.removeColumn('journaux', 'dernier_numero');
    await queryInterface.removeColumn('journaux', 'personnalise');
    await queryInterface.removeColumn('journaux', 'actif');
    await queryInterface.removeColumn('journaux', 'controle_equilibre');
    await queryInterface.removeColumn('journaux', 'date_dernier_reset');
    await queryInterface.removeColumn('journaux', 'utilisateur_creation');

    // Supprimer le type ENUM
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS enum_journaux_reset_sequence;
    `);
  }
};
