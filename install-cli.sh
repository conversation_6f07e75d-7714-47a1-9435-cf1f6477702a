#!/bin/bash

# Script d'installation du CLI SYSCOHADA

echo "🚀 Installation du CLI SYSCOHADA..."

# Vérifier que Node.js est installé
if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

# Vérifier que npm est installé
if ! command -v npm &> /dev/null; then
    echo "❌ npm n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

# Vérifier la version de Node.js
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 14 ]; then
    echo "❌ Node.js version 14 ou supérieure est requise. Version actuelle: $(node -v)"
    exit 1
fi

# Créer le dossier de destination
CLI_DIR="$HOME/.syscohada-cli-tool"
echo "📁 Création du dossier d'installation: $CLI_DIR"
mkdir -p "$CLI_DIR"

# Copier les fichiers nécessaires
echo "📁 Copie des fichiers..."
cp cli.js "$CLI_DIR/"
cp cli-package.json "$CLI_DIR/package.json"

# Vérifier que les fichiers ont été copiés
if [ ! -f "$CLI_DIR/cli.js" ] || [ ! -f "$CLI_DIR/package.json" ]; then
    echo "❌ Erreur lors de la copie des fichiers."
    exit 1
fi

# Se déplacer dans le dossier CLI
cd "$CLI_DIR"

# Installer les dépendances
echo "📦 Installation des dépendances..."
npm install --production --silent

if [ $? -ne 0 ]; then
    echo "❌ Erreur lors de l'installation des dépendances."
    exit 1
fi

# Rendre le script exécutable
chmod +x cli.js

# Créer un lien symbolique global
echo "🔗 Création du lien symbolique global..."
if command -v sudo &> /dev/null; then
    sudo npm link
else
    npm link
fi

if [ $? -ne 0 ]; then
    echo "⚠️ Impossible de créer le lien global. Vous pouvez utiliser le CLI avec:"
    echo "   node $CLI_DIR/cli.js"
    echo ""
    echo "Pour créer le lien global manuellement:"
    echo "   sudo npm link (dans le dossier $CLI_DIR)"
else
    echo "✅ Installation terminée!"
fi

echo ""
echo "🎉 Le CLI SYSCOHADA est maintenant installé."

# Vérifier si la commande syscohada est disponible
if command -v syscohada &> /dev/null; then
    echo "✅ Commande 'syscohada' disponible globalement."
    echo ""
    echo "📚 Pour commencer:"
    echo "  syscohada --help"
    echo "  syscohada config"
    echo "  syscohada auth:setup"
else
    echo "⚠️ La commande 'syscohada' n'est pas disponible globalement."
    echo "Vous pouvez utiliser le CLI avec:"
    echo "  node $CLI_DIR/cli.js --help"
    echo "  node $CLI_DIR/cli.js config"
    echo "  node $CLI_DIR/cli.js auth:setup"
fi

echo ""
echo "📖 Documentation complète disponible dans CLI_README_COMPLETE.md"
echo "🧪 Testez l'installation avec: syscohada status"