/**
 * Routes pour la gestion des sociétés
 * API Comptabilité SYSCOHADA
 */

const express = require('express');
const router = express.Router();

const {
  getAllSocietes,
  getSocieteById,
  createSociete,
  updateSociete,
  deleteSociete,
  getSocieteStats
} = require('../controllers/societeController');

const { validate, schemas } = require('../middleware/validation');

/**
 * @route   GET /api/v1/societes
 * @desc    Récupérer toutes les sociétés
 * @access  Public (sera protégé plus tard)
 */
router.get('/', getAllSocietes);

/**
 * @route   GET /api/v1/societes/:id
 * @desc    Récupérer une société par ID
 * @access  Public
 */
router.get('/:id', getSocieteById);

/**
 * @route   GET /api/v1/societes/:id/stats
 * @desc    Récupérer les statistiques d'une société
 * @access  Public
 */
router.get('/:id/stats', getSocieteStats);

/**
 * @route   POST /api/v1/societes
 * @desc    Créer une nouvelle société
 * @access  Public
 */
router.post('/', 
  validate(schemas.societe.create),
  createSociete
);

/**
 * @route   PUT /api/v1/societes/:id
 * @desc    Mettre à jour une société
 * @access  Public
 */
router.put('/:id',
  validate(schemas.societe.update),
  updateSociete
);

/**
 * @route   DELETE /api/v1/societes/:id
 * @desc    Supprimer une société
 * @access  Public
 */
router.delete('/:id', deleteSociete);

module.exports = router;
