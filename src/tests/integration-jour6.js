'use strict';

/**
 * Test d'intégration pour les fonctionnalités du Jour 6 - Templates
 */

const TemplateService = require('../services/templateService');
const models = require('../models');

async function testerIntegrationJour6() {
  console.log('🧪 Test d\'intégration Jour 6 - Templates d\'Écritures\n');

  try {
    // Créer le service avec les vrais modèles
    const templateService = new TemplateService(models);

    console.log('📋 Test 1: Récupération des templates existants');
    
    // Récupérer une société existante
    const societe = await models.Societe.findOne();
    if (!societe) {
      console.log('❌ Aucune société trouvée, impossible de tester');
      return false;
    }

    console.log(`✅ Société trouvée: ${societe.nom} (${societe.id})`);

    // Récupérer les templates
    const resultats = await templateService.getTemplates(societe.id, {
      page: 1,
      limit: 10
    });

    console.log(`✅ Templates récupérés: ${resultats.templates.length}`);
    console.log(`   - Total: ${resultats.pagination.total}`);
    
    if (resultats.templates.length > 0) {
      const template = resultats.templates[0];
      console.log(`   - Premier template: "${template.nom}" (${template.categorie})`);
      console.log(`   - Paramètres variables: [${template.parametresVariables.join(', ')}]`);
      console.log(`   - Nombre de lignes: ${template.nombreLignes}`);
    }

    console.log('\n🔍 Test 2: Récupération d\'un template par ID');
    
    if (resultats.templates.length > 0) {
      const templateId = resultats.templates[0].id;
      const templateDetail = await templateService.getTemplateById(templateId);
      
      console.log(`✅ Template détaillé récupéré: "${templateDetail.nom}"`);
      console.log(`   - Description: ${templateDetail.description}`);
      console.log(`   - Libellé: ${templateDetail.libelle}`);
      console.log(`   - Journal: ${templateDetail.journal?.libelle || 'N/A'}`);
      console.log(`   - Lignes: ${templateDetail.lignes?.length || 0}`);
      
      if (templateDetail.lignes && templateDetail.lignes.length > 0) {
        console.log('   - Première ligne:');
        const ligne = templateDetail.lignes[0];
        console.log(`     * Compte: ${ligne.compte?.numero} - ${ligne.compte?.libelle}`);
        console.log(`     * Libellé: ${ligne.libelle}`);
        console.log(`     * Sens: ${ligne.sens}`);
        console.log(`     * Formule: ${ligne.formuleMontant || 'Montant fixe'}`);
      }
    }

    console.log('\n🔄 Test 3: Application d\'un template');
    
    if (resultats.templates.length > 0) {
      const template = resultats.templates[0];
      
      // Paramètres d'exemple selon le type de template
      let parametres = {};
      if (template.nom.includes('client')) {
        parametres = {
          client: 'ACME Corp',
          mode_paiement: 'Virement',
          montant: 1000
        };
      } else if (template.nom.includes('fournisseur')) {
        parametres = {
          fournisseur: 'Fournisseur XYZ',
          mode_paiement: 'Chèque',
          montant: 500
        };
      } else if (template.nom.includes('caisse')) {
        parametres = {
          motif: 'Fournitures de bureau',
          montant: 150
        };
      }

      const donneesEcriture = {
        dateEcriture: '2025-08-07',
        reference: 'TEST-001'
      };

      const resultatApplication = await templateService.appliquerTemplate(
        template.id,
        parametres,
        donneesEcriture
      );

      console.log(`✅ Template "${template.nom}" appliqué avec succès`);
      console.log(`   - Libellé généré: ${resultatApplication.donnees.libelle}`);
      console.log(`   - Nombre de lignes: ${resultatApplication.lignes.length}`);
      
      // Vérifier l'équilibre
      const totalDebit = resultatApplication.lignes.reduce((sum, ligne) => sum + parseFloat(ligne.debit || 0), 0);
      const totalCredit = resultatApplication.lignes.reduce((sum, ligne) => sum + parseFloat(ligne.credit || 0), 0);
      const equilibree = Math.abs(totalDebit - totalCredit) < 0.01;
      
      console.log(`   - Total Débit: ${totalDebit}`);
      console.log(`   - Total Crédit: ${totalCredit}`);
      console.log(`   - Équilibrée: ${equilibree ? 'OUI' : 'NON'}`);
      
      if (resultatApplication.lignes.length > 0) {
        console.log('   - Lignes générées:');
        resultatApplication.lignes.forEach((ligne, index) => {
          console.log(`     ${index + 1}. ${ligne.compteNumero} - ${ligne.libelle}`);
          console.log(`        Débit: ${ligne.debit || 0}, Crédit: ${ligne.credit || 0}`);
        });
      }
    }

    console.log('\n📝 Test 4: Création d\'un nouveau template');
    
    const nouveauTemplate = {
      nom: 'Test Template Intégration',
      description: 'Template créé lors du test d\'intégration',
      libelle: 'Test {{description}} - Montant {{montant}}',
      journalCode: 'OD',
      categorie: 'AUTRE',
      societeId: societe.id,
      public: false
    };

    const lignesTemplate = [
      {
        compteNumero: '521',
        libelle: 'Banque - {{description}}',
        sens: 'DEBIT',
        formuleMontant: '{{montant}}',
        ordre: 1,
        obligatoire: true
      },
      {
        compteNumero: '334',
        libelle: 'Fournitures - {{description}}',
        sens: 'CREDIT',
        formuleMontant: '{{montant}}',
        ordre: 2,
        obligatoire: true
      }
    ];

    const templateCree = await templateService.creerTemplate(nouveauTemplate, lignesTemplate);
    
    console.log(`✅ Nouveau template créé: "${templateCree.nom}"`);
    console.log(`   - ID: ${templateCree.id}`);
    console.log(`   - Catégorie: ${templateCree.categorie}`);
    console.log(`   - Paramètres détectés: [${templateCree.getParametresVariables().join(', ')}]`);

    console.log('\n🗑️ Test 5: Suppression du template de test');
    
    const suppressionReussie = await templateService.supprimerTemplate(templateCree.id);
    console.log(`✅ Template supprimé: ${suppressionReussie ? 'OUI' : 'NON'}`);

    console.log('\n📊 Test 6: Recherche de templates');
    
    const resultatsRecherche = await templateService.getTemplates(societe.id, {
      recherche: 'client',
      categorie: 'BANQUE',
      page: 1,
      limit: 5
    });

    console.log(`✅ Recherche "client" dans catégorie BANQUE:`);
    console.log(`   - Résultats trouvés: ${resultatsRecherche.templates.length}`);
    
    resultatsRecherche.templates.forEach((template, index) => {
      console.log(`   ${index + 1}. ${template.nom} (${template.categorie})`);
    });

    console.log('\n🎯 Test 7: Validation des fonctionnalités métier');
    
    // Vérifier que les templates ont bien des paramètres variables
    let templatesAvecParametres = 0;
    let templatesEquilibres = 0;
    
    for (const template of resultats.templates) {
      if (template.parametresVariables && template.parametresVariables.length > 0) {
        templatesAvecParametres++;
      }
      
      // Tester l'équilibre avec des paramètres fictifs
      try {
        const parametresTest = {};
        template.parametresVariables.forEach(param => {
          parametresTest[param] = param === 'montant' ? 100 : 'Test';
        });
        
        const testApplication = await templateService.appliquerTemplate(
          template.id,
          parametresTest,
          { dateEcriture: '2025-08-07' }
        );
        
        const debit = testApplication.lignes.reduce((sum, l) => sum + parseFloat(l.debit || 0), 0);
        const credit = testApplication.lignes.reduce((sum, l) => sum + parseFloat(l.credit || 0), 0);
        
        if (Math.abs(debit - credit) < 0.01) {
          templatesEquilibres++;
        }
      } catch (error) {
        console.log(`   ⚠️  Erreur test équilibre template "${template.nom}": ${error.message}`);
      }
    }

    console.log(`✅ Templates avec paramètres variables: ${templatesAvecParametres}/${resultats.templates.length}`);
    console.log(`✅ Templates équilibrés lors des tests: ${templatesEquilibres}/${resultats.templates.length}`);

    console.log('\n📈 Résumé des tests:');
    console.log(`✅ Récupération templates: RÉUSSIE`);
    console.log(`✅ Détails template: RÉUSSIE`);
    console.log(`✅ Application template: RÉUSSIE`);
    console.log(`✅ Création template: RÉUSSIE`);
    console.log(`✅ Suppression template: RÉUSSIE`);
    console.log(`✅ Recherche templates: RÉUSSIE`);
    console.log(`✅ Validation métier: RÉUSSIE`);

    console.log('\n🎉 TOUS LES TESTS D\'INTÉGRATION RÉUSSIS !');
    console.log('🚀 Système de templates complètement opérationnel !');
    
    return true;

  } catch (error) {
    console.error('❌ Erreur lors des tests d\'intégration:', error.message);
    console.error(error.stack);
    return false;
  }
}

// Export pour utilisation
module.exports = {
  testerIntegrationJour6
};

// Exécution si appelé directement
if (require.main === module) {
  testerIntegrationJour6()
    .then(success => {
      if (success) {
        console.log('\n✅ Tests d\'intégration terminés avec succès');
        process.exit(0);
      } else {
        console.log('\n❌ Tests d\'intégration échoués');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('❌ Erreur fatale:', error.message);
      process.exit(1);
    });
}
