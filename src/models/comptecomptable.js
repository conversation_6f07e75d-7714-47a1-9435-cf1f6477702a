'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class CompteComptable extends Model {
    /**
     * Associations du modèle CompteComptable
     */
    static associate(models) {
      // Un compte appartient à une société
      CompteComptable.belongsTo(models.Societe, {
        foreignKey: 'societeId',
        as: 'societe'
      });

      // Un compte peut avoir un compte parent
      CompteComptable.belongsTo(models.CompteComptable, {
        foreignKey: 'compteParent',
        as: 'parent'
      });

      // Un compte peut avoir plusieurs sous-comptes
      CompteComptable.hasMany(models.CompteComptable, {
        foreignKey: 'compteParent',
        as: 'sousComptes'
      });

      // Un compte peut avoir plusieurs lignes d'écriture
      CompteComptable.hasMany(models.LigneEcriture, {
        foreignKey: 'compteNumero',
        sourceKey: 'numero',
        as: 'lignesEcriture'
      });
    }

    /**
     * Méthodes d'instance
     */

    /**
     * Vérifie si le compte est un compte de bilan
     */
    isCompteBilan() {
      return this.classe >= 1 && this.classe <= 5;
    }

    /**
     * Vérifie si le compte est un compte de gestion
     */
    isCompteGestion() {
      return this.classe >= 6 && this.classe <= 8;
    }

    /**
     * Retourne le libellé de la classe
     */
    getLibelleClasse() {
      const classes = {
        1: 'COMPTES DE RESSOURCES DURABLES',
        2: 'COMPTES D\'ACTIF IMMOBILISE',
        3: 'COMPTES DE STOCKS',
        4: 'COMPTES DE TIERS',
        5: 'COMPTES DE TRESORERIE',
        6: 'COMPTES DE CHARGES DES ACTIVITES ORDINAIRES',
        7: 'COMPTES DE PRODUITS DES ACTIVITES ORDINAIRES',
        8: 'COMPTES DES AUTRES CHARGES ET DES AUTRES PRODUITS'
      };
      return classes[this.classe] || 'CLASSE INCONNUE';
    }

    /**
     * Validation du numéro de compte SYSCOHADA
     */
    static validateNumeroSYCOHADA(numero) {
      const regex = /^[1-8]\d{2,9}$/;
      if (!regex.test(numero)) {
        throw new Error('Le numéro de compte doit commencer par un chiffre de 1 à 8 et contenir 3 à 10 chiffres');
      }
      return true;
    }

    /**
     * Vérifie si le compte est personnalisé (créé par l'utilisateur)
     */
    isComptePersonnalise() {
      return this.personnalise === true;
    }

    /**
     * Vérifie si le compte est modifiable
     */
    isCompteModifiable() {
      return this.modifiable === true;
    }

    /**
     * Vérifie si le compte est actif
     */
    isCompteActif() {
      return this.actif === true;
    }

    /**
     * Vérifie si le lettrage est obligatoire pour ce compte
     */
    isLettrageObligatoire() {
      return this.obligatoireLettrage === true;
    }

    /**
     * Vérifie si le compte a une gestion analytique
     */
    hasGestionAnalytique() {
      return this.typeAnalytique !== 'AUCUN';
    }

    /**
     * Obtient le niveau hiérarchique du compte (nombre de chiffres)
     */
    getNiveauHierarchique() {
      return this.numero.length;
    }

    /**
     * Vérifie si le compte peut avoir des sous-comptes
     */
    canHaveSousComptes() {
      // Les comptes de niveau 1 à 3 peuvent avoir des sous-comptes
      return this.getNiveauHierarchique() <= 3;
    }

    /**
     * Génère le prochain numéro de sous-compte disponible
     */
    async getProchainNumeroSousCompte() {
      const models = this.sequelize.models;

      // Chercher tous les sous-comptes existants
      const sousComptes = await models.CompteComptable.findAll({
        where: {
          societeId: this.societeId,
          numero: {
            [this.sequelize.Op.like]: `${this.numero}%`
          }
        },
        order: [['numero', 'DESC']]
      });

      if (sousComptes.length === 0) {
        return `${this.numero}1`;
      }

      // Trouver le plus grand numéro
      const dernierNumero = sousComptes[0].numero;
      const suffixe = dernierNumero.substring(this.numero.length);
      const prochainSuffixe = parseInt(suffixe) + 1;

      return `${this.numero}${prochainSuffixe}`;
    }

    /**
     * Vérifie si le compte peut être supprimé
     */
    async canBeDeleted() {
      const models = this.sequelize.models;

      // Vérifier s'il y a des écritures sur ce compte
      const nombreEcritures = await models.LigneEcriture.count({
        where: { compteNumero: this.numero }
      });

      if (nombreEcritures > 0) {
        return {
          canDelete: false,
          reason: `Le compte a ${nombreEcritures} écriture(s) associée(s)`
        };
      }

      // Vérifier s'il y a des sous-comptes
      const nombreSousComptes = await models.CompteComptable.count({
        where: {
          societeId: this.societeId,
          compteParent: this.numero
        }
      });

      if (nombreSousComptes > 0) {
        return {
          canDelete: false,
          reason: `Le compte a ${nombreSousComptes} sous-compte(s)`
        };
      }

      // Les comptes standard SYSCOHADA ne peuvent pas être supprimés
      if (!this.personnalise) {
        return {
          canDelete: false,
          reason: 'Les comptes standard SYSCOHADA ne peuvent pas être supprimés'
        };
      }

      return { canDelete: true, reason: null };
    }

    /**
     * Validation avancée pour numéro de compte personnalisé
     */
    static validateNumeroPersonnalise(numero, compteParent = null) {
      // Validation de base SYSCOHADA
      const validationBase = CompteComptable.validateNumeroSYCOHADA(numero);
      if (!validationBase) return validationBase;

      // Si c'est un sous-compte, vérifier la cohérence avec le parent
      if (compteParent) {
        if (!numero.startsWith(compteParent)) {
          return {
            valide: false,
            erreur: `Le sous-compte doit commencer par le numéro du compte parent (${compteParent})`
          };
        }

        if (numero.length <= compteParent.length) {
          return {
            valide: false,
            erreur: 'Le sous-compte doit être plus long que le compte parent'
          };
        }
      }

      return { valide: true, erreur: null };
    }
  }

  CompteComptable.init({
    numero: {
      type: DataTypes.STRING(10),
      primaryKey: true,
      validate: {
        isNumeric: true,
        len: [3, 10],
        isValidSYCOHADA(value) {
          CompteComptable.validateNumeroSYCOHADA(value);
        }
      }
    },
    libelle: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    classe: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 8
      }
    },
    nature: {
      type: DataTypes.ENUM('ACTIF', 'PASSIF', 'CHARGE', 'PRODUIT'),
      allowNull: false
    },
    sens: {
      type: DataTypes.ENUM('DEBIT', 'CREDIT'),
      allowNull: false
    },
    niveau: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 5
      }
    },
    compteParent: {
      type: DataTypes.STRING(10),
      allowNull: true,
      references: {
        model: 'compte_comptables',
        key: 'numero'
      }
    },
    societeId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'societes',
        key: 'id'
      }
    },
    personnalise: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'true si le compte a été créé par l\'utilisateur'
    },
    modifiable: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'false pour les comptes SYSCOHADA standard'
    },
    dateCreation: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Date de création du compte personnalisé'
    },
    utilisateurCreation: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: 'ID de l\'utilisateur qui a créé le compte'
    },
    raisonSociale: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Raison sociale pour les comptes clients/fournisseurs'
    },
    actif: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Compte actif ou désactivé'
    },
    obligatoireLettrage: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Lettrage obligatoire pour ce compte'
    },
    typeAnalytique: {
      type: DataTypes.ENUM('AUCUN', 'CENTRE_COUT', 'PROJET'),
      allowNull: false,
      defaultValue: 'AUCUN',
      comment: 'Type de gestion analytique'
    }
  }, {
    sequelize,
    modelName: 'CompteComptable',
    tableName: 'compte_comptables',
    timestamps: true,
    underscored: true,
    hooks: {
      beforeValidate: (compte) => {
        // Extraire la classe du numéro de compte
        if (compte.numero) {
          compte.classe = parseInt(compte.numero.charAt(0));
        }
      }
    }
  });

  return CompteComptable;
};