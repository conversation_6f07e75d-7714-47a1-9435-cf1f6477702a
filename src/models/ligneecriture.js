'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class LigneEcriture extends Model {
    /**
     * Associations du modèle LigneEcriture
     */
    static associate(models) {
      // Une ligne appartient à une écriture
      LigneEcriture.belongsTo(models.EcritureComptable, {
        foreignKey: 'ecritureId',
        as: 'ecriture'
      });

      // Une ligne appartient à un compte
      LigneEcriture.belongsTo(models.CompteComptable, {
        foreignKey: 'compteNumero',
        targetKey: 'numero',
        as: 'compte'
      });

      // Une ligne peut appartenir à un tiers
      LigneEcriture.belongsTo(models.Party, {
        foreignKey: 'tiersId',
        as: 'tiers'
      });
    }

    /**
     * Méthodes statiques
     */

    /**
     * Valide les données d'une ligne d'écriture
     * @param {Object} donnees - Données à valider
     * @returns {Object} Résultat de validation
     */
    static validateLigneData(donnees) {
      const errors = [];

      if (!donnees.compteNumero) {
        errors.push('Le compte comptable est obligatoire');
      }

      if (!donnees.libelle || donnees.libelle.trim().length < 2) {
        errors.push('Le libellé doit contenir au moins 2 caractères');
      }

      const debit = parseFloat(donnees.debit || 0);
      const credit = parseFloat(donnees.credit || 0);

      if (debit < 0 || credit < 0) {
        errors.push('Les montants débit et crédit doivent être positifs');
      }

      if (debit > 0 && credit > 0) {
        errors.push('Une ligne ne peut pas avoir à la fois un débit et un crédit');
      }

      if (debit === 0 && credit === 0) {
        errors.push('Une ligne doit avoir soit un débit soit un crédit');
      }

      return {
        valide: errors.length === 0,
        erreurs: errors
      };
    }

    /**
     * Calcule le solde d'un compte à une date donnée
     * @param {string} compteNumero - Numéro du compte
     * @param {Date} dateFinale - Date finale
     * @returns {Promise<Object>} Solde {debit, credit, solde}
     */
    static async calculerSoldeCompte(compteNumero, dateFinale) {
      const { Op } = sequelize;

      const lignes = await this.findAll({
        where: {
          compteNumero
        },
        include: [
          {
            model: sequelize.models.EcritureComptable,
            as: 'ecriture',
            where: {
              dateEcriture: {
                [Op.lte]: dateFinale
              },
              statut: 'VALIDEE'
            }
          }
        ]
      });

      let totalDebit = 0;
      let totalCredit = 0;

      lignes.forEach(ligne => {
        totalDebit += parseFloat(ligne.debit || 0);
        totalCredit += parseFloat(ligne.credit || 0);
      });

      return {
        debit: totalDebit,
        credit: totalCredit,
        solde: totalDebit - totalCredit,
        nombreLignes: lignes.length
      };
    }

    /**
     * Méthodes d'instance
     */

    /**
     * Vérifie si la ligne est au débit
     * @returns {boolean} True si débit
     */
    estAuDebit() {
      return parseFloat(this.debit || 0) > 0;
    }

    /**
     * Vérifie si la ligne est au crédit
     * @returns {boolean} True si crédit
     */
    estAuCredit() {
      return parseFloat(this.credit || 0) > 0;
    }

    /**
     * Obtient le montant de la ligne (débit ou crédit)
     * @returns {number} Montant
     */
    getMontant() {
      return parseFloat(this.debit || 0) + parseFloat(this.credit || 0);
    }

    /**
     * Obtient le sens de la ligne (DEBIT ou CREDIT)
     * @returns {string} Sens
     */
    getSens() {
      return this.estAuDebit() ? 'DEBIT' : 'CREDIT';
    }

    /**
     * Formate la ligne pour affichage
     * @returns {Object} Ligne formatée
     */
    formater() {
      return {
        id: this.id,
        compteNumero: this.compteNumero,
        libelle: this.libelle,
        debit: parseFloat(this.debit || 0),
        credit: parseFloat(this.credit || 0),
        montant: this.getMontant(),
        sens: this.getSens(),
        reference: this.reference,
        tiersId: this.tiersId,
        tiersNom: this.tiersNom,
        dateEcheance: this.dateEcheance,
        modeReglement: this.modeReglement,
        sectionAnalytique: this.sectionAnalytique,
        numeroPiece: this.numeroPiece,
        typePiece: this.typePiece,
        lettrage: this.lettrage,
        rapproche: this.rapproche,
        pointage: this.pointage
      };
    }

    /**
     * Vérifie si la ligne est lettrée
     * @returns {boolean} True si lettrée
     */
    estLettree() {
      return this.lettrage !== null && this.lettrage !== '';
    }

    /**
     * Vérifie si la ligne est rapprochée
     * @returns {boolean} True si rapprochée
     */
    estRapprochee() {
      return this.rapproche === true;
    }

    /**
     * Vérifie si la ligne est pointée
     * @returns {boolean} True si pointée
     */
    estPointee() {
      return this.pointage === true;
    }

    /**
     * Vérifie si la ligne a une échéance
     * @returns {boolean} True si échéance définie
     */
    aEcheance() {
      return this.dateEcheance !== null;
    }

    /**
     * Vérifie si la ligne est échue
     * @returns {boolean} True si échue
     */
    estEchue() {
      if (!this.dateEcheance) return false;
      return new Date(this.dateEcheance) < new Date();
    }

    /**
     * Calcule le nombre de jours jusqu'à l'échéance
     * @returns {number|null} Nombre de jours (négatif si échu)
     */
    joursAvantEcheance() {
      if (!this.dateEcheance) return null;
      const aujourd = new Date();
      const echeance = new Date(this.dateEcheance);
      const diffTime = echeance - aujourd;
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    /**
     * Obtient le montant en devise d'origine
     * @returns {number} Montant en devise
     */
    getMontantDevise() {
      if (this.montantDevise) {
        return parseFloat(this.montantDevise);
      }
      return this.getMontant();
    }

    /**
     * Valide les données de la ligne selon SYSCOHADA
     */
    static validateSyscohadaLigneData(donnees) {
      const errors = [];

      if (!donnees.compteNumero) {
        errors.push('Le compte comptable est obligatoire');
      }

      if (!donnees.libelle || donnees.libelle.trim().length < 2) {
        errors.push('Le libellé doit contenir au moins 2 caractères');
      }

      const debit = parseFloat(donnees.debit || 0);
      const credit = parseFloat(donnees.credit || 0);

      if (debit < 0 || credit < 0) {
        errors.push('Les montants débit et crédit doivent être positifs');
      }

      if (debit > 0 && credit > 0) {
        errors.push('Une ligne ne peut pas avoir à la fois un débit et un crédit');
      }

      if (debit === 0 && credit === 0) {
        errors.push('Une ligne doit avoir soit un débit soit un crédit');
      }

      if (donnees.quantite && donnees.quantite < 0) {
        errors.push('La quantité ne peut pas être négative');
      }

      if (donnees.prixUnitaire && donnees.prixUnitaire < 0) {
        errors.push('Le prix unitaire ne peut pas être négatif');
      }

      if (donnees.cours && donnees.cours <= 0) {
        errors.push('Le cours de change doit être positif');
      }

      return {
        valide: errors.length === 0,
        erreurs: errors
      };
    }
  }

  LigneEcriture.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    ecritureId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'ecriture_comptables',
        key: 'id'
      }
    },
    compteNumero: {
      type: DataTypes.STRING(10),
      allowNull: false,
      references: {
        model: 'compte_comptables',
        key: 'numero'
      }
    },
    libelle: {
      type: DataTypes.STRING(200),
      allowNull: true
    },
    montantDebit: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0.00,
      field: 'debit'
    },
    montantCredit: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0.00,
      field: 'credit'
    },
    lettrage: {
      type: DataTypes.STRING(10),
      allowNull: true
    },
    dateLettrage: {
      type: DataTypes.DATE,
      allowNull: true
    },
    utilisateurLettrage: {
      type: DataTypes.UUID,
      allowNull: true
    },
    tiersId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'parties',
        key: 'id'
      }
    },
    tiersNom: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    // Nouveaux champs SYSCOHADA
    dateEcheance: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'date_echeance'
    },
    modeReglement: {
      type: DataTypes.ENUM('ESPECES', 'CHEQUE', 'VIREMENT', 'CARTE_BANCAIRE', 'TRAITE', 'BILLET_ORDRE', 'COMPENSATION', 'AUTRE'),
      allowNull: true,
      field: 'mode_reglement'
    },
    sectionAnalytique: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'section_analytique'
    },
    quantite: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    unite: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    prixUnitaire: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true,
      field: 'prix_unitaire'
    },
    devise: {
      type: DataTypes.STRING(3),
      allowNull: true
    },
    cours: {
      type: DataTypes.DECIMAL(10, 6),
      allowNull: true
    },
    montantDevise: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      field: 'montant_devise'
    },
    numeroPiece: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'numero_piece'
    },
    typePiece: {
      type: DataTypes.ENUM('FACTURE', 'AVOIR', 'RECU', 'CHEQUE', 'VIREMENT', 'ESPECES', 'AUTRE'),
      allowNull: true,
      field: 'type_piece'
    },
    rapproche: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    dateRapprochement: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'date_rapprochement'
    },
    pointage: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    datePointage: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'date_pointage'
    },
    userPointage: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'user_pointage'
    }
  }, {
    sequelize,
    modelName: 'LigneEcriture',
    tableName: 'ligne_ecritures',
    timestamps: true,
    underscored: true
  });

  return LigneEcriture;
};