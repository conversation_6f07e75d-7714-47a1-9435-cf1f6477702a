'use strict';

const { Op } = require('sequelize');
const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  ConflictError 
} = require('../middleware/errorHandler');

/**
 * Service pour la gestion des clôtures d'exercices comptables
 */
class ClotureService {
  constructor(models) {
    this.models = models;
  }

  /**
   * Vérifie la complétude des saisies pour un exercice
   * @param {string} exerciceId - ID de l'exercice à vérifier
   * @returns {Object} Résultat de la vérification
   */
  async verifierCompletudeSaisies(exerciceId) {
    try {
      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId, {
        include: [{ model: this.models.Societe, as: 'societe' }]
      });

      if (!exercice) {
        throw new ValidationError('Exercice comptable non trouvé');
      }

      const resultat = {
        exerciceId,
        societe: exercice.societe.nom,
        exercice: exercice.libelle,
        complet: true,
        anomalies: [],
        statistiques: {}
      };

      // 1. Vérifier les écritures en brouillard
      const ecrituresBrouillard = await this.models.EcritureComptable.count({
        where: {
          exerciceId: exercice.id,
          statut: 'BROUILLARD'
        }
      });

      if (ecrituresBrouillard > 0) {
        resultat.complet = false;
        resultat.anomalies.push({
          type: 'BROUILLARD',
          message: `Il reste ${ecrituresBrouillard} écriture(s) en brouillard`,
          count: ecrituresBrouillard
        });
      }

      // 2. Vérifier l'équilibre des écritures
      const ecrituresDesequilibrees = await this.verifierEquilibreEcritures(exercice.id);
      if (ecrituresDesequilibrees.length > 0) {
        resultat.complet = false;
        resultat.anomalies.push({
          type: 'DESEQUILIBRE',
          message: `${ecrituresDesequilibrees.length} écriture(s) déséquilibrée(s)`,
          ecritures: ecrituresDesequilibrees
        });
      }

      // 3. Vérifier les comptes clients/fournisseurs non lettrés
      const comptesALettrer = await this.getComptesNonLettres(exercice.id);
      if (comptesALettrer.length > 0) {
        resultat.anomalies.push({
          type: 'NON_LETTRE',
          message: `${comptesALettrer.length} compte(s) avec des écritures non lettrées`,
          comptes: comptesALettrer
        });
      }

      // 4. Vérifier les comptes de TVA
      const anomaliesTVA = await this.verifierComptesTVA(exercice.id);
      if (anomaliesTVA.length > 0) {
        resultat.anomalies.push({
          type: 'TVA',
          message: `${anomaliesTVA.length} anomalie(s) sur les comptes de TVA`,
          details: anomaliesTVA
        });
      }

      // 5. Statistiques de l'exercice
      resultat.statistiques = await this.getStatistiquesExercice(exercice.id);

      logger.info('Vérification complétude saisies', {
        exerciceId,
        complet: resultat.complet,
        anomaliesCount: resultat.anomalies.length
      });

      return resultat;

    } catch (error) {
      logger.error('Erreur lors de la vérification de complétude des saisies', {
        exerciceId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Génère les écritures de clôture pour un exercice
   * @param {string} exerciceId - ID de l'exercice
   * @param {string} utilisateurId - ID de l'utilisateur
   * @returns {Object} Résultat de la génération
   */
  async genererEcrituresCloture(exerciceId, utilisateurId) {
    const transaction = await this.models.sequelize.transaction();

    try {
      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId, {
        include: [{ model: this.models.Societe, as: 'societe' }],
        transaction
      });

      if (!exercice) {
        throw new ValidationError('Exercice comptable non trouvé');
      }

      if (exercice.statut !== 'OUVERT') {
        throw new ValidationError(`L'exercice est déjà ${exercice.statut.toLowerCase()}`);
      }

      // 1. Calculer le résultat de l'exercice
      const resultatExercice = await this.calculerResultatExercice(exerciceId, transaction);

      // 2. Créer l'écriture de clôture des comptes de charges et produits
      const ecritureCloture = await this.creerEcritureCloture(
        exercice, 
        resultatExercice, 
        utilisateurId, 
        transaction
      );

      // 3. Créer l'écriture de détermination du résultat
      const ecritureResultat = await this.creerEcritureResultat(
        exercice, 
        resultatExercice, 
        utilisateurId, 
        transaction
      );

      await transaction.commit();

      logger.info('Écritures de clôture générées avec succès', {
        exerciceId,
        utilisateurId,
        resultatExercice,
        ecritureCloture: ecritureCloture.id,
        ecritureResultat: ecritureResultat.id
      });

      return {
        success: true,
        resultatExercice,
        ecritures: [
          await ecritureCloture.getResume(),
          await ecritureResultat.getResume()
        ]
      };

    } catch (error) {
      await transaction.rollback();
      
      logger.error('Erreur lors de la génération des écritures de clôture', {
        exerciceId,
        utilisateurId,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Calcule le résultat de l'exercice
   * @param {string} exerciceId - ID de l'exercice
   * @param {Transaction} transaction - Transaction Sequelize (optionnelle)
   * @returns {number} Résultat de l'exercice
   */
  async calculerResultatExercice(exerciceId, transaction = null) {
    try {
      // Calcul du résultat de l'exercice (Produits - Charges)
      const options = transaction ? { transaction } : {};
      
      const resultats = await this.models.LigneEcriture.findAll({
        attributes: [
          [this.models.sequelize.fn('SUM', 
            this.models.sequelize.literal(`
              CASE 
                WHEN LEFT(compte_numero, 1) IN ('6', '8') THEN montant_debit - montant_credit
                WHEN LEFT(compte_numero, 1) = '7' THEN montant_credit - montant_debit
                ELSE 0
              END
            `)
          ), 'resultatExercice']
        ],
        include: [{
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: { 
            exerciceId,
            statut: { [Op.in]: ['VALIDE', 'BROUILLARD'] }
          },
          attributes: []
        }],
        raw: true,
        ...options
      });

      const resultatExercice = parseFloat(resultats[0]?.resultatExercice || 0);
      
      logger.info('Calcul résultat exercice', {
        exerciceId,
        resultatExercice
      });

      return resultatExercice;

    } catch (error) {
      logger.error('Erreur calcul résultat exercice', {
        exerciceId,
        error: error.message
      });
      throw new AppError('Erreur lors du calcul du résultat de l\'exercice');
    }
  }

  /**
   * Clôture un exercice comptable
   * @param {string} exerciceId - ID de l'exercice à clôturer
   * @param {string} utilisateurId - ID de l'utilisateur qui clôture
   * @param {Object} options - Options de clôture
   * @returns {Object} Résultat de la clôture
   */
  async cloturerExercice(exerciceId, utilisateurId, options = {}) {
    const transaction = await this.models.sequelize.transaction();
    const { commentaire = null, genererEcritures = true } = options;

    try {
      // 1. Vérifier les conditions
      const conditions = await this.verifierConditionsCloture(exerciceId);
      if (!conditions.canClose) {
        throw new ValidationError('Impossible de clôturer l\'exercice', {
          errors: conditions.errors
        });
      }

      // 2. Récupérer l'exercice
      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId, {
        include: [{ model: this.models.Societe, as: 'societe' }],
        transaction
      });

      // 3. Générer les écritures de clôture si demandé
      if (genererEcritures) {
        await this.genererEcrituresCloture(exerciceId, utilisateurId);
      }

      // 4. Calculer le report à nouveau
      const reportANouveau = await this.calculerResultatExercice(exerciceId, transaction);

      // 5. Mettre à jour l'exercice
      await exercice.update({
        statut: 'CLOTURE',
        dateCloture: new Date(),
        utilisateurCloture: utilisateurId,
        commentaireCloture: commentaire,
        reportANouveau
      }, { transaction });

      // 6. Verrouiller toutes les écritures de l'exercice
      await this.models.EcritureComptable.update(
        { statut: 'VALIDE' },
        {
          where: {
            exerciceId,
            statut: 'BROUILLARD'
          },
          transaction
        }
      );

      await transaction.commit();

      logger.info('Exercice clôturé avec succès', {
        exerciceId,
        utilisateurId,
        reportANouveau,
        statistiques: conditions.statistics
      });

      return {
        success: true,
        exercice: await this.models.ExerciceComptable.findByPk(exerciceId),
        reportANouveau,
        statistiques: conditions.statistics
      };

    } catch (error) {
      await transaction.rollback();
      
      logger.error('Erreur lors de la clôture d\'exercice', {
        exerciceId,
        utilisateurId,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Génère les écritures d'à-nouveaux pour l'exercice suivant
   * @param {string} exerciceId - ID de l'exercice clôturé
   * @param {string} nouvelExerciceId - ID du nouvel exercice
   * @param {string} utilisateurId - ID de l'utilisateur
   * @returns {Object} Résultat de la génération
   */
  async genererANouveaux(exerciceId, nouvelExerciceId, utilisateurId) {
    const transaction = await this.models.sequelize.transaction();

    try {
      // 1. Vérifier les exercices
      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId, {
        transaction
      });

      if (!exercice) {
        throw new ValidationError('Exercice comptable source non trouvé');
      }

      if (exercice.statut !== 'CLOTURE') {
        throw new ValidationError('L\'exercice source doit être clôturé');
      }

      const nouvelExercice = await this.models.ExerciceComptable.findByPk(nouvelExerciceId, {
        include: [{ model: this.models.Societe, as: 'societe' }],
        transaction
      });

      if (!nouvelExercice) {
        throw new ValidationError('Nouvel exercice comptable non trouvé');
      }

      if (nouvelExercice.statut !== 'OUVERT') {
        throw new ValidationError('Le nouvel exercice doit être ouvert');
      }

      if (nouvelExercice.societeId !== exercice.societeId) {
        throw new ValidationError('Les deux exercices doivent appartenir à la même société');
      }

      // 2. Récupérer les soldes des comptes de bilan
      const soldesBilan = await this.getSoldesComptesBilan(exerciceId, transaction);

      // 3. Créer l'écriture d'à-nouveaux
      const journal = await this.models.Journal.findOne({
        where: { 
          societeId: nouvelExercice.societeId,
          type: 'OD'
        },
        transaction
      });

      if (!journal) {
        throw new ValidationError('Journal des opérations diverses non trouvé');
      }

      // Générer le numéro d'écriture
      const numeroEcriture = `${journal.code}-AN-${new Date().getFullYear()}`;

      // Créer l'écriture d'à-nouveaux
      const ecritureANouveaux = await this.models.EcritureComptable.create({
        numeroEcriture,
        dateEcriture: nouvelExercice.dateDebut,
        journalCode: journal.code,
        libelle: `À-nouveaux au ${nouvelExercice.dateDebut}`,
        reference: `AN-${exercice.libelle}`,
        exerciceId: nouvelExerciceId,
        statut: 'VALIDE',
        dateValidation: new Date(),
        utilisateurCreation: utilisateurId,
        utilisateurValidation: utilisateurId,
        societeId: nouvelExercice.societeId
      }, { transaction });

      // 4. Créer les lignes d'écriture pour chaque compte de bilan
      const lignesCreees = [];
      
      for (const solde of soldesBilan) {
        if (Math.abs(solde.solde) < 0.01) continue; // Ignorer les soldes nuls
        
        const compte = await this.models.CompteComptable.findOne({
          where: { numero: solde.compteNumero },
          transaction
        });
        
        // Déterminer si le compte est au débit ou au crédit
        const estAuDebit = (compte.sens === 'DEBIT' && solde.solde > 0) || 
                          (compte.sens === 'CREDIT' && solde.solde < 0);
        
        const montantDebit = estAuDebit ? Math.abs(solde.solde) : 0;
        const montantCredit = !estAuDebit ? Math.abs(solde.solde) : 0;
        
        const ligne = await this.models.LigneEcriture.create({
          ecritureId: ecritureANouveaux.id,
          compteNumero: solde.compteNumero,
          libelle: `À-nouveau ${compte.libelle}`,
          montantDebit,
          montantCredit
        }, { transaction });
        
        lignesCreees.push(ligne);
      }

      // 5. Mettre à jour la relation entre les exercices
      await nouvelExercice.update({
        exercicePrecedentId: exerciceId
      }, { transaction });

      await transaction.commit();

      logger.info('Écritures d\'à-nouveaux générées avec succès', {
        exerciceId,
        nouvelExerciceId,
        utilisateurId,
        ecritureId: ecritureANouveaux.id,
        nombreLignes: lignesCreees.length
      });

      return {
        success: true,
        ecritureANouveaux: await ecritureANouveaux.getResume(),
        nombreComptes: lignesCreees.length
      };

    } catch (error) {
      await transaction.rollback();
      
      logger.error('Erreur lors de la génération des à-nouveaux', {
        exerciceId,
        nouvelExerciceId,
        utilisateurId,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Vérifie les conditions préalables à la clôture d'un exercice
   * @param {string} exerciceId - ID de l'exercice à clôturer
   * @returns {Object} Résultat de la vérification
   */
  async verifierConditionsCloture(exerciceId) {
    try {
      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId, {
        include: [
          {
            model: this.models.Societe,
            as: 'societe'
          }
        ]
      });

      if (!exercice) {
        throw new ValidationError('Exercice comptable non trouvé');
      }

      const conditions = {
        exerciceId,
        societe: exercice.societe.nom,
        exercice: exercice.libelle,
        canClose: true,
        errors: [],
        warnings: [],
        statistics: {}
      };

      // 1. Vérifier le statut de l'exercice
      if (exercice.statut !== 'OUVERT') {
        conditions.canClose = false;
        conditions.errors.push(`L'exercice est déjà ${exercice.statut.toLowerCase()}`);
      }

      // 2. Vérifier que la date de fin est dépassée
      const maintenant = new Date();
      const dateFin = new Date(exercice.dateFin);
      if (maintenant < dateFin) {
        conditions.canClose = false;
        conditions.errors.push('La date de fin d\'exercice n\'est pas encore atteinte');
      }

      // 3. Vérifier les écritures en brouillard
      const ecrituresBrouillard = await this.models.EcritureComptable.count({
        where: {
          exerciceId: exercice.id,
          statut: 'BROUILLARD'
        }
      });

      if (ecrituresBrouillard > 0) {
        conditions.canClose = false;
        conditions.errors.push(`Il reste ${ecrituresBrouillard} écriture(s) en brouillard`);
      }

      // 4. Vérifier l'équilibre des écritures
      const ecrituresDesequilibrees = await this.verifierEquilibreEcritures(exercice.id);
      if (ecrituresDesequilibrees.length > 0) {
        conditions.canClose = false;
        conditions.errors.push(`${ecrituresDesequilibrees.length} écriture(s) déséquilibrée(s)`);
      }

      // 5. Statistiques de l'exercice
      conditions.statistics = await this.getStatistiquesExercice(exercice.id);

      // 6. Avertissements (n'empêchent pas la clôture)
      if (conditions.statistics.totalEcritures === 0) {
        conditions.warnings.push('Aucune écriture comptable dans cet exercice');
      }

      if (conditions.statistics.comptesSansEcriture > 0) {
        conditions.warnings.push(`${conditions.statistics.comptesSansEcriture} compte(s) sans écriture`);
      }

      logger.info('Vérification conditions clôture', {
        exerciceId,
        canClose: conditions.canClose,
        errorsCount: conditions.errors.length,
        warningsCount: conditions.warnings.length
      });

      return conditions;

    } catch (error) {
      logger.error('Erreur lors de la vérification des conditions de clôture', {
        exerciceId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Vérifie l'équilibre de toutes les écritures d'un exercice
   * @param {string} exerciceId - ID de l'exercice
   * @returns {Array} Liste des écritures déséquilibrées
   */
  async verifierEquilibreEcritures(exerciceId) {
    const ecrituresDesequilibrees = [];

    const ecritures = await this.models.EcritureComptable.findAll({
      where: { exerciceId },
      include: [
        {
          model: this.models.LigneEcriture,
          as: 'lignes'
        }
      ]
    });

    for (const ecriture of ecritures) {
      const totalDebit = ecriture.lignes.reduce((sum, ligne) => 
        sum + parseFloat(ligne.montantDebit || 0), 0
      );
      const totalCredit = ecriture.lignes.reduce((sum, ligne) => 
        sum + parseFloat(ligne.montantCredit || 0), 0
      );

      if (Math.abs(totalDebit - totalCredit) > 0.01) {
        ecrituresDesequilibrees.push({
          id: ecriture.id,
          numeroEcriture: ecriture.numeroEcriture,
          totalDebit,
          totalCredit,
          difference: totalDebit - totalCredit
        });
      }
    }

    return ecrituresDesequilibrees;
  }

  /**
   * Calcule les statistiques d'un exercice
   * @param {string} exerciceId - ID de l'exercice
   * @returns {Object} Statistiques de l'exercice
   */
  async getStatistiquesExercice(exerciceId) {
    const [
      totalEcritures,
      totalLignes,
      ecrituresValidees,
      comptesSansEcriture
    ] = await Promise.all([
      // Total des écritures
      this.models.EcritureComptable.count({
        where: { exerciceId }
      }),
      
      // Total des lignes d'écriture
      this.models.LigneEcriture.count({
        include: [{
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: { exerciceId }
        }]
      }),
      
      // Écritures validées
      this.models.EcritureComptable.count({
        where: { 
          exerciceId,
          statut: 'VALIDE'
        }
      }),
      
      // Comptes sans écriture (approximation)
      this.models.CompteComptable.count({
        where: {
          numero: {
            [Op.notIn]: this.models.sequelize.literal(`(
              SELECT DISTINCT compte_numero
              FROM ligne_ecritures le
              JOIN ecriture_comptables ec ON le.ecriture_id = ec.id
              WHERE ec.exercice_id = '${exerciceId}'
            )`)
          }
        }
      })
    ]);

    // Calcul des montants totaux
    const montantsTotaux = await this.models.LigneEcriture.findAll({
      attributes: [
        [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_debit')), 'totalDebit'],
        [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_credit')), 'totalCredit']
      ],
      include: [{
        model: this.models.EcritureComptable,
        as: 'ecriture',
        where: { exerciceId },
        attributes: []
      }],
      raw: true
    });

    return {
      totalEcritures,
      totalLignes,
      ecrituresValidees,
      ecrituresBrouillard: totalEcritures - ecrituresValidees,
      comptesSansEcriture,
      totalDebit: parseFloat(montantsTotaux[0]?.totalDebit || 0),
      totalCredit: parseFloat(montantsTotaux[0]?.totalCredit || 0)
    };
  }

  /**
   * Calcule le report à nouveau pour l'exercice suivant
   * @param {string} exerciceId - ID de l'exercice à clôturer
   * @returns {number} Montant du report à nouveau
   */
  async calculerReportANouveau(exerciceId) {
    try {
      // Calcul du résultat de l'exercice (Produits - Charges)
      const resultats = await this.models.LigneEcriture.findAll({
        attributes: [
          [this.models.sequelize.fn('SUM', 
            this.models.sequelize.literal(`
              CASE 
                WHEN LEFT(compte_numero, 1) IN ('6', '8') THEN montant_debit - montant_credit
                WHEN LEFT(compte_numero, 1) = '7' THEN montant_credit - montant_debit
                ELSE 0
              END
            `)
          ), 'resultatExercice']
        ],
        include: [{
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: { exerciceId },
          attributes: []
        }],
        raw: true
      });

      const resultatExercice = parseFloat(resultats[0]?.resultatExercice || 0);
      
      logger.info('Calcul report à nouveau', {
        exerciceId,
        resultatExercice
      });

      return resultatExercice;

    } catch (error) {
      logger.error('Erreur calcul report à nouveau', {
        exerciceId,
        error: error.message
      });
      throw new AppError('Erreur lors du calcul du report à nouveau');
    }
  }

  /**
   * Récupère les comptes avec des écritures non lettrées
   * @param {string} exerciceId - ID de l'exercice
   * @returns {Array} Liste des comptes non lettrés
   */
  async getComptesNonLettres(exerciceId) {
    const comptesALettrer = await this.models.CompteComptable.findAll({
      where: {
        obligatoireLettrage: true
      },
      include: [{
        model: this.models.LigneEcriture,
        as: 'lignesEcriture',
        where: {
          lettrage: null,
          [Op.or]: [
            { montantDebit: { [Op.gt]: 0 } },
            { montantCredit: { [Op.gt]: 0 } }
          ]
        },
        include: [{
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: { exerciceId }
        }],
        required: true
      }]
    });

    return comptesALettrer.map(compte => ({
      numero: compte.numero,
      libelle: compte.libelle,
      nombreLignesNonLettrees: compte.lignesEcriture.length
    }));
  }

  /**
   * Vérifie les comptes de TVA
   * @param {string} exerciceId - ID de l'exercice
   * @returns {Array} Liste des anomalies TVA
   */
  async verifierComptesTVA(exerciceId) {
    // Comptes de TVA (445xx)
    const comptesTVA = await this.models.CompteComptable.findAll({
      where: {
        numero: {
          [Op.like]: '445%'
        }
      }
    });

    const anomalies = [];

    for (const compte of comptesTVA) {
      // Calculer le solde du compte
      const solde = await this.models.LigneEcriture.findAll({
        attributes: [
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_debit')), 'totalDebit'],
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_credit')), 'totalCredit']
        ],
        where: {
          compteNumero: compte.numero
        },
        include: [{
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: { exerciceId },
          attributes: []
        }],
        raw: true
      });

      const totalDebit = parseFloat(solde[0]?.totalDebit || 0);
      const totalCredit = parseFloat(solde[0]?.totalCredit || 0);
      const soldeFinal = totalDebit - totalCredit;

      // Si le solde n'est pas nul, c'est une anomalie
      if (Math.abs(soldeFinal) > 0.01) {
        anomalies.push({
          compteNumero: compte.numero,
          libelle: compte.libelle,
          solde: soldeFinal
        });
      }
    }

    return anomalies;
  }

  /**
   * Récupère les soldes des comptes de bilan
   * @param {string} exerciceId - ID de l'exercice
   * @param {Transaction} transaction - Transaction Sequelize (optionnelle)
   * @returns {Array} Liste des soldes des comptes de bilan
   */
  async getSoldesComptesBilan(exerciceId, transaction = null) {
    const options = transaction ? { transaction } : {};
    
    // Récupérer l'exercice pour avoir la date de fin
    const exercice = await this.models.ExerciceComptable.findByPk(exerciceId, options);
    
    if (!exercice) {
      throw new ValidationError('Exercice comptable non trouvé');
    }
    
    // Récupérer tous les comptes de bilan (classes 1 à 5)
    const comptesBilan = await this.models.CompteComptable.findAll({
      where: {
        [Op.or]: [
          { numero: { [Op.like]: '1%' } },
          { numero: { [Op.like]: '2%' } },
          { numero: { [Op.like]: '3%' } },
          { numero: { [Op.like]: '4%' } },
          { numero: { [Op.like]: '5%' } }
        ]
      },
      ...options
    });
    
    const soldes = [];
    
    for (const compte of comptesBilan) {
      // Calculer le solde du compte à la date de fin de l'exercice
      const solde = await this.models.LigneEcriture.findAll({
        attributes: [
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_debit')), 'totalDebit'],
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_credit')), 'totalCredit']
        ],
        where: {
          compteNumero: compte.numero
        },
        include: [{
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: { 
            exerciceId,
            dateEcriture: { [Op.lte]: exercice.dateFin }
          },
          attributes: []
        }],
        raw: true,
        ...options
      });
      
      const totalDebit = parseFloat(solde[0]?.totalDebit || 0);
      const totalCredit = parseFloat(solde[0]?.totalCredit || 0);
      const soldeFinal = totalDebit - totalCredit;
      
      // Ne garder que les comptes avec un solde non nul
      if (Math.abs(soldeFinal) > 0.01) {
        soldes.push({
          compteNumero: compte.numero,
          libelle: compte.libelle,
          solde: soldeFinal
        });
      }
    }
    
    return soldes;
  }

  /**
   * Crée l'écriture de clôture des comptes de charges et produits
   * @param {Object} exercice - Exercice comptable
   * @param {number} resultatExercice - Résultat de l'exercice
   * @param {string} utilisateurId - ID de l'utilisateur
   * @param {Transaction} transaction - Transaction Sequelize
   * @returns {Object} Écriture créée
   */
  async creerEcritureCloture(exercice, resultatExercice, utilisateurId, transaction) {
    // Récupérer le journal des opérations diverses
    const journal = await this.models.Journal.findOne({
      where: { 
        societeId: exercice.societeId,
        type: 'OD'
      },
      transaction
    });

    if (!journal) {
      throw new ValidationError('Journal des opérations diverses non trouvé');
    }

    // Générer le numéro d'écriture
    const numeroEcriture = `${journal.code}-CLOT-${new Date().getFullYear()}`;

    // Créer l'écriture de clôture
    const ecritureCloture = await this.models.EcritureComptable.create({
      numeroEcriture,
      dateEcriture: exercice.dateFin,
      journalCode: journal.code,
      libelle: `Clôture des comptes de gestion - ${exercice.libelle}`,
      reference: `CLOT-${exercice.libelle}`,
      exerciceId: exercice.id,
      statut: 'VALIDE',
      dateValidation: new Date(),
      utilisateurCreation: utilisateurId,
      utilisateurValidation: utilisateurId,
      societeId: exercice.societeId
    }, { transaction });

    // Récupérer les soldes des comptes de charges (classe 6)
    const soldesCharges = await this.models.LigneEcriture.findAll({
      attributes: [
        'compteNumero',
        [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_debit')), 'totalDebit'],
        [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_credit')), 'totalCredit']
      ],
      where: {
        compteNumero: { [Op.like]: '6%' }
      },
      include: [{
        model: this.models.EcritureComptable,
        as: 'ecriture',
        where: { exerciceId: exercice.id },
        attributes: []
      }],
      group: ['compteNumero'],
      having: this.models.sequelize.literal('SUM(montant_debit) - SUM(montant_credit) != 0'),
      raw: true,
      transaction
    });

    // Récupérer les soldes des comptes de produits (classe 7)
    const soldesProduits = await this.models.LigneEcriture.findAll({
      attributes: [
        'compteNumero',
        [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_debit')), 'totalDebit'],
        [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_credit')), 'totalCredit']
      ],
      where: {
        compteNumero: { [Op.like]: '7%' }
      },
      include: [{
        model: this.models.EcritureComptable,
        as: 'ecriture',
        where: { exerciceId: exercice.id },
        attributes: []
      }],
      group: ['compteNumero'],
      having: this.models.sequelize.literal('SUM(montant_credit) - SUM(montant_debit) != 0'),
      raw: true,
      transaction
    });

    // Créer les lignes d'écriture pour les comptes de charges
    for (const solde of soldesCharges) {
      const compte = await this.models.CompteComptable.findOne({
        where: { numero: solde.compteNumero },
        transaction
      });
      
      const soldeCompte = parseFloat(solde.totalDebit || 0) - parseFloat(solde.totalCredit || 0);
      
      if (Math.abs(soldeCompte) > 0.01) {
        await this.models.LigneEcriture.create({
          ecritureId: ecritureCloture.id,
          compteNumero: solde.compteNumero,
          libelle: `Clôture ${compte.libelle}`,
          montantDebit: 0,
          montantCredit: soldeCompte
        }, { transaction });
      }
    }

    // Créer les lignes d'écriture pour les comptes de produits
    for (const solde of soldesProduits) {
      const compte = await this.models.CompteComptable.findOne({
        where: { numero: solde.compteNumero },
        transaction
      });
      
      const soldeCompte = parseFloat(solde.totalCredit || 0) - parseFloat(solde.totalDebit || 0);
      
      if (Math.abs(soldeCompte) > 0.01) {
        await this.models.LigneEcriture.create({
          ecritureId: ecritureCloture.id,
          compteNumero: solde.compteNumero,
          libelle: `Clôture ${compte.libelle}`,
          montantDebit: soldeCompte,
          montantCredit: 0
        }, { transaction });
      }
    }

    // Créer la ligne d'écriture pour le compte de résultat
    const compteResultat = resultatExercice >= 0 ? '131' : '129';
    
    await this.models.LigneEcriture.create({
      ecritureId: ecritureCloture.id,
      compteNumero: compteResultat,
      libelle: 'Résultat de l\'exercice',
      montantDebit: resultatExercice < 0 ? Math.abs(resultatExercice) : 0,
      montantCredit: resultatExercice > 0 ? resultatExercice : 0
    }, { transaction });

    return ecritureCloture;
  }

  /**
   * Crée l'écriture de détermination du résultat
   * @param {Object} exercice - Exercice comptable
   * @param {number} resultatExercice - Résultat de l'exercice
   * @param {string} utilisateurId - ID de l'utilisateur
   * @param {Transaction} transaction - Transaction Sequelize
   * @returns {Object} Écriture créée
   */
  async creerEcritureResultat(exercice, resultatExercice, utilisateurId, transaction) {
    // Récupérer le journal des opérations diverses
    const journal = await this.models.Journal.findOne({
      where: { 
        societeId: exercice.societeId,
        type: 'OD'
      },
      transaction
    });

    if (!journal) {
      throw new ValidationError('Journal des opérations diverses non trouvé');
    }

    // Générer le numéro d'écriture
    const numeroEcriture = `${journal.code}-RES-${new Date().getFullYear()}`;

    // Créer l'écriture de résultat
    const ecritureResultat = await this.models.EcritureComptable.create({
      numeroEcriture,
      dateEcriture: exercice.dateFin,
      journalCode: journal.code,
      libelle: `Détermination du résultat - ${exercice.libelle}`,
      reference: `RES-${exercice.libelle}`,
      exerciceId: exercice.id,
      statut: 'VALIDE',
      dateValidation: new Date(),
      utilisateurCreation: utilisateurId,
      utilisateurValidation: utilisateurId,
      societeId: exercice.societeId
    }, { transaction });

    // Créer les lignes d'écriture pour le résultat
    const compteResultat = resultatExercice >= 0 ? '131' : '129';
    const compteReportAN = '120';
    
    // Ligne pour le compte de résultat
    await this.models.LigneEcriture.create({
      ecritureId: ecritureResultat.id,
      compteNumero: compteResultat,
      libelle: 'Affectation du résultat',
      montantDebit: resultatExercice > 0 ? resultatExercice : 0,
      montantCredit: resultatExercice < 0 ? Math.abs(resultatExercice) : 0
    }, { transaction });
    
    // Ligne pour le compte de report à nouveau
    await this.models.LigneEcriture.create({
      ecritureId: ecritureResultat.id,
      compteNumero: compteReportAN,
      libelle: 'Report à nouveau',
      montantDebit: resultatExercice < 0 ? Math.abs(resultatExercice) : 0,
      montantCredit: resultatExercice > 0 ? resultatExercice : 0
    }, { transaction });

    return ecritureResultat;
  }
}

module.exports = ClotureService;
