'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Service pour la gestion des paramètres comptables
 */
class ParametreService {
  constructor(models) {
    this.models = models;
    this.cache = new Map(); // Cache simple pour les paramètres fréquents
  }

  /**
   * Obtient tous les paramètres d'une société avec cache
   * @param {string} societeId - ID de la société
   * @param {string} categorie - Catégorie optionnelle
   * @param {boolean} useCache - Utiliser le cache
   * @returns {Object} Paramètres organisés par catégorie
   */
  async getParametresSociete(societeId, categorie = null, useCache = true) {
    const cacheKey = `params_${societeId}_${categorie || 'all'}`;
    
    if (useCache && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const parametres = await this.models.ParametreComptable.getParametresSociete(societeId, categorie);
      
      if (useCache) {
        this.cache.set(cacheKey, parametres);
        // Expiration du cache après 5 minutes
        setTimeout(() => this.cache.delete(cacheKey), 5 * 60 * 1000);
      }

      return parametres;
    } catch (error) {
      logger.error('Erreur lors de la récupération des paramètres', {
        societeId,
        categorie,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Obtient la configuration TVA complète d'une société
   * @param {string} societeId - ID de la société
   * @returns {Object} Configuration TVA
   */
  async getConfigurationTVA(societeId) {
    try {
      const parametresTVA = await this.getParametresSociete(societeId, 'TVA');
      
      const configTVA = {
        tauxNormal: parametresTVA.TVA?.tva_taux_normal || 18,
        tauxReduit: parametresTVA.TVA?.tva_taux_reduit || 9,
        compteCollectee: parametresTVA.TVA?.tva_compte_collectee || '4431',
        compteDeductible: parametresTVA.TVA?.tva_compte_deductible || '4455',
        seuilAssujettissement: parametresTVA.FISCAL?.seuil_ca_tva || 50000000,
        
        // Méthodes utiles
        calculerTVA: function(montantHT, taux = null) {
          const tauxApplique = taux || this.tauxNormal;
          return Math.round(montantHT * tauxApplique / 100 * 100) / 100;
        },
        
        calculerTTC: function(montantHT, taux = null) {
          const tva = this.calculerTVA(montantHT, taux);
          return montantHT + tva;
        },
        
        calculerHT: function(montantTTC, taux = null) {
          const tauxApplique = taux || this.tauxNormal;
          return Math.round(montantTTC / (1 + tauxApplique / 100) * 100) / 100;
        }
      };

      return configTVA;
    } catch (error) {
      logger.error('Erreur lors de la récupération de la configuration TVA', {
        societeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Obtient la configuration fiscale d'une société
   * @param {string} societeId - ID de la société
   * @returns {Object} Configuration fiscale
   */
  async getConfigurationFiscale(societeId) {
    try {
      const parametresFiscaux = await this.getParametresSociete(societeId, 'FISCAL');
      
      return {
        tauxIS: parametresFiscaux.FISCAL?.is_taux || 25,
        tauxTaxeProfessionnelle: parametresFiscaux.FISCAL?.taxe_professionnelle_taux || 2,
        seuilCATVA: parametresFiscaux.FISCAL?.seuil_ca_tva || 50000000,
        
        // Calculs fiscaux
        calculerIS: function(beneficeImposable) {
          return Math.round(beneficeImposable * this.tauxIS / 100 * 100) / 100;
        },
        
        calculerTaxeProfessionnelle: function(chiffreAffaires) {
          return Math.round(chiffreAffaires * this.tauxTaxeProfessionnelle / 100 * 100) / 100;
        },
        
        estAssujettTVA: function(chiffreAffaires) {
          return chiffreAffaires >= this.seuilCATVA;
        }
      };
    } catch (error) {
      logger.error('Erreur lors de la récupération de la configuration fiscale', {
        societeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Met à jour plusieurs paramètres en lot
   * @param {string} societeId - ID de la société
   * @param {Object} parametres - Paramètres à mettre à jour
   * @param {Object} transaction - Transaction Sequelize
   * @returns {Array} Paramètres mis à jour
   */
  async mettreAJourParametres(societeId, parametres, transaction = null) {
    try {
      const parametresMisAJour = [];
      
      for (const [cle, config] of Object.entries(parametres)) {
        const { valeur, type = 'STRING', categorie = 'CUSTOM', description = null } = config;
        
        const parametre = await this.models.ParametreComptable.setParametre(
          societeId, 
          cle, 
          valeur, 
          type, 
          categorie, 
          description
        );
        
        parametresMisAJour.push(parametre);
      }

      // Invalider le cache pour cette société
      this.invaliderCacheSociete(societeId);

      logger.info('Paramètres mis à jour', {
        societeId,
        nombreParametres: parametresMisAJour.length
      });

      return parametresMisAJour;
    } catch (error) {
      logger.error('Erreur lors de la mise à jour des paramètres', {
        societeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Valide la cohérence des paramètres comptables
   * @param {string} societeId - ID de la société
   * @returns {Object} Résultat de la validation
   */
  async validerCoherenceParametres(societeId) {
    try {
      const parametres = await this.getParametresSociete(societeId, null, false);
      const erreurs = [];
      const avertissements = [];

      // Validation TVA
      if (parametres.TVA) {
        const tauxNormal = parametres.TVA.tva_taux_normal;
        const tauxReduit = parametres.TVA.tva_taux_reduit;
        
        if (tauxNormal && (tauxNormal < 0 || tauxNormal > 50)) {
          erreurs.push('Le taux de TVA normal doit être entre 0 et 50%');
        }
        
        if (tauxReduit && (tauxReduit < 0 || tauxReduit >= tauxNormal)) {
          erreurs.push('Le taux de TVA réduit doit être inférieur au taux normal');
        }

        // Vérifier que les comptes TVA existent
        const compteCollectee = parametres.TVA.tva_compte_collectee;
        const compteDeductible = parametres.TVA.tva_compte_deductible;
        
        if (compteCollectee && compteDeductible && compteCollectee === compteDeductible) {
          erreurs.push('Les comptes TVA collectée et déductible doivent être différents');
        }
      }

      // Validation fiscale
      if (parametres.FISCAL) {
        const tauxIS = parametres.FISCAL.is_taux;
        if (tauxIS && (tauxIS < 0 || tauxIS > 50)) {
          erreurs.push('Le taux d\'IS doit être entre 0 et 50%');
        }

        const seuilTVA = parametres.FISCAL.seuil_ca_tva;
        if (seuilTVA && seuilTVA < 0) {
          erreurs.push('Le seuil d\'assujettissement TVA doit être positif');
        }
      }

      // Validation comptes
      if (parametres.COMPTES) {
        const compteBenefice = parametres.COMPTES.compte_resultat_benefice;
        const comptePerte = parametres.COMPTES.compte_resultat_perte;
        
        if (compteBenefice && comptePerte && compteBenefice === comptePerte) {
          erreurs.push('Les comptes de résultat bénéfice et perte doivent être différents');
        }
      }

      // Avertissements pour paramètres manquants critiques
      const parametresCritiques = [
        'tva_taux_normal', 'tva_compte_collectee', 'tva_compte_deductible',
        'compte_resultat_benefice', 'compte_resultat_perte'
      ];

      for (const param of parametresCritiques) {
        const trouve = Object.values(parametres).some(cat => cat[param] !== undefined);
        if (!trouve) {
          avertissements.push(`Paramètre critique manquant: ${param}`);
        }
      }

      return {
        valide: erreurs.length === 0,
        erreurs,
        avertissements,
        nombreParametres: Object.values(parametres).reduce((total, cat) => total + Object.keys(cat).length, 0)
      };
    } catch (error) {
      logger.error('Erreur lors de la validation des paramètres', {
        societeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Initialise les paramètres par défaut pour une société
   * @param {string} societeId - ID de la société
   * @param {string} pays - Code pays (CI, SN, BF, etc.)
   * @returns {Array} Paramètres créés
   */
  async initialiserParametresDefaut(societeId, pays = 'CI') {
    try {
      // Vérifier si des paramètres existent déjà
      const parametresExistants = await this.models.ParametreComptable.count({
        where: { societeId }
      });

      if (parametresExistants > 0) {
        throw new ValidationError('Des paramètres existent déjà pour cette société');
      }

      // Paramètres spécifiques par pays UEMOA/CEMAC
      const parametresPays = this.getParametresPays(pays);
      
      const parametres = await this.models.ParametreComptable.creerParametresDefaut(societeId);
      
      // Appliquer les spécificités du pays
      if (parametresPays) {
        await this.mettreAJourParametres(societeId, parametresPays);
      }

      logger.info('Paramètres par défaut initialisés', {
        societeId,
        pays,
        nombreParametres: parametres.length
      });

      return parametres;
    } catch (error) {
      logger.error('Erreur lors de l\'initialisation des paramètres', {
        societeId,
        pays,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Obtient les paramètres spécifiques par pays
   * @param {string} pays - Code pays
   * @returns {Object} Paramètres spécifiques
   */
  getParametresPays(pays) {
    const parametresPays = {
      // Côte d'Ivoire
      'CI': {
        'tva_taux_normal': { valeur: '18', type: 'NUMBER', categorie: 'TVA' },
        'is_taux': { valeur: '25', type: 'NUMBER', categorie: 'FISCAL' },
        'devise_presentation': { valeur: 'XOF', type: 'STRING', categorie: 'REPORTING' }
      },
      // Sénégal
      'SN': {
        'tva_taux_normal': { valeur: '18', type: 'NUMBER', categorie: 'TVA' },
        'is_taux': { valeur: '30', type: 'NUMBER', categorie: 'FISCAL' },
        'devise_presentation': { valeur: 'XOF', type: 'STRING', categorie: 'REPORTING' }
      },
      // Burkina Faso
      'BF': {
        'tva_taux_normal': { valeur: '18', type: 'NUMBER', categorie: 'TVA' },
        'is_taux': { valeur: '27.5', type: 'NUMBER', categorie: 'FISCAL' },
        'devise_presentation': { valeur: 'XOF', type: 'STRING', categorie: 'REPORTING' }
      },
      // Cameroun (CEMAC)
      'CM': {
        'tva_taux_normal': { valeur: '19.25', type: 'NUMBER', categorie: 'TVA' },
        'is_taux': { valeur: '30', type: 'NUMBER', categorie: 'FISCAL' },
        'devise_presentation': { valeur: 'XAF', type: 'STRING', categorie: 'REPORTING' }
      }
    };

    return parametresPays[pays] || parametresPays['CI']; // Défaut Côte d'Ivoire
  }

  /**
   * Invalide le cache pour une société
   * @param {string} societeId - ID de la société
   */
  invaliderCacheSociete(societeId) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.startsWith(`params_${societeId}_`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Vide tout le cache
   */
  viderCache() {
    this.cache.clear();
  }
}

module.exports = ParametreService;
