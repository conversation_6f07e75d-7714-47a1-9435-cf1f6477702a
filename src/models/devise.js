'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Devise extends Model {
    /**
     * Associations du modèle Devise
     */
    static associate(models) {
      // Une devise peut avoir plusieurs taux de change (source)
      Devise.hasMany(models.TauxChange, {
        foreignKey: 'deviseSource',
        as: 'tauxSource'
      });

      // Une devise peut avoir plusieurs taux de change (cible)
      Devise.hasMany(models.TauxChange, {
        foreignKey: 'deviseCible',
        as: 'tauxCible'
      });

      // Une devise peut être utilisée par plusieurs sociétés
      Devise.hasMany(models.Societe, {
        foreignKey: 'devise',
        as: 'societes'
      });
    }

    /**
     * Méthodes d'instance
     */
    toJSON() {
      const values = { ...this.get() };
      return values;
    }

    /**
     * Formate un montant selon les règles de la devise
     */
    formaterMontant(montant) {
      if (montant === null || montant === undefined) return null;
      
      const montantNum = parseFloat(montant);
      if (isNaN(montantNum)) return null;

      return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: this.code === 'XOF' || this.code === 'XAF' ? 'XOF' : this.code,
        minimumFractionDigits: this.decimales,
        maximumFractionDigits: this.decimales
      }).format(montantNum);
    }

    /**
     * Arrondit un montant selon les décimales de la devise
     */
    arrondirMontant(montant) {
      if (montant === null || montant === undefined) return null;
      
      const montantNum = parseFloat(montant);
      if (isNaN(montantNum)) return null;

      const facteur = Math.pow(10, this.decimales);
      return Math.round(montantNum * facteur) / facteur;
    }

    /**
     * Vérifie si la devise est une devise UEMOA
     */
    isDeviseUEMOA() {
      return this.code === 'XOF';
    }

    /**
     * Vérifie si la devise est une devise CEMAC
     */
    isDeviseCEMAC() {
      return this.code === 'XAF';
    }

    /**
     * Vérifie si la devise est une devise CFA
     */
    isDeviseCFA() {
      return this.isDeviseUEMOA() || this.isDeviseCEMAC();
    }

    /**
     * Obtient le dernier taux de change vers une autre devise
     */
    async getDernierTauxVers(deviseCode) {
      const models = sequelize.models;
      return await models.TauxChange.findOne({
        where: {
          deviseSource: this.code,
          deviseCible: deviseCode
        },
        order: [['dateApplication', 'DESC']]
      });
    }

    /**
     * Convertit un montant vers une autre devise
     */
    async convertirVers(montant, deviseCode, dateTaux = null) {
      if (this.code === deviseCode) {
        return this.arrondirMontant(montant);
      }

      const models = sequelize.models;
      const whereClause = {
        deviseSource: this.code,
        deviseCible: deviseCode
      };

      if (dateTaux) {
        whereClause.dateApplication = { [sequelize.Op.lte]: dateTaux };
      }

      const tauxChange = await models.TauxChange.findOne({
        where: whereClause,
        order: [['dateApplication', 'DESC']]
      });

      if (!tauxChange) {
        throw new Error(`Aucun taux de change trouvé de ${this.code} vers ${deviseCode}`);
      }

      const montantConverti = parseFloat(montant) * parseFloat(tauxChange.taux);
      
      // Arrondir selon les décimales de la devise cible
      const deviseCible = await models.Devise.findByPk(deviseCode);
      if (deviseCible) {
        return deviseCible.arrondirMontant(montantConverti);
      }

      return Math.round(montantConverti * 100) / 100; // Défaut 2 décimales
    }

    /**
     * Méthodes statiques
     */
    static getDevisesUEMOA() {
      return ['XOF']; // Franc CFA UEMOA
    }

    static getDevisesCEMAC() {
      return ['XAF']; // Franc CFA CEMAC
    }

    static getDevisesCFA() {
      return [...Devise.getDevisesUEMOA(), ...Devise.getDevisesCEMAC()];
    }

    static getDevisesInternational() {
      return ['EUR', 'USD', 'GBP', 'JPY', 'CHF'];
    }

    /**
     * Crée les devises par défaut
     */
    static async creerDevisesDefaut() {
      const devisesDefaut = [
        {
          code: 'XOF',
          libelle: 'Franc CFA UEMOA',
          symbole: 'FCFA',
          decimales: 0,
          actif: true
        },
        {
          code: 'XAF',
          libelle: 'Franc CFA CEMAC',
          symbole: 'FCFA',
          decimales: 0,
          actif: true
        },
        {
          code: 'EUR',
          libelle: 'Euro',
          symbole: '€',
          decimales: 2,
          actif: true
        },
        {
          code: 'USD',
          libelle: 'Dollar américain',
          symbole: '$',
          decimales: 2,
          actif: true
        },
        {
          code: 'GBP',
          libelle: 'Livre sterling',
          symbole: '£',
          decimales: 2,
          actif: true
        }
      ];

      const devisesCreees = [];
      for (const deviseData of devisesDefaut) {
        const [devise, created] = await Devise.findOrCreate({
          where: { code: deviseData.code },
          defaults: deviseData
        });
        if (created) {
          devisesCreees.push(devise);
        }
      }

      return devisesCreees;
    }

    /**
     * Obtient la devise par défaut selon le pays
     */
    static getDeviseDefautPays(pays) {
      const devisesPays = {
        // UEMOA
        'CI': 'XOF', 'SN': 'XOF', 'BF': 'XOF', 'ML': 'XOF', 
        'NE': 'XOF', 'TG': 'XOF', 'BJ': 'XOF', 'GW': 'XOF',
        
        // CEMAC
        'CM': 'XAF', 'CF': 'XAF', 'TD': 'XAF', 
        'CG': 'XAF', 'GA': 'XAF', 'GQ': 'XAF'
      };

      return devisesPays[pays] || 'XOF'; // Défaut UEMOA
    }
  }

  Devise.init({
    code: {
      type: DataTypes.STRING(3),
      primaryKey: true,
      validate: {
        isUppercase: true,
        len: [3, 3],
        isAlpha: true
      }
    },
    libelle: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    symbole: {
      type: DataTypes.STRING(10),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 10]
      }
    },
    decimales: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 2,
      validate: {
        min: 0,
        max: 4
      }
    },
    actif: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    }
  }, {
    sequelize,
    modelName: 'Devise',
    tableName: 'devises',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['actif']
      },
      {
        fields: ['libelle']
      }
    ]
  });

  return Devise;
};
