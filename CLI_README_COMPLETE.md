# 🖥️ **CLI SYSCOHADA - Guide d'utilisation complet**

## 📋 **Vue d'ensemble**

Le CLI SYSCOHADA est un outil en ligne de commande puissant qui permet d'interagir avec l'API de comptabilité SYSCOHADA. Il offre une interface intuitive pour gérer tous les aspects de votre comptabilité directement depuis le terminal.

## Installation

### Prérequis

- Node.js (v18 ou supérieur)
- npm (v8 ou supérieur)

### Installation locale

```bash
# Installer les dépendances
npm install --save-dev

# Installer l'outil globalement
npm install -g .
```

### Installation depuis le fichier cli-package.json

```bash
# Créer un nouveau dossier pour l'outil CLI
mkdir syscohada-cli
cd syscohada-cli

# Copier le fichier cli.js et cli-package.json
cp /chemin/vers/api-compta-generale/cli.js .
cp /chemin/vers/api-compta-generale/cli-package.json ./package.json

# Installer les dépendances
npm install

# Installer l'outil globalement
npm install -g .
```

## Configuration

Avant d'utiliser l'outil, vous devez configurer l'URL de l'API:

```bash
syscohada config
```

Par défaut, l'URL est `http://localhost:3000/api/v1`.

## Authentification par Clé API

L'API utilise maintenant un système de clés API au lieu des tokens JWT. Pour utiliser la plupart des commandes, vous devez configurer une clé API:

```bash
syscohada auth:setup
```

Vous serez invité à saisir votre clé API. La clé doit commencer par `sk_` et vous devez l'obtenir d'un administrateur ou la générer si vous avez les permissions admin.

## Commandes disponibles

### Configuration et authentification

- `syscohada config` - Configurer l'URL de l'API
- `syscohada auth:setup` - Configurer la clé API
- `syscohada auth:verify` - Vérifier la validité de la clé API
- `syscohada auth:clear` - Supprimer la clé API configurée
- `syscohada status` - Vérifier le statut de l'API

### Gestion des clés API (Admin uniquement)

- `syscohada apikeys:list [options]` - Lister toutes les clés API
  - `-i, --include-inactive` - Inclure les clés inactives
  - `-l, --limit <number>` - Nombre de clés à afficher
- `syscohada apikeys:create` - Créer une nouvelle clé API
- `syscohada apikeys:deactivate <prefix>` - Désactiver une clé API
- `syscohada apikeys:delete <prefix>` - Supprimer une clé API

### Gestion des sociétés

- `syscohada societes:list` - Lister toutes les sociétés
- `syscohada societes:create` - Créer une nouvelle société
- `syscohada societes:select` - Sélectionner une société comme contexte courant

### Gestion des exercices comptables

- `syscohada exercices:list` - Lister les exercices comptables
- `syscohada exercices:create` - Créer un nouvel exercice comptable

### Gestion des écritures comptables

- `syscohada ecritures:list [options]` - Lister les écritures comptables
  - `-j, --journal <code>` - Filtrer par code journal
  - `-d, --debut <date>` - Date de début (YYYY-MM-DD)
  - `-f, --fin <date>` - Date de fin (YYYY-MM-DD)
  - `-s, --statut <statut>` - Filtrer par statut (BROUILLARD, VALIDEE, CLOTUREE)
  - `-p, --page <number>` - Numéro de page
  - `-l, --limit <number>` - Nombre d'éléments par page
- `syscohada ecritures:create` - Créer une nouvelle écriture comptable
- `syscohada ecritures:show <id>` - Afficher les détails d'une écriture
- `syscohada ecritures:valider <id>` - Valider une écriture comptable

### Gestion des comptes

- `syscohada comptes:list [options]` - Lister les comptes du plan comptable
  - `-c, --classe <classe>` - Filtrer par classe (1-9)
  - `-p, --page <number>` - Numéro de page
  - `-l, --limit <number>` - Nombre d'éléments par page

### Gestion des journaux

- `syscohada journaux:list [options]` - Lister tous les journaux comptables
  - `-t, --type <type>` - Filtrer par type de journal
  - `-a, --actifs-seulement` - Afficher seulement les journaux actifs
- `syscohada journaux:create` - Créer un nouveau journal comptable

### Gestion des modèles d'écritures (Templates)

- `syscohada templates:list [options]` - Lister les modèles d'écritures
  - `-c, --categorie <categorie>` - Filtrer par catégorie
  - `-p, --publics` - Afficher seulement les templates publics
- `syscohada templates:create` - Créer un nouveau modèle d'écriture

### Lettrage

- `syscohada lettrage:comptes` - Lister les comptes avec des écritures non lettrées
- `syscohada lettrage:auto <compteNumero>` - Effectuer un lettrage automatique
  - `-t, --tolerance <montant>` - Tolérance de montant

### Gestion des tiers

- `syscohada tiers:list [options]` - Lister tous les tiers
  - `-t, --type <type>` - Filtrer par type (CLIENT, FOURNISSEUR, AUTRE)
  - `-a, --actifs` - Afficher seulement les tiers actifs
- `syscohada tiers:create` - Créer un nouveau tiers

### Import/Export

- `syscohada import:ecritures <fichier>` - Importer des écritures depuis un fichier Excel
  - `-j, --journal <code>` - Code du journal par défaut
  - `-v, --valider` - Valider automatiquement les écritures importées
- `syscohada export:ecritures [options]` - Exporter les écritures vers un fichier Excel
  - `-j, --journal <code>` - Filtrer par journal
  - `-d, --debut <date>` - Date de début (YYYY-MM-DD)
  - `-f, --fin <date>` - Date de fin (YYYY-MM-DD)
  - `-o, --output <fichier>` - Nom du fichier de sortie

### Rapports

- `syscohada rapports:generer` - Générer un rapport comptable
  - Types disponibles: balance, grand-livre, journal, balance-agee, compte-resultat, bilan
  - Formats: PDF, Excel, JSON

### Paramètres

- `syscohada parametres:list` - Lister les paramètres de la société
- `syscohada parametres:set <cle> <valeur>` - Définir un paramètre
  - `-d, --description <desc>` - Description du paramètre

### Plan comptable

- `syscohada plan:personnalise [options]` - Afficher le plan comptable personnalisé
  - `-c, --classe <classe>` - Filtrer par classe (1-8)
- `syscohada plan:personnaliser <numero>` - Personnaliser un compte du plan comptable

### États financiers

- `syscohada etats:generer` - Générer un état financier

### Analyses

- `syscohada analyses:generer` - Générer une analyse comptable

### Tableau de bord

- `syscohada dashboard` - Afficher le tableau de bord de la société

### Amortissements

- `syscohada amortissements:list` - Lister les amortissements
- `syscohada amortissements:create` - Créer un nouvel amortissement
- `syscohada amortissements:plan <id>` - Afficher le plan d'amortissement
- `syscohada amortissements:calculer-dotations` - Calculer les dotations
- `syscohada amortissements:generer-ecritures` - Générer les écritures d'amortissement

### Clôture d'exercice

- `syscohada exercices:valider-cloture <id>` - Valider la clôture d'un exercice
- `syscohada exercices:simuler-cloture <id>` - Simuler la clôture d'un exercice
- `syscohada exercices:cloturer <id>` - Clôturer un exercice
- `syscohada exercices:rouvrir <id>` - Rouvrir un exercice clôturé
- `syscohada exercices:statut-cloture <id>` - Vérifier le statut de clôture
- `syscohada exercices:vue-clotures` - Vue d'ensemble des clôtures

## Exemples d'utilisation

### Configuration initiale

```bash
# Configurer l'API
syscohada config
# URL: http://localhost:3000/api/v1

# Configurer la clé API
syscohada auth:setup
# Clé API: sk_your_api_key_here

# Vérifier la configuration
syscohada status
```

### Workflow typique

```bash
# 1. Sélectionner une société
syscohada societes:select

# 2. Lister les exercices
syscohada exercices:list

# 3. Créer des journaux si nécessaire
syscohada journaux:create

# 4. Créer des écritures
syscohada ecritures:create

# 5. Consulter les écritures
syscohada ecritures:list

# 6. Effectuer le lettrage
syscohada lettrage:comptes
syscohada lettrage:auto 411000

# 7. Générer des états
syscohada etats:generer
```

### Gestion des écritures

```bash
# Lister les écritures du journal VT pour janvier 2024
syscohada ecritures:list -j VT -d 2024-01-01 -f 2024-01-31

# Afficher une écriture spécifique
syscohada ecritures:show 12345678-1234-1234-1234-123456789012

# Valider une écriture
syscohada ecritures:valider 12345678-1234-1234-1234-123456789012
```

### Gestion des journaux

```bash
# Lister tous les journaux
syscohada journaux:list

# Lister seulement les journaux actifs
syscohada journaux:list --actifs-seulement

# Créer un nouveau journal
syscohada journaux:create
```

### Gestion des templates

```bash
# Lister les templates
syscohada templates:list

# Lister les templates de vente
syscohada templates:list -c VENTE

# Créer un nouveau template
syscohada templates:create
```

### Import/Export

```bash
# Importer des écritures depuis un fichier Excel
syscohada import:ecritures ./ecritures.xlsx -j VT -v

# Exporter les écritures vers Excel
syscohada export:ecritures -j VT -d 2024-01-01 -f 2024-01-31 -o export_janvier.xlsx
```

### Lettrage

```bash
# Voir les comptes à lettrer
syscohada lettrage:comptes

# Lettrage automatique du compte client 411000
syscohada lettrage:auto 411000

# Lettrage avec tolérance de 0.01
syscohada lettrage:auto 411000 -t 0.01
```

### Rapports

```bash
# Générer une balance générale en PDF
syscohada rapports:generer
# Sélectionner: Balance générale, dates, format PDF

# Générer un grand livre en Excel
syscohada rapports:generer
# Sélectionner: Grand livre, dates, format Excel
```

### Gestion des tiers

```bash
# Lister tous les tiers
syscohada tiers:list

# Lister seulement les clients actifs
syscohada tiers:list -t CLIENT -a

# Créer un nouveau tiers
syscohada tiers:create
```

### Paramètres

```bash
# Lister les paramètres
syscohada parametres:list

# Définir un paramètre
syscohada parametres:set "devise_defaut" "XOF" -d "Devise par défaut de la société"
```

### Plan comptable

```bash
# Voir le plan comptable personnalisé
syscohada plan:personnalise

# Voir seulement la classe 4
syscohada plan:personnalise -c 4

# Personnaliser un compte
syscohada plan:personnaliser 411000
```

### Amortissements

```bash
# Lister les amortissements
syscohada amortissements:list

# Calculer les dotations pour décembre 2024
syscohada amortissements:calculer-dotations

# Générer les écritures d'amortissement
syscohada amortissements:generer-ecritures
```

## Configuration

Le CLI stocke sa configuration dans `~/.syscohada-cli/config.json`:

```json
{
  "apiUrl": "http://localhost:3000/api/v1",
  "apiKey": "sk_your_api_key_here",
  "currentSociete": {
    "id": "12345678-1234-1234-1234-123456789012",
    "nom": "Ma Société"
  }
}
```

## Dépannage

### Erreur "Clé API invalide"

1. Vérifiez que votre clé API est correcte avec `syscohada auth:verify`
2. Si nécessaire, reconfigurez avec `syscohada auth:setup`

### Erreur "API inaccessible"

1. Vérifiez que l'API est démarrée
2. Vérifiez l'URL avec `syscohada config`
3. Testez la connexion avec `syscohada status`

### Erreur "Aucune société sélectionnée"

1. Listez les sociétés disponibles avec `syscohada societes:list`
2. Sélectionnez une société avec `syscohada societes:select`

### Erreur lors de l'import

1. Vérifiez le format du fichier Excel
2. Assurez-vous que les colonnes requises sont présentes
3. Vérifiez que le journal spécifié existe

### Erreur de lettrage

1. Vérifiez que le compte existe
2. Assurez-vous qu'il y a des écritures non lettrées sur ce compte
3. Vérifiez les permissions de votre clé API

## Fonctionnalités avancées

### Utilisation de templates pour créer des écritures

1. Créez d'abord un template avec `syscohada templates:create`
2. Utilisez le template lors de la création d'écritures avec `syscohada ecritures:create`

### Workflow de clôture d'exercice

1. Vérifiez le statut: `syscohada exercices:statut-cloture <id>`
2. Simulez la clôture: `syscohada exercices:simuler-cloture <id>`
3. Validez la clôture: `syscohada exercices:valider-cloture <id>`
4. Clôturez définitivement: `syscohada exercices:cloturer <id>`

### Automatisation avec scripts

Le CLI peut être utilisé dans des scripts bash pour automatiser des tâches:

```bash
#!/bin/bash
# Script d'import quotidien

# Importer les écritures
syscohada import:ecritures ./daily_entries.xlsx -j VT -v

# Effectuer le lettrage automatique
syscohada lettrage:auto 411000
syscohada lettrage:auto 401000

# Générer un rapport
syscohada export:ecritures -d $(date -d "yesterday" +%Y-%m-%d) -f $(date +%Y-%m-%d)
```

## Sécurité

### Bonnes pratiques

1. **Protection de la clé API**
   - Ne partagez jamais votre clé API
   - Stockez-la de manière sécurisée
   - Utilisez des clés avec les permissions minimales nécessaires

2. **Rotation des clés**
   - Changez régulièrement vos clés API
   - Désactivez immédiatement les clés compromises

3. **Monitoring**
   - Surveillez l'utilisation de vos clés
   - Vérifiez régulièrement les clés actives

### En cas de compromission

Si vous pensez que votre clé API a été compromise:

1. Contactez immédiatement un administrateur
2. La clé sera désactivée
3. Une nouvelle clé vous sera fournie

```bash
# Pour les administrateurs - désactiver une clé compromise
syscohada apikeys:deactivate sk_compromised_key
```

## Support

Pour obtenir de l'aide sur une commande spécifique:

```bash
syscohada <commande> --help
```

Pour afficher toutes les commandes disponibles:

```bash
syscohada --help
```

## Changelog

### Version 1.0.0
- Ajout de toutes les fonctionnalités de base
- Support complet de l'API SYSCOHADA
- Gestion des journaux, templates, lettrage
- Import/Export Excel
- Génération de rapports
- Gestion des tiers et paramètres
- Plan comptable personnalisable
- Amortissements et clôture d'exercice
- Interface utilisateur intuitive avec inquirer.js
- Gestion complète des erreurs et validation