'use strict';

const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/dashboardController');
const auth = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const { query, body } = require('express-validator');

/**
 * Routes pour les tableaux de bord financiers
 */

// Middleware d'authentification pour toutes les routes
router.use(auth.protect);

/**
 * @route GET /api/v1/dashboard/kpi
 * @desc Récupère les KPIs financiers
 * @access Private
 */
router.get('/kpi', [
  query('dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  query('dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  query('exerciceId').optional().isString().withMessage('L\'ID de l\'exercice doit être une chaîne'),
  validate
], dashboardController.getKPIFinanciers);

/**
 * @route POST /api/v1/dashboard/evolution
 * @desc Récupère l'évolution du chiffre d'affaires
 * @access Private
 */
router.post('/evolution', [
  body('periodes').isArray().withMessage('Les périodes doivent être un tableau'),
  body('periodes.*.dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  body('periodes.*.dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  body('periodes.*.libelle').optional().isString().withMessage('Le libellé doit être une chaîne'),
  validate
], dashboardController.getEvolutionChiffreAffaires);

/**
 * @route GET /api/v1/dashboard/analyse
 * @desc Récupère l'analyse des charges et produits
 * @access Private
 */
router.get('/analyse', [
  query('dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  query('dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  query('exerciceId').optional().isString().withMessage('L\'ID de l\'exercice doit être une chaîne'),
  query('niveauDetail').optional().isInt({ min: 1, max: 8 }).withMessage('Le niveau de détail doit être un entier entre 1 et 8'),
  validate
], dashboardController.getAnalyseChargesProduits);

/**
 * @route GET /api/v1/dashboard/ratios
 * @desc Récupère les ratios financiers
 * @access Private
 */
router.get('/ratios', [
  query('dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  query('dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  query('exerciceId').optional().isString().withMessage('L\'ID de l\'exercice doit être une chaîne'),
  validate
], dashboardController.getRatiosFinanciers);

/**
 * @route GET /api/v1/dashboard/alertes
 * @desc Récupère les alertes financières
 * @access Private
 */
router.get('/alertes', dashboardController.getAlertes);

module.exports = router;