/**
 * Routes pour la gestion des journaux comptables
 * API Comptabilité SYSCOHADA
 */

const express = require('express');
const router = express.Router();

const {
  getAllJournaux,
  getJournalByCode,
  createJournal,
  updateJournal,
  deleteJournal,
  getJournauxByType,
  getTypesJournaux,
  getJournalStats,
  // Nouvelles méthodes pour les séquences
  genererProchainNumero,
  getSequenceStats,
  configurerSequence,
  resetSequence,
  getSequenceStatsSociete,
  resetSequencesAutomatique,
  getJournauxActifs,
  toggleJournalActif
} = require('../controllers/journalController');

const { validate, schemas } = require('../middleware/validation');

/**
 * @route   GET /api/v1/journaux
 * @desc    Récupérer tous les journaux
 * @access  Public
 */
router.get('/', getAllJournaux);

/**
 * @route   GET /api/v1/journaux/types
 * @desc    Récupérer les types de journaux disponibles
 * @access  Public
 */
router.get('/types', getTypesJournaux);

/**
 * @route   GET /api/v1/journaux/type/:type
 * @desc    Récupérer les journaux par type
 * @access  Public
 */
router.get('/type/:type', getJournauxByType);

/**
 * @route   GET /api/v1/journaux/:code
 * @desc    Récupérer un journal par code
 * @access  Public
 */
router.get('/:code', getJournalByCode);

/**
 * @route   GET /api/v1/journaux/:code/stats
 * @desc    Récupérer les statistiques d'un journal
 * @access  Public
 */
router.get('/:code/stats', getJournalStats);

/**
 * @route   POST /api/v1/journaux
 * @desc    Créer un nouveau journal
 * @access  Public
 */
router.post('/',
  // validate(schemas.journal.create), // À créer si nécessaire
  createJournal
);

/**
 * @route   PUT /api/v1/journaux/:code
 * @desc    Mettre à jour un journal
 * @access  Public
 */
router.put('/:code',
  // validate(schemas.journal.update), // À créer si nécessaire
  updateJournal
);

/**
 * @route   DELETE /api/v1/journaux/:code
 * @desc    Supprimer un journal
 * @access  Public
 */
router.delete('/:code', deleteJournal);

// ===== NOUVELLES ROUTES POUR LES SÉQUENCES =====

/**
 * @route   POST /api/v1/journaux/:code/generer-numero
 * @desc    Générer le prochain numéro pour un journal
 * @access  Public
 */
router.post('/:code/generer-numero', genererProchainNumero);

/**
 * @route   GET /api/v1/journaux/:code/sequence-stats
 * @desc    Obtenir les statistiques de séquence d'un journal
 * @access  Public
 */
router.get('/:code/sequence-stats', getSequenceStats);

/**
 * @route   PUT /api/v1/journaux/:code/configurer-sequence
 * @desc    Configurer les paramètres de séquence d'un journal
 * @access  Public
 */
router.put('/:code/configurer-sequence', configurerSequence);

/**
 * @route   POST /api/v1/journaux/:code/reset-sequence
 * @desc    Réinitialiser la séquence d'un journal
 * @access  Public
 */
router.post('/:code/reset-sequence', resetSequence);

/**
 * @route   PATCH /api/v1/journaux/:code/toggle-actif
 * @desc    Activer/désactiver un journal
 * @access  Public
 */
router.patch('/:code/toggle-actif', toggleJournalActif);

/**
 * @route   GET /api/v1/journaux/societe/:societeId/actifs
 * @desc    Obtenir les journaux actifs d'une société
 * @access  Public
 */
router.get('/societe/:societeId/actifs', getJournauxActifs);

/**
 * @route   GET /api/v1/journaux/societe/:societeId/sequence-stats
 * @desc    Obtenir les statistiques de séquences pour tous les journaux d'une société
 * @access  Public
 */
router.get('/societe/:societeId/sequence-stats', getSequenceStatsSociete);

/**
 * @route   POST /api/v1/journaux/reset-sequences-automatique
 * @desc    Réinitialiser automatiquement toutes les séquences qui en ont besoin
 * @access  Public
 */
router.post('/reset-sequences-automatique', resetSequencesAutomatique);

module.exports = router;
