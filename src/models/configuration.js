'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Configuration extends Model {
    /**
     * Associations du modèle Configuration
     */
    static associate(models) {
      // Une configuration peut être liée à une société (pour config spécifique)
      Configuration.belongsTo(models.Societe, {
        foreignKey: 'societeId',
        as: 'societe'
      });
    }

    /**
     * Méthodes statiques
     */

    /**
     * Obtient une configuration par clé
     * @param {string} cle - Clé de configuration
     * @param {string} societeId - ID société (optionnel)
     * @returns {Promise<any>} Valeur de configuration
     */
    static async getConfig(cle, societeId = null) {
      const config = await this.findOne({
        where: { cle, societeId }
      });

      if (!config) {
        // Chercher une config globale si pas de config société
        if (societeId) {
          return await this.getConfig(cle, null);
        }
        return null;
      }

      // Parser la valeur selon le type
      return this.parseValeur(config.valeur, config.type);
    }

    /**
     * Définit une configuration
     * @param {string} cle - Clé de configuration
     * @param {any} valeur - Valeur à stocker
     * @param {Object} options - Options (type, description, societeId, etc.)
     * @returns {Promise<Configuration>} Configuration créée/mise à jour
     */
    static async setConfig(cle, valeur, options = {}) {
      const {
        type = 'string',
        description = null,
        societeId = null,
        categorie = 'general',
        modifiable = true
      } = options;

      const valeurString = this.stringifyValeur(valeur, type);

      const [config, created] = await this.findOrCreate({
        where: { cle, societeId },
        defaults: {
          cle,
          valeur: valeurString,
          type,
          description,
          societeId,
          categorie,
          modifiable
        }
      });

      if (!created) {
        await config.update({
          valeur: valeurString,
          type,
          description,
          categorie,
          modifiable
        });
      }

      return config;
    }

    /**
     * Obtient toutes les configurations d'une catégorie
     * @param {string} categorie - Catégorie de configuration
     * @param {string} societeId - ID société (optionnel)
     * @returns {Promise<Object>} Configurations groupées par clé
     */
    static async getConfigsByCategorie(categorie, societeId = null) {
      const configs = await this.findAll({
        where: { categorie, societeId },
        order: [['cle', 'ASC']]
      });

      const result = {};
      configs.forEach(config => {
        result[config.cle] = this.parseValeur(config.valeur, config.type);
      });

      return result;
    }

    /**
     * Initialise les configurations par défaut
     * @returns {Promise<void>}
     */
    static async initialiserConfigsDefaut() {
      const configsDefaut = [
        // Configurations générales
        {
          cle: 'app_nom',
          valeur: 'API Comptabilité SYSCOHADA',
          type: 'string',
          description: 'Nom de l\'application',
          categorie: 'general'
        },
        {
          cle: 'app_version',
          valeur: '1.0.0',
          type: 'string',
          description: 'Version de l\'application',
          categorie: 'general',
          modifiable: false
        },
        {
          cle: 'devise_principale',
          valeur: 'XOF',
          type: 'string',
          description: 'Devise principale du système',
          categorie: 'comptabilite'
        },
        {
          cle: 'format_date',
          valeur: 'DD/MM/YYYY',
          type: 'string',
          description: 'Format d\'affichage des dates',
          categorie: 'affichage'
        },
        {
          cle: 'format_nombre',
          valeur: 'fr-FR',
          type: 'string',
          description: 'Format d\'affichage des nombres',
          categorie: 'affichage'
        },
        {
          cle: 'exercice_duree_mois',
          valeur: '12',
          type: 'integer',
          description: 'Durée standard d\'un exercice en mois',
          categorie: 'comptabilite'
        },
        {
          cle: 'backup_auto',
          valeur: 'true',
          type: 'boolean',
          description: 'Sauvegarde automatique activée',
          categorie: 'maintenance'
        },
        {
          cle: 'backup_frequence_heures',
          valeur: '24',
          type: 'integer',
          description: 'Fréquence de sauvegarde en heures',
          categorie: 'maintenance'
        },
        {
          cle: 'audit_retention_jours',
          valeur: '365',
          type: 'integer',
          description: 'Durée de rétention des logs d\'audit en jours',
          categorie: 'securite'
        },
        {
          cle: 'session_duree_heures',
          valeur: '24',
          type: 'integer',
          description: 'Durée de session utilisateur en heures',
          categorie: 'securite'
        }
      ];

      for (const config of configsDefaut) {
        await this.setConfig(config.cle, config.valeur, {
          type: config.type,
          description: config.description,
          categorie: config.categorie,
          modifiable: config.modifiable !== false
        });
      }
    }

    /**
     * Parse une valeur selon son type
     * @param {string} valeur - Valeur à parser
     * @param {string} type - Type de la valeur
     * @returns {any} Valeur parsée
     */
    static parseValeur(valeur, type) {
      if (valeur === null || valeur === undefined) return null;

      switch (type) {
        case 'boolean':
          return valeur === 'true' || valeur === true;
        case 'integer':
          return parseInt(valeur, 10);
        case 'float':
          return parseFloat(valeur);
        case 'json':
          try {
            return JSON.parse(valeur);
          } catch {
            return null;
          }
        case 'array':
          try {
            return JSON.parse(valeur);
          } catch {
            return [];
          }
        default:
          return valeur;
      }
    }

    /**
     * Convertit une valeur en string pour stockage
     * @param {any} valeur - Valeur à convertir
     * @param {string} type - Type de la valeur
     * @returns {string} Valeur stringifiée
     */
    static stringifyValeur(valeur, type) {
      if (valeur === null || valeur === undefined) return null;

      switch (type) {
        case 'json':
        case 'array':
          return JSON.stringify(valeur);
        default:
          return String(valeur);
      }
    }

    /**
     * Méthodes d'instance
     */

    /**
     * Obtient la valeur parsée de cette configuration
     */
    getValeurParsee() {
      return this.constructor.parseValeur(this.valeur, this.type);
    }

    /**
     * Met à jour la valeur de cette configuration
     * @param {any} nouvelleValeur - Nouvelle valeur
     */
    async updateValeur(nouvelleValeur) {
      if (!this.modifiable) {
        throw new Error('Cette configuration n\'est pas modifiable');
      }

      const valeurString = this.constructor.stringifyValeur(nouvelleValeur, this.type);
      await this.update({ valeur: valeurString });
    }
  }

  Configuration.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    cle: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 100]
      }
    },
    valeur: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    type: {
      type: DataTypes.ENUM('string', 'integer', 'float', 'boolean', 'json', 'array'),
      allowNull: false,
      defaultValue: 'string'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    categorie: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'general'
    },
    modifiable: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    societeId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'societe_id',
      references: {
        model: 'societes',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'Configuration',
    tableName: 'configurations',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['cle', 'societe_id']
      },
      {
        fields: ['categorie']
      },
      {
        fields: ['societe_id']
      }
    ]
  });

  return Configuration;
};
