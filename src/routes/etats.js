'use strict';

const express = require('express');
const router = express.Router();
const etatController = require('../controllers/etatController');
const auth = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const { query } = require('express-validator');

/**
 * Routes pour les états comptables
 */

// Middleware d'authentification pour toutes les routes
router.use(auth.protect);

/**
 * @route GET /api/v1/etats/grand-livre
 * @desc Génère le grand livre pour tous les comptes
 * @access Private
 */
router.get('/grand-livre', [
  query('dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  query('dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  query('format').optional().isIn(['excel', 'pdf']).withMessage('Le format doit être excel ou pdf'),
  query('includeNonValidees').optional().isBoolean().withMessage('includeNonValidees doit être un booléen'),
  query('detailLettrage').optional().isBoolean().withMessage('detailLettrage doit être un booléen'),
  query('triParDate').optional().isBoolean().withMessage('triParDate doit être un booléen'),
  validate
], etatController.genererGrandLivre);

/**
 * @route GET /api/v1/etats/grand-livre/:compteNumero
 * @desc Génère le grand livre pour un compte spécifique
 * @access Private
 */
router.get('/grand-livre/:compteNumero', [
  query('dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  query('dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  query('format').optional().isIn(['excel', 'pdf']).withMessage('Le format doit être excel ou pdf'),
  query('includeNonValidees').optional().isBoolean().withMessage('includeNonValidees doit être un booléen'),
  query('detailLettrage').optional().isBoolean().withMessage('detailLettrage doit être un booléen'),
  query('triParDate').optional().isBoolean().withMessage('triParDate doit être un booléen'),
  validate
], etatController.genererGrandLivre);

/**
 * @route GET /api/v1/etats/journal
 * @desc Génère le journal comptable pour tous les journaux
 * @access Private
 */
router.get('/journal', [
  query('dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  query('dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  query('format').optional().isIn(['excel', 'pdf']).withMessage('Le format doit être excel ou pdf'),
  query('includeNonValidees').optional().isBoolean().withMessage('includeNonValidees doit être un booléen'),
  query('groupeParJour').optional().isBoolean().withMessage('groupeParJour doit être un booléen'),
  validate
], etatController.genererJournal);

/**
 * @route GET /api/v1/etats/journal/:journalCode
 * @desc Génère le journal comptable pour un journal spécifique
 * @access Private
 */
router.get('/journal/:journalCode', [
  query('dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  query('dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  query('format').optional().isIn(['excel', 'pdf']).withMessage('Le format doit être excel ou pdf'),
  query('includeNonValidees').optional().isBoolean().withMessage('includeNonValidees doit être un booléen'),
  query('groupeParJour').optional().isBoolean().withMessage('groupeParJour doit être un booléen'),
  validate
], etatController.genererJournal);

/**
 * @route GET /api/v1/etats/balance
 * @desc Génère la balance générale
 * @access Private
 */
router.get('/balance', [
  query('dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  query('dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  query('format').optional().isIn(['excel', 'pdf']).withMessage('Le format doit être excel ou pdf'),
  query('niveauDetail').optional().isIn(['TOUS', 'DETAIL', 'COLLECTIF']).withMessage('niveauDetail doit être TOUS, DETAIL ou COLLECTIF'),
  query('seulementAvecMouvement').optional().isBoolean().withMessage('seulementAvecMouvement doit être un booléen'),
  query('includeNonValidees').optional().isBoolean().withMessage('includeNonValidees doit être un booléen'),
  validate
], etatController.genererBalanceGenerale);

/**
 * @route GET /api/v1/etats/balance-agee
 * @desc Génère la balance âgée (balance par antériorité)
 * @access Private
 */
router.get('/balance-agee', [
  query('dateReference').isDate().withMessage('La date de référence doit être une date valide'),
  query('format').optional().isIn(['excel', 'pdf']).withMessage('Le format doit être excel ou pdf'),
  query('compteDebut').optional().isString().withMessage('compteDebut doit être une chaîne'),
  query('compteFin').optional().isString().withMessage('compteFin doit être une chaîne'),
  query('seulementAvecSolde').optional().isBoolean().withMessage('seulementAvecSolde doit être un booléen'),
  validate
], etatController.genererBalanceAgee);

/**
 * @route GET /api/v1/etats/centralisateur
 * @desc Génère le centralisateur des journaux
 * @access Private
 */
router.get('/centralisateur', [
  query('dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  query('dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  query('format').optional().isIn(['excel', 'pdf']).withMessage('Le format doit être excel ou pdf'),
  query('includeNonValidees').optional().isBoolean().withMessage('includeNonValidees doit être un booléen'),
  query('groupeParMois').optional().isBoolean().withMessage('groupeParMois doit être un booléen'),
  validate
], etatController.genererCentralisateur);

module.exports = router;