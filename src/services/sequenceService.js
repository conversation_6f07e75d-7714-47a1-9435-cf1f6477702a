'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Service pour la gestion des séquences de numérotation automatique
 * Gère la génération de numéros uniques avec verrouillage concurrentiel
 */
class SequenceService {
  constructor(models) {
    this.models = models;
  }

  /**
   * Génère le prochain numéro pour un journal donné
   * Utilise un verrouillage pour éviter les conflits concurrentiels
   * @param {string} journalCode - Code du journal
   * @param {Object} transaction - Transaction Sequelize (optionnelle)
   * @returns {Promise<string>} Numéro formaté généré
   */
  async genererProchainNumero(journalCode, transaction = null) {
    const t = transaction || await this.models.sequelize.transaction();
    
    try {
      // Verrouiller le journal pour éviter les conflits concurrentiels
      const journal = await this.models.Journal.findByPk(journalCode, {
        lock: true,
        transaction: t
      });

      if (!journal) {
        throw new NotFoundError('Journal');
      }

      if (!journal.actif) {
        throw new ValidationError('Le journal n\'est pas actif');
      }

      // Vérifier si la séquence doit être réinitialisée
      if (journal.needsSequenceReset()) {
        await this.resetSequence(journalCode, t);
        // Recharger le journal après reset
        await journal.reload({ transaction: t });
      }

      // Incrémenter le compteur
      const nouveauNumero = journal.dernierNumero + 1;
      const numeroFormate = journal.formaterNumero(nouveauNumero);

      // Mettre à jour le journal avec le nouveau numéro
      await journal.update({
        dernierNumero: nouveauNumero,
        numeroSequence: journal.numeroSequence + 1
      }, { transaction: t });

      // Valider la transaction si elle a été créée ici
      if (!transaction) {
        await t.commit();
      }

      logger.info('Numéro généré avec succès', {
        journalCode,
        numeroGenere: numeroFormate,
        nouveauCompteur: nouveauNumero
      });

      return numeroFormate;

    } catch (error) {
      // Annuler la transaction si elle a été créée ici
      if (!transaction) {
        await t.rollback();
      }
      
      logger.error('Erreur lors de la génération du numéro', {
        journalCode,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Réinitialise la séquence d'un journal
   * @param {string} journalCode - Code du journal
   * @param {Object} transaction - Transaction Sequelize (optionnelle)
   * @returns {Promise<Object>} Résultat du reset
   */
  async resetSequence(journalCode, transaction = null) {
    const t = transaction || await this.models.sequelize.transaction();
    
    try {
      const journal = await this.models.Journal.findByPk(journalCode, {
        lock: true,
        transaction: t
      });

      if (!journal) {
        throw new NotFoundError('Journal');
      }

      const ancienNumero = journal.dernierNumero;
      
      // Réinitialiser les compteurs
      await journal.update({
        dernierNumero: 0,
        numeroSequence: 0,
        dateDernierReset: new Date()
      }, { transaction: t });

      // Valider la transaction si elle a été créée ici
      if (!transaction) {
        await t.commit();
      }

      logger.info('Séquence réinitialisée', {
        journalCode,
        ancienNumero,
        nouveauNumero: 0,
        dateReset: new Date()
      });

      return {
        success: true,
        journalCode,
        ancienNumero,
        nouveauNumero: 0,
        dateReset: new Date()
      };

    } catch (error) {
      if (!transaction) {
        await t.rollback();
      }
      
      logger.error('Erreur lors du reset de séquence', {
        journalCode,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Obtient les statistiques de séquence pour un journal
   * @param {string} journalCode - Code du journal
   * @returns {Promise<Object>} Statistiques de la séquence
   */
  async getStatistiquesSequence(journalCode) {
    try {
      const journal = await this.models.Journal.findByPk(journalCode);
      
      if (!journal) {
        throw new NotFoundError('Journal');
      }

      const prochainNumero = journal.genererProchainNumero();
      const needsReset = journal.needsSequenceReset();

      return {
        journalCode: journal.code,
        libelle: journal.libelle,
        type: journal.type,
        dernierNumero: journal.dernierNumero,
        numeroSequence: journal.numeroSequence,
        prochainNumero,
        prefixeNumero: journal.prefixeNumero,
        longueurNumero: journal.longueurNumero,
        resetSequence: journal.resetSequence,
        dateDernierReset: journal.dateDernierReset,
        needsReset,
        actif: journal.actif
      };

    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques', {
        journalCode,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Obtient les statistiques de tous les journaux d'une société
   * @param {string} societeId - ID de la société
   * @returns {Promise<Array>} Statistiques de tous les journaux
   */
  async getStatistiquesSequencesSociete(societeId) {
    try {
      const journaux = await this.models.Journal.findAll({
        where: { societeId, actif: true },
        order: [['code', 'ASC']]
      });

      const statistiques = await Promise.all(
        journaux.map(async (journal) => {
          const prochainNumero = journal.genererProchainNumero();
          const needsReset = journal.needsSequenceReset();

          return {
            journalCode: journal.code,
            libelle: journal.libelle,
            type: journal.type,
            dernierNumero: journal.dernierNumero,
            numeroSequence: journal.numeroSequence,
            prochainNumero,
            needsReset,
            resetSequence: journal.resetSequence,
            dateDernierReset: journal.dateDernierReset
          };
        })
      );

      return {
        societeId,
        nombreJournaux: journaux.length,
        journaux: statistiques,
        journauxNeedingReset: statistiques.filter(j => j.needsReset).length
      };

    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques société', {
        societeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Configure les paramètres de séquence d'un journal
   * @param {string} journalCode - Code du journal
   * @param {Object} params - Nouveaux paramètres
   * @returns {Promise<Object>} Journal mis à jour
   */
  async configurerSequence(journalCode, params) {
    const transaction = await this.models.sequelize.transaction();
    
    try {
      const journal = await this.models.Journal.findByPk(journalCode, {
        lock: true,
        transaction
      });

      if (!journal) {
        throw new NotFoundError('Journal');
      }

      // Valider les paramètres
      const validation = this.models.Journal.validateSequenceParams(params);
      if (!validation.valide) {
        throw new ValidationError(validation.erreurs.join(', '));
      }

      // Mettre à jour les paramètres
      const champsAMettreAJour = {};
      if (params.prefixeNumero !== undefined) champsAMettreAJour.prefixeNumero = params.prefixeNumero;
      if (params.longueurNumero !== undefined) champsAMettreAJour.longueurNumero = params.longueurNumero;
      if (params.resetSequence !== undefined) champsAMettreAJour.resetSequence = params.resetSequence;

      await journal.update(champsAMettreAJour, { transaction });

      await transaction.commit();

      logger.info('Configuration de séquence mise à jour', {
        journalCode,
        nouveauxParametres: champsAMettreAJour
      });

      return await journal.reload();

    } catch (error) {
      await transaction.rollback();
      
      logger.error('Erreur lors de la configuration de séquence', {
        journalCode,
        params,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Réinitialise toutes les séquences qui en ont besoin
   * @param {string} societeId - ID de la société (optionnel)
   * @returns {Promise<Object>} Résultat des resets
   */
  async resetSequencesAutomatique(societeId = null) {
    const transaction = await this.models.sequelize.transaction();
    
    try {
      const whereClause = { actif: true };
      if (societeId) whereClause.societeId = societeId;

      const journaux = await this.models.Journal.findAll({
        where: whereClause,
        transaction
      });

      const resets = [];
      
      for (const journal of journaux) {
        if (journal.needsSequenceReset()) {
          const resultat = await this.resetSequence(journal.code, transaction);
          resets.push(resultat);
        }
      }

      await transaction.commit();

      logger.info('Reset automatique des séquences terminé', {
        societeId,
        nombreResets: resets.length,
        journauxReset: resets.map(r => r.journalCode)
      });

      return {
        success: true,
        nombreResets: resets.length,
        resets
      };

    } catch (error) {
      await transaction.rollback();
      
      logger.error('Erreur lors du reset automatique', {
        societeId,
        error: error.message
      });
      
      throw error;
    }
  }
}

module.exports = SequenceService;
