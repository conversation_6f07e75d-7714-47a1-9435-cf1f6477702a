'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Vérifier si la colonne date_lettrage existe déjà
    const tableDescription = await queryInterface.describeTable('ligne_ecritures');

    // Ajouter seulement le champ utilisateur_lettrage s'il n'existe pas
    if (!tableDescription.utilisateur_lettrage) {
      await queryInterface.addColumn('ligne_ecritures', 'utilisateur_lettrage', {
        type: Sequelize.UUID,
        allowNull: true,
        comment: 'ID de l\'utilisateur qui a effectué le lettrage'
      });
    }

    // Ajouter des index pour optimiser les requêtes de lettrage (seulement s'ils n'existent pas)
    try {
      await queryInterface.addIndex('ligne_ecritures', ['date_lettrage'], {
        name: 'idx_ligne_date_lettrage'
      });
    } catch (error) {
      console.log('Index date_lettrage existe déjà');
    }

    try {
      await queryInterface.addIndex('ligne_ecritures', ['utilisateur_lettrage'], {
        name: 'idx_ligne_utilisateur_lettrage'
      });
    } catch (error) {
      console.log('Index utilisateur_lettrage existe déjà');
    }

    try {
      await queryInterface.addIndex('ligne_ecritures', ['lettrage', 'compte_numero'], {
        name: 'idx_ligne_lettrage_compte'
      });
    } catch (error) {
      console.log('Index lettrage_compte existe déjà');
    }
  },

  async down(queryInterface, Sequelize) {
    // Supprimer les index
    await queryInterface.removeIndex('ligne_ecritures', ['date_lettrage']);
    await queryInterface.removeIndex('ligne_ecritures', ['utilisateur_lettrage']);
    await queryInterface.removeIndex('ligne_ecritures', ['lettrage', 'compte_numero']);

    // Supprimer les colonnes
    await queryInterface.removeColumn('ligne_ecritures', 'date_lettrage');
    await queryInterface.removeColumn('ligne_ecritures', 'utilisateur_lettrage');
  }
};
