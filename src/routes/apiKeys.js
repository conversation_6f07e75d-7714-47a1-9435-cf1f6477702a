/**
 * Routes pour la gestion des clés API
 * API Comptabilité SYSCOHADA
 */

const express = require('express');
const router = express.Router();

const {
  createApi<PERSON>ey,
  listApiKeys,
  getApiKey,
  updateApiKey,
  deleteApiKey,
  deactivateApiKey,
  cleanupExpiredKeys,
  verifyCurrentApiKey
} = require('../controllers/apiKeyController');

const { protect, requirePermission } = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const Joi = require('joi');

// Schémas de validation
const createApiKeySchema = Joi.object({
  name: Joi.string().min(3).max(100).required().messages({
    'string.min': 'Le nom doit contenir au moins 3 caractères',
    'string.max': 'Le nom ne peut pas dépasser 100 caractères',
    'any.required': 'Le nom est requis'
  }),
  permissions: Joi.array().items(
    Joi.string().valid('read', 'write', 'admin')
  ).default(['read']).messages({
    'array.includes': 'Permissions valides: read, write, admin'
  }),
  expiresAt: Joi.date().iso().greater('now').optional().messages({
    'date.greater': 'La date d\'expiration doit être dans le futur'
  }),
  metadata: Joi.object().optional()
});

const updateApiKeySchema = Joi.object({
  name: Joi.string().min(3).max(100).optional(),
  permissions: Joi.array().items(
    Joi.string().valid('read', 'write', 'admin')
  ).optional(),
  expiresAt: Joi.date().iso().allow(null).optional(),
  isActive: Joi.boolean().optional(),
  metadata: Joi.object().optional()
});

const listApiKeysSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  includeInactive: Joi.boolean().default(false)
});

/**
 * @route   GET /api/v1/api-keys/verify
 * @desc    Vérifier la clé API actuelle
 * @access  Private
 */
router.get('/verify', 
  protect,
  verifyCurrentApiKey
);

/**
 * @route   POST /api/v1/api-keys
 * @desc    Créer une nouvelle clé API
 * @access  Admin
 */
router.post('/', 
  protect,
  requirePermission('admin'),
  validate(createApiKeySchema),
  createApiKey
);

/**
 * @route   GET /api/v1/api-keys
 * @desc    Lister les clés API
 * @access  Admin
 */
router.get('/', 
  protect,
  requirePermission('admin'),
  validate(listApiKeysSchema, 'query'),
  listApiKeys
);

/**
 * @route   GET /api/v1/api-keys/:id
 * @desc    Récupérer une clé API par ID
 * @access  Admin
 */
router.get('/:id', 
  protect,
  requirePermission('admin'),
  getApiKey
);

/**
 * @route   PUT /api/v1/api-keys/:id
 * @desc    Mettre à jour une clé API
 * @access  Admin
 */
router.put('/:id', 
  protect,
  requirePermission('admin'),
  validate(updateApiKeySchema),
  updateApiKey
);

/**
 * @route   DELETE /api/v1/api-keys/:id
 * @desc    Supprimer une clé API
 * @access  Admin
 */
router.delete('/:id', 
  protect,
  requirePermission('admin'),
  deleteApiKey
);

/**
 * @route   POST /api/v1/api-keys/:id/deactivate
 * @desc    Désactiver une clé API
 * @access  Admin
 */
router.post('/:id/deactivate', 
  protect,
  requirePermission('admin'),
  deactivateApiKey
);

/**
 * @route   POST /api/v1/api-keys/cleanup
 * @desc    Nettoyer les clés expirées
 * @access  Admin
 */
router.post('/cleanup', 
  protect,
  requirePermission('admin'),
  cleanupExpiredKeys
);

module.exports = router;