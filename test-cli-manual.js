#!/usr/bin/env node

/**
 * Script de test manuel du CLI SYSCOHADA
 * Guide pas à pas pour tester toutes les fonctionnalités
 */

const chalk = require('chalk');
const fs = require('fs');
const path = require('path');

const CLI_PATH = path.join(__dirname, 'cli.js');

console.log(chalk.blue('🧪 Guide de Test Manuel CLI SYSCOHADA\n'));

const tests = [
  {
    category: 'Configuration de base',
    commands: [
      {
        cmd: `node ${CLI_PATH} --help`,
        desc: 'Afficher l\'aide générale',
        expected: 'Liste des commandes disponibles'
      },
      {
        cmd: `node ${CLI_PATH} --version`,
        desc: 'Afficher la version',
        expected: 'Version 1.0.0'
      },
      {
        cmd: `node ${CLI_PATH} config`,
        desc: 'Configurer l\'URL de l\'API',
        expected: 'Prompt pour l\'URL',
        interactive: true,
        inputs: ['http://localhost:3000/api/v1']
      },
      {
        cmd: `node ${CLI_PATH} status`,
        desc: 'Vérifier le statut de l\'API',
        expected: 'Statut API ou erreur de connexion'
      }
    ]
  },
  {
    category: 'Authentification',
    commands: [
      {
        cmd: `node ${CLI_PATH} auth:setup`,
        desc: 'Configurer la clé API',
        expected: 'Prompt pour la clé API',
        interactive: true,
        note: 'Utiliser une vraie clé API commençant par sk_'
      },
      {
        cmd: `node ${CLI_PATH} auth:verify`,
        desc: 'Vérifier la clé API',
        expected: 'Informations de la clé API'
      },
      {
        cmd: `node ${CLI_PATH} auth:clear`,
        desc: 'Supprimer la clé API',
        expected: 'Confirmation de suppression'
      }
    ]
  },
  {
    category: 'Gestion des clés API (Admin)',
    commands: [
      {
        cmd: `node ${CLI_PATH} apikeys:list`,
        desc: 'Lister les clés API',
        expected: 'Liste des clés ou erreur de permission',
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} apikeys:list --include-inactive --limit 5`,
        desc: 'Lister avec options',
        expected: 'Liste filtrée des clés',
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} apikeys:create`,
        desc: 'Créer une clé API',
        expected: 'Prompt de création ou erreur de permission',
        interactive: true,
        requiresAuth: true
      }
    ]
  },
  {
    category: 'Gestion des sociétés',
    commands: [
      {
        cmd: `node ${CLI_PATH} societes:list`,
        desc: 'Lister les sociétés',
        expected: 'Liste des sociétés',
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} societes:create`,
        desc: 'Créer une société',
        expected: 'Prompt de création',
        interactive: true,
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} societes:select`,
        desc: 'Sélectionner une société',
        expected: 'Liste pour sélection',
        interactive: true,
        requiresAuth: true
      }
    ]
  },
  {
    category: 'Gestion des exercices',
    commands: [
      {
        cmd: `node ${CLI_PATH} exercices:list`,
        desc: 'Lister les exercices',
        expected: 'Liste des exercices ou message si aucun',
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} exercices:create`,
        desc: 'Créer un exercice',
        expected: 'Prompt de création',
        interactive: true,
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} exercices:vue-clotures`,
        desc: 'Vue des clôtures',
        expected: 'État des clôtures',
        requiresAuth: true
      }
    ]
  },
  {
    category: 'Plan comptable et comptes',
    commands: [
      {
        cmd: `node ${CLI_PATH} comptes:list`,
        desc: 'Lister les comptes',
        expected: 'Plan comptable',
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} comptes:list --classe 1`,
        desc: 'Lister par classe',
        expected: 'Comptes de la classe 1',
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} plan:personnalise`,
        desc: 'Plan comptable personnalisé',
        expected: 'Plan avec personnalisations',
        requiresAuth: true
      }
    ]
  },
  {
    category: 'Journaux et écritures',
    commands: [
      {
        cmd: `node ${CLI_PATH} journaux:list`,
        desc: 'Lister les journaux',
        expected: 'Liste des journaux',
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} ecritures:list`,
        desc: 'Lister les écritures',
        expected: 'Liste des écritures',
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} ecritures:list --statut BROUILLARD`,
        desc: 'Écritures en brouillard',
        expected: 'Écritures non validées',
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} templates:list`,
        desc: 'Lister les templates',
        expected: 'Templates d\'écriture',
        requiresAuth: true
      }
    ]
  },
  {
    category: 'États et rapports',
    commands: [
      {
        cmd: `node ${CLI_PATH} etats:generer`,
        desc: 'Générer un état',
        expected: 'Sélection du type d\'état',
        interactive: true,
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} dashboard`,
        desc: 'Tableau de bord',
        expected: 'Indicateurs comptables',
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} analyses:generer`,
        desc: 'Générer une analyse',
        expected: 'Sélection du type d\'analyse',
        interactive: true,
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} rapports:generer`,
        desc: 'Générer un rapport',
        expected: 'Sélection du rapport',
        interactive: true,
        requiresAuth: true
      }
    ]
  },
  {
    category: 'Amortissements',
    commands: [
      {
        cmd: `node ${CLI_PATH} amortissements:list`,
        desc: 'Lister les amortissements',
        expected: 'Liste des amortissements',
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} amortissements:calculer-dotations`,
        desc: 'Calculer les dotations',
        expected: 'Calcul des dotations',
        requiresAuth: true
      }
    ]
  },
  {
    category: 'Tiers et lettrage',
    commands: [
      {
        cmd: `node ${CLI_PATH} tiers:list`,
        desc: 'Lister les tiers',
        expected: 'Liste des tiers',
        requiresAuth: true
      },
      {
        cmd: `node ${CLI_PATH} lettrage:comptes`,
        desc: 'Lettrage des comptes',
        expected: 'État du lettrage',
        requiresAuth: true
      }
    ]
  },
  {
    category: 'Import/Export',
    commands: [
      {
        cmd: `node ${CLI_PATH} export:ecritures`,
        desc: 'Exporter les écritures',
        expected: 'Sélection du format d\'export',
        interactive: true,
        requiresAuth: true
      }
    ]
  },
  {
    category: 'Paramètres',
    commands: [
      {
        cmd: `node ${CLI_PATH} parametres:list`,
        desc: 'Lister les paramètres',
        expected: 'Liste des paramètres',
        requiresAuth: true
      }
    ]
  }
];

console.log(chalk.yellow('📋 INSTRUCTIONS:'));
console.log('1. Assurez-vous que l\'API est démarrée (npm run dev)');
console.log('2. Ayez une clé API valide prête');
console.log('3. Exécutez chaque commande et vérifiez le résultat\n');

tests.forEach((category, categoryIndex) => {
  console.log(chalk.blue(`\n=== ${categoryIndex + 1}. ${category.category.toUpperCase()} ===\n`));
  
  category.commands.forEach((test, testIndex) => {
    console.log(chalk.cyan(`${categoryIndex + 1}.${testIndex + 1} ${test.desc}`));
    console.log(chalk.white(`Commande: ${test.cmd}`));
    console.log(chalk.green(`Attendu: ${test.expected}`));
    
    if (test.requiresAuth) {
      console.log(chalk.yellow(`⚠️  Nécessite une authentification`));
    }
    
    if (test.interactive) {
      console.log(chalk.magenta(`🔄 Commande interactive`));
      if (test.inputs) {
        console.log(chalk.gray(`   Réponses suggérées: ${test.inputs.join(', ')}`));
      }
    }
    
    if (test.note) {
      console.log(chalk.gray(`📝 Note: ${test.note}`));
    }
    
    console.log(''); // Ligne vide
  });
});

console.log(chalk.red('\n=== TESTS DE GESTION D\'ERREURS ===\n'));
console.log('❌ Commande inexistante:');
console.log(`node ${CLI_PATH} commande-inexistante`);
console.log('Attendu: Message d\'erreur ou aide\n');

console.log('❌ Commande sans authentification:');
console.log(`node ${CLI_PATH} auth:clear`);
console.log(`node ${CLI_PATH} societes:list`);
console.log('Attendu: Message d\'erreur demandant l\'authentification\n');

// Créer un fichier de résultats vide
const resultsFile = path.join(__dirname, 'cli-manual-test-results.md');
const template = `# Résultats des Tests Manuels CLI SYSCOHADA

Date: ${new Date().toISOString()}

## Instructions
- ✅ pour un test réussi
- ❌ pour un test échoué  
- ⚠️ pour un test partiellement réussi
- 📝 pour des notes

## Résultats

${tests.map((category, categoryIndex) => 
  `### ${categoryIndex + 1}. ${category.category}

${category.commands.map((test, testIndex) => 
  `#### ${categoryIndex + 1}.${testIndex + 1} ${test.desc}
**Commande:** \`${test.cmd}\`
**Attendu:** ${test.expected}
**Résultat:** [ ] ✅ / [ ] ❌ / [ ] ⚠️
**Notes:** 

---`).join('\n\n')}`).join('\n\n')}

## Tests d'erreurs

### Commande inexistante
**Résultat:** [ ] ✅ / [ ] ❌
**Notes:** 

### Commande sans authentification  
**Résultat:** [ ] ✅ / [ ] ❌
**Notes:**

## Résumé
- Tests réussis: __/__
- Tests échoués: __/__
- Taux de réussite: __%

## Notes générales

`;

fs.writeFileSync(resultsFile, template);
console.log(chalk.green(`📄 Template de résultats créé: ${resultsFile}`));
console.log(chalk.blue('\n🚀 Vous pouvez maintenant commencer les tests manuels!'));