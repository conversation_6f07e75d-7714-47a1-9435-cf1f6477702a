'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Créer la table template_ecritures
    await queryInterface.createTable('template_ecritures', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      nom: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      libelle: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'Libellé du template avec paramètres variables {{param}}'
      },
      journal_code: {
        type: Sequelize.STRING(10),
        allowNull: false,
        references: {
          model: 'journaux',
          key: 'code'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      categorie: {
        type: Sequelize.ENUM(
          'VENTE', 'ACHAT', 'BANQUE', 'CAISSE', 'PAIE', 
          'AMORTISSEMENT', 'PROVISION', 'AUTRE'
        ),
        allowNull: false,
        defaultValue: 'AUTRE'
      },
      societe_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'societes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      actif: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      public: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'Template partageable entre utilisateurs'
      },
      utilisateur_creation: {
        type: Sequelize.UUID,
        allowNull: true,
        comment: 'ID de l\'utilisateur créateur'
      },
      tags_recherche: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Tags pour faciliter la recherche'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Créer la table template_ligne_ecritures
    await queryInterface.createTable('template_ligne_ecritures', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      template_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'template_ecritures',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      compte_numero: {
        type: Sequelize.STRING(10),
        allowNull: false,
        references: {
          model: 'compte_comptables',
          key: 'numero'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      libelle: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'Libellé avec paramètres variables {{param}}'
      },
      sens: {
        type: Sequelize.ENUM('DEBIT', 'CREDIT', 'VARIABLE'),
        allowNull: false,
        comment: 'VARIABLE permet de déterminer le sens selon les paramètres'
      },
      montant_fixe: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        comment: 'Montant fixe si pas de formule'
      },
      formule_montant: {
        type: Sequelize.STRING(500),
        allowNull: true,
        comment: 'Formule de calcul avec paramètres {{param}} + - * /'
      },
      ordre: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: 'Ordre d\'affichage dans le template'
      },
      obligatoire: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Ligne obligatoire dans l\'écriture générée'
      },
      commentaire: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Commentaire pour l\'utilisateur'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Créer les index pour template_ecritures
    await queryInterface.addIndex('template_ecritures', {
      fields: ['societe_id', 'actif'],
      name: 'idx_template_ecritures_societe_actif'
    });

    await queryInterface.addIndex('template_ecritures', {
      fields: ['categorie'],
      name: 'idx_template_ecritures_categorie'
    });

    await queryInterface.addIndex('template_ecritures', {
      fields: ['journal_code'],
      name: 'idx_template_ecritures_journal'
    });

    // Index pour la recherche textuelle sur le nom
    await queryInterface.addIndex('template_ecritures', {
      fields: ['nom'],
      name: 'idx_template_ecritures_nom'
    });

    // Créer les index pour template_ligne_ecritures
    await queryInterface.addIndex('template_ligne_ecritures', {
      fields: ['template_id', 'ordre'],
      name: 'idx_template_lignes_template_ordre'
    });

    await queryInterface.addIndex('template_ligne_ecritures', {
      fields: ['compte_numero'],
      name: 'idx_template_lignes_compte'
    });

    // Contraintes de validation
    await queryInterface.addConstraint('template_ligne_ecritures', {
      fields: ['montant_fixe'],
      type: 'check',
      name: 'check_montant_fixe_positive',
      where: {
        montant_fixe: {
          [Sequelize.Op.gte]: 0
        }
      }
    });

    await queryInterface.addConstraint('template_ligne_ecritures', {
      fields: ['ordre'],
      type: 'check',
      name: 'check_ordre_positive',
      where: {
        ordre: {
          [Sequelize.Op.gt]: 0
        }
      }
    });
  },

  async down(queryInterface, Sequelize) {
    // Supprimer les contraintes
    await queryInterface.removeConstraint('template_ligne_ecritures', 'check_montant_fixe_positive');
    await queryInterface.removeConstraint('template_ligne_ecritures', 'check_ordre_positive');

    // Supprimer les index
    await queryInterface.removeIndex('template_ligne_ecritures', 'idx_template_lignes_compte');
    await queryInterface.removeIndex('template_ligne_ecritures', 'idx_template_lignes_template_ordre');
    await queryInterface.removeIndex('template_ecritures', 'idx_template_ecritures_nom');
    await queryInterface.removeIndex('template_ecritures', 'idx_template_ecritures_journal');
    await queryInterface.removeIndex('template_ecritures', 'idx_template_ecritures_categorie');
    await queryInterface.removeIndex('template_ecritures', 'idx_template_ecritures_societe_actif');

    // Supprimer les tables
    await queryInterface.dropTable('template_ligne_ecritures');
    await queryInterface.dropTable('template_ecritures');
  }
};
