'use strict';

const express = require('express');
const router = express.Router();
const importExportController = require('../controllers/importExportController');
const { uploadWithErrorHandling, cleanupTempFile } = require('../middleware/upload');
const { protect } = require('../middleware/auth');
const { body, param, query } = require('express-validator');
const validation = require('../middleware/validation');

/**
 * Routes pour l'import/export d'écritures comptables
 * Toutes les routes nécessitent une authentification
 */

// ==========================================
// ROUTES D'IMPORT
// ==========================================

/**
 * @route POST /api/v1/import/excel
 * @desc Importe des écritures depuis un fichier Excel
 * @access Private
 */
router.post('/excel',
  protect,
  uploadWithErrorHandling,
  [
    body('societeId')
      .notEmpty()
      .withMessage('L\'ID de la société est obligatoire')
      .isUUID()
      .withMessage('L\'ID de la société doit être un UUID valide'),
    body('exerciceId')
      .optional()
      .isUUID()
      .withMessage('L\'ID de l\'exercice doit être un UUID valide'),
    body('remplacerExistants')
      .optional()
      .isBoolean()
      .withMessage('remplacerExistants doit être un booléen'),
    body('validerUniquement')
      .optional()
      .isBoolean()
      .withMessage('validerUniquement doit être un booléen'),
    body('validerEcritures')
      .optional()
      .isBoolean()
      .withMessage('validerEcritures doit être un booléen')
  ],
  validation.handleValidationErrors,
  importExportController.importerExcel,
  cleanupTempFile
);

/**
 * @route POST /api/v1/import/csv
 * @desc Importe des écritures depuis un fichier CSV
 * @access Private
 */
router.post('/csv',
  protect,
  uploadWithErrorHandling,
  [
    body('societeId')
      .notEmpty()
      .withMessage('L\'ID de la société est obligatoire')
      .isUUID()
      .withMessage('L\'ID de la société doit être un UUID valide'),
    body('exerciceId')
      .optional()
      .isUUID()
      .withMessage('L\'ID de l\'exercice doit être un UUID valide'),
    body('remplacerExistants')
      .optional()
      .isBoolean()
      .withMessage('remplacerExistants doit être un booléen'),
    body('validerUniquement')
      .optional()
      .isBoolean()
      .withMessage('validerUniquement doit être un booléen'),
    body('validerEcritures')
      .optional()
      .isBoolean()
      .withMessage('validerEcritures doit être un booléen'),
    body('separateur')
      .optional()
      .isLength({ min: 1, max: 1 })
      .withMessage('Le séparateur doit être un seul caractère')
  ],
  validation.handleValidationErrors,
  importExportController.importerCSV,
  cleanupTempFile
);

// ==========================================
// ROUTES D'EXPORT
// ==========================================

/**
 * @route GET /api/v1/export/excel
 * @desc Exporte des écritures vers Excel
 * @access Private
 */
router.get('/excel',
  protect,
  [
    query('societeId')
      .optional()
      .isUUID()
      .withMessage('L\'ID de la société doit être un UUID valide'),
    query('exerciceId')
      .optional()
      .isUUID()
      .withMessage('L\'ID de l\'exercice doit être un UUID valide'),
    query('journalCode')
      .optional()
      .isLength({ min: 1, max: 10 })
      .withMessage('Le code journal doit faire entre 1 et 10 caractères'),
    query('statut')
      .optional()
      .isIn(['BROUILLARD', 'VALIDEE', 'CLOTUREE'])
      .withMessage('Statut invalide'),
    query('dateDebut')
      .optional()
      .isISO8601()
      .withMessage('Date de début invalide (format ISO 8601 requis)'),
    query('dateFin')
      .optional()
      .isISO8601()
      .withMessage('Date de fin invalide (format ISO 8601 requis)'),
    query('includeDetails')
      .optional()
      .isBoolean()
      .withMessage('includeDetails doit être un booléen'),
    query('includeStatistiques')
      .optional()
      .isBoolean()
      .withMessage('includeStatistiques doit être un booléen'),
    query('formatDate')
      .optional()
      .isIn(['DD/MM/YYYY', 'YYYY-MM-DD', 'YYYYMMDD'])
      .withMessage('Format de date invalide')
  ],
  validation.handleValidationErrors,
  importExportController.exporterExcel
);

/**
 * @route GET /api/v1/export/csv
 * @desc Exporte des écritures vers CSV
 * @access Private
 */
router.get('/csv',
  protect,
  [
    query('societeId')
      .optional()
      .isUUID()
      .withMessage('L\'ID de la société doit être un UUID valide'),
    query('exerciceId')
      .optional()
      .isUUID()
      .withMessage('L\'ID de l\'exercice doit être un UUID valide'),
    query('journalCode')
      .optional()
      .isLength({ min: 1, max: 10 })
      .withMessage('Le code journal doit faire entre 1 et 10 caractères'),
    query('statut')
      .optional()
      .isIn(['BROUILLARD', 'VALIDEE', 'CLOTUREE'])
      .withMessage('Statut invalide'),
    query('dateDebut')
      .optional()
      .isISO8601()
      .withMessage('Date de début invalide (format ISO 8601 requis)'),
    query('dateFin')
      .optional()
      .isISO8601()
      .withMessage('Date de fin invalide (format ISO 8601 requis)'),
    query('separateur')
      .optional()
      .isLength({ min: 1, max: 1 })
      .withMessage('Le séparateur doit être un seul caractère'),
    query('includeDetails')
      .optional()
      .isBoolean()
      .withMessage('includeDetails doit être un booléen'),
    query('formatDate')
      .optional()
      .isIn(['DD/MM/YYYY', 'YYYY-MM-DD', 'YYYYMMDD'])
      .withMessage('Format de date invalide')
  ],
  validation.handleValidationErrors,
  importExportController.exporterCSV
);

/**
 * @route GET /api/v1/export/fec/:exerciceId
 * @desc Exporte le Fichier des Écritures Comptables (FEC) pour un exercice
 * @access Private
 */
router.get('/fec/:exerciceId',
  protect,
  [
    param('exerciceId')
      .notEmpty()
      .withMessage('L\'ID de l\'exercice est obligatoire')
      .isUUID()
      .withMessage('L\'ID de l\'exercice doit être un UUID valide'),
    query('separateur')
      .optional()
      .isLength({ min: 1, max: 1 })
      .withMessage('Le séparateur doit être un seul caractère'),
    query('encodage')
      .optional()
      .isIn(['utf8', 'latin1', 'ascii'])
      .withMessage('Encodage invalide'),
    query('includeEnTetes')
      .optional()
      .isBoolean()
      .withMessage('includeEnTetes doit être un booléen')
  ],
  validation.handleValidationErrors,
  importExportController.exporterFEC
);

// ==========================================
// ROUTES UTILITAIRES
// ==========================================

/**
 * @route GET /api/v1/import-export/formats
 * @desc Retourne les formats supportés et leurs spécifications
 * @access Private
 */
router.get('/formats',
  protect,
  (req, res) => {
    res.json({
      success: true,
      data: {
        import: {
          formats: ['excel', 'csv'],
          extensions: ['.xlsx', '.xls', '.csv'],
          tailleLimite: '50 MB',
          colonnesObligatoires: [
            'Numéro Écriture',
            'Date Écriture',
            'Journal',
            'Libellé Écriture',
            'Compte',
            'Libellé Ligne',
            'Débit',
            'Crédit'
          ],
          colonnesOptionnelles: [
            'Référence',
            'Pièce Justificative',
            'Référence Ligne'
          ]
        },
        export: {
          formats: ['excel', 'csv', 'fec'],
          options: {
            excel: {
              includeDetails: 'Inclure les lignes d\'écriture',
              includeStatistiques: 'Inclure les statistiques',
              formatDate: 'Format des dates (DD/MM/YYYY, YYYY-MM-DD, YYYYMMDD)'
            },
            csv: {
              separateur: 'Caractère séparateur (défaut: ;)',
              includeDetails: 'Inclure les lignes d\'écriture',
              formatDate: 'Format des dates'
            },
            fec: {
              separateur: 'Caractère séparateur (défaut: |)',
              encodage: 'Encodage du fichier (utf8, latin1, ascii)',
              includeEnTetes: 'Inclure les en-têtes'
            }
          }
        }
      }
    });
  }
);

/**
 * @route GET /api/v1/import-export/template/:format
 * @desc Génère un template d'import pour le format spécifié
 * @access Private
 */
router.get('/template/:format',
  protect,
  [
    param('format')
      .isIn(['excel', 'csv'])
      .withMessage('Format invalide. Formats supportés: excel, csv')
  ],
  validation.handleValidationErrors,
  async (req, res, next) => {
    try {
      const { format } = req.params;
      
      // Template d'exemple pour les écritures
      const templateData = [
        {
          'Numéro Écriture': 'VT001',
          'Date Écriture': '2024-01-15',
          'Journal': 'VT',
          'Libellé Écriture': 'Vente marchandises client ABC',
          'Compte': '411000',
          'Libellé Ligne': 'Client ABC',
          'Débit': '1200.00',
          'Crédit': '0.00',
          'Référence': 'FACT001',
          'Pièce Justificative': 'FACT001.pdf',
          'Référence Ligne': ''
        },
        {
          'Numéro Écriture': 'VT001',
          'Date Écriture': '2024-01-15',
          'Journal': 'VT',
          'Libellé Écriture': 'Vente marchandises client ABC',
          'Compte': '701000',
          'Libellé Ligne': 'Ventes de marchandises',
          'Débit': '0.00',
          'Crédit': '1000.00',
          'Référence': 'FACT001',
          'Pièce Justificative': 'FACT001.pdf',
          'Référence Ligne': ''
        },
        {
          'Numéro Écriture': 'VT001',
          'Date Écriture': '2024-01-15',
          'Journal': 'VT',
          'Libellé Écriture': 'Vente marchandises client ABC',
          'Compte': '445700',
          'Libellé Ligne': 'TVA collectée',
          'Débit': '0.00',
          'Crédit': '200.00',
          'Référence': 'FACT001',
          'Pièce Justificative': 'FACT001.pdf',
          'Référence Ligne': ''
        }
      ];

      const ImportExportService = require('../services/importExportService');
      const service = new ImportExportService();
      
      // Utiliser la méthode existante pour générer le template
      // (nous devrons l'adapter pour les écritures)
      
      res.json({
        success: true,
        message: 'Template généré avec succès',
        data: {
          format,
          exemple: templateData,
          instructions: {
            'Numéro Écriture': 'Identifiant unique de l\'écriture (max 20 caractères)',
            'Date Écriture': 'Date au format YYYY-MM-DD',
            'Journal': 'Code du journal comptable',
            'Libellé Écriture': 'Description de l\'écriture',
            'Compte': 'Numéro du compte comptable',
            'Libellé Ligne': 'Description de la ligne d\'écriture',
            'Débit': 'Montant au débit (format décimal avec point)',
            'Crédit': 'Montant au crédit (format décimal avec point)',
            'Référence': 'Référence de l\'écriture (optionnel)',
            'Pièce Justificative': 'Référence de la pièce justificative (optionnel)',
            'Référence Ligne': 'Référence spécifique à la ligne (optionnel)'
          }
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;
