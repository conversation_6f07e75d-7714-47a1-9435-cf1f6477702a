'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Party extends Model {
    /**
     * Associations du modèle Party
     */
    static associate(models) {
      // Un tiers appartient à une société
      Party.belongsTo(models.Societe, {
        foreignKey: 'societeId',
        as: 'societe'
      });

      // Un tiers peut avoir plusieurs lignes d'écriture
      Party.hasMany(models.LigneEcriture, {
        foreignKey: 'tiersId',
        as: 'lignesEcriture'
      });

      // Un tiers peut avoir un compte comptable associé
      Party.belongsTo(models.CompteComptable, {
        foreignKey: 'compteComptable',
        targetKey: 'numero',
        as: 'compte'
      });
    }

    /**
     * Méthodes statiques
     */

    /**
     * Recherche des tiers par nom ou code
     * @param {string} societeId - ID de la société
     * @param {string} terme - Terme de recherche
     * @param {string} type - Type de tiers (optionnel)
     * @returns {Promise<Array>} Liste des tiers
     */
    static async rechercher(societeId, terme, type = null) {
      const { Op } = sequelize;
      const whereClause = {
        societeId,
        [Op.or]: [
          { nom: { [Op.iLike]: `%${terme}%` } },
          { code: { [Op.iLike]: `%${terme}%` } }
        ]
      };

      if (type) {
        whereClause.type = type;
      }

      return await this.findAll({
        where: whereClause,
        order: [['nom', 'ASC']],
        limit: 50
      });
    }

    /**
     * Obtient les clients d'une société
     * @param {string} societeId - ID de la société
     * @returns {Promise<Array>} Liste des clients
     */
    static async getClients(societeId) {
      const { Op } = sequelize;
      return await this.findAll({
        where: {
          societeId,
          type: { [Op.in]: ['CLIENT', 'CLIENT_FOURNISSEUR'] }
        },
        order: [['nom', 'ASC']]
      });
    }

    /**
     * Obtient les fournisseurs d'une société
     * @param {string} societeId - ID de la société
     * @returns {Promise<Array>} Liste des fournisseurs
     */
    static async getFournisseurs(societeId) {
      const { Op } = sequelize;
      return await this.findAll({
        where: {
          societeId,
          type: { [Op.in]: ['FOURNISSEUR', 'CLIENT_FOURNISSEUR'] }
        },
        order: [['nom', 'ASC']]
      });
    }

    /**
     * Valide les données d'un tiers
     * @param {Object} donnees - Données à valider
     * @returns {Object} Résultat de validation
     */
    static validatePartyData(donnees) {
      const errors = [];

      if (!donnees.code || donnees.code.trim().length < 2) {
        errors.push('Le code doit contenir au moins 2 caractères');
      }

      if (!donnees.nom || donnees.nom.trim().length < 2) {
        errors.push('Le nom doit contenir au moins 2 caractères');
      }

      if (!donnees.type || !['CLIENT', 'FOURNISSEUR', 'CLIENT_FOURNISSEUR'].includes(donnees.type)) {
        errors.push('Le type doit être CLIENT, FOURNISSEUR ou CLIENT_FOURNISSEUR');
      }

      if (donnees.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(donnees.email)) {
        errors.push('L\'adresse email n\'est pas valide');
      }

      if (donnees.plafondCredit && parseFloat(donnees.plafondCredit) < 0) {
        errors.push('Le plafond de crédit ne peut pas être négatif');
      }

      return {
        valide: errors.length === 0,
        erreurs: errors
      };
    }

    /**
     * Méthodes d'instance
     */

    /**
     * Calcule le solde du tiers
     * @param {Date} dateFinale - Date finale (optionnelle)
     * @returns {Promise<Object>} Solde {debit, credit, solde}
     */
    async calculerSolde(dateFinale = null) {
      const models = this.sequelize.models;
      const { Op } = sequelize;

      const whereClause = {
        tiersId: this.id
      };

      const includeClause = {
        model: models.EcritureComptable,
        as: 'ecriture',
        where: {
          statut: 'VALIDEE'
        }
      };

      if (dateFinale) {
        includeClause.where.dateEcriture = { [Op.lte]: dateFinale };
      }

      const lignes = await models.LigneEcriture.findAll({
        where: whereClause,
        include: [includeClause]
      });

      let totalDebit = 0;
      let totalCredit = 0;

      lignes.forEach(ligne => {
        totalDebit += parseFloat(ligne.debit || 0);
        totalCredit += parseFloat(ligne.credit || 0);
      });

      return {
        debit: totalDebit,
        credit: totalCredit,
        solde: totalDebit - totalCredit,
        nombreOperations: lignes.length
      };
    }

    /**
     * Vérifie si le tiers est un client
     * @returns {boolean} True si client
     */
    estClient() {
      return this.type === 'CLIENT' || this.type === 'CLIENT_FOURNISSEUR';
    }

    /**
     * Vérifie si le tiers est un fournisseur
     * @returns {boolean} True si fournisseur
     */
    estFournisseur() {
      return this.type === 'FOURNISSEUR' || this.type === 'CLIENT_FOURNISSEUR';
    }

    /**
     * Vérifie si le plafond de crédit est dépassé
     * @returns {Promise<boolean>} True si dépassé
     */
    async plafondDepasse() {
      if (!this.plafondCredit || this.plafondCredit <= 0) {
        return false;
      }

      const solde = await this.calculerSolde();
      return Math.abs(solde.solde) > this.plafondCredit;
    }

    /**
     * Génère automatiquement un compte comptable pour le tiers
     * @returns {string} Numéro de compte généré
     */
    genererCompteComptable() {
      const prefixe = this.estClient() ? '411' : '401';
      const suffixe = this.code.padStart(3, '0');
      return `${prefixe}${suffixe}`;
    }

    /**
     * Formate le tiers pour affichage
     * @returns {Object} Tiers formaté
     */
    formater() {
      return {
        id: this.id,
        code: this.code,
        nom: this.nom,
        type: this.type,
        civilite: this.civilite,
        adresse: this.adresse,
        ville: this.ville,
        pays: this.pays,
        telephone: this.telephone,
        email: this.email,
        compteComptable: this.compteComptable,
        conditionsPaiement: this.conditionsPaiement,
        plafondCredit: parseFloat(this.plafondCredit || 0),
        numeroContribuable: this.numeroContribuable,
        assujettiTva: this.assujettiTva,
        actif: this.actif
      };
    }
  }

  Party.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    code: {
      type: DataTypes.STRING(20),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 20]
      }
    },
    nom: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 255]
      }
    },
    type: {
      type: DataTypes.ENUM('CLIENT', 'FOURNISSEUR', 'CLIENT_FOURNISSEUR'),
      allowNull: false
    },
    civilite: {
      type: DataTypes.ENUM('M', 'MME', 'MLLE', 'DR', 'PROF', 'SARL', 'SA', 'SAS', 'EURL', 'SNC', 'GIE'),
      allowNull: true
    },
    adresse: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    ville: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    pays: {
      type: DataTypes.STRING(100),
      allowNull: true,
      defaultValue: 'Côte d\'Ivoire'
    },
    telephone: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        is: /^[\d\s\-+()]*$/
      }
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        isEmail: true
      }
    },
    compteComptable: {
      type: DataTypes.STRING(10),
      allowNull: true,
      references: {
        model: 'compte_comptables',
        key: 'numero'
      }
    },
    conditionsPaiement: {
      type: DataTypes.STRING(100),
      allowNull: true,
      defaultValue: 'Comptant'
    },
    plafondCredit: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      defaultValue: 0.00,
      validate: {
        min: 0
      }
    },
    numeroContribuable: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    assujettiTva: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    actif: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    societeId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'societes',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'Party',
    tableName: 'parties',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['societe_id', 'code']
      },
      {
        fields: ['societe_id', 'type']
      },
      {
        fields: ['nom']
      },
      {
        fields: ['compte_comptable']
      }
    ]
  });

  return Party;
};