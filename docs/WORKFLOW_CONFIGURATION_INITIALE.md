## Paramètres Comptables

### Description

Cette étape permet de configurer les paramètres comptables spécifiques à l'entreprise, tels que les comptes par défaut, les taux de TVA, et autres paramètres qui influencent le fonctionnement du système.

### Interface Utilisateur

#### Écran Principal - Paramètres Comptables

```
┌─────────────────────────────────────────────────────┐
│ Paramètres Comptables - SARL AFRICAN BUSINESS       │
├─────────────────────────────────────────────────────┤
│                                                     │
│  Comptes par Défaut                                 │
│  ┌─────────────────────────┐ ┌─────────────────────┐│
│  │ Compte client           │ │ Compte fournisseur  ││
│  │ ┌─────────────────────┐ │ │ ┌─────────────────┐ ││
│  │ │ 411000              │ │ │ │ 401000          │ ││
│  │ └─────────────────────┘ │ │ └─────────────────┘ ││
│  └─────────────────────────┘ └─────────────────────┘│
│                                                     │
│  ┌─────────────────────────┐ ┌─────────────────────┐│
│  │ Compte TVA collectée    │ │ Compte TVA déduct.  ││
│  │ ┌─────────────────────┐ │ │ ┌─────────────────┐ ││
│  │ │ 443100              │ │ │ │ 445200          │ ││
│  │ └─────────────────────┘ │ │ └─────────────────┘ ││
│  └─────────────────────────┘ └─────────────────────┘│
│                                                     │
│  ┌─────────────────────────┐ ┌─────────────────────┐│
│  │ Compte banque principal │ │ Compte caisse       ││
│  │ ┌─────────────────────┐ │ │ ┌─────────────────┐ ││
│  │ │ 521000              │ │ │ │ 571000          │ ││
│  │ └─────────────────────┘ │ │ └─────────────────┘ ││
│  └─────────────────────────┘ └─────────────────────┘│
│                                                     │
│  Paramètres TVA                                     │
│  ┌─────────────────────────┐ ┌─────────────────────┐│
│  │ Taux de TVA par défaut  │ │ Méthode de calcul   ││
│  │ ┌─────────────────────┐ │ │ ┌─────────────────┐ ││
│  │ │ 18%                 │ │ │ │ Sur HT          │ ││
│  │ └─────────────────────┘ │ │ └─────────────────┘ ││
│  └─────────────────────────┘ └─────────────────────┘│
│                                                     │
│  Paramètres Divers                                  │
│  ┌─────────────────────────────────────────────────┐│
│  │ [x] Activer contrôle équilibre des écritures    ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────────────────────────────────────────┐│
│  │ [x] Activer validation SYSCOHADA automatique    ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────────────────────────────────────────┐│
│  │ [ ] Autoriser écritures déséquilibrées          ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────┐    ┌──────────────┐                │
│  │   Annuler   │    │  Enregistrer │                │
│  └─────────────┘    └──────────────┘                │
└─────────────────────────────────────────────────────┘
```

### Champs et Validation

| Champ | Type | Obligatoire | Validation | Description |
|-------|------|-------------|------------|-------------|
| Compte client | Texte | Oui | Compte existant | Compte client par défaut |
| Compte fournisseur | Texte | Oui | Compte existant | Compte fournisseur par défaut |
| Compte TVA collectée | Texte | Non | Compte existant | Compte TVA sur ventes |
| Compte TVA déductible | Texte | Non | Compte existant | Compte TVA sur achats |
| Compte banque principal | Texte | Oui | Compte existant | Compte bancaire principal |
| Compte caisse | Texte | Oui | Compte existant | Compte de caisse |
| Taux de TVA par défaut | Nombre | Non | 0-100% | Taux standard (ex: 18%) |
| Méthode de calcul TVA | Sélection | Non | SUR_HT, SUR_TTC | Base de calcul TVA |
| Contrôle équilibre | Booléen | Non | - | Vérification équilibre |
| Validation SYSCOHADA | Booléen | Non | - | Validation automatique |
| Écritures déséquilibrées | Booléen | Non | - | Autorisation exception |

### Interactions API

#### Configuration des Paramètres

```http
PUT /api/v1/parametres/batch
{
  "societeId": "uuid-societe",
  "parametres": [
    {"cle": "compte_client_defaut", "valeur": "411000"},
    {"cle": "compte_fournisseur_defaut", "valeur": "401000"},
    {"cle": "compte_tva_collectee", "valeur": "443100"},
    {"cle": "compte_tva_deductible", "valeur": "445200"},
    {"cle": "compte_banque_principal", "valeur": "521000"},
    {"cle": "compte_caisse", "valeur": "571000"},
    {"cle": "tva_defaut", "valeur": "18"},
    {"cle": "methode_calcul_tva", "valeur": "SUR_HT"},
    {"cle": "controle_equilibre", "valeur": "true"},
    {"cle": "validation_syscohada", "valeur": "true"},
    {"cle": "autoriser_desequilibre", "valeur": "false"}
  ]
}
```

#### Réponse Succès

```http
Status: 200 OK
{
  "success": true,
  "message": "Paramètres mis à jour avec succès",
  "data": {
    "parametresModifies": 11,
    "dateModification": "2025-08-07T14:45:00Z"
  }
}
```

### Gestion des Erreurs

| Code | Message | Action Utilisateur |
|------|---------|-------------------|
| 400 | "Le compte client par défaut est requis" | Remplir le champ obligatoire |
| 400 | "Le compte 411999 n'existe pas" | Vérifier l'existence du compte |
| 403 | "Droits insuffisants pour modifier les paramètres" | Contacter l'administrateur |

### Considérations Spéciales

- Les paramètres sont spécifiques à chaque société
- Possibilité de définir des paramètres par exercice comptable
- Option pour importer des paramètres depuis une autre société

---

## Validation et Finalisation

### Description

Cette étape finale permet de vérifier et valider l'ensemble de la configuration avant de commencer à utiliser le système. Elle inclut des contrôles de cohérence et la génération d'un rapport de configuration.

### Interface Utilisateur

#### Écran de Validation

```
┌─────────────────────────────────────────────────────┐
│ Validation de la Configuration                      │
├─────────────────────────────────────────────────────┤
│                                                     │
│  Société: SARL AFRICAN BUSINESS                     │
│                                                     │
│  Vérifications                                      │
│  ┌─────────────────────────────────────────────────┐│
│  │ ✅ Informations société complètes               ││
│  │ ✅ Exercice comptable configuré                 ││
│  │ ✅ Plan comptable personnalisé                  ││
│  │ ✅ Journaux configurés                          ││
│  │ ✅ Paramètres comptables définis                ││
│  │ ⚠️ Comptes bancaires incomplets                ││
│  │ ⚠️ Taux de change non définis                  ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  Résumé de la Configuration                         │
│  ┌─────────────────────────────────────────────────┐│
│  │ • 1 Société                                     ││
│  │ • 1 Exercice comptable                          ││
│  │ • 227 Comptes comptables                        ││
│  │ • 6 Journaux                                    ││
│  │ • 11 Paramètres définis                         ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  Actions                                            │
│  ┌─────────────────────────┐ ┌─────────────────────┐│
│  │ 📋 Générer rapport      │ │ 📧 Envoyer par email││
│  └─────────────────────────┘ └─────────────────────┘│
│                                                     │
│  ┌─────────────┐    ┌──────────────┐                │
│  │   Retour    │    │   Finaliser  │                │
│  └─────────────┘    └──────────────┘                │
└─────────────────────────────────────────────────────┘
```

#### Rapport de Configuration

```
┌─────────────────────────────────────────────────────┐
│ Rapport de Configuration                            │
├─────────────────────────────────────────────────────┤
│                                                     │
│  SARL AFRICAN BUSINESS                              │
│  Configuration générée le 07/08/2025 à 14:50        │
│                                                     │
│  1. Informations Société                            │
│  ───────────────────────────────────────────────────│
│  Nom: SARL AFRICAN BUSINESS                         │
│  Forme juridique: SARL                              │
│  Numéro d'identification: SN123456789               │
│  Adresse: 123 Avenue de l'Indépendance, Dakar       │
│  Devise: XOF                                        │
│                                                     │
│  2. Exercice Comptable                              │
│  ───────────────────────────────────────────────────│
│  Libellé: Exercice 2025                             │
│  Période: 01/01/2025 - 31/12/2025                   │
│  Statut: OUVERT                                     │
│                                                     │
│  3. Plan Comptable                                  │
│  ───────────────────────────────────────────────────│
│  Comptes standard: 220                              │
│  Comptes personnalisés: 7                           │
│  Total: 227 comptes                                 │
│                                                     │
│  4. Journaux                                        │
│  ───────────────────────────────────────────────────│
│  AC: Achats                                         │
│  VT: Ventes                                         │
│  BQ: Banque                                         │
│  CA: Caisse                                         │
│  OD: Opérations Diverses                            │
│  PA: Journal de Paie                                │
│                                                     │
│  5. Paramètres Comptables                           │
│  ───────────────────────────────────────────────────│
│  Compte client défaut: 411000                       │
│  Compte fournisseur défaut: 401000                  │
│  Taux TVA défaut: 18%                               │
│  ...                                                │
│                                                     │
│  6. Avertissements                                  │
│  ───────────────────────────────────────────────────│
│  • Comptes bancaires incomplets                     │
│  • Taux de change non définis                       │
│                                                     │
│  ┌─────────────────────────┐ ┌─────────────────────┐│
│  │ 💾 Télécharger PDF      │ │ 🖨️ Imprimer        ││
│  └─────────────────────────┘ └─────────────────────┘│
│                                                     │
└─────────────────────────────────────────────────────┘
```

### Vérifications Automatiques

| Vérification | Description | Niveau |
|--------------|-------------|--------|
| Informations société | Vérifier que les informations obligatoires sont renseignées | Critique |
| Exercice comptable | Vérifier qu'au moins un exercice est configuré | Critique |
| Plan comptable | Vérifier que le plan comptable est complet | Critique |
| Journaux | Vérifier que les journaux standard sont configurés | Critique |
| Paramètres comptables | Vérifier que les paramètres obligatoires sont définis | Critique |
| Comptes bancaires | Vérifier que les comptes bancaires sont configurés | Avertissement |
| Taux de change | Vérifier que les taux de change sont définis (si multi-devises) | Avertissement |

### Interactions API

#### Validation de la Configuration

```http
GET /api/v1/configuration/validation?societeId=uuid-societe
```

#### Réponse Succès

```http
Status: 200 OK
{
  "success": true,
  "data": {
    "verifications": [
      {"type": "SOCIETE", "statut": "SUCCES", "message": "Informations société complètes"},
      {"type": "EXERCICE", "statut": "SUCCES", "message": "Exercice comptable configuré"},
      {"type": "PLAN_COMPTABLE", "statut": "SUCCES", "message": "Plan comptable personnalisé"},
      {"type": "JOURNAUX", "statut": "SUCCES", "message": "Journaux configurés"},
      {"type": "PARAMETRES", "statut": "SUCCES", "message": "Paramètres comptables définis"},
      {"type": "BANQUES", "statut": "AVERTISSEMENT", "message": "Comptes bancaires incomplets"},
      {"type": "TAUX_CHANGE", "statut": "AVERTISSEMENT", "message": "Taux de change non définis"}
    ],
    "resume": {
      "societes": 1,
      "exercices": 1,
      "comptes": 227,
      "journaux": 6,
      "parametres": 11
    }
  }
}
```

#### Génération du Rapport

```http
GET /api/v1/configuration/rapport?societeId=uuid-societe&format=PDF
```

### Considérations Spéciales

- Possibilité de corriger les problèmes directement depuis l'écran de validation
- Option pour ignorer certains avertissements non critiques
- Envoi du rapport par email aux parties prenantes pour validation

---

## Considérations Techniques

### Architecture Frontend-Backend

La mise en œuvre de ce workflow de configuration initiale repose sur une architecture claire :

1. **Frontend** :
   - Composants React modulaires pour chaque étape
   - Gestion d'état avec Redux pour maintenir la cohérence
   - Validation côté client pour feedback immédiat
   - Formulaires multi-étapes avec sauvegarde progressive

2. **Backend** :
   - API RESTful pour toutes les opérations
   - Validation côté serveur pour garantir l'intégrité
   - Transactions pour assurer la cohérence des données
   - Journalisation des actions pour audit

### Optimisations de Performance

- **Chargement différé** des données volumineuses (plan comptable)
- **Mise en cache** des données de référence (comptes, journaux)
- **Validation asynchrone** pour ne pas bloquer l'interface
- **Sauvegarde automatique** des formulaires en cours d'édition

### Sécurité

- **Contrôle d'accès** basé sur les rôles pour chaque étape
- **Validation des données** côté serveur pour prévenir les injections
- **Journalisation** de toutes les actions de configuration
- **Vérification des permissions** avant chaque opération sensible

### Extensibilité

Le workflow est conçu pour être extensible :

- **Modules optionnels** activables selon les besoins
- **Points d'extension** pour personnalisation
- **API documentée** pour intégration avec d'autres systèmes
- **Hooks** pour ajouter des validations personnalisées

---

## Annexes

### A. Modèle de Données

```mermaid
erDiagram
    SOCIETE {
        uuid id
        string nom
        string formeJuridique
        string numeroIdentification
        decimal capital
        string devise
        boolean multiDevises
    }
    EXERCICE {
        uuid id
        uuid societeId
        string libelle
        date dateDebut
        date dateFin
        string statut
    }
    COMPTE {
        string numero
        uuid societeId
        string libelle
        int classe
        string nature
        string sens
    }
    JOURNAL {
        string code
        uuid societeId
        string libelle
        string type
        string prefixeNumero
    }
    PARAMETRE {
        uuid id
        uuid societeId
        string cle
        string valeur
    }
    
    SOCIETE ||--o{ EXERCICE : "possède"
    SOCIETE ||--o{ COMPTE : "possède"
    SOCIETE ||--o{ JOURNAL : "possède"
    SOCIETE ||--o{ PARAMETRE : "possède"
```

### B. Exemples de Configuration

#### Exemple 1: PME Commerciale

```json
{
  "societe": {
    "nom": "SARL AFRICAN BUSINESS",
    "formeJuridique": "SARL",
    "numeroIdentification": "SN123456789",
    "capital": 5000000,
    "devise": "XOF",
    "multiDevises": false
  },
  "exercice": {
    "libelle": "Exercice 2025",
    "dateDebut": "2025-01-01",
    "dateFin": "2025-12-31"
  },
  "journaux": [
    {"code": "AC", "libelle": "Achats"},
    {"code": "VT", "libelle": "Ventes"},
    {"code": "BQ", "libelle": "Banque"},
    {"code": "CA", "libelle": "Caisse"},
    {"code": "OD", "libelle": "Opérations Diverses"}
  ],
  "parametres": {
    "compte_client_defaut": "411000",
    "compte_fournisseur_defaut": "401000",
    "tva_defaut": "18"
  }
}
```

#### Exemple 2: Cabinet Comptable Multi-Clients

```json
{
  "societe": {
    "nom": "CABINET EXPERTISE COMPTABLE",
    "formeJuridique": "SARL",
    "numeroIdentification": "SN987654321",
    "capital": 10000000,
    "devise": "XOF",
    "multiDevises": true
  },
  "exercice": {
    "libelle": "Exercice 2025",
    "dateDebut": "2025-01-01",
    "dateFin": "2025-12-31"
  },
  "journaux": [
    {"code": "AC", "libelle": "Achats"},
    {"code": "VT", "libelle": "Ventes"},
    {"code": "BQ", "libelle": "Banque"},
    {"code": "CA", "libelle": "Caisse"},
    {"code": "OD", "libelle": "Opérations Diverses"},
    {"code": "PA", "libelle": "Paie"},
    {"code": "AN", "libelle": "A-Nouveaux"}
  ],
  "parametres": {
    "compte_client_defaut": "411000",
    "compte_fournisseur_defaut": "401000",
    "tva_defaut": "18",
    "validation_syscohada": "true",
    "controle_equilibre": "true"
  }
}
```

### C. Checklist de Configuration

- [ ] **Création de la société**
  - [ ] Informations générales complètes
  - [ ] Paramètres fiscaux définis
  - [ ] Options comptables configurées

- [ ] **Configuration des exercices**
  - [ ] Exercice courant créé
  - [ ] Dates correctement définies
  - [ ] Statut vérifié

- [ ] **Plan comptable**
  - [ ] Plan standard importé
  - [ ] Comptes personnalisés ajoutés
  - [ ] Comptes auxiliaires configurés

- [ ] **Journaux comptables**
  - [ ] Journaux standard configurés
  - [ ] Journaux spécifiques ajoutés
  - [ ] Numérotation paramétrée

- [ ] **Paramètres comptables**
  - [ ] Comptes par défaut définis
  - [ ] Paramètres TVA configurés
  - [ ] Options de contrôle activées

- [ ] **Validation finale**
  - [ ] Vérifications automatiques passées
  - [ ] Rapport généré et validé
  - [ ] Configuration finalisée

---

*Document préparé par l'équipe d'architecture, version 1.0*