/**
 * Configuration centralisée des variables d'environnement
 * API Comptabilité SYSCOHADA
 */

require('dotenv').config();

const config = {
  // Environnement
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: parseInt(process.env.PORT) || 3000,
  API_VERSION: process.env.API_VERSION || 'v1',

  // Base de données
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    name: process.env.DB_NAME || 'compta_syscohada',
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    dialect: 'postgres',
    logging: process.env.NODE_ENV === 'development',
    pool: {
      max: parseInt(process.env.DB_POOL_MAX) || 10,
      min: parseInt(process.env.DB_POOL_MIN) || 0,
      acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 30000,
      idle: parseInt(process.env.DB_POOL_IDLE) || 10000
    }
  },

  // Sécurité
  security: {
    jwtSecret: process.env.JWT_SECRET || 'dev_secret_change_in_production',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12
  },

  // CORS
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: process.env.CORS_CREDENTIALS === 'true'
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/app.log',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5
  },

  // Pagination
  pagination: {
    defaultPageSize: parseInt(process.env.DEFAULT_PAGE_SIZE) || 20,
    maxPageSize: parseInt(process.env.MAX_PAGE_SIZE) || 100
  },

  // Upload
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'pdf,jpg,jpeg,png').split(',')
  }
};

/**
 * Validation de la configuration
 */
function validateConfig() {
  const required = [
    'database.host',
    'database.name',
    'database.username',
    'security.jwtSecret'
  ];

  const missing = [];

  required.forEach(path => {
    const keys = path.split('.');
    let value = config;
    
    for (const key of keys) {
      value = value[key];
      if (value === undefined || value === '') {
        missing.push(path);
        break;
      }
    }
  });

  if (missing.length > 0) {
    throw new Error(`Variables d'environnement manquantes: ${missing.join(', ')}`);
  }

  // Validation spécifique pour la production
  if (config.NODE_ENV === 'production') {
    if (config.security.jwtSecret === 'dev_secret_change_in_production') {
      throw new Error('JWT_SECRET doit être changé en production');
    }
  }

  return true;
}

/**
 * Affichage de la configuration (sans les secrets)
 */
function displayConfig() {
  const safeConfig = {
    ...config,
    security: {
      ...config.security,
      jwtSecret: '***HIDDEN***'
    },
    database: {
      ...config.database,
      password: '***HIDDEN***'
    }
  };

  console.log('📋 Configuration chargée:');
  console.log(`   Environnement: ${config.NODE_ENV}`);
  console.log(`   Port: ${config.PORT}`);
  console.log(`   Base de données: ${config.database.host}:${config.database.port}/${config.database.name}`);
  console.log(`   Logging: ${config.logging.level}`);
  
  return safeConfig;
}

module.exports = {
  config,
  validateConfig,
  displayConfig
};
