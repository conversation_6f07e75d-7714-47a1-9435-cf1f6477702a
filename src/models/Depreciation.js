'use strict';

const { DataTypes } = require('sequelize');

/**
 * Modèle Depreciation - Gestion des amortissements
 * Conforme aux normes SYSCOHADA pour les immobilisations
 */
module.exports = (sequelize) => {
  const Depreciation = sequelize.define('Depreciation', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      comment: 'Identifiant unique de l\'amortissement'
    },

    // Informations de base
    code: {
      type: DataTypes.STRING(20),
      allowNull: false,
      comment: 'Code unique de l\'immobilisation'
    },

    libelle: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: 'Libellé de l\'immobilisation'
    },

    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Description détaillée de l\'immobilisation'
    },

    // Classification
    categorie: {
      type: DataTypes.ENUM(
        'TERRAIN',
        'BATIMENT',
        'MATERIEL_TRANSPORT',
        'MATERIEL_BUREAU',
        'MATERIEL_INFORMATIQUE',
        'MOBILIER',
        'INSTALLATION',
        'MATERIEL_INDUSTRIEL',
        'BREVETS_LICENCES',
        'LOGICIELS',
        'AUTRES'
      ),
      allowNull: false,
      comment: 'Catégorie de l\'immobilisation selon SYSCOHADA'
    },

    // Comptes comptables
    compteImmobilisation: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: 'Compte d\'immobilisation (classe 2)'
    },

    compteAmortissement: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: 'Compte d\'amortissement (classe 28)'
    },

    compteDotation: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: '681100',
      comment: 'Compte de dotation aux amortissements'
    },

    // Valeurs financières
    valeurAcquisition: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      validate: {
        min: 0
      },
      comment: 'Valeur d\'acquisition HT'
    },

    valeurResiduelle: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Valeur résiduelle estimée'
    },

    valeurAmortissable: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      validate: {
        min: 0
      },
      comment: 'Valeur amortissable (acquisition - résiduelle)'
    },

    // Dates
    dateAcquisition: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Date d\'acquisition de l\'immobilisation'
    },

    dateMiseEnService: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Date de mise en service (début amortissement)'
    },

    dateFinAmortissement: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'Date de fin d\'amortissement calculée'
    },

    // Paramètres d'amortissement
    methodeAmortissement: {
      type: DataTypes.ENUM(
        'LINEAIRE',
        'DEGRESSIF',
        'PROGRESSIF',
        'VARIABLE',
        'EXCEPTIONNEL'
      ),
      allowNull: false,
      defaultValue: 'LINEAIRE',
      comment: 'Méthode d\'amortissement'
    },

    dureeAmortissement: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 100
      },
      comment: 'Durée d\'amortissement en années'
    },

    tauxAmortissement: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      validate: {
        min: 0,
        max: 100
      },
      comment: 'Taux d\'amortissement annuel (%)'
    },

    coefficientDegressif: {
      type: DataTypes.DECIMAL(3, 2),
      allowNull: true,
      comment: 'Coefficient pour amortissement dégressif'
    },

    // Prorata temporis
    prorataTemporisPremierExercice: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Appliquer le prorata temporis la première année'
    },

    // Cumuls et soldes
    cumulAmortissements: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Cumul des amortissements pratiqués'
    },

    valeurNetteComptable: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Valeur nette comptable (acquisition - cumul amortissements)'
    },

    dotationExerciceCourant: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Dotation de l\'exercice en cours'
    },

    // Statut et gestion
    statut: {
      type: DataTypes.ENUM(
        'EN_SERVICE',
        'HORS_SERVICE',
        'CEDE',
        'REFORME',
        'TOTALEMENT_AMORTI'
      ),
      allowNull: false,
      defaultValue: 'EN_SERVICE',
      comment: 'Statut de l\'immobilisation'
    },

    amortissementTermine: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Amortissement terminé'
    },

    // Informations complémentaires
    numeroSerie: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Numéro de série ou d\'identification'
    },

    localisation: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Localisation physique de l\'immobilisation'
    },

    fournisseur: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Nom du fournisseur'
    },

    numeroFacture: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: 'Numéro de facture d\'acquisition'
    },

    // Informations fiscales
    regimeFiscal: {
      type: DataTypes.ENUM(
        'NORMAL',
        'SIMPLIFIE',
        'EXONERE'
      ),
      allowNull: false,
      defaultValue: 'NORMAL',
      comment: 'Régime fiscal applicable'
    },

    deductibleTVA: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'TVA déductible sur l\'acquisition'
    },

    // Métadonnées
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Notes et observations'
    },

    actif: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Immobilisation active'
    }
  }, {
    tableName: 'depreciations',
    timestamps: true,
    paranoid: true, // Soft delete
    indexes: [
      {
        fields: ['societeId']
      },
      {
        fields: ['code', 'societeId'],
        unique: true
      },
      {
        fields: ['categorie']
      },
      {
        fields: ['statut']
      },
      {
        fields: ['dateMiseEnService']
      },
      {
        fields: ['compteImmobilisation']
      },
      {
        fields: ['methodeAmortissement']
      }
    ],
    hooks: {
      beforeValidate: (depreciation) => {
        // Calcul automatique de la valeur amortissable
        if (depreciation.valeurAcquisition && depreciation.valeurResiduelle !== undefined) {
          depreciation.valeurAmortissable = depreciation.valeurAcquisition - (depreciation.valeurResiduelle || 0);
        }

        // Calcul automatique de la valeur nette comptable
        if (depreciation.valeurAcquisition && depreciation.cumulAmortissements !== undefined) {
          depreciation.valeurNetteComptable = depreciation.valeurAcquisition - depreciation.cumulAmortissements;
        }

        // Calcul automatique du taux d'amortissement pour méthode linéaire
        if (depreciation.methodeAmortissement === 'LINEAIRE' && depreciation.dureeAmortissement) {
          depreciation.tauxAmortissement = (100 / depreciation.dureeAmortissement).toFixed(2);
        }

        // Calcul de la date de fin d'amortissement
        if (depreciation.dateMiseEnService && depreciation.dureeAmortissement) {
          const dateFin = new Date(depreciation.dateMiseEnService);
          dateFin.setFullYear(dateFin.getFullYear() + depreciation.dureeAmortissement);
          depreciation.dateFinAmortissement = dateFin;
        }

        // Vérification si l'amortissement est terminé
        if (depreciation.valeurAmortissable && depreciation.cumulAmortissements) {
          depreciation.amortissementTermine = depreciation.cumulAmortissements >= depreciation.valeurAmortissable;
        }
      }
    },
    validate: {
      valeurResiduelleInferieure() {
        if (this.valeurResiduelle && this.valeurAcquisition && this.valeurResiduelle >= this.valeurAcquisition) {
          throw new Error('La valeur résiduelle doit être inférieure à la valeur d\'acquisition');
        }
      },
      cumulAmortissementsValide() {
        if (this.cumulAmortissements && this.valeurAmortissable && this.cumulAmortissements > this.valeurAmortissable) {
          throw new Error('Le cumul des amortissements ne peut pas dépasser la valeur amortissable');
        }
      },
      dateMiseEnServiceValide() {
        if (this.dateMiseEnService && this.dateAcquisition && this.dateMiseEnService < this.dateAcquisition) {
          throw new Error('La date de mise en service ne peut pas être antérieure à la date d\'acquisition');
        }
      },
      compteImmobilisationValide() {
        if (this.compteImmobilisation && !this.compteImmobilisation.startsWith('2')) {
          throw new Error('Le compte d\'immobilisation doit appartenir à la classe 2');
        }
      },
      compteAmortissementValide() {
        if (this.compteAmortissement && !this.compteAmortissement.startsWith('28')) {
          throw new Error('Le compte d\'amortissement doit appartenir à la classe 28');
        }
      }
    }
  });

  // Associations
  Depreciation.associate = function(models) {
    // Relation avec Société
    Depreciation.belongsTo(models.Societe, {
      foreignKey: {
        name: 'societeId',
        allowNull: false
      },
      as: 'societe',
      onDelete: 'CASCADE'
    });

    // Relation avec les comptes comptables
    Depreciation.belongsTo(models.CompteComptable, {
      foreignKey: 'compteImmobilisation',
      targetKey: 'numero',
      as: 'compteImmobilisationDetail',
      constraints: false
    });

    Depreciation.belongsTo(models.CompteComptable, {
      foreignKey: 'compteAmortissement',
      targetKey: 'numero',
      as: 'compteAmortissementDetail',
      constraints: false
    });

    Depreciation.belongsTo(models.CompteComptable, {
      foreignKey: 'compteDotation',
      targetKey: 'numero',
      as: 'compteDotationDetail',
      constraints: false
    });

    // Relation avec les écritures d'amortissement
    Depreciation.hasMany(models.EcritureComptable, {
      foreignKey: 'depreciationId',
      as: 'ecrituresAmortissement',
      constraints: false
    });

    // Relation avec les plans d'amortissement
    Depreciation.hasMany(models.DepreciationPlan, {
      foreignKey: 'depreciationId',
      as: 'planAmortissement'
    });
  };

  // Méthodes d'instance
  Depreciation.prototype.calculerDotationAnnuelle = function(exercice) {
    const anneeExercice = new Date(exercice).getFullYear();
    const anneeMiseEnService = new Date(this.dateMiseEnService).getFullYear();
    
    switch (this.methodeAmortissement) {
      case 'LINEAIRE':
        return this.calculerDotationLineaire(anneeExercice, anneeMiseEnService);
      case 'DEGRESSIF':
        return this.calculerDotationDegressive(anneeExercice, anneeMiseEnService);
      default:
        return this.calculerDotationLineaire(anneeExercice, anneeMiseEnService);
    }
  };

  Depreciation.prototype.calculerDotationLineaire = function(anneeExercice, anneeMiseEnService) {
    let dotation = this.valeurAmortissable * (this.tauxAmortissement / 100);
    
    // Prorata temporis pour la première année
    if (this.prorataTemporisPremierExercice && anneeExercice === anneeMiseEnService) {
      const moisMiseEnService = new Date(this.dateMiseEnService).getMonth() + 1;
      const prorata = (12 - moisMiseEnService + 1) / 12;
      dotation = dotation * prorata;
    }
    
    // Vérifier que la dotation ne dépasse pas le restant à amortir
    const restantAAmortir = this.valeurAmortissable - this.cumulAmortissements;
    return Math.min(dotation, restantAAmortir);
  };

  Depreciation.prototype.calculerDotationDegressive = function(anneeExercice, anneeMiseEnService) {
    const anneesEcoulees = anneeExercice - anneeMiseEnService;
    const tauxDegressif = this.tauxAmortissement * (this.coefficientDegressif || 1.5);
    
    let valeurResiduelle = this.valeurAmortissable;
    for (let i = 0; i < anneesEcoulees; i++) {
      valeurResiduelle = valeurResiduelle * (1 - tauxDegressif / 100);
    }
    
    const dotation = valeurResiduelle * (tauxDegressif / 100);
    const restantAAmortir = this.valeurAmortissable - this.cumulAmortissements;
    
    return Math.min(dotation, restantAAmortir);
  };

  Depreciation.prototype.estTotalementAmorti = function() {
    return this.cumulAmortissements >= this.valeurAmortissable;
  };

  Depreciation.prototype.getPourcentageAmortissement = function() {
    if (this.valeurAmortissable === 0) return 0;
    return (this.cumulAmortissements / this.valeurAmortissable) * 100;
  };

  // Méthodes de classe
  Depreciation.findBySociete = function(societeId, options = {}) {
    return this.findAll({
      where: { societeId, ...options.where },
      include: options.include || [
        { model: this.sequelize.models.CompteComptable, as: 'compteImmobilisationDetail' },
        { model: this.sequelize.models.CompteComptable, as: 'compteAmortissementDetail' }
      ],
      order: options.order || [['code', 'ASC']]
    });
  };

  Depreciation.findByCategorie = function(societeId, categorie) {
    return this.findAll({
      where: { societeId, categorie },
      order: [['code', 'ASC']]
    });
  };

  Depreciation.findEnService = function(societeId) {
    return this.findAll({
      where: { 
        societeId, 
        statut: 'EN_SERVICE',
        amortissementTermine: false
      },
      order: [['code', 'ASC']]
    });
  };

  return Depreciation;
};