'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('societes', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4
      },
      nom: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      adresse: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      telephone: {
        type: Sequelize.STRING(20),
        allowNull: true
      },
      email: {
        type: Sequelize.STRING(50),
        allowNull: true,
        unique: true
      },
      numero_contribuable: {
        type: Sequelize.STRING(50),
        allowNull: true,
        unique: true
      },
      forme_juridique: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      capital: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true
      },
      exercice_debut: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      exercice_fin: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      devise: {
        type: Sequelize.STRING(3),
        allowNull: false,
        defaultValue: 'XOF'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Index pour améliorer les performances
    await queryInterface.addIndex('societes', ['nom']);
    await queryInterface.addIndex('societes', ['numero_contribuable']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('societes');
  }
};