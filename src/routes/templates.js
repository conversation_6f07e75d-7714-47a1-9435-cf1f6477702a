'use strict';

const express = require('express');
const Joi = require('joi');
const router = express.Router();

const templateController = require('../controllers/templateController');
const { protect } = require('../middleware/auth');
const { validate } = require('../middleware/validation');

// Middleware pour initialiser les services
router.use((req, res, next) => {
  if (!templateController.templateService) {
    templateController.initServices(req.app.get('models'));
  }
  next();
});

// Schémas de validation Joi
const schemas = {
  creerTemplate: Joi.object({
    donnees: Joi.object({
      nom: Joi.string().min(3).max(100).required()
        .messages({
          'string.min': 'Le nom doit contenir au moins 3 caractères',
          'string.max': 'Le nom ne peut pas dépasser 100 caractères',
          'any.required': 'Le nom est obligatoire'
        }),
      description: Joi.string().max(1000).allow('', null),
      libelle: Joi.string().min(2).max(255).required()
        .messages({
          'string.min': 'Le libellé doit contenir au moins 2 caractères',
          'any.required': 'Le libellé est obligatoire'
        }),
      journalCode: Joi.string().max(10).required()
        .messages({
          'any.required': 'Le code journal est obligatoire'
        }),
      categorie: Joi.string().valid(
        'VENTE', 'ACHAT', 'BANQUE', 'CAISSE', 'PAIE', 
        'AMORTISSEMENT', 'PROVISION', 'AUTRE'
      ).required(),
      societeId: Joi.string().uuid().required()
        .messages({
          'any.required': 'L\'ID de la société est obligatoire'
        }),
      public: Joi.boolean().default(false),
      tagsRecherche: Joi.string().max(1000).allow('', null)
    }).required(),
    lignes: Joi.array().items(
      Joi.object({
        compteNumero: Joi.string().max(10).required()
          .messages({
            'any.required': 'Le compte comptable est obligatoire'
          }),
        libelle: Joi.string().min(2).max(255).required()
          .messages({
            'string.min': 'Le libellé doit contenir au moins 2 caractères',
            'any.required': 'Le libellé est obligatoire'
          }),
        sens: Joi.string().valid('DEBIT', 'CREDIT', 'VARIABLE').required(),
        montantFixe: Joi.number().min(0).allow(null),
        formuleMontant: Joi.string().max(500).allow('', null),
        ordre: Joi.number().integer().min(1).default(1),
        obligatoire: Joi.boolean().default(true),
        commentaire: Joi.string().max(1000).allow('', null)
      })
    ).min(2).required()
      .messages({
        'array.min': 'Un template doit avoir au moins 2 lignes'
      })
  }),

  creerDepuisEcriture: Joi.object({
    nom: Joi.string().min(3).max(100).required(),
    description: Joi.string().max(1000).allow('', null),
    libelle: Joi.string().min(2).max(255).allow('', null),
    journalCode: Joi.string().max(10).allow('', null),
    categorie: Joi.string().valid(
      'VENTE', 'ACHAT', 'BANQUE', 'CAISSE', 'PAIE', 
      'AMORTISSEMENT', 'PROVISION', 'AUTRE'
    ).default('AUTRE'),
    societeId: Joi.string().uuid().allow('', null),
    public: Joi.boolean().default(false)
  }),

  appliquerTemplate: Joi.object({
    parametres: Joi.object().pattern(
      Joi.string(),
      Joi.alternatives().try(Joi.string(), Joi.number())
    ).default({}),
    donneesEcriture: Joi.object({
      dateEcriture: Joi.date().iso().allow('', null),
      libelle: Joi.string().max(255).allow('', null),
      journalCode: Joi.string().max(10).allow('', null),
      societeId: Joi.string().uuid().allow('', null),
      reference: Joi.string().max(50).allow('', null),
      pieceJustificative: Joi.string().max(100).allow('', null)
    }).default({}),
    creerEcriture: Joi.boolean().default(false)
  })
};

/**
 * @swagger
 * components:
 *   schemas:
 *     Template:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         nom:
 *           type: string
 *         description:
 *           type: string
 *         libelle:
 *           type: string
 *         categorie:
 *           type: string
 *           enum: [VENTE, ACHAT, BANQUE, CAISSE, PAIE, AMORTISSEMENT, PROVISION, AUTRE]
 *         journalCode:
 *           type: string
 *         actif:
 *           type: boolean
 *         public:
 *           type: boolean
 *         parametresVariables:
 *           type: array
 *           items:
 *             type: string
 *         nombreLignes:
 *           type: integer
 */

/**
 * @swagger
 * /api/v1/templates:
 *   get:
 *     summary: Obtient les templates avec filtres
 *     tags: [Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: categorie
 *         schema:
 *           type: string
 *           enum: [VENTE, ACHAT, BANQUE, CAISSE, PAIE, AMORTISSEMENT, PROVISION, AUTRE]
 *         description: Catégorie de template
 *       - in: query
 *         name: journalCode
 *         schema:
 *           type: string
 *         description: Code du journal
 *       - in: query
 *         name: recherche
 *         schema:
 *           type: string
 *         description: Recherche textuelle
 *       - in: query
 *         name: actif
 *         schema:
 *           type: boolean
 *         description: Templates actifs uniquement
 *       - in: query
 *         name: public
 *         schema:
 *           type: boolean
 *         description: Templates publics uniquement
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Numéro de page
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Nombre d'éléments par page
 *     responses:
 *       200:
 *         description: Templates récupérés avec succès
 *       400:
 *         description: ID société manquant
 */
router.get('/',
  protect,
  templateController.getTemplates.bind(templateController)
);

/**
 * @swagger
 * /api/v1/templates/categories:
 *   get:
 *     summary: Obtient les catégories de templates disponibles
 *     tags: [Templates]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Catégories récupérées avec succès
 */
router.get('/categories',
  protect,
  templateController.getCategories.bind(templateController)
);

/**
 * @swagger
 * /api/v1/templates:
 *   post:
 *     summary: Crée un nouveau template
 *     tags: [Templates]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               donnees:
 *                 type: object
 *                 properties:
 *                   nom:
 *                     type: string
 *                   description:
 *                     type: string
 *                   libelle:
 *                     type: string
 *                   journalCode:
 *                     type: string
 *                   categorie:
 *                     type: string
 *                   societeId:
 *                     type: string
 *                     format: uuid
 *                   public:
 *                     type: boolean
 *               lignes:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     compteNumero:
 *                       type: string
 *                     libelle:
 *                       type: string
 *                     sens:
 *                       type: string
 *                       enum: [DEBIT, CREDIT, VARIABLE]
 *                     montantFixe:
 *                       type: number
 *                     formuleMontant:
 *                       type: string
 *                     ordre:
 *                       type: integer
 *     responses:
 *       201:
 *         description: Template créé avec succès
 *       400:
 *         description: Données invalides
 */
router.post('/',
  protect,
  validate(schemas.creerTemplate),
  templateController.creerTemplate.bind(templateController)
);

/**
 * @swagger
 * /api/v1/templates/depuis-ecriture/{ecritureId}:
 *   post:
 *     summary: Crée un template à partir d'une écriture existante
 *     tags: [Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: ecritureId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'écriture source
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               nom:
 *                 type: string
 *               description:
 *                 type: string
 *               categorie:
 *                 type: string
 *               public:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Template créé depuis l'écriture avec succès
 *       404:
 *         description: Écriture non trouvée
 */
router.post('/depuis-ecriture/:ecritureId',
  protect,
  validate(schemas.creerDepuisEcriture),
  templateController.creerTemplateDepuisEcriture.bind(templateController)
);

/**
 * @swagger
 * /api/v1/templates/{id}:
 *   get:
 *     summary: Obtient un template par son ID
 *     tags: [Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID du template
 *     responses:
 *       200:
 *         description: Template récupéré avec succès
 *       404:
 *         description: Template non trouvé
 */
router.get('/:id',
  protect,
  templateController.getTemplateById.bind(templateController)
);

/**
 * @swagger
 * /api/v1/templates/{id}/appliquer:
 *   post:
 *     summary: Applique un template pour créer une écriture
 *     tags: [Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID du template
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               parametres:
 *                 type: object
 *                 description: Valeurs des paramètres variables
 *               donneesEcriture:
 *                 type: object
 *                 properties:
 *                   dateEcriture:
 *                     type: string
 *                     format: date
 *                   libelle:
 *                     type: string
 *                   reference:
 *                     type: string
 *               creerEcriture:
 *                 type: boolean
 *                 description: Créer directement l'écriture
 *     responses:
 *       200:
 *         description: Template appliqué avec succès
 *       404:
 *         description: Template non trouvé
 */
router.post('/:id/appliquer',
  protect,
  validate(schemas.appliquerTemplate),
  templateController.appliquerTemplate.bind(templateController)
);

/**
 * @swagger
 * /api/v1/templates/{id}:
 *   delete:
 *     summary: Supprime un template
 *     tags: [Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID du template
 *     responses:
 *       200:
 *         description: Template supprimé avec succès
 *       404:
 *         description: Template non trouvé
 */
router.delete('/:id',
  protect,
  templateController.supprimerTemplate.bind(templateController)
);

module.exports = router;
