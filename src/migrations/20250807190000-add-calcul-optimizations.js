'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('Création des optimisations pour les calculs...');

    // Fonction utilitaire pour créer des index de manière sécurisée
    const addIndexSafely = async (tableName, columns, options = {}) => {
      try {
        await queryInterface.addIndex(tableName, columns, options);
        console.log(`Index ${options.name || 'sans nom'} créé avec succès`);
      } catch (error) {
        console.log(`Index ${options.name || 'sans nom'} existe déjà ou erreur: ${error.message}`);
      }
    };

    // ==========================================
    // INDEX OPTIMISÉS POUR LES CALCULS
    // ==========================================

    // Index composites pour les calculs de soldes
    await addIndexSafely('ligne_ecritures', ['compte_numero', 'debit'], {
      name: 'idx_ligne_compte_debit'
    });

    await addIndexSafely('ligne_ecritures', ['compte_numero', 'credit'], {
      name: 'idx_ligne_compte_credit'
    });

    // Index pour les calculs par période avec société
    await addIndexSafely('ecriture_comptables', 
      ['societe_id', 'date_ecriture', 'statut'], {
      name: 'idx_ecriture_societe_date_statut'
    });

    // Index pour les calculs par exercice
    await addIndexSafely('ecriture_comptables', 
      ['exercice_id', 'statut', 'date_ecriture'], {
      name: 'idx_ecriture_exercice_statut_date'
    });

    // Index pour les calculs par journal
    await addIndexSafely('ecriture_comptables', 
      ['journal_code', 'date_ecriture', 'statut'], {
      name: 'idx_ecriture_journal_date_statut'
    });

    // Index pour les comptes par classe (balance générale)
    await addIndexSafely('compte_comptables', 
      ['classe', 'nature', 'societe_id'], {
      name: 'idx_compte_classe_nature_societe'
    });

    // Index pour les comptes auxiliaires (balance auxiliaire)
    await addIndexSafely('compte_comptables', 
      ['numero', 'nature'], {
      name: 'idx_compte_numero_nature'
    });

    // ==========================================
    // VUES MATÉRIALISÉES POUR LES CALCULS FRÉQUENTS
    // ==========================================

    // Vue matérialisée pour les soldes de comptes par mois
    try {
      await queryInterface.sequelize.query(`
        CREATE MATERIALIZED VIEW IF NOT EXISTS vue_soldes_mensuels AS
        SELECT 
          le.compte_numero,
          ec.societe_id,
          ec.exercice_id,
          DATE_TRUNC('month', ec.date_ecriture) as mois,
          SUM(le.debit) as total_debit,
          SUM(le.credit) as total_credit,
          SUM(le.debit) - SUM(le.credit) as solde,
          COUNT(*) as nombre_lignes
        FROM ligne_ecritures le
        JOIN ecriture_comptables ec ON le.ecriture_id = ec.id
        WHERE ec.statut = 'VALIDEE'
        GROUP BY le.compte_numero, ec.societe_id, ec.exercice_id, DATE_TRUNC('month', ec.date_ecriture)
      `);

      // Index sur la vue matérialisée
      await queryInterface.sequelize.query(`
        CREATE INDEX IF NOT EXISTS idx_vue_soldes_mensuels_compte_mois 
        ON vue_soldes_mensuels (compte_numero, mois)
      `);

      await queryInterface.sequelize.query(`
        CREATE INDEX IF NOT EXISTS idx_vue_soldes_mensuels_societe_exercice 
        ON vue_soldes_mensuels (societe_id, exercice_id)
      `);

      console.log('Vue matérialisée vue_soldes_mensuels créée avec succès');
    } catch (error) {
      console.log(`Vue matérialisée vue_soldes_mensuels existe déjà ou erreur: ${error.message}`);
    }

    // Vue matérialisée pour les totaux par journal et mois
    try {
      await queryInterface.sequelize.query(`
        CREATE MATERIALIZED VIEW IF NOT EXISTS vue_totaux_journaux_mensuels AS
        SELECT 
          ec.journal_code,
          ec.societe_id,
          ec.exercice_id,
          DATE_TRUNC('month', ec.date_ecriture) as mois,
          SUM(le.debit) as total_debit,
          SUM(le.credit) as total_credit,
          COUNT(DISTINCT ec.id) as nombre_ecritures,
          COUNT(le.id) as nombre_lignes
        FROM ecriture_comptables ec
        JOIN ligne_ecritures le ON ec.id = le.ecriture_id
        WHERE ec.statut = 'VALIDEE'
        GROUP BY ec.journal_code, ec.societe_id, ec.exercice_id, DATE_TRUNC('month', ec.date_ecriture)
      `);

      // Index sur la vue matérialisée
      await queryInterface.sequelize.query(`
        CREATE INDEX IF NOT EXISTS idx_vue_totaux_journaux_journal_mois 
        ON vue_totaux_journaux_mensuels (journal_code, mois)
      `);

      console.log('Vue matérialisée vue_totaux_journaux_mensuels créée avec succès');
    } catch (error) {
      console.log(`Vue matérialisée vue_totaux_journaux_mensuels existe déjà ou erreur: ${error.message}`);
    }

    // Vue matérialisée pour les balances par classe
    try {
      await queryInterface.sequelize.query(`
        CREATE MATERIALIZED VIEW IF NOT EXISTS vue_balance_classes AS
        SELECT 
          cc.classe,
          cc.societe_id,
          ec.exercice_id,
          DATE_TRUNC('month', ec.date_ecriture) as mois,
          SUM(le.debit) as total_debit,
          SUM(le.credit) as total_credit,
          COUNT(DISTINCT le.compte_numero) as nombre_comptes,
          COUNT(le.id) as nombre_lignes
        FROM ligne_ecritures le
        JOIN ecriture_comptables ec ON le.ecriture_id = ec.id
        JOIN compte_comptables cc ON le.compte_numero = cc.numero
        WHERE ec.statut = 'VALIDEE'
        GROUP BY cc.classe, cc.societe_id, ec.exercice_id, DATE_TRUNC('month', ec.date_ecriture)
      `);

      // Index sur la vue matérialisée
      await queryInterface.sequelize.query(`
        CREATE INDEX IF NOT EXISTS idx_vue_balance_classes_classe_mois 
        ON vue_balance_classes (classe, mois)
      `);

      console.log('Vue matérialisée vue_balance_classes créée avec succès');
    } catch (error) {
      console.log(`Vue matérialisée vue_balance_classes existe déjà ou erreur: ${error.message}`);
    }

    // ==========================================
    // FONCTIONS STOCKÉES POUR LES CALCULS
    // ==========================================

    // Fonction pour rafraîchir toutes les vues matérialisées
    try {
      await queryInterface.sequelize.query(`
        CREATE OR REPLACE FUNCTION rafraichir_vues_calculs()
        RETURNS void AS $$
        BEGIN
          REFRESH MATERIALIZED VIEW CONCURRENTLY vue_soldes_mensuels;
          REFRESH MATERIALIZED VIEW CONCURRENTLY vue_totaux_journaux_mensuels;
          REFRESH MATERIALIZED VIEW CONCURRENTLY vue_balance_classes;
        END;
        $$ LANGUAGE plpgsql;
      `);

      console.log('Fonction rafraichir_vues_calculs créée avec succès');
    } catch (error) {
      console.log(`Fonction rafraichir_vues_calculs existe déjà ou erreur: ${error.message}`);
    }

    // Fonction pour calculer rapidement le solde d'un compte
    try {
      await queryInterface.sequelize.query(`
        CREATE OR REPLACE FUNCTION calculer_solde_compte_rapide(
          p_compte_numero VARCHAR(10),
          p_date_fin DATE,
          p_societe_id UUID DEFAULT NULL,
          p_exercice_id UUID DEFAULT NULL
        )
        RETURNS TABLE(
          total_debit DECIMAL(15,2),
          total_credit DECIMAL(15,2),
          solde DECIMAL(15,2),
          nombre_lignes BIGINT
        ) AS $$
        BEGIN
          RETURN QUERY
          SELECT 
            COALESCE(SUM(le.debit), 0)::DECIMAL(15,2) as total_debit,
            COALESCE(SUM(le.credit), 0)::DECIMAL(15,2) as total_credit,
            COALESCE(SUM(le.debit) - SUM(le.credit), 0)::DECIMAL(15,2) as solde,
            COUNT(le.id) as nombre_lignes
          FROM ligne_ecritures le
          JOIN ecriture_comptables ec ON le.ecriture_id = ec.id
          WHERE le.compte_numero = p_compte_numero
            AND ec.date_ecriture <= p_date_fin
            AND ec.statut = 'VALIDEE'
            AND (p_societe_id IS NULL OR ec.societe_id = p_societe_id)
            AND (p_exercice_id IS NULL OR ec.exercice_id = p_exercice_id);
        END;
        $$ LANGUAGE plpgsql;
      `);

      console.log('Fonction calculer_solde_compte_rapide créée avec succès');
    } catch (error) {
      console.log(`Fonction calculer_solde_compte_rapide existe déjà ou erreur: ${error.message}`);
    }

    // ==========================================
    // TRIGGERS POUR MAINTENIR LES VUES À JOUR
    // ==========================================

    // Trigger pour rafraîchir les vues quand des écritures sont validées
    try {
      await queryInterface.sequelize.query(`
        CREATE OR REPLACE FUNCTION trigger_rafraichir_vues_calculs()
        RETURNS trigger AS $$
        BEGIN
          -- Programmer le rafraîchissement des vues (asynchrone)
          PERFORM pg_notify('rafraichir_vues_calculs', '');
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `);

      await queryInterface.sequelize.query(`
        DROP TRIGGER IF EXISTS trigger_ecriture_validee ON ecriture_comptables;
        CREATE TRIGGER trigger_ecriture_validee
          AFTER UPDATE OF statut ON ecriture_comptables
          FOR EACH ROW
          WHEN (NEW.statut = 'VALIDEE' AND OLD.statut != 'VALIDEE')
          EXECUTE FUNCTION trigger_rafraichir_vues_calculs();
      `);

      console.log('Triggers pour les vues matérialisées créés avec succès');
    } catch (error) {
      console.log(`Triggers existent déjà ou erreur: ${error.message}`);
    }

    console.log('Optimisations pour les calculs créées avec succès');
  },

  async down(queryInterface, Sequelize) {
    console.log('Suppression des optimisations pour les calculs...');

    // Supprimer les triggers
    try {
      await queryInterface.sequelize.query('DROP TRIGGER IF EXISTS trigger_ecriture_validee ON ecriture_comptables');
      await queryInterface.sequelize.query('DROP FUNCTION IF EXISTS trigger_rafraichir_vues_calculs()');
    } catch (error) {
      console.log(`Erreur lors de la suppression des triggers: ${error.message}`);
    }

    // Supprimer les fonctions
    try {
      await queryInterface.sequelize.query('DROP FUNCTION IF EXISTS calculer_solde_compte_rapide(VARCHAR, DATE, UUID, UUID)');
      await queryInterface.sequelize.query('DROP FUNCTION IF EXISTS rafraichir_vues_calculs()');
    } catch (error) {
      console.log(`Erreur lors de la suppression des fonctions: ${error.message}`);
    }

    // Supprimer les vues matérialisées
    try {
      await queryInterface.sequelize.query('DROP MATERIALIZED VIEW IF EXISTS vue_balance_classes');
      await queryInterface.sequelize.query('DROP MATERIALIZED VIEW IF EXISTS vue_totaux_journaux_mensuels');
      await queryInterface.sequelize.query('DROP MATERIALIZED VIEW IF EXISTS vue_soldes_mensuels');
    } catch (error) {
      console.log(`Erreur lors de la suppression des vues: ${error.message}`);
    }

    // Supprimer les index
    const indexesToDrop = [
      'idx_ligne_compte_debit',
      'idx_ligne_compte_credit',
      'idx_ecriture_societe_date_statut',
      'idx_ecriture_exercice_statut_date',
      'idx_ecriture_journal_date_statut',
      'idx_compte_classe_nature_societe',
      'idx_compte_numero_nature'
    ];

    for (const indexName of indexesToDrop) {
      try {
        await queryInterface.removeIndex('ligne_ecritures', indexName);
      } catch (error) {
        try {
          await queryInterface.removeIndex('ecriture_comptables', indexName);
        } catch (error2) {
          try {
            await queryInterface.removeIndex('compte_comptables', indexName);
          } catch (error3) {
            console.log(`Index ${indexName} non trouvé ou erreur: ${error3.message}`);
          }
        }
      }
    }

    console.log('Optimisations pour les calculs supprimées');
  }
};
