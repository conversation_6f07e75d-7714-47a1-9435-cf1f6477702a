'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('parties', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      code: {
        type: Sequelize.STRING(20),
        allowNull: false
      },
      nom: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      type: {
        type: Sequelize.ENUM('CLIENT', 'FOURNISSEUR', 'CLIENT_FOURNISSEUR'),
        allowNull: false
      },
      civilite: {
        type: Sequelize.ENUM('M', 'MME', 'MLLE', 'DR', 'PROF', 'SARL', 'SA', 'SAS', 'EURL', 'SNC', 'GIE'),
        allowNull: true
      },
      adresse: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      ville: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      pays: {
        type: Sequelize.STRING(100),
        allowNull: true,
        defaultValue: 'Côte d\'Ivoire'
      },
      telephone: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      compte_comptable: {
        type: Sequelize.STRING(10),
        allowNull: true,
        references: {
          model: 'compte_comptables',
          key: 'numero'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      conditions_paiement: {
        type: Sequelize.STRING(100),
        allowNull: true,
        defaultValue: 'Comptant'
      },
      plafond_credit: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        defaultValue: 0.00
      },
      numero_contribuable: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      assujetti_tva: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      actif: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      societe_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'societes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Ajout des index pour optimiser les performances
    await queryInterface.addIndex('parties', {
      fields: ['societe_id', 'code'],
      unique: true,
      name: 'parties_societe_code_unique'
    });

    await queryInterface.addIndex('parties', {
      fields: ['societe_id', 'type'],
      name: 'parties_societe_type_idx'
    });

    await queryInterface.addIndex('parties', {
      fields: ['nom'],
      name: 'parties_nom_idx'
    });

    await queryInterface.addIndex('parties', {
      fields: ['compte_comptable'],
      name: 'parties_compte_comptable_idx'
    });

    await queryInterface.addIndex('parties', {
      fields: ['email'],
      name: 'parties_email_idx'
    });

    await queryInterface.addIndex('parties', {
      fields: ['numero_contribuable'],
      name: 'parties_numero_contribuable_idx'
    });
  },

  async down(queryInterface, Sequelize) {
    // Suppression des index
    await queryInterface.removeIndex('parties', 'parties_societe_code_unique');
    await queryInterface.removeIndex('parties', 'parties_societe_type_idx');
    await queryInterface.removeIndex('parties', 'parties_nom_idx');
    await queryInterface.removeIndex('parties', 'parties_compte_comptable_idx');
    await queryInterface.removeIndex('parties', 'parties_email_idx');
    await queryInterface.removeIndex('parties', 'parties_numero_contribuable_idx');

    // Suppression de la table
    await queryInterface.dropTable('parties');
  }
};