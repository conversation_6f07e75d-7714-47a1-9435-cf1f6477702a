'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');
const MovementService = require('../services/movementService');
const EntryValidationService = require('../services/entryValidationService');

/**
 * Controller pour la gestion des mouvements comptables automatiques
 * Conforme aux normes SYSCOHADA - Phase 2
 */
class MovementController {
  constructor() {
    this.movementService = null;
    this.entryValidationService = null;
  }

  /**
   * Initialise les services avec les modèles
   * @param {Object} models - Modèles Sequelize
   */
  init(models) {
    this.movementService = new MovementService(models);
    this.entryValidationService = new EntryValidationService(models);
  }

  /**
   * Génère une écriture de vente automatique
   * POST /api/v1/movements/vente
   */
  async genererEcritureVente(req, res, next) {
    try {
      const { 
        societeId,
        clientId,
        montantHT,
        tauxTva = 18,
        dateVente,
        numeroFacture,
        libelle,
        compteVente = '701100', // Ventes de marchandises
        journalCode = 'VT' // Journal des ventes
      } = req.body;

      // Validation des données requises
      if (!societeId || !clientId || !montantHT || !dateVente || !numeroFacture) {
        return res.status(400).json({
          success: false,
          message: 'Données obligatoires manquantes',
          required: ['societeId', 'clientId', 'montantHT', 'dateVente', 'numeroFacture'],
          code: 'DONNEES_MANQUANTES'
        });
      }

      // Génération de l'écriture de vente
      const resultat = await this.movementService.genererEcritureVente({
        societeId,
        clientId,
        montantHT: parseFloat(montantHT),
        tauxTva: parseFloat(tauxTva),
        dateVente: new Date(dateVente),
        numeroFacture,
        libelle: libelle || `Vente facture ${numeroFacture}`,
        compteVente,
        journalCode,
        utilisateurCreation: req.user?.id
      });

      logger.info('Écriture de vente générée automatiquement', {
        ecritureId: resultat.ecriture.id,
        numeroFacture,
        montantHT,
        clientId,
        utilisateur: req.user?.id
      });

      res.status(201).json({
        success: true,
        message: 'Écriture de vente générée avec succès',
        data: {
          ecriture: resultat.ecriture,
          lignes: resultat.lignes,
          calculs: resultat.calculs,
          validation: resultat.validation
        }
      });

    } catch (error) {
      logger.error('Erreur génération écriture vente', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère une écriture d'achat automatique
   * POST /api/v1/movements/achat
   */
  async genererEcritureAchat(req, res, next) {
    try {
      const { 
        societeId,
        fournisseurId,
        montantHT,
        tauxTva = 18,
        dateAchat,
        numeroFacture,
        libelle,
        compteAchat = '601100', // Achats de marchandises
        journalCode = 'AC' // Journal des achats
      } = req.body;

      // Validation des données requises
      if (!societeId || !fournisseurId || !montantHT || !dateAchat || !numeroFacture) {
        return res.status(400).json({
          success: false,
          message: 'Données obligatoires manquantes',
          required: ['societeId', 'fournisseurId', 'montantHT', 'dateAchat', 'numeroFacture'],
          code: 'DONNEES_MANQUANTES'
        });
      }

      // Génération de l'écriture d'achat
      const resultat = await this.movementService.genererEcritureAchat({
        societeId,
        fournisseurId,
        montantHT: parseFloat(montantHT),
        tauxTva: parseFloat(tauxTva),
        dateAchat: new Date(dateAchat),
        numeroFacture,
        libelle: libelle || `Achat facture ${numeroFacture}`,
        compteAchat,
        journalCode,
        utilisateurCreation: req.user?.id
      });

      logger.info('Écriture d\'achat générée automatiquement', {
        ecritureId: resultat.ecriture.id,
        numeroFacture,
        montantHT,
        fournisseurId,
        utilisateur: req.user?.id
      });

      res.status(201).json({
        success: true,
        message: 'Écriture d\'achat générée avec succès',
        data: {
          ecriture: resultat.ecriture,
          lignes: resultat.lignes,
          calculs: resultat.calculs,
          validation: resultat.validation
        }
      });

    } catch (error) {
      logger.error('Erreur génération écriture achat', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère une écriture de règlement client
   * POST /api/v1/movements/reglement-client
   */
  async genererReglementClient(req, res, next) {
    try {
      const { 
        societeId,
        clientId,
        montant,
        dateReglement,
        modeReglement = 'VIREMENT',
        numeroReglement,
        libelle,
        compteTresorerie = '512100', // Banque
        journalCode = 'BQ' // Journal de banque
      } = req.body;

      // Validation des données requises
      if (!societeId || !clientId || !montant || !dateReglement) {
        return res.status(400).json({
          success: false,
          message: 'Données obligatoires manquantes',
          required: ['societeId', 'clientId', 'montant', 'dateReglement'],
          code: 'DONNEES_MANQUANTES'
        });
      }

      // Génération de l'écriture de règlement
      const resultat = await this.movementService.genererReglementClient({
        societeId,
        clientId,
        montant: parseFloat(montant),
        dateReglement: new Date(dateReglement),
        modeReglement,
        numeroReglement,
        libelle: libelle || `Règlement client ${numeroReglement || ''}`,
        compteTresorerie,
        journalCode,
        utilisateurCreation: req.user?.id
      });

      logger.info('Écriture de règlement client générée', {
        ecritureId: resultat.ecriture.id,
        montant,
        clientId,
        modeReglement,
        utilisateur: req.user?.id
      });

      res.status(201).json({
        success: true,
        message: 'Écriture de règlement client générée avec succès',
        data: {
          ecriture: resultat.ecriture,
          lignes: resultat.lignes,
          lettrage: resultat.lettrage,
          validation: resultat.validation
        }
      });

    } catch (error) {
      logger.error('Erreur génération règlement client', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère une écriture de règlement fournisseur
   * POST /api/v1/movements/reglement-fournisseur
   */
  async genererReglementFournisseur(req, res, next) {
    try {
      const { 
        societeId,
        fournisseurId,
        montant,
        dateReglement,
        modeReglement = 'VIREMENT',
        numeroReglement,
        libelle,
        compteTresorerie = '512100', // Banque
        journalCode = 'BQ' // Journal de banque
      } = req.body;

      // Validation des données requises
      if (!societeId || !fournisseurId || !montant || !dateReglement) {
        return res.status(400).json({
          success: false,
          message: 'Données obligatoires manquantes',
          required: ['societeId', 'fournisseurId', 'montant', 'dateReglement'],
          code: 'DONNEES_MANQUANTES'
        });
      }

      // Génération de l'écriture de règlement
      const resultat = await this.movementService.genererReglementFournisseur({
        societeId,
        fournisseurId,
        montant: parseFloat(montant),
        dateReglement: new Date(dateReglement),
        modeReglement,
        numeroReglement,
        libelle: libelle || `Règlement fournisseur ${numeroReglement || ''}`,
        compteTresorerie,
        journalCode,
        utilisateurCreation: req.user?.id
      });

      logger.info('Écriture de règlement fournisseur générée', {
        ecritureId: resultat.ecriture.id,
        montant,
        fournisseurId,
        modeReglement,
        utilisateur: req.user?.id
      });

      res.status(201).json({
        success: true,
        message: 'Écriture de règlement fournisseur générée avec succès',
        data: {
          ecriture: resultat.ecriture,
          lignes: resultat.lignes,
          lettrage: resultat.lettrage,
          validation: resultat.validation
        }
      });

    } catch (error) {
      logger.error('Erreur génération règlement fournisseur', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère des écritures d'amortissement
   * POST /api/v1/movements/amortissement
   */
  async genererAmortissement(req, res, next) {
    try {
      const { 
        societeId,
        immobilisationId,
        montantAmortissement,
        dateAmortissement,
        libelle,
        compteImmobilisation,
        compteAmortissement,
        compteDotation = '681100', // Dotations aux amortissements
        journalCode = 'OD' // Journal des opérations diverses
      } = req.body;

      // Validation des données requises
      if (!societeId || !montantAmortissement || !dateAmortissement || !compteImmobilisation || !compteAmortissement) {
        return res.status(400).json({
          success: false,
          message: 'Données obligatoires manquantes',
          required: ['societeId', 'montantAmortissement', 'dateAmortissement', 'compteImmobilisation', 'compteAmortissement'],
          code: 'DONNEES_MANQUANTES'
        });
      }

      // Génération de l'écriture d'amortissement
      const resultat = await this.movementService.genererAmortissement({
        societeId,
        immobilisationId,
        montantAmortissement: parseFloat(montantAmortissement),
        dateAmortissement: new Date(dateAmortissement),
        libelle: libelle || `Amortissement ${dateAmortissement}`,
        compteImmobilisation,
        compteAmortissement,
        compteDotation,
        journalCode,
        utilisateurCreation: req.user?.id
      });

      logger.info('Écriture d\'amortissement générée', {
        ecritureId: resultat.ecriture.id,
        montantAmortissement,
        compteImmobilisation,
        utilisateur: req.user?.id
      });

      res.status(201).json({
        success: true,
        message: 'Écriture d\'amortissement générée avec succès',
        data: {
          ecriture: resultat.ecriture,
          lignes: resultat.lignes,
          calculs: resultat.calculs,
          validation: resultat.validation
        }
      });

    } catch (error) {
      logger.error('Erreur génération amortissement', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère des écritures de paie
   * POST /api/v1/movements/paie
   */
  async genererEcriturePaie(req, res, next) {
    try {
      const { 
        societeId,
        periode,
        salaires,
        charges,
        dateVersement,
        libelle,
        journalCode = 'PA' // Journal de paie
      } = req.body;

      // Validation des données requises
      if (!societeId || !periode || !salaires || !dateVersement) {
        return res.status(400).json({
          success: false,
          message: 'Données obligatoires manquantes',
          required: ['societeId', 'periode', 'salaires', 'dateVersement'],
          code: 'DONNEES_MANQUANTES'
        });
      }

      // Génération des écritures de paie
      const resultat = await this.movementService.genererEcriturePaie({
        societeId,
        periode,
        salaires,
        charges: charges || {},
        dateVersement: new Date(dateVersement),
        libelle: libelle || `Paie ${periode}`,
        journalCode,
        utilisateurCreation: req.user?.id
      });

      logger.info('Écritures de paie générées', {
        nombreEcritures: resultat.ecritures.length,
        periode,
        totalSalaires: resultat.totaux.salaires,
        utilisateur: req.user?.id
      });

      res.status(201).json({
        success: true,
        message: 'Écritures de paie générées avec succès',
        data: {
          ecritures: resultat.ecritures,
          totaux: resultat.totaux,
          ventilation: resultat.ventilation,
          validation: resultat.validation
        }
      });

    } catch (error) {
      logger.error('Erreur génération écritures paie', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Obtient les templates d'écritures disponibles
   * GET /api/v1/movements/templates
   */
  async getTemplatesEcritures(req, res, next) {
    try {
      const { societeId } = req.query;

      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'ID société obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      const templates = await this.movementService.getTemplatesEcritures(societeId);

      res.json({
        success: true,
        message: 'Templates d\'écritures récupérés avec succès',
        data: {
          templates,
          total: templates.length
        }
      });

    } catch (error) {
      logger.error('Erreur récupération templates', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Valide un mouvement avant génération
   * POST /api/v1/movements/validate
   */
  async validateMovement(req, res, next) {
    try {
      const { type, donnees } = req.body;

      if (!type || !donnees) {
        return res.status(400).json({
          success: false,
          message: 'Type de mouvement et données obligatoires',
          code: 'DONNEES_MANQUANTES'
        });
      }

      const validation = await this.movementService.validateMovement(type, donnees);

      res.json({
        success: true,
        message: 'Validation du mouvement effectuée',
        data: {
          validation,
          peutGenerer: validation.valide,
          typeMovement: type
        }
      });

    } catch (error) {
      logger.error('Erreur validation mouvement', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }
}

module.exports = new MovementController();