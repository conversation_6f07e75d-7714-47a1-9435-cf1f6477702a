'use strict';

const CalculService = require('../services/calculService');
const logger = require('../config/logger');
const { ValidationError, NotFoundError } = require('../middleware/errorHandler');

/**
 * Contrôleur pour les calculs comptables et gestion des soldes
 */
class CalculController {
  constructor() {
    this.calculService = new CalculService();
  }

  /**
   * Calcule le solde d'un compte
   * GET /api/v1/calculs/solde/:compteNumero
   */
  async calculerSoldeCompte(req, res, next) {
    try {
      const { compteNumero } = req.params;
      const {
        dateDebut,
        dateFin,
        societeId,
        exerciceId,
        includeNonValidees = false,
        useCache = true
      } = req.query;

      // Validation des paramètres
      if (!dateFin) {
        return res.status(400).json({
          success: false,
          message: 'La date de fin est obligatoire',
          code: 'DATE_FIN_MANQUANTE'
        });
      }

      const options = {
        societeId,
        exerciceId,
        includeNonValidees: includeNonValidees === 'true',
        useCache: useCache !== 'false'
      };

      const solde = await this.calculService.calculerSoldeCompte(
        compteNumero,
        dateDebut ? new Date(dateDebut) : null,
        new Date(dateFin),
        options
      );

      logger.info('Solde calculé via API', {
        utilisateur: req.user?.id,
        compteNumero,
        solde: solde.solde,
        sensActuel: solde.sensActuel
      });

      res.json({
        success: true,
        data: {
          solde
        }
      });

    } catch (error) {
      logger.error('Erreur lors du calcul du solde via API', {
        utilisateur: req.user?.id,
        compteNumero: req.params.compteNumero,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Calcule les soldes progressifs d'un compte
   * GET /api/v1/calculs/soldes-progressifs/:compteNumero
   */
  async calculerSoldesProgressifs(req, res, next) {
    try {
      const { compteNumero } = req.params;
      const {
        dateDebut,
        dateFin,
        societeId,
        exerciceId,
        includeNonValidees = false,
        groupeParMois = false
      } = req.query;

      // Validation des paramètres
      if (!dateDebut || !dateFin) {
        return res.status(400).json({
          success: false,
          message: 'Les dates de début et de fin sont obligatoires',
          code: 'DATES_MANQUANTES'
        });
      }

      const options = {
        societeId,
        exerciceId,
        includeNonValidees: includeNonValidees === 'true',
        groupeParMois: groupeParMois === 'true'
      };

      const soldesProgressifs = await this.calculService.calculerSoldesProgressifs(
        compteNumero,
        new Date(dateDebut),
        new Date(dateFin),
        options
      );

      logger.info('Soldes progressifs calculés via API', {
        utilisateur: req.user?.id,
        compteNumero,
        nombrePeriodes: soldesProgressifs.soldesProgressifs.length
      });

      res.json({
        success: true,
        data: {
          soldesProgressifs
        }
      });

    } catch (error) {
      logger.error('Erreur lors du calcul des soldes progressifs via API', {
        utilisateur: req.user?.id,
        compteNumero: req.params.compteNumero,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Calcule les totaux d'un journal
   * GET /api/v1/calculs/totaux-journal/:journalCode
   */
  async calculerTotauxJournal(req, res, next) {
    try {
      const { journalCode } = req.params;
      const {
        dateDebut,
        dateFin,
        societeId,
        exerciceId,
        includeNonValidees = false,
        groupeParCompte = false
      } = req.query;

      // Validation des paramètres
      if (!dateDebut || !dateFin) {
        return res.status(400).json({
          success: false,
          message: 'Les dates de début et de fin sont obligatoires',
          code: 'DATES_MANQUANTES'
        });
      }

      const options = {
        societeId,
        exerciceId,
        includeNonValidees: includeNonValidees === 'true',
        groupeParCompte: groupeParCompte === 'true'
      };

      const totauxJournal = await this.calculService.calculerTotauxJournal(
        journalCode,
        new Date(dateDebut),
        new Date(dateFin),
        options
      );

      logger.info('Totaux journal calculés via API', {
        utilisateur: req.user?.id,
        journalCode,
        totalDebit: totauxJournal.totaux.totalDebit,
        totalCredit: totauxJournal.totaux.totalCredit
      });

      res.json({
        success: true,
        data: {
          totauxJournal
        }
      });

    } catch (error) {
      logger.error('Erreur lors du calcul des totaux journal via API', {
        utilisateur: req.user?.id,
        journalCode: req.params.journalCode,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Calcule la balance générale
   * GET /api/v1/calculs/balance
   */
  async calculerBalanceGenerale(req, res, next) {
    try {
      const {
        dateDebut,
        dateFin,
        societeId,
        exerciceId,
        includeNonValidees = false,
        niveauDetail = 'TOUS',
        classeComptes,
        seulementAvecMouvement = false
      } = req.query;

      // Validation des paramètres
      if (!dateDebut || !dateFin) {
        return res.status(400).json({
          success: false,
          message: 'Les dates de début et de fin sont obligatoires',
          code: 'DATES_MANQUANTES'
        });
      }

      const options = {
        societeId,
        exerciceId,
        includeNonValidees: includeNonValidees === 'true',
        niveauDetail,
        classeComptes: classeComptes ? classeComptes.split(',').map(c => parseInt(c)) : null,
        seulementAvecMouvement: seulementAvecMouvement === 'true'
      };

      const balanceGenerale = await this.calculService.calculerBalanceGenerale(
        new Date(dateDebut),
        new Date(dateFin),
        options
      );

      logger.info('Balance générale calculée via API', {
        utilisateur: req.user?.id,
        nombreComptes: balanceGenerale.totauxGeneraux.nombreComptes,
        equilibre: balanceGenerale.totauxGeneraux.equilibre
      });

      res.json({
        success: true,
        data: {
          balanceGenerale
        }
      });

    } catch (error) {
      logger.error('Erreur lors du calcul de la balance générale via API', {
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Calcule la balance auxiliaire
   * GET /api/v1/calculs/balance-auxiliaire/:compteCollectif
   */
  async calculerBalanceAuxiliaire(req, res, next) {
    try {
      const { compteCollectif } = req.params;
      const {
        dateDebut,
        dateFin,
        societeId,
        exerciceId,
        includeNonValidees = false,
        seulementAvecMouvement = false
      } = req.query;

      // Validation des paramètres
      if (!dateDebut || !dateFin) {
        return res.status(400).json({
          success: false,
          message: 'Les dates de début et de fin sont obligatoires',
          code: 'DATES_MANQUANTES'
        });
      }

      const options = {
        societeId,
        exerciceId,
        includeNonValidees: includeNonValidees === 'true',
        seulementAvecMouvement: seulementAvecMouvement === 'true'
      };

      const balanceAuxiliaire = await this.calculService.calculerBalanceAuxiliaire(
        compteCollectif,
        new Date(dateDebut),
        new Date(dateFin),
        options
      );

      logger.info('Balance auxiliaire calculée via API', {
        utilisateur: req.user?.id,
        compteCollectif,
        nombreComptes: balanceAuxiliaire.totaux.nombreComptes
      });

      res.json({
        success: true,
        data: {
          balanceAuxiliaire
        }
      });

    } catch (error) {
      logger.error('Erreur lors du calcul de la balance auxiliaire via API', {
        utilisateur: req.user?.id,
        compteCollectif: req.params.compteCollectif,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Calcule les statistiques générales
   * GET /api/v1/calculs/statistiques
   */
  async calculerStatistiquesGenerales(req, res, next) {
    try {
      const {
        dateDebut,
        dateFin,
        societeId
      } = req.query;

      // Validation des paramètres
      if (!dateDebut || !dateFin || !societeId) {
        return res.status(400).json({
          success: false,
          message: 'Les dates de début, de fin et l\'ID de société sont obligatoires',
          code: 'PARAMETRES_MANQUANTS'
        });
      }

      const statistiques = await this.calculService.calculerStatistiquesGenerales(
        societeId,
        new Date(dateDebut),
        new Date(dateFin)
      );

      logger.info('Statistiques générales calculées via API', {
        utilisateur: req.user?.id,
        societeId,
        nombreEcritures: statistiques.ecritures.nombre
      });

      res.json({
        success: true,
        data: {
          statistiques
        }
      });

    } catch (error) {
      logger.error('Erreur lors du calcul des statistiques générales via API', {
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Gère le cache des calculs
   * GET /api/v1/calculs/cache/stats - Statistiques du cache
   * DELETE /api/v1/calculs/cache - Vider le cache
   */
  async gererCache(req, res, next) {
    try {
      if (req.method === 'GET') {
        // Statistiques du cache
        const stats = this.calculService.getStatistiquesCache();
        
        res.json({
          success: true,
          data: {
            cache: stats
          }
        });

      } else if (req.method === 'DELETE') {
        // Vider le cache
        this.calculService.viderCache();
        
        logger.info('Cache des calculs vidé via API', {
          utilisateur: req.user?.id
        });

        res.json({
          success: true,
          message: 'Cache vidé avec succès'
        });
      }

    } catch (error) {
      logger.error('Erreur lors de la gestion du cache via API', {
        utilisateur: req.user?.id,
        method: req.method,
        error: error.message
      });
      next(error);
    }
  }
}

module.exports = new CalculController();
