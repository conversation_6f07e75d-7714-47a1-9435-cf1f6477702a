# Stratégie d'Authentification et Gestion des Droits
## API Comptabilité SYSCOHADA

**Date** : 7 Août 2025  
**Version** : 1.0  
**Statut** : Proposition  

---

## 📋 Table des Matières

1. [Introduction](#introduction)
2. [Mod<PERSON><PERSON> de Sécurité](#modèle-de-sécurité)
3. [Authentification](#authentification)
4. [Gestion des Utilisateurs](#gestion-des-utilisateurs)
5. [Gestion des Rôles et Permissions](#gestion-des-rôles-et-permissions)
6. [Contrôle d'Accès](#contrôle-daccès)
7. [Audit et Traçabilité](#audit-et-traçabilité)
8. [Sécurité des Données](#sécurité-des-données)
9. [Implémentation Technique](#implémentation-technique)
10. [Bonnes Pratiques](#bonnes-pratiques)

---

## Introduction

Ce document définit la stratégie d'authentification et de gestion des droits pour l'API de comptabilité SYSCOHADA. Cette stratégie vise à garantir la sécurité, la confidentialité et l'intégrité des données comptables tout en permettant un accès flexible et granulaire aux différentes fonctionnalités du système.

### Objectifs

- Sécuriser l'accès à l'API et aux données comptables
- Définir un modèle de rôles et permissions adapté aux besoins comptables
- Assurer la traçabilité complète des actions utilisateurs
- Respecter les normes de sécurité et de conformité
- Faciliter l'administration des droits utilisateurs

### Principes Directeurs

- **Principe du moindre privilège** : Chaque utilisateur ne dispose que des droits strictement nécessaires à ses fonctions
- **Séparation des responsabilités** : Distinction claire entre les rôles de saisie, validation et supervision
- **Traçabilité complète** : Toute action est enregistrée avec son auteur et son contexte
- **Défense en profondeur** : Multiples couches de sécurité
- **Simplicité d'administration** : Interface intuitive pour la gestion des droits

---

## Modèle de Sécurité

### Vue d'Ensemble

Le modèle de sécurité s'articule autour de trois concepts fondamentaux :

1. **Utilisateurs** : Personnes physiques accédant au système
2. **Rôles** : Ensembles de permissions prédéfinis correspondant à des fonctions métier
3. **Permissions** : Droits granulaires sur des ressources ou actions spécifiques

### Contextes de Sécurité

La sécurité s'applique dans différents contextes :

- **Société** : Périmètre d'une entité juridique
- **Exercice comptable** : Période comptable définie
- **Journal** : Type de journal comptable
- **Module fonctionnel** : Ensemble cohérent de fonctionnalités (ex: saisie, reporting)

---

## Authentification

### Mécanisme d'Authentification

L'API utilise l'authentification JWT (JSON Web Token) avec les caractéristiques suivantes :

- **Tokens signés** avec algorithme HMAC SHA-256 (HS256)
- **Durée de validité** configurable (par défaut 24h)
- **Refresh tokens** pour renouvellement sans reconnexion
- **Révocation possible** des tokens en cas de compromission

### Processus d'Authentification

1. L'utilisateur s'authentifie avec ses identifiants (email/mot de passe)
2. Le serveur vérifie les identifiants et génère un JWT contenant :
   - Identifiant utilisateur
   - Rôles et permissions de base
   - Date d'expiration
   - Signature cryptographique
3. Le client stocke le token et l'inclut dans l'en-tête `Authorization` de chaque requête
4. Le serveur vérifie la validité du token pour chaque requête

### Sécurité des Mots de Passe

- Stockage avec hachage bcrypt (12 rounds minimum)
- Politique de complexité obligatoire (longueur, caractères spéciaux)
- Expiration et renouvellement périodiques
- Verrouillage temporaire après tentatives échouées

### Multi-facteur (MFA)

Pour les utilisateurs à privilèges élevés (administrateurs, experts-comptables) :
- Authentification à deux facteurs obligatoire
- Options : application TOTP, SMS, email

---

## Gestion des Utilisateurs

### Modèle de Données Utilisateur

```javascript
{
  "id": "uuid",
  "email": "<EMAIL>",
  "nom": "Nom Utilisateur",
  "statut": "ACTIF", // ACTIF, INACTIF, BLOQUÉ
  "dateCreation": "2025-01-01T00:00:00Z",
  "dernièreConnexion": "2025-08-07T09:15:00Z",
  "rôles": ["COMPTABLE", "VALIDATEUR"],
  "sociétés": [
    {
      "societeId": "uuid-societe-1",
      "rôles": ["COMPTABLE"],
      "journaux": ["VT", "AC"]
    },
    {
      "societeId": "uuid-societe-2",
      "rôles": ["VALIDATEUR"],
      "journaux": ["OD"]
    }
  ],
  "préférences": {
    "langue": "fr",
    "fuseauHoraire": "Africa/Dakar"
  }
}
```

### Cycle de Vie Utilisateur

1. **Création** : Par administrateur ou auto-inscription (si activée)
2. **Activation** : Confirmation par email
3. **Attribution des rôles** : Configuration initiale des droits
4. **Utilisation normale** : Accès selon les droits attribués
5. **Modification des droits** : Évolution selon les besoins
6. **Désactivation** : Suspension temporaire
7. **Suppression** : Archivage des données et révocation des accès

---

## Gestion des Rôles et Permissions

### Rôles Prédéfinis

| Rôle | Description | Niveau d'accès |
|------|-------------|----------------|
| **ADMINISTRATEUR** | Gestion complète du système | Tous droits système |
| **EXPERT_COMPTABLE** | Supervision et validation finale | Tous droits comptables |
| **COMPTABLE_SENIOR** | Validation et reporting | Validation, états, paramétrages |
| **COMPTABLE_JUNIOR** | Saisie et opérations courantes | Saisie brouillard, lettrage |
| **AUDITEUR** | Consultation en lecture seule | Lecture seule |
| **DIRECTEUR_FINANCIER** | Tableaux de bord et analyses | Lecture + tableaux de bord |

### Permissions Granulaires

Les permissions sont organisées par catégories :

#### Écritures Comptables
- `ECRITURE_CREER` : Créer des écritures en brouillard
- `ECRITURE_MODIFIER` : Modifier des écritures en brouillard
- `ECRITURE_SUPPRIMER` : Supprimer des écritures en brouillard
- `ECRITURE_VALIDER` : Valider des écritures (passage en définitif)
- `ECRITURE_CONSULTER` : Consulter les écritures

#### Journaux
- `JOURNAL_CONFIGURER` : Configurer les journaux
- `JOURNAL_VT_ACCEDER` : Accéder au journal des ventes
- `JOURNAL_AC_ACCEDER` : Accéder au journal des achats
- `JOURNAL_OD_ACCEDER` : Accéder au journal des opérations diverses
- `JOURNAL_BQ_ACCEDER` : Accéder au journal de banque
- `JOURNAL_CA_ACCEDER` : Accéder au journal de caisse

#### Plan Comptable
- `COMPTE_CREER` : Créer des comptes
- `COMPTE_MODIFIER` : Modifier des comptes
- `COMPTE_SUPPRIMER` : Supprimer des comptes
- `COMPTE_CONSULTER` : Consulter les comptes

#### Lettrage et Rapprochement
- `LETTRAGE_EFFECTUER` : Effectuer des lettrages
- `LETTRAGE_ANNULER` : Annuler des lettrages
- `RAPPROCHEMENT_EFFECTUER` : Effectuer des rapprochements bancaires

#### États et Reporting
- `ETAT_CONSULTER_BASIQUE` : Consulter les états de base (grand livre, balance)
- `ETAT_CONSULTER_AVANCE` : Consulter les états avancés (bilan, résultat)
- `ETAT_EXPORTER` : Exporter les états
- `TABLEAU_BORD_CONSULTER` : Consulter les tableaux de bord

#### Clôture et Périodes
- `PERIODE_OUVRIR` : Ouvrir une période comptable
- `PERIODE_FERMER` : Fermer une période comptable
- `EXERCICE_CLOTURER` : Clôturer un exercice
- `EXERCICE_ROUVRIR` : Rouvrir un exercice clôturé provisoirement

#### Administration
- `UTILISATEUR_GERER` : Gérer les utilisateurs
- `ROLE_GERER` : Gérer les rôles et permissions
- `SOCIETE_GERER` : Gérer les sociétés
- `PARAMETRE_GERER` : Gérer les paramètres système
- `AUDIT_CONSULTER` : Consulter les logs d'audit

### Matrice Rôles-Permissions

| Permission | ADMIN | EXPERT | COMPTABLE_SR | COMPTABLE_JR | AUDITEUR | DIR_FIN |
|------------|-------|--------|--------------|--------------|----------|---------|
| ECRITURE_CREER | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ |
| ECRITURE_MODIFIER | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ |
| ECRITURE_SUPPRIMER | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |
| ECRITURE_VALIDER | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |
| ECRITURE_CONSULTER | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| JOURNAL_CONFIGURER | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |
| JOURNAL_OD_ACCEDER | ✅ | ✅ | ✅ | ❌ | ✅ | ✅ |
| COMPTE_CREER | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |
| LETTRAGE_EFFECTUER | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ |
| ETAT_CONSULTER_AVANCE | ✅ | ✅ | ✅ | ❌ | ✅ | ✅ |
| EXERCICE_CLOTURER | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| UTILISATEUR_GERER | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| AUDIT_CONSULTER | ✅ | ✅ | ❌ | ❌ | ✅ | ❌ |

### Restrictions et Limites

En plus des permissions, des restrictions peuvent être appliquées :

- **Limites de montant** : Montant maximum autorisé par écriture
- **Restrictions temporelles** : Plages horaires autorisées
- **Restrictions géographiques** : Adresses IP ou zones autorisées
- **Quotas** : Nombre maximum d'opérations par période

---

## Contrôle d'Accès

### Middleware d'Autorisation

Le contrôle d'accès est implémenté via un middleware qui :

1. Extrait les informations utilisateur du token JWT
2. Vérifie les permissions requises pour la ressource demandée
3. Applique les règles de contrôle d'accès contextuelles
4. Autorise ou refuse l'accès

### Niveaux de Contrôle

Le contrôle s'effectue à plusieurs niveaux :

- **Niveau route** : Permissions globales requises pour accéder à un endpoint
- **Niveau ressource** : Permissions sur une ressource spécifique (société, journal)
- **Niveau champ** : Permissions sur des champs spécifiques d'une ressource
- **Niveau données** : Filtrage des données selon le contexte utilisateur

### Exemple de Règles d'Accès

```javascript
// Règle pour la route POST /api/v1/ecritures
{
  "route": "POST /api/v1/ecritures",
  "permissions": ["ECRITURE_CREER"],
  "restrictions": [
    {
      "type": "JOURNAL",
      "check": "req.body.journalCode in user.journauxAutorises"
    },
    {
      "type": "MONTANT",
      "check": "req.body.montantTotal <= user.montantMaximum"
    },
    {
      "type": "PERIODE",
      "check": "isExerciceOuvert(req.body.dateEcriture, user.societeId)"
    }
  ]
}
```

### Workflow d'Approbation

Pour certaines opérations sensibles, un workflow d'approbation est mis en place :

1. L'utilisateur initie une action (ex: écriture > 1M XOF)
2. Le système détecte le besoin d'approbation
3. La demande est mise en attente et notifiée aux approbateurs
4. Un utilisateur avec droits d'approbation valide ou rejette
5. L'action est exécutée ou annulée selon la décision

---

## Audit et Traçabilité

### Journalisation Complète

Toutes les actions sont journalisées avec les informations suivantes :

- **Qui** : Identifiant utilisateur, rôle actif
- **Quoi** : Action réalisée, ressource concernée
- **Quand** : Horodatage précis
- **Où** : Adresse IP, appareil
- **Comment** : Contexte technique (API, interface)
- **Avant/Après** : État avant et après modification

### Niveaux de Journalisation

| Niveau | Description | Exemples |
|--------|-------------|----------|
| **SYSTÈME** | Événements système | Démarrage, arrêt, erreurs |
| **SÉCURITÉ** | Événements de sécurité | Connexions, déconnexions, tentatives échouées |
| **AUDIT** | Actions métier | CRUD sur données comptables |
| **MÉTIER** | Événements fonctionnels | Validation écriture, clôture exercice |

### Conservation et Archivage

- Conservation des logs d'audit pendant 10 ans minimum
- Archivage sécurisé et immuable
- Horodatage certifié pour les opérations critiques

### Alertes et Détection

- Détection d'activités suspectes
- Alertes sur opérations sensibles
- Monitoring des tentatives d'accès non autorisés

---

## Sécurité des Données

### Chiffrement

- **En transit** : TLS 1.3 pour toutes les communications
- **Au repos** : Chiffrement AES-256 des données sensibles
- **Clés** : Gestion sécurisée des clés de chiffrement

### Protection des Données Sensibles

- Masquage des données sensibles dans les logs
- Anonymisation pour les environnements de test
- Pseudonymisation pour les analyses

### Classification des Données

| Niveau | Description | Exemples | Protection |
|--------|-------------|----------|------------|
| **PUBLIC** | Données publiques | Plan comptable standard | Aucune protection spéciale |
| **INTERNE** | Usage interne | Paramètres comptables | Authentification simple |
| **CONFIDENTIEL** | Données sensibles | Écritures comptables | Authentification forte + chiffrement |
| **RESTREINT** | Très sensible | Données financières | MFA + chiffrement + accès limité |

---

## Implémentation Technique

### Architecture de Sécurité

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Client         │     │  API Gateway    │     │  API Services   │
│  (Frontend)     │────▶│  (Sécurité)     │────▶│  (Métier)       │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │                        │
                               ▼                        ▼
                        ┌─────────────────┐     ┌─────────────────┐
                        │  Auth Service   │     │  Audit Service  │
                        │  (IAM)          │     │  (Logs)         │
                        └─────────────────┘     └─────────────────┘
```

### Composants Clés

1. **Service d'Authentification**
   - Gestion des utilisateurs et identités
   - Émission et validation des JWT
   - Gestion MFA

2. **Middleware d'Autorisation**
   - Vérification des permissions
   - Application des règles d'accès
   - Filtrage contextuel des données

3. **Service d'Audit**
   - Capture des événements
   - Stockage sécurisé
   - Requêtage et reporting

4. **Module d'Administration**
   - Interface de gestion des utilisateurs
   - Configuration des rôles et permissions
   - Monitoring et alertes

### Modèles de Données

#### Utilisateur
```sql
CREATE TABLE utilisateurs (
  id UUID PRIMARY KEY,
  email VARCHAR(100) UNIQUE NOT NULL,
  nom VARCHAR(100) NOT NULL,
  mot_de_passe_hash VARCHAR(100) NOT NULL,
  statut VARCHAR(20) NOT NULL,
  date_creation TIMESTAMP NOT NULL,
  derniere_connexion TIMESTAMP,
  preferences JSONB
);
```

#### Rôle
```sql
CREATE TABLE roles (
  id UUID PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  nom VARCHAR(100) NOT NULL,
  description TEXT,
  est_systeme BOOLEAN DEFAULT FALSE
);
```

#### Permission
```sql
CREATE TABLE permissions (
  id UUID PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  nom VARCHAR(100) NOT NULL,
  description TEXT,
  categorie VARCHAR(50) NOT NULL
);
```

#### Attribution Rôle
```sql
CREATE TABLE utilisateur_roles (
  utilisateur_id UUID REFERENCES utilisateurs(id),
  role_id UUID REFERENCES roles(id),
  societe_id UUID REFERENCES societes(id),
  date_attribution TIMESTAMP NOT NULL,
  attribue_par UUID REFERENCES utilisateurs(id),
  PRIMARY KEY (utilisateur_id, role_id, societe_id)
);
```

#### Audit Log
```sql
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY,
  timestamp TIMESTAMP NOT NULL,
  utilisateur_id UUID REFERENCES utilisateurs(id),
  action VARCHAR(50) NOT NULL,
  ressource_type VARCHAR(50) NOT NULL,
  ressource_id VARCHAR(100) NOT NULL,
  societe_id UUID REFERENCES societes(id),
  details JSONB,
  ip_address VARCHAR(50),
  user_agent TEXT
);
```

---

## Bonnes Pratiques

### Pour les Administrateurs

1. **Principe du moindre privilège**
   - Attribuer uniquement les droits nécessaires
   - Réviser périodiquement les attributions

2. **Séparation des responsabilités**
   - Séparer les rôles de saisie et validation
   - Mettre en place des workflows d'approbation

3. **Monitoring actif**
   - Surveiller les tentatives d'accès inhabituelles
   - Analyser les patterns d'utilisation

### Pour les Développeurs

1. **Sécurité par conception**
   - Intégrer la sécurité dès la conception
   - Effectuer des revues de code sécurité

2. **Validation des entrées**
   - Valider toutes les entrées utilisateur
   - Utiliser des schémas de validation stricts

3. **Tests de sécurité**
   - Effectuer des tests d'intrusion réguliers
   - Analyser les vulnérabilités

### Pour les Utilisateurs

1. **Hygiène des mots de passe**
   - Utiliser des mots de passe forts et uniques
   - Activer l'authentification à deux facteurs

2. **Vigilance**
   - Signaler les comportements suspects
   - Verrouiller les sessions inactives

3. **Formation continue**
   - Se former aux bonnes pratiques de sécurité
   - Rester informé des menaces actuelles

---

## Conclusion

Cette stratégie d'authentification et de gestion des droits fournit un cadre robuste pour sécuriser l'API de comptabilité SYSCOHADA tout en offrant la flexibilité nécessaire aux différents profils d'utilisateurs. Elle s'appuie sur des standards éprouvés (JWT, RBAC) et intègre les meilleures pratiques de sécurité.

L'implémentation progressive de cette stratégie permettra d'atteindre un niveau de sécurité optimal tout en maintenant une expérience utilisateur fluide.

---

## Annexes

### A. Glossaire

- **JWT** : JSON Web Token, standard pour la création de tokens d'accès
- **RBAC** : Role-Based Access Control, contrôle d'accès basé sur les rôles
- **MFA** : Multi-Factor Authentication, authentification à plusieurs facteurs
- **TOTP** : Time-based One-Time Password, mot de passe à usage unique basé sur le temps

### B. Références

- OWASP Security Guidelines
- GDPR/RGPD Compliance Requirements
- SYSCOHADA Regulatory Framework
- ISO 27001 Security Standards

---

*Document préparé par l'équipe d'architecture, version 1.0*