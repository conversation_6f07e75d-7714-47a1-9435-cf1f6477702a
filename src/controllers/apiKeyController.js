/**
 * Contrôleur pour la gestion des clés API
 * API Comptabilité SYSCOHADA
 */

const ApiKeyService = require('../services/apiKeyService');
const { asyncHandler } = require('../middleware/errorHandler');
const { logger } = require('../config/logger');

/**
 * @desc    Créer une nouvelle clé API
 * @route   POST /api/v1/api-keys
 * @access  Admin
 */
const createApiKey = asyncHandler(async (req, res) => {
  const { name, permissions, expiresAt, metadata } = req.body;

  const apiKey = await ApiKeyService.createApiKey({
    name,
    permissions,
    expiresAt: expiresAt ? new Date(expiresAt) : null,
    metadata,
    createdBy: req.apiKey?.name || 'system'
  });

  logger.info('Nouvelle clé API créée via API', {
    keyId: apiKey.id,
    keyName: apiKey.name,
    createdBy: req.apiKey?.name
  });

  res.status(201).json({
    success: true,
    message: 'Clé API créée avec succès',
    data: apiKey
  });
});

/**
 * @desc    Lister les clés API
 * @route   GET /api/v1/api-keys
 * @access  Admin
 */
const listApiKeys = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    includeInactive = false
  } = req.query;

  const result = await ApiKeyService.listApiKeys({
    page: parseInt(page),
    limit: parseInt(limit),
    includeInactive: includeInactive === 'true'
  });

  res.json({
    success: true,
    message: 'Clés API récupérées avec succès',
    data: result.apiKeys,
    pagination: result.pagination
  });
});

/**
 * @desc    Récupérer une clé API par ID
 * @route   GET /api/v1/api-keys/:id
 * @access  Admin
 */
const getApiKey = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const apiKey = await ApiKeyService.getApiKeyById(id);

  if (!apiKey) {
    return res.status(404).json({
      success: false,
      message: 'Clé API non trouvée'
    });
  }

  res.json({
    success: true,
    message: 'Clé API récupérée avec succès',
    data: apiKey
  });
});

/**
 * @desc    Mettre à jour une clé API
 * @route   PUT /api/v1/api-keys/:id
 * @access  Admin
 */
const updateApiKey = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { name, permissions, expiresAt, isActive, metadata } = req.body;

  const updatedApiKey = await ApiKeyService.updateApiKey(id, {
    name,
    permissions,
    expiresAt: expiresAt ? new Date(expiresAt) : undefined,
    isActive,
    metadata
  });

  if (!updatedApiKey) {
    return res.status(404).json({
      success: false,
      message: 'Clé API non trouvée'
    });
  }

  logger.info('Clé API mise à jour via API', {
    keyId: updatedApiKey.id,
    keyName: updatedApiKey.name,
    updatedBy: req.apiKey?.name
  });

  res.json({
    success: true,
    message: 'Clé API mise à jour avec succès',
    data: updatedApiKey
  });
});

/**
 * @desc    Supprimer une clé API
 * @route   DELETE /api/v1/api-keys/:id
 * @access  Admin
 */
const deleteApiKey = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const deleted = await ApiKeyService.deleteApiKey(id);

  if (!deleted) {
    return res.status(404).json({
      success: false,
      message: 'Clé API non trouvée'
    });
  }

  logger.info('Clé API supprimée via API', {
    keyId: id,
    deletedBy: req.apiKey?.name
  });

  res.json({
    success: true,
    message: 'Clé API supprimée avec succès'
  });
});

/**
 * @desc    Désactiver une clé API
 * @route   POST /api/v1/api-keys/:id/deactivate
 * @access  Admin
 */
const deactivateApiKey = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const deactivatedApiKey = await ApiKeyService.deactivateApiKey(id);

  if (!deactivatedApiKey) {
    return res.status(404).json({
      success: false,
      message: 'Clé API non trouvée'
    });
  }

  logger.info('Clé API désactivée via API', {
    keyId: deactivatedApiKey.id,
    keyName: deactivatedApiKey.name,
    deactivatedBy: req.apiKey?.name
  });

  res.json({
    success: true,
    message: 'Clé API désactivée avec succès',
    data: deactivatedApiKey
  });
});

/**
 * @desc    Nettoyer les clés expirées
 * @route   POST /api/v1/api-keys/cleanup
 * @access  Admin
 */
const cleanupExpiredKeys = asyncHandler(async (req, res) => {
  const deletedCount = await ApiKeyService.cleanupExpiredKeys();

  logger.info('Nettoyage des clés expirées via API', {
    deletedCount,
    triggeredBy: req.apiKey?.name
  });

  res.json({
    success: true,
    message: `${deletedCount} clé(s) expirée(s) supprimée(s)`,
    data: { deletedCount }
  });
});

/**
 * @desc    Vérifier la clé API actuelle
 * @route   GET /api/v1/api-keys/verify
 * @access  Private
 */
const verifyCurrentApiKey = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'Clé API valide',
    data: {
      apiKey: {
        id: req.apiKey.id,
        name: req.apiKey.name,
        prefix: req.apiKey.prefix,
        permissions: req.apiKey.permissions
      }
    }
  });
});

module.exports = {
  createApiKey,
  listApiKeys,
  getApiKey,
  updateApiKey,
  deleteApiKey,
  deactivateApiKey,
  cleanupExpiredKeys,
  verifyCurrentApiKey
};