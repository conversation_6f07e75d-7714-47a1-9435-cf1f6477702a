'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ecriture_comptables', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      numero_ecriture: {
        type: Sequelize.STRING(20),
        allowNull: false,
        unique: true
      },
      date_ecriture: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      journal_code: {
        type: Sequelize.STRING(10),
        allowNull: false,
        references: {
          model: 'journaux',
          key: 'code'
        }
      },
      libelle: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      reference: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      statut: {
        type: Sequelize.ENUM('BROUILLARD', 'VALIDE'),
        allowNull: false,
        defaultValue: 'BROUILLARD'
      },
      societe_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'societes',
          key: 'id'
        }
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Index pour améliorer les performances
    await queryInterface.addIndex('ecriture_comptables', ['date_ecriture']);
    await queryInterface.addIndex('ecriture_comptables', ['journal_code']);
    await queryInterface.addIndex('ecriture_comptables', ['statut']);
    await queryInterface.addIndex('ecriture_comptables', ['societe_id']);
    await queryInterface.addIndex('ecriture_comptables', ['numero_ecriture']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ecriture_comptables');
  }
};