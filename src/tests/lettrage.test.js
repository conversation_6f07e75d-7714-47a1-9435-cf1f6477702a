'use strict';

const request = require('supertest');
const { expect } = require('chai');
const app = require('../app');
const models = require('../models');
const LettrageService = require('../services/lettrageService');

describe('Tests du Lettrage et Rapprochements', () => {
  let societeTest, exerciceTest, journalTest, compteTest;
  let ecritureTest1, ecritureTest2;
  let ligneTest1, ligneTest2, ligneTest3, ligneTest4;
  let lettrageService;

  before(async () => {
    // Initialiser le service de lettrage
    lettrageService = new LettrageService(models);

    // Créer des données de test
    societeTest = await models.Societe.create({
      nom: 'Société Test Lettrage',
      sigle: 'STL',
      adresse: 'Test Address',
      telephone: '123456789',
      email: '<EMAIL>',
      formeJuridique: 'SARL',
      capital: 1000000,
      numeroRccm: 'RCCM-TEST-001',
      numeroIfu: 'IFU-TEST-001'
    });

    exerciceTest = await models.ExerciceComptable.create({
      societeId: societeTest.id,
      libelle: 'Exercice Test 2024',
      dateDebut: '2024-01-01',
      dateFin: '2024-12-31',
      statut: 'OUVERT'
    });

    journalTest = await models.Journal.create({
      code: 'BQ',
      libelle: 'Journal Banque Test',
      type: 'TRESORERIE',
      societeId: societeTest.id
    });

    // Créer un compte client pour les tests
    compteTest = await models.CompteComptable.findOne({
      where: { numero: '411000' } // Compte clients
    });

    if (!compteTest) {
      compteTest = await models.CompteComptable.create({
        numero: '411000',
        libelle: 'Clients',
        classe: '4',
        nature: 'ACTIF'
      });
    }
  });

  beforeEach(async () => {
    // Nettoyer les écritures de test avant chaque test
    await models.LigneEcriture.destroy({ where: {} });
    await models.EcritureComptable.destroy({ where: {} });
  });

  describe('Service LettrageService', () => {
    it('devrait créer des écritures de test pour le lettrage', async () => {
      // Créer première écriture (facture client)
      ecritureTest1 = await models.EcritureComptable.create({
        societeId: societeTest.id,
        exerciceId: exerciceTest.id,
        journalCode: journalTest.code,
        numeroEcriture: 'BQ001',
        dateEcriture: '2024-06-01',
        libelle: 'Facture client 001',
        reference: 'FACT-001',
        statut: 'VALIDEE'
      });

      // Lignes de la première écriture
      ligneTest1 = await models.LigneEcriture.create({
        ecritureId: ecritureTest1.id,
        compteNumero: '411000',
        libelle: 'Facture client 001',
        montantDebit: 1000.00,
        montantCredit: 0.00
      });

      const ligneTest1Contrepartie = await models.LigneEcriture.create({
        ecritureId: ecritureTest1.id,
        compteNumero: '701000',
        libelle: 'Vente marchandises',
        montantDebit: 0.00,
        montantCredit: 1000.00
      });

      // Créer deuxième écriture (règlement client)
      ecritureTest2 = await models.EcritureComptable.create({
        societeId: societeTest.id,
        exerciceId: exerciceTest.id,
        journalCode: journalTest.code,
        numeroEcriture: 'BQ002',
        dateEcriture: '2024-06-15',
        libelle: 'Règlement client 001',
        reference: 'REG-001',
        statut: 'VALIDEE'
      });

      // Lignes de la deuxième écriture
      ligneTest2 = await models.LigneEcriture.create({
        ecritureId: ecritureTest2.id,
        compteNumero: '411000',
        libelle: 'Règlement client 001',
        montantDebit: 0.00,
        montantCredit: 1000.00
      });

      const ligneTest2Contrepartie = await models.LigneEcriture.create({
        ecritureId: ecritureTest2.id,
        compteNumero: '521000',
        libelle: 'Banque',
        montantDebit: 1000.00,
        montantCredit: 0.00
      });

      expect(ligneTest1).to.exist;
      expect(ligneTest2).to.exist;
    });

    it('devrait effectuer un lettrage manuel', async () => {
      // Créer les écritures de test
      await this.test.parent.tests[0].fn();

      const resultat = await lettrageService.lettrageManuel(
        '411000',
        [ligneTest1.id, ligneTest2.id],
        null,
        'user-test-id'
      );

      expect(resultat.success).to.be.true;
      expect(resultat.codeLettrage).to.exist;
      expect(resultat.nombreLignes).to.equal(2);

      // Vérifier que les lignes sont bien lettrées
      const lignesLettrees = await models.LigneEcriture.findAll({
        where: {
          id: [ligneTest1.id, ligneTest2.id]
        }
      });

      lignesLettrees.forEach(ligne => {
        expect(ligne.lettrage).to.equal(resultat.codeLettrage);
        expect(ligne.dateLettrage).to.exist;
        expect(ligne.utilisateurLettrage).to.equal('user-test-id');
      });
    });

    it('devrait calculer les soldes lettrés et non lettrés', async () => {
      // Créer les écritures et effectuer le lettrage
      await this.test.parent.tests[0].fn();
      await lettrageService.lettrageManuel(
        '411000',
        [ligneTest1.id, ligneTest2.id],
        null,
        'user-test-id'
      );

      const soldeLettre = await lettrageService.getSoldeLettre('411000');
      const soldeNonLettre = await lettrageService.getSoldeNonLettre('411000');

      expect(soldeLettre).to.equal(0); // Les lignes s'équilibrent
      expect(soldeNonLettre).to.equal(0); // Toutes les lignes sont lettrées
    });

    it('devrait effectuer un délettrage', async () => {
      // Créer les écritures et effectuer le lettrage
      await this.test.parent.tests[0].fn();
      const lettrage = await lettrageService.lettrageManuel(
        '411000',
        [ligneTest1.id, ligneTest2.id],
        null,
        'user-test-id'
      );

      // Effectuer le délettrage
      const resultat = await lettrageService.delettrage(
        [ligneTest1.id, ligneTest2.id],
        'user-test-id'
      );

      expect(resultat.success).to.be.true;
      expect(resultat.nombreLignes).to.equal(2);
      expect(resultat.codesLettrage).to.include(lettrage.codeLettrage);

      // Vérifier que les lignes ne sont plus lettrées
      const lignesDelettrees = await models.LigneEcriture.findAll({
        where: {
          id: [ligneTest1.id, ligneTest2.id]
        }
      });

      lignesDelettrees.forEach(ligne => {
        expect(ligne.lettrage).to.be.null;
        expect(ligne.dateLettrage).to.be.null;
        expect(ligne.utilisateurLettrage).to.be.null;
      });
    });

    it('devrait récupérer les lignes à lettrer', async () => {
      // Créer les écritures de test
      await this.test.parent.tests[0].fn();

      const resultat = await lettrageService.getLignesALettrer('411000');

      expect(resultat.lignes).to.be.an('array');
      expect(resultat.lignes.length).to.equal(2);
      expect(resultat.pagination).to.exist;
      expect(resultat.pagination.total).to.equal(2);
    });
  });

  describe('API Lettrage', () => {
    beforeEach(async () => {
      // Créer les écritures de test pour chaque test API
      ecritureTest1 = await models.EcritureComptable.create({
        societeId: societeTest.id,
        exerciceId: exerciceTest.id,
        journalCode: journalTest.code,
        numeroEcriture: 'BQ001',
        dateEcriture: '2024-06-01',
        libelle: 'Facture client 001',
        reference: 'FACT-001',
        statut: 'VALIDEE'
      });

      ligneTest1 = await models.LigneEcriture.create({
        ecritureId: ecritureTest1.id,
        compteNumero: '411000',
        libelle: 'Facture client 001',
        montantDebit: 1000.00,
        montantCredit: 0.00
      });

      ecritureTest2 = await models.EcritureComptable.create({
        societeId: societeTest.id,
        exerciceId: exerciceTest.id,
        journalCode: journalTest.code,
        numeroEcriture: 'BQ002',
        dateEcriture: '2024-06-15',
        libelle: 'Règlement client 001',
        reference: 'REG-001',
        statut: 'VALIDEE'
      });

      ligneTest2 = await models.LigneEcriture.create({
        ecritureId: ecritureTest2.id,
        compteNumero: '411000',
        libelle: 'Règlement client 001',
        montantDebit: 0.00,
        montantCredit: 1000.00
      });
    });

    it('POST /api/v1/lettrage/manuel - devrait effectuer un lettrage manuel', async () => {
      const response = await request(app)
        .post('/api/v1/lettrage/manuel')
        .send({
          compteNumero: '411000',
          ligneIds: [ligneTest1.id, ligneTest2.id]
        })
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.codeLettrage).to.exist;
      expect(response.body.data.nombreLignes).to.equal(2);
    });

    it('GET /api/v1/lettrage/lignes/:compteNumero - devrait récupérer les lignes à lettrer', async () => {
      const response = await request(app)
        .get('/api/v1/lettrage/lignes/411000')
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.lignes).to.be.an('array');
      expect(response.body.data.pagination).to.exist;
    });

    it('GET /api/v1/lettrage/soldes/:compteNumero - devrait calculer les soldes', async () => {
      const response = await request(app)
        .get('/api/v1/lettrage/soldes/411000')
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.soldes).to.exist;
      expect(response.body.data.soldes.lettre).to.be.a('number');
      expect(response.body.data.soldes.nonLettre).to.be.a('number');
      expect(response.body.data.soldes.total).to.be.a('number');
    });
  });

  after(async () => {
    // Nettoyer les données de test
    if (societeTest) {
      await models.LigneEcriture.destroy({ where: {} });
      await models.EcritureComptable.destroy({ where: {} });
      await models.ExerciceComptable.destroy({ where: { societeId: societeTest.id } });
      await models.Journal.destroy({ where: { societeId: societeTest.id } });
      await models.Societe.destroy({ where: { id: societeTest.id } });
    }
  });
});
