'use strict';

/* eslint-env jest */

const AnalyseService = require('../services/analyseService');
const models = require('../models');

// Mock des modèles et services
jest.mock('../models', () => {
  const mockModels = {
    Societe: {
      findByPk: jest.fn()
    },
    CompteComptable: {
      findOne: jest.fn(),
      findAll: jest.fn()
    },
    ExerciceComptable: {
      findOne: jest.fn(),
      findByPk: jest.fn()
    },
    EcritureComptable: {
      findAll: jest.fn()
    },
    LigneEcriture: {
      findAll: jest.fn(),
      findOne: jest.fn()
    },
    sequelize: {
      Op: {
        between: 'between',
        gte: 'gte',
        lte: 'lte',
        in: 'in',
        like: 'like',
        ne: 'ne',
        or: 'or'
      }
    }
  };
  return mockModels;
});

// Mock des utilitaires d'analyse
jest.mock('../utils/analyseCalculations', () => ({
  calculerTendance: jest.fn().mockReturnValue({
    tendance: 'HAUSSE',
    variation: 500,
    variationPourcentage: 10
  }),
  detecterAnomalies: jest.fn().mockReturnValue([
    {
      type: 'VALEUR_ABERRANTE',
      index: 2,
      valeur: 1500,
      moyenne: 1000,
      ecart: 500,
      seuil: 400
    }
  ]),
  calculerPrevisions: jest.fn().mockReturnValue([1100, 1200, 1300]),
  calculerPrevisionsMoyenneMobile: jest.fn(),
  calculerPrevisionsRegressionLineaire: jest.fn(),
  comparerBudget: jest.fn().mockReturnValue({
    ecartTotal: 500,
    ecartPourcentage: 5,
    ecarts: [
      {
        reel: 1500,
        budget: 1000,
        ecart: 500,
        ecartPourcentage: 50
      }
    ]
  })
}));

describe('AnalyseService', () => {
  let analyseService;
  
  beforeEach(() => {
    jest.clearAllMocks();
    analyseService = new AnalyseService(models);
    
    // Mock des méthodes du calculService
    analyseService.calculService = {
      calculerSoldeCompte: jest.fn().mockResolvedValue({
        compteNumero: '401000',
        libelle: 'Fournisseurs',
        sensNaturel: 'CREDIT',
        totalDebit: 1000,
        totalCredit: 1500,
        solde: 500,
        sensActuel: 'CREDIT',
        soldeNaturel: 500,
        nombreLignes: 10,
        dateCalcul: new Date(),
        periode: {
          dateDebut: null,
          dateFin: new Date()
        }
      }),
      calculerBalanceGenerale: jest.fn().mockResolvedValue({
        periode: { dateDebut: new Date(), dateFin: new Date() },
        lignesBalance: [
          {
            compteNumero: '401000',
            libelle: 'Fournisseurs',
            classe: 4,
            nature: 'COLLECTIF',
            sensNaturel: 'CREDIT',
            niveau: 6,
            totalDebit: 1000,
            totalCredit: 1500,
            solde: 500,
            sensActuel: 'CREDIT',
            nombreLignes: 10
          },
          {
            compteNumero: '607000',
            libelle: 'Achats de marchandises',
            classe: 6,
            nature: 'DETAIL',
            sensNaturel: 'DEBIT',
            niveau: 6,
            totalDebit: 1500,
            totalCredit: 0,
            solde: 1500,
            sensActuel: 'DEBIT',
            nombreLignes: 5
          },
          {
            compteNumero: '707000',
            libelle: 'Ventes de marchandises',
            classe: 7,
            nature: 'DETAIL',
            sensNaturel: 'CREDIT',
            niveau: 6,
            totalDebit: 0,
            totalCredit: 2000,
            solde: 2000,
            sensActuel: 'CREDIT',
            nombreLignes: 8
          }
        ]
      }),
      validerPeriode: jest.fn()
    };
    
    // Mock des réponses des modèles
    models.Societe.findByPk.mockResolvedValue({
      id: '1',
      raisonSociale: 'Société Test',
      siret: '12345678901234'
    });
    
    models.CompteComptable.findOne.mockResolvedValue({
      numero: '401000',
      libelle: 'Fournisseurs',
      sens: 'CREDIT',
      nature: 'COLLECTIF'
    });
    
    models.CompteComptable.findAll.mockResolvedValue([
      {
        numero: '401000',
        libelle: 'Fournisseurs',
        sens: 'CREDIT',
        nature: 'COLLECTIF'
      },
      {
        numero: '607000',
        libelle: 'Achats de marchandises',
        sens: 'DEBIT',
        nature: 'DETAIL'
      },
      {
        numero: '707000',
        libelle: 'Ventes de marchandises',
        sens: 'CREDIT',
        nature: 'DETAIL'
      }
    ]);
    
    models.ExerciceComptable.findOne.mockResolvedValue({
      id: '1',
      annee: '2025',
      dateDebut: new Date('2025-01-01'),
      dateFin: new Date('2025-12-31'),
      statut: 'OUVERT'
    });
    
    models.EcritureComptable.findAll.mockResolvedValue([
      {
        id: '1',
        numeroEcriture: 'ACH2025001',
        dateEcriture: new Date('2025-01-15'),
        journalCode: 'ACH',
        libelle: 'Facture fournisseur',
        reference: 'FAC123',
        pieceJustificative: 'FAC123.pdf',
        statut: 'VALIDEE',
        lignes: [
          {
            id: '1',
            ecritureId: '1',
            compteNumero: '401000',
            libelle: 'Fournisseur ABC',
            debit: 0,
            credit: 1000,
            compte: {
              numero: '401000',
              libelle: 'Fournisseurs',
              sens: 'CREDIT'
            }
          },
          {
            id: '2',
            ecritureId: '1',
            compteNumero: '607000',
            libelle: 'Achat de marchandises',
            debit: 1000,
            credit: 0,
            compte: {
              numero: '607000',
              libelle: 'Achats de marchandises',
              sens: 'DEBIT'
            }
          }
        ]
      }
    ]);
  });
  
  describe('analyseEvolutionComptes', () => {
    it('devrait analyser l\'évolution des comptes', async () => {
      // Arrange
      const comptes = ['401000', '607000'];
      const periodes = [
        {
          dateDebut: new Date('2025-01-01'),
          dateFin: new Date('2025-01-31'),
          libelle: 'Janvier 2025'
        },
        {
          dateDebut: new Date('2025-02-01'),
          dateFin: new Date('2025-02-28'),
          libelle: 'Février 2025'
        }
      ];
      const options = {
        societeId: '1',
        exerciceId: '1'
      };
      
      // Act
      const resultat = await analyseService.analyseEvolutionComptes(comptes, periodes, options);
      
      // Assert
      expect(models.Societe.findByPk).toHaveBeenCalledWith('1');
      expect(models.CompteComptable.findOne).toHaveBeenCalled();
      expect(analyseService.calculService.calculerSoldeCompte).toHaveBeenCalled();
      expect(resultat).toHaveProperty('societe');
      expect(resultat).toHaveProperty('periodes');
      expect(resultat).toHaveProperty('resultatsComptes');
      expect(resultat.resultatsComptes).toHaveLength(2);
      expect(resultat.resultatsComptes[0]).toHaveProperty('tendance');
      expect(resultat.resultatsComptes[0]).toHaveProperty('anomalies');
      expect(resultat.resultatsComptes[0]).toHaveProperty('previsions');
    });
  });
  
  describe('detectionAnomalies', () => {
    it('devrait détecter les anomalies dans les écritures comptables', async () => {
      // Arrange
      const societeId = '1';
      const periode = {
        dateDebut: new Date('2025-01-01'),
        dateFin: new Date('2025-01-31')
      };
      const options = {
        exerciceId: '1',
        seuilMontant: 5000,
        seuilEcartType: 2,
        seuilFrequence: 3
      };
      
      // Act
      const resultat = await analyseService.detectionAnomalies(societeId, periode, options);
      
      // Assert
      expect(models.Societe.findByPk).toHaveBeenCalledWith('1');
      expect(models.EcritureComptable.findAll).toHaveBeenCalled();
      expect(resultat).toHaveProperty('societe');
      expect(resultat).toHaveProperty('periode');
      expect(resultat).toHaveProperty('anomalies');
      expect(resultat.anomalies).toHaveProperty('montantsEleves');
      expect(resultat.anomalies).toHaveProperty('ecrituresNonEquilibrees');
      expect(resultat.anomalies).toHaveProperty('comptesInhabituels');
      expect(resultat.anomalies).toHaveProperty('frequencesAnormales');
    });
  });
  
  describe('previsionsFinancieres', () => {
    it('devrait calculer des prévisions financières', async () => {
      // Arrange
      const societeId = '1';
      const horizon = 3;
      const options = {
        methode: 'regression_lineaire',
        periodeReference: 'mois',
        nombrePeriodesPasses: 12,
        comptes: ['607000', '707000']
      };
      
      // Act
      const resultat = await analyseService.previsionsFinancieres(societeId, horizon, options);
      
      // Assert
      expect(models.Societe.findByPk).toHaveBeenCalledWith('1');
      expect(models.CompteComptable.findOne).toHaveBeenCalled();
      expect(analyseService.calculService.calculerSoldeCompte).toHaveBeenCalled();
      expect(resultat).toHaveProperty('societe');
      expect(resultat).toHaveProperty('parametres');
      expect(resultat).toHaveProperty('periodesPasses');
      expect(resultat).toHaveProperty('periodesFutures');
      expect(resultat).toHaveProperty('resultatsComptes');
    });
  });
  
  describe('comparaisonsBudgetaires', () => {
    it('devrait comparer les résultats réels avec les budgets', async () => {
      // Arrange
      const societeId = '1';
      const periode = {
        dateDebut: new Date('2025-01-01'),
        dateFin: new Date('2025-01-31')
      };
      const options = {
        exerciceId: '1'
      };
      
      // Act
      const resultat = await analyseService.comparaisonsBudgetaires(societeId, periode, options);
      
      // Assert
      expect(models.Societe.findByPk).toHaveBeenCalledWith('1');
      expect(analyseService.calculService.calculerBalanceGenerale).toHaveBeenCalled();
      expect(resultat).toHaveProperty('societe');
      expect(resultat).toHaveProperty('periode');
      expect(resultat).toHaveProperty('comparaisons');
      expect(resultat).toHaveProperty('totaux');
      expect(resultat.totaux).toHaveProperty('budget');
      expect(resultat.totaux).toHaveProperty('reel');
      expect(resultat.totaux).toHaveProperty('ecart');
    });
  });
  
  describe('benchmarkingSectoriel', () => {
    it('devrait effectuer un benchmarking sectoriel', async () => {
      // Arrange
      const secteur = 'commerce';
      const ratios = ['rentabilite', 'liquidite'];
      const options = {
        societeId: '1'
      };
      
      // Act
      const resultat = await analyseService.benchmarkingSectoriel(secteur, ratios, options);
      
      // Assert
      expect(models.Societe.findByPk).toHaveBeenCalledWith('1');
      expect(models.ExerciceComptable.findOne).toHaveBeenCalled();
      expect(resultat).toHaveProperty('societe');
      expect(resultat).toHaveProperty('secteur');
      expect(resultat).toHaveProperty('comparaisons');
      expect(resultat.comparaisons.length).toBeGreaterThan(0);
    });
  });
});