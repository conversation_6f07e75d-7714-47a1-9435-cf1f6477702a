'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Modification de la taille du champ nom (100 -> 255 caractères)
    await queryInterface.changeColumn('societes', 'nom', {
      type: Sequelize.STRING(255),
      allowNull: false
    });

    // Modification de la taille du champ telephone (20 -> 50 caractères)
    await queryInterface.changeColumn('societes', 'telephone', {
      type: Sequelize.STRING(50),
      allowNull: true
    });

    // Modification de la taille du champ email (50 -> 255 caractères)
    await queryInterface.changeColumn('societes', 'email', {
      type: Sequelize.STRING(255),
      allowNull: true
    });

    // Ajout des nouveaux champs SYSCOHADA
    await queryInterface.addColumn('societes', 'numero_rccm', {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: 'Numéro du Registre de Commerce et du Crédit Mobilier'
    });

    await queryInterface.addColumn('societes', 'regime_fiscal', {
      type: Sequelize.ENUM('REEL_NORMAL', 'REEL_SIMPLIFIE', 'SYNTHETIQUE'),
      allowNull: false,
      defaultValue: 'REEL_NORMAL',
      comment: 'Régime fiscal de la société selon SYSCOHADA'
    });

    await queryInterface.addColumn('societes', 'statut', {
      type: Sequelize.ENUM('ACTIF', 'SUSPENDU', 'FERME'),
      allowNull: false,
      defaultValue: 'ACTIF',
      comment: 'Statut de la société'
    });

    await queryInterface.addColumn('societes', 'logo_url', {
      type: Sequelize.STRING(500),
      allowNull: true,
      comment: 'URL du logo de la société'
    });

    await queryInterface.addColumn('societes', 'secteur_activite', {
      type: Sequelize.STRING(100),
      allowNull: true,
      comment: 'Secteur d\'activité principal'
    });

    await queryInterface.addColumn('societes', 'numero_cnps', {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: 'Numéro CNPS (Caisse Nationale de Prévoyance Sociale)'
    });

    await queryInterface.addColumn('societes', 'date_creation', {
      type: Sequelize.DATEONLY,
      allowNull: true,
      comment: 'Date de création de la société'
    });

    await queryInterface.addColumn('societes', 'date_debut_activite', {
      type: Sequelize.DATEONLY,
      allowNull: true,
      comment: 'Date de début d\'activité'
    });

    await queryInterface.addColumn('societes', 'representant_legal', {
      type: Sequelize.STRING(255),
      allowNull: true,
      comment: 'Nom du représentant légal'
    });

    await queryInterface.addColumn('societes', 'fonction_representant', {
      type: Sequelize.STRING(100),
      allowNull: true,
      comment: 'Fonction du représentant légal'
    });

    await queryInterface.addColumn('societes', 'site_web', {
      type: Sequelize.STRING(255),
      allowNull: true,
      comment: 'Site web de la société'
    });

    await queryInterface.addColumn('societes', 'code_postal', {
      type: Sequelize.STRING(20),
      allowNull: true,
      comment: 'Code postal'
    });

    await queryInterface.addColumn('societes', 'ville', {
      type: Sequelize.STRING(100),
      allowNull: true,
      comment: 'Ville du siège social'
    });

    await queryInterface.addColumn('societes', 'pays', {
      type: Sequelize.STRING(100),
      allowNull: false,
      defaultValue: 'Côte d\'Ivoire',
      comment: 'Pays du siège social'
    });

    // Ajout d'index pour optimiser les performances
    await queryInterface.addIndex('societes', {
      fields: ['numero_rccm'],
      name: 'societes_numero_rccm_idx'
    });

    await queryInterface.addIndex('societes', {
      fields: ['regime_fiscal'],
      name: 'societes_regime_fiscal_idx'
    });

    await queryInterface.addIndex('societes', {
      fields: ['statut'],
      name: 'societes_statut_idx'
    });

    await queryInterface.addIndex('societes', {
      fields: ['secteur_activite'],
      name: 'societes_secteur_activite_idx'
    });
  },

  async down(queryInterface, Sequelize) {
    // Suppression des index
    await queryInterface.removeIndex('societes', 'societes_numero_rccm_idx');
    await queryInterface.removeIndex('societes', 'societes_regime_fiscal_idx');
    await queryInterface.removeIndex('societes', 'societes_statut_idx');
    await queryInterface.removeIndex('societes', 'societes_secteur_activite_idx');

    // Suppression des colonnes ajoutées
    await queryInterface.removeColumn('societes', 'pays');
    await queryInterface.removeColumn('societes', 'ville');
    await queryInterface.removeColumn('societes', 'code_postal');
    await queryInterface.removeColumn('societes', 'site_web');
    await queryInterface.removeColumn('societes', 'fonction_representant');
    await queryInterface.removeColumn('societes', 'representant_legal');
    await queryInterface.removeColumn('societes', 'date_debut_activite');
    await queryInterface.removeColumn('societes', 'date_creation');
    await queryInterface.removeColumn('societes', 'numero_cnps');
    await queryInterface.removeColumn('societes', 'secteur_activite');
    await queryInterface.removeColumn('societes', 'logo_url');
    await queryInterface.removeColumn('societes', 'statut');
    await queryInterface.removeColumn('societes', 'regime_fiscal');
    await queryInterface.removeColumn('societes', 'numero_rccm');

    // Restauration des tailles originales des champs
    await queryInterface.changeColumn('societes', 'email', {
      type: Sequelize.STRING(50),
      allowNull: true
    });

    await queryInterface.changeColumn('societes', 'telephone', {
      type: Sequelize.STRING(20),
      allowNull: true
    });

    await queryInterface.changeColumn('societes', 'nom', {
      type: Sequelize.STRING(100),
      allowNull: false
    });
  }
};