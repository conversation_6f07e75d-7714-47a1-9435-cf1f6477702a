# Documentation: Tableaux de Bord et Analyses Financières

Ce document décrit l'implémentation des fonctionnalités de tableaux de bord (Day 13) et d'analyses financières (Day 14) dans l'API de comptabilité générale conforme au SYSCOHADA.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture](#architecture)
3. [Module Tableaux de Bord](#module-tableaux-de-bord)
   - [Service](#service-de-tableau-de-bord)
   - [Contrôleur](#contrôleur-de-tableau-de-bord)
   - [Routes](#routes-de-tableau-de-bord)
   - [Utilitaires](#utilitaires-de-tableau-de-bord)
4. [Module Analyses Financières](#module-analyses-financières)
   - [Service](#service-danalyse)
   - [Contrôleur](#contrôleur-danalyse)
   - [Routes](#routes-danalyse)
   - [Utilitaires](#utilitaires-danalyse)
5. [Tests Unitaires](#tests-unitaires)
6. [Exemples d'utilisation](#exemples-dutilisation)
7. [Considérations techniques](#considérations-techniques)

## Vue d'ensemble

Les modules de tableaux de bord et d'analyses financières fournissent des fonctionnalités avancées pour l'analyse des données comptables et financières d'une entreprise. Ces modules permettent aux utilisateurs de:

- Visualiser les indicateurs clés de performance (KPI) financiers
- Analyser l'évolution du chiffre d'affaires
- Examiner la répartition des charges et produits
- Calculer et interpréter les ratios financiers
- Recevoir des alertes sur des situations financières critiques
- Analyser l'évolution des comptes sur différentes périodes
- Détecter des anomalies dans les écritures comptables
- Réaliser des prévisions financières
- Comparer les résultats réels avec les budgets
- Effectuer des benchmarks sectoriels

Ces fonctionnalités sont exposées via une API RESTful, permettant leur intégration dans diverses interfaces utilisateur.

## Architecture

L'implémentation suit une architecture en couches:

1. **Routes**: Définissent les endpoints de l'API
2. **Contrôleurs**: Gèrent les requêtes HTTP et les réponses
3. **Services**: Contiennent la logique métier
4. **Utilitaires**: Fournissent des fonctions de calcul spécialisées
5. **Modèles**: Représentent les données et interagissent avec la base de données

Cette séparation des préoccupations permet une meilleure maintenabilité et testabilité du code.

## Module Tableaux de Bord

### Service de Tableau de Bord

Le service `DashboardService` (`src/services/dashboardService.js`) fournit les méthodes suivantes:

#### `getKPIFinanciers(societeId, periode, options)`

Calcule les indicateurs clés de performance financiers pour une société et une période données.

**Paramètres**:
- `societeId`: ID de la société
- `periode`: Objet contenant `dateDebut` et `dateFin`
- `options`: Options supplémentaires (exerciceId, etc.)

**Retourne**:
- Objet contenant les KPIs financiers (chiffre d'affaires, résultat net, trésorerie, etc.)

#### `getEvolutionChiffreAffaires(societeId, periodes, options)`

Analyse l'évolution du chiffre d'affaires sur plusieurs périodes.

**Paramètres**:
- `societeId`: ID de la société
- `periodes`: Tableau d'objets période (`dateDebut`, `dateFin`, `libelle`)
- `options`: Options supplémentaires

**Retourne**:
- Objet contenant l'évolution du chiffre d'affaires avec variations et tendances

#### `getAnalyseChargesProduits(societeId, periode, options)`

Analyse la répartition des charges et produits pour une période donnée.

**Paramètres**:
- `societeId`: ID de la société
- `periode`: Objet contenant `dateDebut` et `dateFin`
- `options`: Options supplémentaires

**Retourne**:
- Objet contenant l'analyse détaillée des charges et produits

#### `getRatiosFinanciers(societeId, periode, options)`

Calcule les principaux ratios financiers.

**Paramètres**:
- `societeId`: ID de la société
- `periode`: Objet contenant `dateDebut` et `dateFin`
- `options`: Options supplémentaires

**Retourne**:
- Objet contenant les ratios financiers (liquidité, solvabilité, rentabilité, etc.)

#### `getAlertes(societeId, options)`

Génère des alertes financières basées sur des seuils prédéfinis.

**Paramètres**:
- `societeId`: ID de la société
- `options`: Options supplémentaires (exerciceId, seuils, etc.)

**Retourne**:
- Tableau d'alertes financières avec niveaux de gravité et recommandations

### Contrôleur de Tableau de Bord

Le contrôleur `DashboardController` (`src/controllers/dashboardController.js`) expose les fonctionnalités du service via des endpoints HTTP:

- `getKPIFinanciers`: Gère les requêtes pour obtenir les KPIs financiers
- `getEvolutionChiffreAffaires`: Gère les requêtes pour l'évolution du chiffre d'affaires
- `getAnalyseChargesProduits`: Gère les requêtes pour l'analyse des charges et produits
- `getRatiosFinanciers`: Gère les requêtes pour les ratios financiers
- `getAlertes`: Gère les requêtes pour les alertes financières

Chaque méthode du contrôleur:
1. Valide les paramètres de la requête
2. Appelle la méthode correspondante du service
3. Formate et renvoie la réponse
4. Gère les erreurs potentielles

### Routes de Tableau de Bord

Les routes pour le tableau de bord sont définies dans `src/routes/dashboard.js`:

```javascript
// GET /api/dashboard/kpi/:societeId
router.get('/kpi/:societeId', dashboardController.getKPIFinanciers);

// POST /api/dashboard/evolution-ca/:societeId
router.post('/evolution-ca/:societeId', dashboardController.getEvolutionChiffreAffaires);

// GET /api/dashboard/charges-produits/:societeId
router.get('/charges-produits/:societeId', dashboardController.getAnalyseChargesProduits);

// GET /api/dashboard/ratios/:societeId
router.get('/ratios/:societeId', dashboardController.getRatiosFinanciers);

// GET /api/dashboard/alertes/:societeId
router.get('/alertes/:societeId', dashboardController.getAlertes);
```

Ces routes sont ensuite intégrées dans le routeur principal de l'application (`src/routes/index.js`).

### Utilitaires de Tableau de Bord

Le fichier `src/utils/dashboardCalculations.js` contient des fonctions utilitaires pour les calculs spécifiques aux tableaux de bord:

- `calculerKPIs`: Calcule les indicateurs clés de performance
- `calculerEvolutionCA`: Calcule l'évolution du chiffre d'affaires
- `analyserChargesProduits`: Analyse la répartition des charges et produits
- `calculerRatiosFinanciers`: Calcule les ratios financiers
- `genererAlertes`: Génère des alertes financières basées sur des seuils

Ces fonctions sont utilisées par le service de tableau de bord pour effectuer les calculs nécessaires.

## Module Analyses Financières

### Service d'Analyse

Le service `AnalyseService` (`src/services/analyseService.js`) fournit les méthodes suivantes:

#### `analyseEvolutionComptes(comptes, periodes, options)`

Analyse l'évolution de comptes spécifiques sur plusieurs périodes.

**Paramètres**:
- `comptes`: Tableau de numéros de comptes à analyser
- `periodes`: Tableau d'objets période (`dateDebut`, `dateFin`, `libelle`)
- `options`: Options supplémentaires (societeId, exerciceId, etc.)

**Retourne**:
- Objet contenant l'analyse détaillée de l'évolution des comptes avec tendances et anomalies

#### `detectionAnomalies(societeId, periode, options)`

Détecte les anomalies dans les écritures comptables.

**Paramètres**:
- `societeId`: ID de la société
- `periode`: Objet contenant `dateDebut` et `dateFin`
- `options`: Options de détection (seuils, critères, etc.)

**Retourne**:
- Objet contenant les anomalies détectées par catégorie

#### `previsionsFinancieres(societeId, horizon, options)`

Génère des prévisions financières basées sur les données historiques.

**Paramètres**:
- `societeId`: ID de la société
- `horizon`: Nombre de périodes futures à prévoir
- `options`: Options de prévision (méthode, comptes à prévoir, etc.)

**Retourne**:
- Objet contenant les prévisions financières pour les périodes futures

#### `comparaisonsBudgetaires(societeId, periode, options)`

Compare les résultats réels avec les budgets.

**Paramètres**:
- `societeId`: ID de la société
- `periode`: Objet contenant `dateDebut` et `dateFin`
- `options`: Options supplémentaires

**Retourne**:
- Objet contenant les comparaisons entre résultats réels et budgets

#### `benchmarkingSectoriel(secteur, ratios, options)`

Effectue un benchmarking sectoriel pour comparer les performances de l'entreprise avec celles du secteur.

**Paramètres**:
- `secteur`: Code du secteur d'activité
- `ratios`: Tableau de ratios à comparer
- `options`: Options supplémentaires (societeId, etc.)

**Retourne**:
- Objet contenant les comparaisons sectorielles pour les ratios spécifiés

### Contrôleur d'Analyse

Le contrôleur `AnalyseController` (`src/controllers/analyseController.js`) expose les fonctionnalités du service via des endpoints HTTP:

- `analyseEvolutionComptes`: Gère les requêtes pour l'analyse de l'évolution des comptes
- `detectionAnomalies`: Gère les requêtes pour la détection d'anomalies
- `previsionsFinancieres`: Gère les requêtes pour les prévisions financières
- `comparaisonsBudgetaires`: Gère les requêtes pour les comparaisons budgétaires
- `benchmarkingSectoriel`: Gère les requêtes pour le benchmarking sectoriel

Chaque méthode du contrôleur:
1. Valide les paramètres de la requête
2. Appelle la méthode correspondante du service
3. Formate et renvoie la réponse
4. Gère les erreurs potentielles

### Routes d'Analyse

Les routes pour les analyses financières sont définies dans `src/routes/analyses.js`:

```javascript
// POST /api/analyses/evolution-comptes
router.post('/evolution-comptes', analyseController.analyseEvolutionComptes);

// POST /api/analyses/detection-anomalies/:societeId
router.post('/detection-anomalies/:societeId', analyseController.detectionAnomalies);

// POST /api/analyses/previsions/:societeId/:horizon
router.post('/previsions/:societeId/:horizon', analyseController.previsionsFinancieres);

// POST /api/analyses/comparaisons-budgetaires/:societeId
router.post('/comparaisons-budgetaires/:societeId', analyseController.comparaisonsBudgetaires);

// POST /api/analyses/benchmarking/:secteur
router.post('/benchmarking/:secteur', analyseController.benchmarkingSectoriel);
```

Ces routes sont ensuite intégrées dans le routeur principal de l'application (`src/routes/index.js`).

### Utilitaires d'Analyse

Le fichier `src/utils/analyseCalculations.js` contient des fonctions utilitaires pour les calculs spécifiques aux analyses financières:

- `calculerTendance`: Calcule la tendance d'une série temporelle
- `detecterAnomalies`: Détecte les anomalies dans une série de données
- `calculerPrevisions`: Calcule des prévisions basées sur des données historiques
- `calculerPrevisionsMoyenneMobile`: Calcule des prévisions avec la méthode de la moyenne mobile
- `calculerPrevisionsRegressionLineaire`: Calcule des prévisions avec la méthode de régression linéaire
- `comparerBudget`: Compare les résultats réels avec les budgets

Ces fonctions sont utilisées par le service d'analyse pour effectuer les calculs nécessaires.

## Tests Unitaires

Des tests unitaires ont été implémentés pour les services de tableau de bord et d'analyse:

### Tests du Service de Tableau de Bord

Le fichier `src/tests/dashboard.test.js` contient les tests unitaires pour le service `DashboardService`. Ces tests vérifient:

- Le calcul des KPIs financiers
- L'analyse de l'évolution du chiffre d'affaires
- L'analyse des charges et produits
- Le calcul des ratios financiers
- La génération d'alertes

### Tests du Service d'Analyse

Le fichier `src/tests/analyse.test.js` contient les tests unitaires pour le service `AnalyseService`. Ces tests vérifient:

- L'analyse de l'évolution des comptes
- La détection d'anomalies
- Les prévisions financières
- Les comparaisons budgétaires
- Le benchmarking sectoriel

Les tests utilisent Jest comme framework de test et des mocks pour simuler les interactions avec les modèles et autres services.

## Exemples d'utilisation

### Obtenir les KPIs financiers

```javascript
// Requête
GET /api/dashboard/kpi/1?dateDebut=2025-01-01&dateFin=2025-12-31

// Réponse
{
  "societe": {
    "id": "1",
    "raisonSociale": "Société Test"
  },
  "periode": {
    "dateDebut": "2025-01-01T00:00:00.000Z",
    "dateFin": "2025-12-31T00:00:00.000Z"
  },
  "kpis": {
    "chiffreAffaires": 120000,
    "resultatNet": 35000,
    "tresorerie": 45000,
    "totalBilan": 250000,
    "capitauxPropres": 150000,
    "ratios": {
      "rentabilite": 0.29,
      "liquidite": 1.8,
      "endettement": 0.4
    }
  }
}
```

### Analyser l'évolution des comptes

```javascript
// Requête
POST /api/analyses/evolution-comptes
{
  "comptes": ["401000", "411000"],
  "periodes": [
    {
      "dateDebut": "2025-01-01",
      "dateFin": "2025-01-31",
      "libelle": "Janvier 2025"
    },
    {
      "dateDebut": "2025-02-01",
      "dateFin": "2025-02-28",
      "libelle": "Février 2025"
    }
  ],
  "options": {
    "societeId": "1",
    "exerciceId": "1"
  }
}

// Réponse
{
  "societe": {
    "id": "1",
    "raisonSociale": "Société Test"
  },
  "periodes": [
    {
      "dateDebut": "2025-01-01T00:00:00.000Z",
      "dateFin": "2025-01-31T00:00:00.000Z",
      "libelle": "Janvier 2025"
    },
    {
      "dateDebut": "2025-02-01T00:00:00.000Z",
      "dateFin": "2025-02-28T00:00:00.000Z",
      "libelle": "Février 2025"
    }
  ],
  "resultatsComptes": [
    {
      "compte": {
        "numero": "401000",
        "libelle": "Fournisseurs"
      },
      "soldes": [
        {
          "periode": "Janvier 2025",
          "solde": 15000,
          "sensActuel": "CREDIT"
        },
        {
          "periode": "Février 2025",
          "solde": 18000,
          "sensActuel": "CREDIT",
          "variation": 3000,
          "variationPourcentage": 20
        }
      ],
      "tendance": "HAUSSE",
      "anomalies": [],
      "previsions": []
    },
    {
      "compte": {
        "numero": "411000",
        "libelle": "Clients"
      },
      "soldes": [
        {
          "periode": "Janvier 2025",
          "solde": 25000,
          "sensActuel": "DEBIT"
        },
        {
          "periode": "Février 2025",
          "solde": 22000,
          "sensActuel": "DEBIT",
          "variation": -3000,
          "variationPourcentage": -12
        }
      ],
      "tendance": "BAISSE",
      "anomalies": [],
      "previsions": []
    }
  ]
}
```

## Considérations techniques

### Performance

Pour optimiser les performances:

1. Les calculs complexes sont séparés dans des fichiers utilitaires
2. Les requêtes à la base de données sont optimisées pour minimiser le nombre d'appels
3. Les résultats intermédiaires sont mis en cache lorsque possible

### Extensibilité

L'architecture est conçue pour être facilement extensible:

1. Ajout de nouveaux indicateurs ou ratios dans les utilitaires
2. Implémentation de nouvelles méthodes d'analyse ou de prévision
3. Extension des contrôleurs pour exposer de nouvelles fonctionnalités

### Conformité SYSCOHADA

Toutes les analyses et calculs sont conformes aux normes SYSCOHADA, en particulier:

1. Respect des classes de comptes et de leur sens naturel
2. Calcul des soldes et ratios selon les définitions du SYSCOHADA
3. Prise en compte des spécificités des états financiers SYSCOHADA

### Sécurité

Les mesures de sécurité incluent:

1. Validation des entrées utilisateur
2. Vérification des autorisations d'accès aux données
3. Protection contre les injections SQL et autres vulnérabilités

### Limitations actuelles et améliorations futures

1. Implémentation de tests d'intégration pour les API
2. Ajout de plus de méthodes de prévision financière
3. Amélioration de la détection d'anomalies avec des algorithmes plus sophistiqués
4. Intégration avec des sources de données externes pour le benchmarking sectoriel
5. Développement d'une interface utilisateur pour visualiser les tableaux de bord et analyses