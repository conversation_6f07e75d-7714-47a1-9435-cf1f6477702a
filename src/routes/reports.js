'use strict';

const express = require('express');
const router = express.Router();
const Joi = require('joi');

const reportController = require('../controllers/reportController');
const { protect } = require('../middleware/auth');
const { validate } = require('../middleware/validation');

/**
 * Schémas de validation Joi pour les rapports
 */

// Schéma de base pour les rapports
const baseReportSchema = Joi.object({
  societeId: Joi.string().uuid().required(),
  dateDebut: Joi.date().iso().optional(),
  dateFin: Joi.date().iso().optional(),
  format: Joi.string().valid('JSON', 'PDF', 'EXCEL').default('JSON')
});

// Schéma pour la balance générale
const balanceSchema = baseReportSchema.keys({
  niveauDetail: Joi.string().valid('CLASSE', 'COMPTE', 'SOUS_COMPTE').default('COMPTE'),
  includeNonMouvementes: Joi.boolean().default(false)
});

// Schéma pour le grand livre
const grandLivreSchema = baseReportSchema.keys({
  includeReports: Joi.boolean().default(true),
  groupeParMois: Joi.boolean().default(false)
});

// Schéma pour les soldes tiers
const soldesTiersSchema = baseReportSchema.keys({
  type: Joi.string().valid('CLIENT', 'FOURNISSEUR').optional(),
  seulementEchus: Joi.boolean().default(false),
  includeDetails: Joi.boolean().default(false)
});

// Schéma pour les états financiers
const etatFinancierSchema = baseReportSchema.keys({
  exerciceId: Joi.string().uuid().optional(),
  dateArrete: Joi.date().iso().optional(),
  modele: Joi.string().valid('NORMAL', 'SIMPLIFIE').default('NORMAL')
});

// Schéma pour les ratios financiers
const ratiosSchema = baseReportSchema.keys({
  exercicePrecedent: Joi.boolean().default(false)
});

// Schéma pour la trésorerie
const tresorerieSchema = baseReportSchema.keys({
  previsionJours: Joi.number().integer().min(1).max(365).default(30),
  includePrevisionnels: Joi.boolean().default(true)
});

/**
 * Routes pour les rapports comptables
 */

/**
 * @swagger
 * /api/v1/reports/balance-generale:
 *   get:
 *     summary: Génère la balance générale
 *     tags: [Rapports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début de période
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin de période
 *       - in: query
 *         name: niveauDetail
 *         schema:
 *           type: string
 *           enum: [CLASSE, COMPTE, SOUS_COMPTE]
 *           default: COMPTE
 *         description: Niveau de détail souhaité
 *       - in: query
 *         name: includeNonMouvementes
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Inclure les comptes non mouvementés
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [JSON, PDF, EXCEL]
 *           default: JSON
 *         description: Format de sortie
 *     responses:
 *       200:
 *         description: Balance générale générée avec succès
 *       400:
 *         description: Paramètres invalides
 */
router.get('/balance-generale',
  protect,
  validate(balanceSchema, 'query'),
  reportController.genererBalanceGenerale.bind(reportController)
);

/**
 * @swagger
 * /api/v1/reports/grand-livre/{compteNumero}:
 *   get:
 *     summary: Génère le grand livre d'un compte
 *     tags: [Rapports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: compteNumero
 *         required: true
 *         schema:
 *           type: string
 *         description: Numéro du compte
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début de période
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin de période
 *       - in: query
 *         name: includeReports
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Inclure les reports à nouveau
 *       - in: query
 *         name: groupeParMois
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Grouper les mouvements par mois
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [JSON, PDF, EXCEL]
 *           default: JSON
 *         description: Format de sortie
 *     responses:
 *       200:
 *         description: Grand livre généré avec succès
 *       400:
 *         description: Paramètres invalides
 *       404:
 *         description: Compte inexistant
 */
router.get('/grand-livre/:compteNumero',
  protect,
  validate(Joi.object({
    compteNumero: Joi.string().pattern(/^\d{6,10}$/).required()
  }), 'params'),
  validate(grandLivreSchema, 'query'),
  reportController.genererGrandLivre.bind(reportController)
);

/**
 * @swagger
 * /api/v1/reports/soldes-tiers:
 *   get:
 *     summary: Génère l'état des soldes tiers
 *     tags: [Rapports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [CLIENT, FOURNISSEUR]
 *         description: Type de tiers (tous si non spécifié)
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début de période
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin de période
 *       - in: query
 *         name: seulementEchus
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Afficher seulement les tiers en retard
 *       - in: query
 *         name: includeDetails
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Inclure le détail des mouvements
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [JSON, PDF, EXCEL]
 *           default: JSON
 *         description: Format de sortie
 *     responses:
 *       200:
 *         description: Soldes tiers générés avec succès
 *       400:
 *         description: Paramètres invalides
 */
router.get('/soldes-tiers',
  protect,
  validate(soldesTiersSchema, 'query'),
  reportController.genererSoldesTiers.bind(reportController)
);

/**
 * @swagger
 * /api/v1/reports/bilan:
 *   get:
 *     summary: Génère le bilan SYSCOHADA
 *     tags: [États Financiers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: exerciceId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice comptable
 *       - in: query
 *         name: dateArrete
 *         schema:
 *           type: string
 *           format: date
 *         description: Date d'arrêté des comptes
 *       - in: query
 *         name: modele
 *         schema:
 *           type: string
 *           enum: [NORMAL, SIMPLIFIE]
 *           default: NORMAL
 *         description: Modèle de bilan
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [JSON, PDF, EXCEL]
 *           default: JSON
 *         description: Format de sortie
 *     responses:
 *       200:
 *         description: Bilan SYSCOHADA généré avec succès
 *       400:
 *         description: Paramètres invalides
 */
router.get('/bilan',
  protect,
  validate(etatFinancierSchema, 'query'),
  reportController.genererBilan.bind(reportController)
);

/**
 * @swagger
 * /api/v1/reports/compte-resultat:
 *   get:
 *     summary: Génère le compte de résultat SYSCOHADA
 *     tags: [États Financiers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: exerciceId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice comptable
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début de période
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin de période
 *       - in: query
 *         name: modele
 *         schema:
 *           type: string
 *           enum: [NORMAL, SIMPLIFIE]
 *           default: NORMAL
 *         description: Modèle de compte de résultat
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [JSON, PDF, EXCEL]
 *           default: JSON
 *         description: Format de sortie
 *     responses:
 *       200:
 *         description: Compte de résultat SYSCOHADA généré avec succès
 *       400:
 *         description: Paramètres invalides
 */
router.get('/compte-resultat',
  protect,
  validate(etatFinancierSchema, 'query'),
  reportController.genererCompteResultat.bind(reportController)
);

/**
 * @swagger
 * /api/v1/reports/ratios-financiers:
 *   get:
 *     summary: Génère les ratios financiers
 *     tags: [Analyses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début de période
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin de période
 *       - in: query
 *         name: exercicePrecedent
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Inclure la comparaison avec l'exercice précédent
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [JSON, PDF, EXCEL]
 *           default: JSON
 *         description: Format de sortie
 *     responses:
 *       200:
 *         description: Ratios financiers générés avec succès
 *       400:
 *         description: Paramètres invalides
 */
router.get('/ratios-financiers',
  protect,
  validate(ratiosSchema, 'query'),
  reportController.genererRatiosFinanciers.bind(reportController)
);

/**
 * @swagger
 * /api/v1/reports/situation-tresorerie:
 *   get:
 *     summary: Génère la situation de trésorerie
 *     tags: [Analyses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début de période
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin de période
 *       - in: query
 *         name: previsionJours
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 365
 *           default: 30
 *         description: Nombre de jours de prévision
 *       - in: query
 *         name: includePrevisionnels
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Inclure les mouvements prévisionnels
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [JSON, PDF, EXCEL]
 *           default: JSON
 *         description: Format de sortie
 *     responses:
 *       200:
 *         description: Situation de trésorerie générée avec succès
 *       400:
 *         description: Paramètres invalides
 */
router.get('/situation-tresorerie',
  protect,
  validate(tresorerieSchema, 'query'),
  reportController.genererSituationTresorerie.bind(reportController)
);

/**
 * @swagger
 * /api/v1/reports/journal/{journalCode}:
 *   get:
 *     summary: Génère un journal comptable
 *     tags: [Rapports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: journalCode
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *           maxLength: 10
 *         description: Code du journal
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *       - in: query
 *         name: dateDebut
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de début de période
 *       - in: query
 *         name: dateFin
 *         schema:
 *           type: string
 *           format: date
 *         description: Date de fin de période
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [JSON, PDF, EXCEL]
 *           default: JSON
 *         description: Format de sortie
 *     responses:
 *       200:
 *         description: Journal comptable généré avec succès
 *       400:
 *         description: Paramètres invalides
 *       404:
 *         description: Journal inexistant
 */
router.get('/journal/:journalCode',
  protect,
  validate(Joi.object({
    journalCode: Joi.string().min(2).max(10).required()
  }), 'params'),
  validate(baseReportSchema, 'query'),
  reportController.genererJournal.bind(reportController)
);

/**
 * @swagger
 * /api/v1/reports/available:
 *   get:
 *     summary: Liste les rapports disponibles
 *     tags: [Rapports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *     responses:
 *       200:
 *         description: Liste des rapports disponibles
 *       400:
 *         description: ID société manquant
 */
router.get('/available',
  protect,
  validate(Joi.object({
    societeId: Joi.string().uuid().required()
  }), 'query'),
  reportController.getReportsDisponibles.bind(reportController)
);

module.exports = router;