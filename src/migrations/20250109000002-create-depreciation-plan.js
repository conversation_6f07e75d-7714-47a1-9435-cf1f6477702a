'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('depreciation_plans', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      depreciationId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'depreciations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      exercice: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      dateDebut: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      dateFin: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      valeurDebutPeriode: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      cumulDebutPeriode: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0
      },
      dotationPeriode: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      dotationTheorique: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      prorataTemporisMois: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      cumulFinPeriode: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      valeurFinPeriode: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      tauxPeriode: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false
      },
      baseCalcul: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      statut: {
        type: Sequelize.ENUM(
          'PREVISIONNEL',
          'REALISE',
          'AJUSTE',
          'ANNULE'
        ),
        allowNull: false,
        defaultValue: 'PREVISIONNEL'
      },
      ecritureGeneree: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      ecritureId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'ecriture_comptables',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      methodeCalcul: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      observations: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Index pour les performances
    await queryInterface.addIndex('depreciation_plans', ['depreciationId']);
    await queryInterface.addIndex('depreciation_plans', ['exercice']);
    await queryInterface.addIndex('depreciation_plans', ['depreciationId', 'exercice'], {
      unique: true,
      name: 'depreciation_plans_depreciation_exercice_unique'
    });
    await queryInterface.addIndex('depreciation_plans', ['statut']);
    await queryInterface.addIndex('depreciation_plans', ['ecritureGeneree']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('depreciation_plans');
  }
};