'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');
const EcritureService = require('../services/ecritureService');
const RechercheService = require('../services/rechercheService');
const EntryValidationService = require('../services/entryValidationService');

/**
 * Controller pour la gestion des écritures comptables
 */
class EcritureController {
  constructor() {
    this.ecritureService = null;
    this.rechercheService = null;
    this.entryValidationService = null;
  }

  /**
   * Initialise le service avec les modèles
   * @param {Object} models - Modèles Sequelize
   */
  init(models) {
    this.ecritureService = new EcritureService(models);
    this.rechercheService = new RechercheService(models);
    this.entryValidationService = new EntryValidationService(models);
  }

  /**
   * Crée une nouvelle écriture comptable
   * POST /api/v1/ecritures
   */
  async creerEcriture(req, res, next) {
    try {
      const { donnees, lignes } = req.body;

      // Validation des données requises
      if (!donnees || !lignes) {
        return res.status(400).json({
          success: false,
          message: 'Les données de l\'écriture et les lignes sont obligatoires',
          code: 'DONNEES_MANQUANTES'
        });
      }

      // Ajouter l'utilisateur de création si authentifié
      if (req.user) {
        donnees.utilisateurCreation = req.user.id;
      }

      // Créer l'écriture
      const ecriture = await this.ecritureService.creerEcriture(donnees, lignes);

      logger.info('Écriture créée via API', {
        ecritureId: ecriture.id,
        utilisateur: req.user?.id,
        journalCode: donnees.journalCode,
        nombreLignes: lignes.length
      });

      res.status(201).json({
        success: true,
        message: 'Écriture créée avec succès',
        data: {
          ecriture: await this.formatEcritureResponse(ecriture)
        }
      });

    } catch (error) {
      logger.error('Erreur création écriture via API', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Obtient la liste des écritures avec filtres et pagination
   * GET /api/v1/ecritures
   */
  async getEcritures(req, res, next) {
    try {
      const filtres = {
        page: parseInt(req.query.page) || 1,
        limit: Math.min(parseInt(req.query.limit) || 50, 100), // Max 100
        societeId: req.query.societeId,
        journalCode: req.query.journalCode,
        statut: req.query.statut,
        dateDebut: req.query.dateDebut,
        dateFin: req.query.dateFin,
        compteNumero: req.query.compteNumero,
        recherche: req.query.recherche
      };

      // Filtrer les valeurs undefined
      Object.keys(filtres).forEach(key => {
        if (filtres[key] === undefined || filtres[key] === '') {
          delete filtres[key];
        }
      });

      const resultats = await this.ecritureService.getEcritures(filtres);

      // Formater les écritures pour la réponse
      const ecrituresFormatees = await Promise.all(
        resultats.ecritures.map(ecriture => this.formatEcritureResponse(ecriture))
      );

      res.json({
        success: true,
        data: {
          ecritures: ecrituresFormatees,
          pagination: resultats.pagination
        },
        filtres: filtres
      });

    } catch (error) {
      logger.error('Erreur récupération écritures via API', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Obtient une écriture par son ID
   * GET /api/v1/ecritures/:id
   */
  async getEcritureById(req, res, next) {
    try {
      const { id } = req.params;

      const ecriture = await this.ecritureService.getEcritureById(id);
      const ecritureFormatee = await this.formatEcritureResponse(ecriture);

      res.json({
        success: true,
        data: {
          ecriture: ecritureFormatee
        }
      });

    } catch (error) {
      if (error instanceof NotFoundError) {
        return res.status(404).json({
          success: false,
          message: 'Écriture non trouvée',
          code: 'ECRITURE_NON_TROUVEE'
        });
      }

      logger.error('Erreur récupération écriture par ID via API', {
        error: error.message,
        ecritureId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Met à jour une écriture (seulement si en brouillard)
   * PUT /api/v1/ecritures/:id
   */
  async modifierEcriture(req, res, next) {
    try {
      const { id } = req.params;
      const { donnees, lignes } = req.body;

      const ecriture = await this.ecritureService.modifierEcriture(id, donnees, lignes);
      const ecritureFormatee = await this.formatEcritureResponse(ecriture);

      logger.info('Écriture modifiée via API', {
        ecritureId: id,
        utilisateur: req.user?.id,
        donneesModifiees: !!donnees,
        lignesModifiees: !!lignes
      });

      res.json({
        success: true,
        message: 'Écriture modifiée avec succès',
        data: {
          ecriture: ecritureFormatee
        }
      });

    } catch (error) {
      if (error instanceof NotFoundError) {
        return res.status(404).json({
          success: false,
          message: 'Écriture non trouvée',
          code: 'ECRITURE_NON_TROUVEE'
        });
      }

      if (error instanceof ValidationError) {
        return res.status(400).json({
          success: false,
          message: error.message,
          code: 'VALIDATION_ECHOUEE'
        });
      }

      logger.error('Erreur modification écriture via API', {
        error: error.message,
        ecritureId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Supprime une écriture (seulement si en brouillard)
   * DELETE /api/v1/ecritures/:id
   */
  async supprimerEcriture(req, res, next) {
    try {
      const { id } = req.params;

      await this.ecritureService.supprimerEcriture(id);

      logger.info('Écriture supprimée via API', {
        ecritureId: id,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Écriture supprimée avec succès'
      });

    } catch (error) {
      if (error instanceof NotFoundError) {
        return res.status(404).json({
          success: false,
          message: 'Écriture non trouvée',
          code: 'ECRITURE_NON_TROUVEE'
        });
      }

      if (error instanceof ValidationError) {
        return res.status(400).json({
          success: false,
          message: error.message,
          code: 'SUPPRESSION_INTERDITE'
        });
      }

      logger.error('Erreur suppression écriture via API', {
        error: error.message,
        ecritureId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Valide une écriture (passage de BROUILLARD à VALIDEE)
   * POST /api/v1/ecritures/:id/valider
   */
  async validerEcriture(req, res, next) {
    try {
      const { id } = req.params;

      // Ajouter l'utilisateur de validation si authentifié
      const utilisateurValidation = req.user?.id;

      const ecriture = await this.ecritureService.validerEcriture(id);
      
      // Mettre à jour l'utilisateur de validation si fourni
      if (utilisateurValidation && ecriture.update) {
        await ecriture.update({ utilisateurValidation });
      }

      const ecritureFormatee = await this.formatEcritureResponse(ecriture);

      logger.info('Écriture validée via API', {
        ecritureId: id,
        numeroEcriture: ecriture.numeroEcriture,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Écriture validée avec succès',
        data: {
          ecriture: ecritureFormatee
        }
      });

    } catch (error) {
      if (error instanceof NotFoundError) {
        return res.status(404).json({
          success: false,
          message: 'Écriture non trouvée',
          code: 'ECRITURE_NON_TROUVEE'
        });
      }

      if (error instanceof ValidationError) {
        return res.status(400).json({
          success: false,
          message: error.message,
          code: 'VALIDATION_ECHOUEE'
        });
      }

      logger.error('Erreur validation écriture via API', {
        error: error.message,
        ecritureId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Duplique une écriture pour créer un template
   * POST /api/v1/ecritures/:id/dupliquer
   */
  async dupliquerEcriture(req, res, next) {
    try {
      const { id } = req.params;

      const template = await this.ecritureService.dupliquerEcriture(id);

      logger.info('Écriture dupliquée via API', {
        ecritureOriginale: id,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Template d\'écriture créé avec succès',
        data: {
          template
        }
      });

    } catch (error) {
      if (error instanceof NotFoundError) {
        return res.status(404).json({
          success: false,
          message: 'Écriture non trouvée',
          code: 'ECRITURE_NON_TROUVEE'
        });
      }

      logger.error('Erreur duplication écriture via API', {
        error: error.message,
        ecritureId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Valide une écriture selon les règles SYSCOHADA sans la sauvegarder
   * POST /api/v1/ecritures/valider-syscohada
   */
  async validerEcritureSYSCOHADA(req, res, next) {
    try {
      const { donnees, lignes } = req.body;

      if (!donnees || !lignes) {
        return res.status(400).json({
          success: false,
          message: 'Les données de l\'écriture et les lignes sont obligatoires',
          code: 'DONNEES_MANQUANTES'
        });
      }

      const validation = await this.ecritureService.validerEcritureSYSCOHADA(donnees, lignes);

      res.json({
        success: true,
        data: {
          validation
        }
      });

    } catch (error) {
      logger.error('Erreur validation SYSCOHADA via API', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Obtient les écritures en brouillard d'une société
   * GET /api/v1/ecritures/brouillards
   */
  async getBrouillards(req, res, next) {
    try {
      const { societeId } = req.query;

      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'L\'ID société est obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      const filtres = {
        societeId,
        statut: 'BROUILLARD',
        page: parseInt(req.query.page) || 1,
        limit: Math.min(parseInt(req.query.limit) || 50, 100)
      };

      const resultats = await this.ecritureService.getEcritures(filtres);

      // Formater les écritures pour la réponse
      const ecrituresFormatees = await Promise.all(
        resultats.ecritures.map(ecriture => this.formatEcritureResponse(ecriture))
      );

      res.json({
        success: true,
        data: {
          brouillards: ecrituresFormatees,
          pagination: resultats.pagination
        }
      });

    } catch (error) {
      logger.error('Erreur récupération brouillards via API', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Obtient les écritures d'un journal pour une période
   * GET /api/v1/ecritures/journal/:code
   */
  async getEcrituresJournal(req, res, next) {
    try {
      const { code } = req.params;
      const { dateDebut, dateFin, page = 1, limit = 50 } = req.query;

      if (!dateDebut || !dateFin) {
        return res.status(400).json({
          success: false,
          message: 'Les dates de début et fin sont obligatoires',
          code: 'DATES_MANQUANTES'
        });
      }

      const filtres = {
        journalCode: code,
        dateDebut,
        dateFin,
        page: parseInt(page),
        limit: Math.min(parseInt(limit), 100)
      };

      const resultats = await this.ecritureService.getEcritures(filtres);

      // Formater les écritures pour la réponse
      const ecrituresFormatees = await Promise.all(
        resultats.ecritures.map(ecriture => this.formatEcritureResponse(ecriture))
      );

      // Calculer les totaux du journal
      const totaux = ecrituresFormatees.reduce((acc, ecriture) => {
        acc.totalDebit += ecriture.totaux.debit;
        acc.totalCredit += ecriture.totaux.credit;
        acc.nombreEcritures++;
        return acc;
      }, { totalDebit: 0, totalCredit: 0, nombreEcritures: 0 });

      res.json({
        success: true,
        data: {
          journal: {
            code,
            dateDebut,
            dateFin
          },
          ecritures: ecrituresFormatees,
          totaux,
          pagination: resultats.pagination
        }
      });

    } catch (error) {
      logger.error('Erreur récupération écritures journal via API', {
        error: error.message,
        journalCode: req.params.code,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Obtient les statistiques des écritures
   * GET /api/v1/ecritures/statistiques
   */
  async getStatistiques(req, res, next) {
    try {
      const { societeId, dateDebut, dateFin } = req.query;

      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'L\'ID société est obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      // Statistiques par statut
      const statsStatut = await Promise.all([
        this.ecritureService.getEcritures({ societeId, statut: 'BROUILLARD', limit: 1 }),
        this.ecritureService.getEcritures({ societeId, statut: 'VALIDEE', limit: 1 }),
        this.ecritureService.getEcritures({ societeId, statut: 'CLOTUREE', limit: 1 })
      ]);

      // Statistiques par journal si période fournie
      let statsJournaux = null;
      if (dateDebut && dateFin) {
        const journaux = ['VT', 'AC', 'BQ', 'CA', 'OD'];
        const statsJournauxPromises = journaux.map(async (code) => {
          const resultats = await this.ecritureService.getEcritures({
            societeId,
            journalCode: code,
            dateDebut,
            dateFin,
            limit: 1000 // Pour calculer les totaux
          });

          const totaux = resultats.ecritures.reduce((acc, ecriture) => {
            acc.totalDebit += parseFloat(ecriture.totaux?.debit || 0);
            acc.totalCredit += parseFloat(ecriture.totaux?.credit || 0);
            return acc;
          }, { totalDebit: 0, totalCredit: 0 });

          return {
            code,
            nombreEcritures: resultats.pagination.total,
            ...totaux
          };
        });

        statsJournaux = await Promise.all(statsJournauxPromises);
      }

      const statistiques = {
        parStatut: {
          brouillards: statsStatut[0].pagination.total,
          validees: statsStatut[1].pagination.total,
          cloturees: statsStatut[2].pagination.total,
          total: statsStatut[0].pagination.total + statsStatut[1].pagination.total + statsStatut[2].pagination.total
        },
        parJournal: statsJournaux,
        periode: dateDebut && dateFin ? { dateDebut, dateFin } : null
      };

      res.json({
        success: true,
        data: {
          statistiques
        }
      });

    } catch (error) {
      logger.error('Erreur récupération statistiques via API', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Formate une écriture pour la réponse API
   * @param {Object} ecriture - Écriture Sequelize
   * @returns {Object} Écriture formatée
   */
  async formatEcritureResponse(ecriture) {
    const resume = await ecriture.getResume();
    
    return {
      id: ecriture.id,
      numeroEcriture: ecriture.numeroEcriture,
      dateEcriture: ecriture.dateEcriture,
      libelle: ecriture.libelle,
      reference: ecriture.reference,
      pieceJustificative: ecriture.pieceJustificative,
      statut: ecriture.statut,
      dateValidation: ecriture.dateValidation,
      journalCode: ecriture.journalCode,
      journal: ecriture.journal ? {
        code: ecriture.journal.code,
        libelle: ecriture.journal.libelle,
        type: ecriture.journal.type
      } : null,
      exerciceId: ecriture.exerciceId,
      exercice: ecriture.exercice ? {
        id: ecriture.exercice.id,
        libelle: ecriture.exercice.libelle,
        dateDebut: ecriture.exercice.dateDebut,
        dateFin: ecriture.exercice.dateFin,
        statut: ecriture.exercice.statut
      } : null,
      societeId: ecriture.societeId,
      societe: ecriture.societe ? {
        id: ecriture.societe.id,
        nom: ecriture.societe.nom
      } : null,
      lignes: ecriture.lignes ? ecriture.lignes.map(ligne => ({
        id: ligne.id,
        compteNumero: ligne.compteNumero,
        compte: ligne.compte ? {
          numero: ligne.compte.numero,
          libelle: ligne.compte.libelle,
          classe: ligne.compte.classe,
          nature: ligne.compte.nature
        } : null,
        libelle: ligne.libelle,
        debit: parseFloat(ligne.debit || 0),
        credit: parseFloat(ligne.credit || 0),
        reference: ligne.reference,
        lettrage: ligne.lettrage,
        dateLettrage: ligne.dateLettrage,
        ordre: ligne.ordre
      })) : [],
      totaux: {
        debit: resume.totalDebit,
        credit: resume.totalCredit,
        equilibree: resume.equilibree,
        nombreLignes: resume.nombreLignes
      },
      utilisateurCreation: ecriture.utilisateurCreation,
      utilisateurValidation: ecriture.utilisateurValidation,
      createdAt: ecriture.createdAt,
      updatedAt: ecriture.updatedAt
    };
  }

  // ==========================================
  // GESTION DES BROUILLARDS - JOUR 5
  // ==========================================

  /**
   * Obtient les brouillards avec filtres avancés
   * GET /api/v1/ecritures/brouillards/avances
   */
  async getBrouillardsAvances(req, res, next) {
    try {
      const filtres = {
        ...req.query,
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 50
      };

      const resultats = await this.ecritureService.getBrouillards(filtres);

      logger.info('Brouillards avancés récupérés via API', {
        utilisateur: req.user?.id,
        filtres,
        total: resultats.pagination.total
      });

      res.json({
        success: true,
        message: 'Brouillards récupérés avec succès',
        data: resultats
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des brouillards avancés', {
        query: req.query,
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Valide plusieurs écritures en lot
   * POST /api/v1/ecritures/valider-lot
   */
  async validerEcrituresEnLot(req, res, next) {
    try {
      const { ecritureIds } = req.body;

      if (!ecritureIds || !Array.isArray(ecritureIds) || ecritureIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'La liste des IDs d\'écritures est obligatoire',
          code: 'ECRITURE_IDS_MANQUANTS'
        });
      }

      const utilisateurId = req.user?.id;
      const resultats = await this.ecritureService.validerEcrituresEnLot(ecritureIds, utilisateurId);

      logger.info('Validation en lot effectuée via API', {
        utilisateur: utilisateurId,
        total: resultats.statistiques.total,
        succes: resultats.statistiques.succes,
        echecs: resultats.statistiques.echecs
      });

      res.json({
        success: true,
        message: `Validation en lot terminée: ${resultats.statistiques.succes} succès, ${resultats.statistiques.echecs} échecs`,
        data: resultats
      });

    } catch (error) {
      logger.error('Erreur lors de la validation en lot', {
        body: req.body,
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Vérifie les permissions de modification d'une écriture
   * GET /api/v1/ecritures/:id/permissions
   */
  async verifierPermissions(req, res, next) {
    try {
      const { id } = req.params;
      const { action } = req.query;

      if (!action || !['modifier', 'supprimer', 'valider'].includes(action)) {
        return res.status(400).json({
          success: false,
          message: 'Action obligatoire: modifier, supprimer ou valider',
          code: 'ACTION_INVALIDE'
        });
      }

      const utilisateurId = req.user?.id;
      const permissions = await this.ecritureService.verifierPermissionsModification(id, utilisateurId, action);

      res.json({
        success: true,
        data: {
          permissions
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la vérification des permissions', {
        ecritureId: req.params.id,
        action: req.query.action,
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Obtient l'historique des changements d'état d'une écriture
   * GET /api/v1/ecritures/:id/historique
   */
  async getHistoriqueEtat(req, res, next) {
    try {
      const { id } = req.params;

      const historique = await this.ecritureService.getHistoriqueEtat(id);

      res.json({
        success: true,
        data: {
          historique
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'historique', {
        ecritureId: req.params.id,
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Obtient les statistiques des brouillards
   * GET /api/v1/ecritures/brouillards/statistiques
   */
  async getStatistiquesBrouillards(req, res, next) {
    try {
      const { societeId, dateDebut, dateFin } = req.query;

      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'L\'ID de la société est obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      const options = { dateDebut, dateFin };
      const statistiques = await this.ecritureService.getStatistiquesBrouillards(societeId, options);

      res.json({
        success: true,
        data: {
          statistiques
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques brouillards', {
        query: req.query,
        utilisateur: req.user?.id,
        error: error.message
      });
      next(error);
    }
  }

  /**
   * Effectue une recherche avancée multi-critères
   * POST /api/v1/ecritures/recherche
   */
  async rechercheAvancee(req, res, next) {
    try {
      const criteres = req.body;

      // Validation des critères de base
      if (!criteres || Object.keys(criteres).length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Au moins un critère de recherche est obligatoire',
          code: 'CRITERES_MANQUANTS'
        });
      }

      const resultats = await this.rechercheService.rechercheAvancee(criteres);

      logger.info('Recherche avancée effectuée via API', {
        criteres: Object.keys(criteres),
        resultats: resultats.pagination.total,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Recherche avancée effectuée avec succès',
        data: resultats
      });

    } catch (error) {
      logger.error('Erreur lors de la recherche avancée via API', {
        error: error.message,
        criteres: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Effectue une recherche textuelle globale
   * GET /api/v1/ecritures/recherche-textuelle
   */
  async rechercheTextuelle(req, res, next) {
    try {
      const { texte, ...options } = req.query;

      // Validation du texte de recherche
      if (!texte || texte.trim().length < 2) {
        return res.status(400).json({
          success: false,
          message: 'Le texte de recherche doit contenir au moins 2 caractères',
          code: 'TEXTE_RECHERCHE_INVALIDE'
        });
      }

      // Conversion des paramètres booléens
      if (options.rechercherDansLignes !== undefined) {
        options.rechercherDansLignes = options.rechercherDansLignes === 'true';
      }
      if (options.caseSensitive !== undefined) {
        options.caseSensitive = options.caseSensitive === 'true';
      }
      if (options.motExact !== undefined) {
        options.motExact = options.motExact === 'true';
      }

      // Conversion des champs en tableau
      if (options.champs && typeof options.champs === 'string') {
        options.champs = options.champs.split(',');
      }

      const resultats = await this.rechercheService.rechercheTextuelle(texte, options);

      logger.info('Recherche textuelle effectuée via API', {
        texte,
        resultats: resultats.pagination.total,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Recherche textuelle effectuée avec succès',
        data: resultats
      });

    } catch (error) {
      logger.error('Erreur lors de la recherche textuelle via API', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Effectue une recherche par montant
   * GET /api/v1/ecritures/recherche-montant
   */
  async rechercheParMontant(req, res, next) {
    try {
      const { montantMin, montantMax, ...options } = req.query;

      // Validation des montants
      if (montantMin === undefined && montantMax === undefined) {
        return res.status(400).json({
          success: false,
          message: 'Au moins un montant (minimum ou maximum) est obligatoire',
          code: 'MONTANTS_MANQUANTS'
        });
      }

      // Conversion des paramètres numériques
      const montantMinNum = montantMin ? parseFloat(montantMin) : undefined;
      const montantMaxNum = montantMax ? parseFloat(montantMax) : undefined;

      if (options.tolerance) {
        options.tolerance = parseFloat(options.tolerance);
      }

      const resultats = await this.rechercheService.rechercheParMontant(
        montantMinNum,
        montantMaxNum,
        options
      );

      logger.info('Recherche par montant effectuée via API', {
        montantMin: montantMinNum,
        montantMax: montantMaxNum,
        resultats: resultats.pagination.total,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Recherche par montant effectuée avec succès',
        data: resultats
      });

    } catch (error) {
      logger.error('Erreur lors de la recherche par montant via API', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Effectue une recherche par compte
   * GET /api/v1/ecritures/recherche-compte/:compteNumero
   */
  async rechercheParCompte(req, res, next) {
    try {
      const { compteNumero } = req.params;
      const options = req.query;

      // Validation du numéro de compte
      if (!compteNumero) {
        return res.status(400).json({
          success: false,
          message: 'Le numéro de compte est obligatoire',
          code: 'COMPTE_NUMERO_MANQUANT'
        });
      }

      // Conversion des paramètres booléens
      if (options.includeHierarchie !== undefined) {
        options.includeHierarchie = options.includeHierarchie === 'true';
      }

      const resultats = await this.rechercheService.rechercheParCompte(compteNumero, options);

      logger.info('Recherche par compte effectuée via API', {
        compteNumero,
        resultats: resultats.pagination.total,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Recherche par compte effectuée avec succès',
        data: resultats
      });

    } catch (error) {
      logger.error('Erreur lors de la recherche par compte via API', {
        error: error.message,
        compteNumero: req.params.compteNumero,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Effectue une recherche par période
   * GET /api/v1/ecritures/recherche-periode
   */
  async rechercheParPeriode(req, res, next) {
    try {
      const { dateDebut, dateFin, ...options } = req.query;

      // Validation des dates
      if (!dateDebut || !dateFin) {
        return res.status(400).json({
          success: false,
          message: 'Les dates de début et de fin sont obligatoires',
          code: 'DATES_MANQUANTES'
        });
      }

      // Conversion des paramètres booléens
      ['groupeParJournal', 'groupeParMois', 'includeAnalyses'].forEach(param => {
        if (options[param] !== undefined) {
          options[param] = options[param] === 'true';
        }
      });

      const resultats = await this.rechercheService.rechercheParPeriode(
        dateDebut,
        dateFin,
        options
      );

      logger.info('Recherche par période effectuée via API', {
        dateDebut,
        dateFin,
        resultats: resultats.pagination.total,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Recherche par période effectuée avec succès',
        data: resultats
      });

    } catch (error) {
      logger.error('Erreur lors de la recherche par période via API', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Valide une écriture selon les normes SYSCOHADA
   * POST /api/v1/ecritures/validate
   */
  async validateEntry(req, res, next) {
    try {
      const { donnees, lignes, societeId } = req.body;

      // Validation des données requises
      if (!donnees || !lignes || !societeId) {
        return res.status(400).json({
          success: false,
          message: 'Les données de l\'écriture, les lignes et l\'ID société sont obligatoires',
          code: 'DONNEES_MANQUANTES'
        });
      }

      if (!this.entryValidationService) {
        return res.status(500).json({
          success: false,
          message: 'Service de validation non initialisé',
          code: 'SERVICE_NON_INITIALISE'
        });
      }

      // Validation avec le service spécialisé
      const resultatValidation = await this.entryValidationService.validateEntry(
        donnees, 
        lignes, 
        societeId
      );

      logger.info('Validation écriture effectuée via API', {
        numeroEcriture: donnees.numeroEcriture,
        valide: resultatValidation.valide,
        niveauValidation: resultatValidation.niveauValidation,
        nombreErreurs: resultatValidation.erreurs.length,
        nombreAvertissements: resultatValidation.avertissements.length,
        utilisateur: req.user?.id
      });

      // Statut HTTP selon le résultat
      const statusCode = resultatValidation.valide ? 200 : 422;

      res.status(statusCode).json({
        success: true,
        message: resultatValidation.valide ? 
          'Écriture conforme aux normes SYSCOHADA' : 
          'Écriture non conforme - Corrections requises',
        data: {
          validation: resultatValidation,
          conformiteSYSCOHADA: resultatValidation.valide,
          niveauValidation: resultatValidation.niveauValidation
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la validation d\'écriture via API', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }
}

module.exports = new EcritureController();
