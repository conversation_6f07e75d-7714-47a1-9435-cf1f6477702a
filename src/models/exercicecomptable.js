'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class ExerciceComptable extends Model {
    /**
     * Associations du modèle ExerciceComptable
     */
    static associate(models) {
      // Un exercice appartient à une société
      ExerciceComptable.belongsTo(models.Societe, {
        foreignKey: 'societeId',
        as: 'societe'
      });

      // Un exercice peut avoir un exercice précédent
      ExerciceComptable.belongsTo(models.ExerciceComptable, {
        foreignKey: 'exercicePrecedentId',
        as: 'exercicePrecedent'
      });

      // Un exercice peut avoir plusieurs exercices suivants
      ExerciceComptable.hasMany(models.ExerciceComptable, {
        foreignKey: 'exercicePrecedentId',
        as: 'exercicesSuivants'
      });

      // Un exercice peut avoir plusieurs écritures comptables
      ExerciceComptable.hasMany(models.EcritureComptable, {
        foreignKey: 'exerciceId',
        as: 'ecritures'
      });
    }

    /**
     * Méthodes d'instance
     */
    toJSON() {
      const values = { ...this.get() };
      return values;
    }

    /**
     * Vérifie si l'exercice est l'exercice courant
     */
    isExerciceCourant() {
      const maintenant = new Date();
      const dateDebut = new Date(this.dateDebut);
      const dateFin = new Date(this.dateFin);
      
      return maintenant >= dateDebut && maintenant <= dateFin && this.statut === 'OUVERT';
    }

    /**
     * Vérifie si l'exercice peut être clôturé
     */
    async canBeClosed() {
      // Vérifier que l'exercice est ouvert
      if (this.statut !== 'OUVERT') {
        return { canClose: false, reason: 'L\'exercice n\'est pas ouvert' };
      }

      // Vérifier que la date de fin est dépassée
      const maintenant = new Date();
      const dateFin = new Date(this.dateFin);
      if (maintenant < dateFin) {
        return { canClose: false, reason: 'La date de fin d\'exercice n\'est pas encore atteinte' };
      }

      // Vérifier qu'il n'y a pas d'écritures en brouillard
      const models = sequelize.models;
      const ecrituresBrouillard = await models.EcritureComptable.count({
        where: {
          exerciceId: this.id,
          statut: 'BROUILLARD'
        }
      });

      if (ecrituresBrouillard > 0) {
        return { 
          canClose: false, 
          reason: `Il reste ${ecrituresBrouillard} écriture(s) en brouillard` 
        };
      }

      return { canClose: true, reason: null };
    }

    /**
     * Calcule la durée de l'exercice en jours
     */
    getDureeExercice() {
      const dateDebut = new Date(this.dateDebut);
      const dateFin = new Date(this.dateFin);
      return Math.ceil((dateFin - dateDebut) / (1000 * 60 * 60 * 24)) + 1;
    }

    /**
     * Validation de l'exercice comptable
     */
    validateExercice() {
      if (this.dateDebut >= this.dateFin) {
        throw new Error('La date de début d\'exercice doit être antérieure à la date de fin');
      }

      const dureeExercice = this.getDureeExercice();
      if (dureeExercice > 366) {
        throw new Error('Un exercice comptable ne peut dépasser 366 jours');
      }

      // Vérifier que les dates sont cohérentes avec l'année
      const anneeDebut = new Date(this.dateDebut).getFullYear();
      const anneeFin = new Date(this.dateFin).getFullYear();
      if (anneeFin - anneeDebut > 1) {
        throw new Error('Un exercice ne peut s\'étaler sur plus de 2 années civiles');
      }
    }

    /**
     * Vérifie le chevauchement avec d'autres exercices de la même société
     */
    static async validateNonChevauchement(societeId, dateDebut, dateFin, excludeId = null) {
      const whereClause = {
        societeId,
        [sequelize.Op.or]: [
          {
            dateDebut: {
              [sequelize.Op.between]: [dateDebut, dateFin]
            }
          },
          {
            dateFin: {
              [sequelize.Op.between]: [dateDebut, dateFin]
            }
          },
          {
            [sequelize.Op.and]: [
              { dateDebut: { [sequelize.Op.lte]: dateDebut } },
              { dateFin: { [sequelize.Op.gte]: dateFin } }
            ]
          }
        ]
      };

      if (excludeId) {
        whereClause.id = { [sequelize.Op.ne]: excludeId };
      }

      const exercicesChevauchants = await ExerciceComptable.findAll({
        where: whereClause
      });

      if (exercicesChevauchants.length > 0) {
        throw new Error('Les dates de l\'exercice chevauchent avec un exercice existant');
      }
    }
  }

  ExerciceComptable.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    societeId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'societes',
        key: 'id'
      }
    },
    libelle: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [3, 100]
      }
    },
    dateDebut: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    dateFin: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    statut: {
      type: DataTypes.ENUM('OUVERT', 'CLOTURE', 'ARCHIVE'),
      allowNull: false,
      defaultValue: 'OUVERT'
    },
    exercicePrecedentId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'exercice_comptables',
        key: 'id'
      }
    },
    reportANouveau: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0,
      validate: {
        isDecimal: true
      }
    },
    dateOuverture: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    dateCloture: {
      type: DataTypes.DATE,
      allowNull: true
    },
    utilisateurCloture: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: 'ID de l\'utilisateur qui a clôturé l\'exercice'
    },
    commentaireCloture: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'ExerciceComptable',
    tableName: 'exercice_comptables',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['societeId', 'dateDebut', 'dateFin']
      },
      {
        fields: ['societeId', 'statut']
      },
      {
        unique: true,
        fields: ['societeId', 'libelle']
      }
    ],
    hooks: {
      beforeValidate: async (exercice) => {
        if (exercice.dateDebut && exercice.dateFin) {
          exercice.validateExercice();
        }
      },
      beforeCreate: async (exercice) => {
        await ExerciceComptable.validateNonChevauchement(
          exercice.societeId,
          exercice.dateDebut,
          exercice.dateFin
        );
      },
      beforeUpdate: async (exercice) => {
        if (exercice.changed('dateDebut') || exercice.changed('dateFin')) {
          await ExerciceComptable.validateNonChevauchement(
            exercice.societeId,
            exercice.dateDebut,
            exercice.dateFin,
            exercice.id
          );
        }
      }
    }
  });

  return ExerciceComptable;
};
