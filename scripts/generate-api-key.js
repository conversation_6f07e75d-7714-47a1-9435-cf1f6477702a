#!/usr/bin/env node

/**
 * Script CLI pour générer des clés API
 * API Comptabilité SYSCOHADA
 */

const path = require('path');
const { program } = require('commander');

// Configurer le chemin vers les modèles
process.env.NODE_PATH = path.join(__dirname, '..');
require('module')._initPaths();

const ApiKeyService = require('../src/services/apiKeyService');
const { logger } = require('../src/config/logger');

// Configuration du programme CLI
program
  .name('generate-api-key')
  .description('Générateur de clés API pour l\'API Comptabilité SYSCOHADA')
  .version('1.0.0');

program
  .command('create')
  .description('Créer une nouvelle clé API')
  .requiredOption('-n, --name <name>', 'Nom de la clé API')
  .option('-p, --permissions <permissions>', 'Permissions (read,write,admin)', 'read')
  .option('-e, --expires <date>', 'Date d\'expiration (ISO format)')
  .option('-m, --metadata <json>', 'Métadonnées JSON')
  .action(async (options) => {
    try {
      console.log('🔑 Génération d\'une nouvelle clé API...\n');

      // Traiter les permissions
      const permissions = options.permissions.split(',').map(p => p.trim());
      
      // Traiter les métadonnées
      let metadata = {};
      if (options.metadata) {
        try {
          metadata = JSON.parse(options.metadata);
        } catch (error) {
          console.error('❌ Erreur: Métadonnées JSON invalides');
          process.exit(1);
        }
      }

      // Traiter la date d'expiration
      let expiresAt = null;
      if (options.expires) {
        expiresAt = new Date(options.expires);
        if (isNaN(expiresAt.getTime())) {
          console.error('❌ Erreur: Date d\'expiration invalide');
          process.exit(1);
        }
      }

      // Créer la clé API
      const apiKey = await ApiKeyService.createApiKey({
        name: options.name,
        permissions,
        expiresAt,
        metadata,
        createdBy: 'cli-script'
      });

      console.log('✅ Clé API créée avec succès!\n');
      console.log('📋 Détails de la clé:');
      console.log(`   ID: ${apiKey.id}`);
      console.log(`   Nom: ${apiKey.name}`);
      console.log(`   Préfixe: ${apiKey.prefix}`);
      console.log(`   Permissions: ${apiKey.permissions.join(', ')}`);
      console.log(`   Expire le: ${apiKey.expiresAt ? apiKey.expiresAt.toISOString() : 'Jamais'}`);
      console.log(`   Créée le: ${apiKey.createdAt.toISOString()}\n`);
      
      console.log('🔐 Clé API (à conserver précieusement):');
      console.log(`   ${apiKey.key}\n`);
      
      console.log('⚠️  IMPORTANT: Cette clé ne sera plus jamais affichée!');
      console.log('   Copiez-la et conservez-la en lieu sûr.\n');
      
      console.log('📖 Utilisation:');
      console.log('   Header: Authorization: Bearer <clé>');
      console.log('   ou Header: X-API-Key: <clé>');
      console.log('   ou Query: ?api_key=<clé> (moins sécurisé)\n');

    } catch (error) {
      console.error('❌ Erreur lors de la création de la clé API:', error.message);
      logger.error('Erreur CLI génération clé API', { error: error.message });
      process.exit(1);
    }
  });

program
  .command('list')
  .description('Lister les clés API existantes')
  .option('-i, --include-inactive', 'Inclure les clés inactives')
  .option('-l, --limit <number>', 'Nombre de clés à afficher', '20')
  .action(async (options) => {
    try {
      console.log('📋 Liste des clés API...\n');

      const result = await ApiKeyService.listApiKeys({
        includeInactive: options.includeInactive,
        limit: parseInt(options.limit)
      });

      if (result.apiKeys.length === 0) {
        console.log('ℹ️  Aucune clé API trouvée.');
        return;
      }

      console.log(`📊 ${result.apiKeys.length} clé(s) trouvée(s):\n`);

      result.apiKeys.forEach((key, index) => {
        const status = key.isActive ? '🟢 Active' : '🔴 Inactive';
        const expires = key.expiresAt ? 
          `Expire le ${new Date(key.expiresAt).toLocaleDateString()}` : 
          'Jamais';
        const lastUsed = key.lastUsedAt ? 
          `Dernière utilisation: ${new Date(key.lastUsedAt).toLocaleDateString()}` : 
          'Jamais utilisée';

        console.log(`${index + 1}. ${key.name} (${key.prefix})`);
        console.log(`   ID: ${key.id}`);
        console.log(`   Statut: ${status}`);
        console.log(`   Permissions: ${key.permissions.join(', ')}`);
        console.log(`   ${expires}`);
        console.log(`   ${lastUsed}`);
        console.log(`   Créée le: ${new Date(key.createdAt).toLocaleDateString()}`);
        console.log('');
      });

    } catch (error) {
      console.error('❌ Erreur lors de la récupération des clés API:', error.message);
      logger.error('Erreur CLI liste clés API', { error: error.message });
      process.exit(1);
    }
  });

program
  .command('deactivate <id>')
  .description('Désactiver une clé API')
  .action(async (id) => {
    try {
      console.log(`🔒 Désactivation de la clé API ${id}...\n`);

      const result = await ApiKeyService.deactivateApiKey(id);

      if (!result) {
        console.error('❌ Clé API non trouvée');
        process.exit(1);
      }

      console.log('✅ Clé API désactivée avec succès!');
      console.log(`   Nom: ${result.name}`);
      console.log(`   Préfixe: ${result.prefix}`);

    } catch (error) {
      console.error('❌ Erreur lors de la désactivation:', error.message);
      logger.error('Erreur CLI désactivation clé API', { error: error.message, id });
      process.exit(1);
    }
  });

program
  .command('cleanup')
  .description('Nettoyer les clés expirées')
  .action(async () => {
    try {
      console.log('🧹 Nettoyage des clés expirées...\n');

      const deletedCount = await ApiKeyService.cleanupExpiredKeys();

      console.log(`✅ ${deletedCount} clé(s) expirée(s) supprimée(s)`);

    } catch (error) {
      console.error('❌ Erreur lors du nettoyage:', error.message);
      logger.error('Erreur CLI nettoyage clés API', { error: error.message });
      process.exit(1);
    }
  });

// Gestion des erreurs non capturées
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Erreur non gérée:', reason);
  logger.error('Erreur non gérée dans CLI', { reason, promise });
  process.exit(1);
});

// Analyser les arguments de la ligne de commande
program.parse();