#!/usr/bin/env node

/**
 * Script d'exécution des tests d'intégration CLI SYSCOHADA
 * Teste toutes les fonctionnalités avec une API active
 */

const { exec } = require('child_process');
const { promisify } = require('util');
const chalk = require('chalk');
const fs = require('fs');

const execAsync = promisify(exec);
const CLI_PATH = 'node /home/<USER>/Documents/GitHub/api-compta-generale/cli.js';

class IntegrationTestRunner {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
  }

  async runTest(command, description, expectSuccess = true) {
    console.log(chalk.blue(`🧪 Test: ${description}`));
    console.log(chalk.gray(`   Commande: ${CLI_PATH} ${command}`));
    
    try {
      const { stdout, stderr } = await execAsync(`${CLI_PATH} ${command}`, { timeout: 30000 });
      
      const success = expectSuccess ? !stderr || stderr.includes('[dotenv') : false;
      const status = success ? '✅' : '❌';
      
      console.log(chalk.green(`   ${status} Résultat:`));
      
      // Afficher les premières lignes du résultat
      const output = stdout || stderr;
      const lines = output.split('\n').slice(0, 3);
      lines.forEach(line => {
        if (line.trim()) {
          console.log(chalk.gray(`      ${line.trim()}`));
        }
      });
      
      this.results.push({
        command,
        description,
        success: success,
        output: output.substring(0, 200),
        timestamp: new Date().toISOString()
      });
      
      console.log(''); // Ligne vide
      return { success, output };
      
    } catch (error) {
      const success = !expectSuccess; // Si on s'attend à une erreur, c'est un succès
      const status = success ? '✅' : '❌';
      
      console.log(chalk.red(`   ${status} Erreur: ${error.message}`));
      
      this.results.push({
        command,
        description,
        success: success,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      console.log(''); // Ligne vide
      return { success, error: error.message };
    }
  }

  async runAllTests() {
    console.log(chalk.blue('🚀 === TESTS D\'INTÉGRATION CLI SYSCOHADA ===\n'));
    
    // 1. Tests de base
    console.log(chalk.yellow('=== 1. TESTS DE BASE ==='));
    await this.runTest('--help', 'Affichage de l\'aide');
    await this.runTest('--version', 'Affichage de la version');
    await this.runTest('status', 'Vérification du statut de l\'API');
    
    // 2. Tests d'authentification
    console.log(chalk.yellow('=== 2. TESTS D\'AUTHENTIFICATION ==='));
    await this.runTest('auth:verify', 'Vérification de la clé API');
    
    // 3. Gestion des clés API (Admin)
    console.log(chalk.yellow('=== 3. GESTION DES CLÉS API ==='));
    await this.runTest('apikeys:list', 'Liste des clés API');
    await this.runTest('apikeys:list --include-inactive', 'Liste avec clés inactives');
    await this.runTest('apikeys:list --limit 5', 'Liste avec limite');
    
    // 4. Gestion des sociétés
    console.log(chalk.yellow('=== 4. GESTION DES SOCIÉTÉS ==='));
    await this.runTest('societes:list', 'Liste des sociétés');
    
    // 5. Gestion des exercices
    console.log(chalk.yellow('=== 5. GESTION DES EXERCICES ==='));
    await this.runTest('exercices:list', 'Liste des exercices');
    await this.runTest('exercices:vue-clotures', 'Vue des clôtures');
    
    // 6. Plan comptable
    console.log(chalk.yellow('=== 6. PLAN COMPTABLE ==='));
    await this.runTest('comptes:list', 'Liste des comptes');
    await this.runTest('comptes:list --classe 1', 'Comptes classe 1');
    await this.runTest('comptes:list --limit 10', 'Comptes avec limite');
    await this.runTest('plan:personnalise', 'Plan comptable personnalisé');
    
    // 7. Journaux
    console.log(chalk.yellow('=== 7. JOURNAUX ==='));
    await this.runTest('journaux:list', 'Liste des journaux');
    
    // 8. Écritures comptables
    console.log(chalk.yellow('=== 8. ÉCRITURES COMPTABLES ==='));
    await this.runTest('ecritures:list', 'Liste des écritures');
    await this.runTest('ecritures:list --limit 5', 'Écritures avec limite');
    await this.runTest('ecritures:list --statut BROUILLARD', 'Écritures en brouillard');
    await this.runTest('ecritures:list --statut VALIDEE', 'Écritures validées');
    
    // 9. Templates
    console.log(chalk.yellow('=== 9. TEMPLATES D\'ÉCRITURES ==='));
    await this.runTest('templates:list', 'Liste des templates');
    await this.runTest('templates:list --limit 10', 'Templates avec limite');
    
    // 10. Tiers
    console.log(chalk.yellow('=== 10. TIERS ==='));
    await this.runTest('tiers:list', 'Liste des tiers');
    await this.runTest('tiers:list --limit 10', 'Tiers avec limite');
    
    // 11. Lettrage
    console.log(chalk.yellow('=== 11. LETTRAGE ==='));
    await this.runTest('lettrage:comptes', 'Comptes à lettrer');
    
    // 12. Amortissements
    console.log(chalk.yellow('=== 12. AMORTISSEMENTS ==='));
    await this.runTest('amortissements:list', 'Liste des amortissements');
    await this.runTest('amortissements:calculer-dotations', 'Calcul des dotations');
    
    // 13. États et rapports
    console.log(chalk.yellow('=== 13. ÉTATS ET RAPPORTS ==='));
    await this.runTest('dashboard', 'Tableau de bord');
    
    // 14. Paramètres
    console.log(chalk.yellow('=== 14. PARAMÈTRES ==='));
    await this.runTest('parametres:list', 'Liste des paramètres');
    
    // 15. Export
    console.log(chalk.yellow('=== 15. EXPORT ==='));
    await this.runTest('export:ecritures --format json --limit 5', 'Export écritures JSON');
    
    // 16. Tests d'erreur (doivent échouer)
    console.log(chalk.yellow('=== 16. TESTS DE GESTION D\'ERREURS ==='));
    await this.runTest('commande-inexistante', 'Commande inexistante', false);
    await this.runTest('ecritures:show 999999', 'Écriture inexistante', false);
    
    return this.generateReport();
  }

  generateReport() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    
    const totalTests = this.results.length;
    const successTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - successTests;
    const successRate = totalTests > 0 ? (successTests / totalTests * 100).toFixed(1) : 0;

    console.log(chalk.yellow('\n=== RAPPORT DE TESTS D\'INTÉGRATION ==='));
    console.log(`📊 Total des tests: ${totalTests}`);
    console.log(`✅ Tests réussis: ${chalk.green(successTests)}`);
    console.log(`❌ Tests échoués: ${chalk.red(failedTests)}`);
    console.log(`📈 Taux de réussite: ${chalk.blue(successRate)}%`);
    console.log(`⏱️  Durée totale: ${Math.round(duration/1000)}s`);

    if (failedTests > 0) {
      console.log(chalk.red('\n❌ TESTS ÉCHOUÉS:'));
      this.results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`   - ${r.description}: ${r.error || 'Erreur inconnue'}`);
        });
    }

    // Sauvegarder le rapport détaillé
    const report = {
      summary: {
        totalTests,
        successTests,
        failedTests,
        successRate: `${successRate}%`,
        duration: `${Math.round(duration/1000)}s`,
        timestamp: new Date().toISOString()
      },
      results: this.results
    };

    const reportFile = '/home/<USER>/Documents/GitHub/api-compta-generale/integration-test-report.json';
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    console.log(`\n📄 Rapport détaillé sauvegardé: ${reportFile}`);

    return report;
  }
}

// Exécution
if (require.main === module) {
  const runner = new IntegrationTestRunner();
  
  runner.runAllTests()
    .then(report => {
      const exitCode = report.summary.failedTests > 0 ? 1 : 0;
      console.log(chalk.green(`\n🎉 Tests d'intégration terminés (code de sortie: ${exitCode})`));
      process.exit(exitCode);
    })
    .catch(error => {
      console.error(chalk.red('❌ Erreur fatale lors des tests:'), error);
      process.exit(1);
    });
}

module.exports = IntegrationTestRunner;