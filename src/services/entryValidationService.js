'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Service spécialisé pour la validation des écritures comptables
 * Conforme aux normes SYSCOHADA - Phase 1
 */
class EntryValidationService {
  constructor(models) {
    this.models = models;
  }

  /**
   * Validation complète d'une écriture comptable
   * @param {Object} ecritureData - Données de l'écriture
   * @param {Array} lignesData - Données des lignes
   * @param {string} societeId - ID de la société
   * @returns {Promise<Object>} Résultat de validation
   */
  async validateEntry(ecritureData, lignesData, societeId) {
    const erreurs = [];
    const avertissements = [];
    const suggestions = [];

    try {
      // 1. Validation équilibre débit/crédit (CRITIQUE)
      const equilibre = this.validateBalance(lignesData);
      if (!equilibre.valide) {
        erreurs.push(...equilibre.erreurs);
      }

      // 2. Validation existence des comptes (CRITIQUE)
      const comptes = await this.validateAccountsExistence(lignesData, societeId);
      if (!comptes.valide) {
        erreurs.push(...comptes.erreurs);
      }
      if (comptes.suggestions) {
        suggestions.push(...comptes.suggestions);
      }

      // 3. Validation dates dans l'exercice (CRITIQUE)
      const dates = await this.validateDatesInExercise(ecritureData, societeId);
      if (!dates.valide) {
        erreurs.push(...dates.erreurs);
      }

      // 4. Validation numéros pièce uniques (CRITIQUE)
      const numeroPiece = await this.validateUniqueReference(ecritureData, societeId);
      if (!numeroPiece.valide) {
        erreurs.push(...numeroPiece.erreurs);
      }

      // 5. Validation cohérence comptes par type mouvement (HAUTE)
      const coherenceComptes = await this.validateAccountCoherence(ecritureData, lignesData);
      if (!coherenceComptes.valide) {
        avertissements.push(...coherenceComptes.avertissements);
      }
      if (coherenceComptes.suggestions) {
        suggestions.push(...coherenceComptes.suggestions);
      }

      // 6. Validation spécifique par journal (HAUTE)
      const journal = await this.validateJournalSpecific(ecritureData, lignesData, societeId);
      if (!journal.valide) {
        avertissements.push(...journal.avertissements);
      }

      // 7. Validation tiers (si applicable)
      const tiers = await this.validateParties(lignesData, societeId);
      if (!tiers.valide) {
        erreurs.push(...tiers.erreurs);
      }

      const resultat = {
        valide: erreurs.length === 0,
        erreurs,
        avertissements,
        suggestions,
        niveauValidation: this.determineValidationLevel(erreurs, avertissements),
        recommandations: this.generateRecommendations(erreurs, avertissements, suggestions)
      };

      logger.info('Validation écriture effectuée', {
        numeroEcriture: ecritureData.numeroEcriture,
        valide: resultat.valide,
        nombreErreurs: erreurs.length,
        nombreAvertissements: avertissements.length,
        nombreSuggestions: suggestions.length
      });

      return resultat;

    } catch (error) {
      logger.error('Erreur lors de la validation d\'écriture', {
        numeroEcriture: ecritureData.numeroEcriture,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Validation équilibre débit/crédit
   * @param {Array} lignesData - Données des lignes
   * @returns {Object} Résultat validation
   */
  validateBalance(lignesData) {
    const erreurs = [];
    
    let totalDebit = 0;
    let totalCredit = 0;

    lignesData.forEach((ligne, index) => {
      const debit = parseFloat(ligne.debit || 0);
      const credit = parseFloat(ligne.credit || 0);

      // Validation montants positifs
      if (debit < 0 || credit < 0) {
        erreurs.push(`Ligne ${index + 1}: Les montants doivent être positifs`);
      }

      // Validation exclusivité débit/crédit
      if (debit > 0 && credit > 0) {
        erreurs.push(`Ligne ${index + 1}: Une ligne ne peut avoir à la fois débit et crédit`);
      }

      // Validation montant non nul
      if (debit === 0 && credit === 0) {
        erreurs.push(`Ligne ${index + 1}: Une ligne doit avoir soit un débit soit un crédit`);
      }

      totalDebit += debit;
      totalCredit += credit;
    });

    // Validation équilibre global
    if (Math.abs(totalDebit - totalCredit) >= 0.01) {
      erreurs.push(`Écriture déséquilibrée: Débit ${totalDebit.toFixed(2)} ≠ Crédit ${totalCredit.toFixed(2)}`);
    }

    return {
      valide: erreurs.length === 0,
      erreurs,
      totalDebit,
      totalCredit
    };
  }

  /**
   * Validation existence des comptes
   * @param {Array} lignesData - Données des lignes
   * @param {string} societeId - ID de la société
   * @returns {Promise<Object>} Résultat validation
   */
  async validateAccountsExistence(lignesData, societeId) {
    const erreurs = [];
    const suggestions = [];

    for (const [index, ligne] of lignesData.entries()) {
      if (!ligne.compteNumero) {
        erreurs.push(`Ligne ${index + 1}: Compte comptable obligatoire`);
        continue;
      }

      const compte = await this.models.CompteComptable.findOne({
        where: {
          numero: ligne.compteNumero,
          societeId
        }
      });

      if (!compte) {
        erreurs.push(`Ligne ${index + 1}: Compte ${ligne.compteNumero} inexistant`);
        
        // Suggestion de comptes similaires
        const comptesSimilaires = await this.findSimilarAccounts(ligne.compteNumero, societeId);
        if (comptesSimilaires.length > 0) {
          suggestions.push({
            ligne: index + 1,
            compteRecherche: ligne.compteNumero,
            comptesSuggeres: comptesSimilaires.slice(0, 3)
          });
        }
      } else if (!compte.actif) {
        erreurs.push(`Ligne ${index + 1}: Compte ${ligne.compteNumero} inactif`);
      }
    }

    return {
      valide: erreurs.length === 0,
      erreurs,
      suggestions
    };
  }

  /**
   * Validation dates dans l'exercice
   * @param {Object} ecritureData - Données de l'écriture
   * @param {string} societeId - ID de la société
   * @returns {Promise<Object>} Résultat validation
   */
  async validateDatesInExercise(ecritureData, societeId) {
    const erreurs = [];

    if (!ecritureData.dateEcriture) {
      erreurs.push('Date d\'écriture obligatoire');
      return { valide: false, erreurs };
    }

    // Recherche de l'exercice ouvert
    const exercice = await this.models.ExerciceComptable.findOne({
      where: {
        societeId,
        statut: 'OUVERT'
      }
    });

    if (!exercice) {
      erreurs.push('Aucun exercice comptable ouvert trouvé');
      return { valide: false, erreurs };
    }

    const dateEcriture = new Date(ecritureData.dateEcriture);
    const dateDebut = new Date(exercice.dateDebut);
    const dateFin = new Date(exercice.dateFin);

    if (dateEcriture < dateDebut || dateEcriture > dateFin) {
      erreurs.push(`Date d'écriture hors exercice (${dateDebut.toLocaleDateString()} - ${dateFin.toLocaleDateString()})`);
    }

    // Validation date pièce si fournie
    if (ecritureData.datePiece) {
      const datePiece = new Date(ecritureData.datePiece);
      if (datePiece > dateEcriture) {
        erreurs.push('Date de pièce postérieure à la date d\'écriture');
      }
    }

    return {
      valide: erreurs.length === 0,
      erreurs
    };
  }

  /**
   * Validation unicité référence
   * @param {Object} ecritureData - Données de l'écriture
   * @param {string} societeId - ID de la société
   * @returns {Promise<Object>} Résultat validation
   */
  async validateUniqueReference(ecritureData, societeId) {
    const erreurs = [];

    if (!ecritureData.numeroEcriture) {
      erreurs.push('Numéro d\'écriture obligatoire');
      return { valide: false, erreurs };
    }

    const ecritureExistante = await this.models.EcritureComptable.findOne({
      where: {
        numeroEcriture: ecritureData.numeroEcriture,
        societeId,
        ...(ecritureData.id && { id: { [this.models.Sequelize.Op.ne]: ecritureData.id } })
      }
    });

    if (ecritureExistante) {
      erreurs.push(`Numéro d'écriture ${ecritureData.numeroEcriture} déjà utilisé`);
    }

    return {
      valide: erreurs.length === 0,
      erreurs
    };
  }

  /**
   * Validation cohérence comptes par type d'opération
   * @param {Object} ecritureData - Données de l'écriture
   * @param {Array} lignesData - Données des lignes
   * @returns {Promise<Object>} Résultat validation
   */
  async validateAccountCoherence(ecritureData, lignesData) {
    const avertissements = [];
    const suggestions = [];

    if (!ecritureData.typeOperation || ecritureData.typeOperation === 'AUTRE') {
      return { valide: true, avertissements, suggestions };
    }

    const reglesCoherence = {
      'VENTE': {
        debits: ['411', '512'], // Clients, Banque
        credits: ['701', '445'], // Ventes, TVA collectée
        message: 'Vente: Débiter clients/banque, Créditer ventes/TVA'
      },
      'ACHAT': {
        debits: ['601', '445'], // Achats, TVA déductible
        credits: ['401', '512'], // Fournisseurs, Banque
        message: 'Achat: Débiter achats/TVA, Créditer fournisseurs/banque'
      },
      'TRESORERIE': {
        debits: ['512', '531'], // Banque, Caisse
        credits: ['512', '531'], // Banque, Caisse
        message: 'Trésorerie: Mouvements entre comptes de trésorerie'
      }
    };

    const regle = reglesCoherence[ecritureData.typeOperation];
    if (!regle) return { valide: true, avertissements, suggestions };

    lignesData.forEach((ligne, index) => {
      const classe = ligne.compteNumero ? ligne.compteNumero.substring(0, 3) : '';
      const debit = parseFloat(ligne.debit || 0);
      const credit = parseFloat(ligne.credit || 0);

      if (debit > 0 && !regle.debits.some(c => classe.startsWith(c))) {
        avertissements.push(`Ligne ${index + 1}: Compte ${ligne.compteNumero} inhabituel au débit pour ${ecritureData.typeOperation}`);
        suggestions.push({
          ligne: index + 1,
          message: regle.message,
          comptesRecommandes: regle.debits
        });
      }

      if (credit > 0 && !regle.credits.some(c => classe.startsWith(c))) {
        avertissements.push(`Ligne ${index + 1}: Compte ${ligne.compteNumero} inhabituel au crédit pour ${ecritureData.typeOperation}`);
        suggestions.push({
          ligne: index + 1,
          message: regle.message,
          comptesRecommandes: regle.credits
        });
      }
    });

    return {
      valide: true,
      avertissements,
      suggestions
    };
  }

  /**
   * Validation spécifique par journal
   * @param {Object} ecritureData - Données de l'écriture
   * @param {Array} lignesData - Données des lignes
   * @param {string} societeId - ID de la société
   * @returns {Promise<Object>} Résultat validation
   */
  async validateJournalSpecific(ecritureData, lignesData, societeId) {
    const avertissements = [];

    const journal = await this.models.Journal.findOne({
      where: {
        code: ecritureData.journalCode,
        societeId
      }
    });

    if (!journal) {
      return { valide: false, avertissements: ['Journal inexistant'] };
    }

    // Validation selon le type de journal
    switch (journal.type) {
      case 'VENTE':
        if (!lignesData.some(l => l.compteNumero && l.compteNumero.startsWith('411'))) {
          avertissements.push('Journal de vente: Compte client (411) recommandé');
        }
        break;
      case 'ACHAT':
        if (!lignesData.some(l => l.compteNumero && l.compteNumero.startsWith('401'))) {
          avertissements.push('Journal d\'achat: Compte fournisseur (401) recommandé');
        }
        break;
      case 'TRESORERIE':
        if (!lignesData.some(l => l.compteNumero && (l.compteNumero.startsWith('512') || l.compteNumero.startsWith('531')))) {
          avertissements.push('Journal de trésorerie: Compte banque/caisse recommandé');
        }
        break;
    }

    return {
      valide: true,
      avertissements
    };
  }

  /**
   * Validation des tiers
   * @param {Array} lignesData - Données des lignes
   * @param {string} societeId - ID de la société
   * @returns {Promise<Object>} Résultat validation
   */
  async validateParties(lignesData, societeId) {
    const erreurs = [];

    for (const [index, ligne] of lignesData.entries()) {
      if (ligne.tiersId) {
        const tiers = await this.models.Party.findOne({
          where: {
            id: ligne.tiersId,
            societeId
          }
        });

        if (!tiers) {
          erreurs.push(`Ligne ${index + 1}: Tiers inexistant`);
        } else if (!tiers.actif) {
          erreurs.push(`Ligne ${index + 1}: Tiers inactif`);
        }
      }
    }

    return {
      valide: erreurs.length === 0,
      erreurs
    };
  }

  /**
   * Recherche de comptes similaires
   * @param {string} numeroRecherche - Numéro recherché
   * @param {string} societeId - ID de la société
   * @returns {Promise<Array>} Comptes similaires
   */
  async findSimilarAccounts(numeroRecherche, societeId) {
    const { Op } = this.models.Sequelize;
    
    return await this.models.CompteComptable.findAll({
      where: {
        societeId,
        actif: true,
        [Op.or]: [
          { numero: { [Op.like]: `${numeroRecherche.substring(0, 2)}%` } },
          { intitule: { [Op.iLike]: `%${numeroRecherche}%` } }
        ]
      },
      attributes: ['numero', 'intitule'],
      limit: 5,
      order: [['numero', 'ASC']]
    });
  }

  /**
   * Détermine le niveau de validation
   * @param {Array} erreurs - Liste des erreurs
   * @param {Array} avertissements - Liste des avertissements
   * @returns {string} Niveau de validation
   */
  determineValidationLevel(erreurs, avertissements) {
    if (erreurs.length > 0) return 'NON_CONFORME';
    if (avertissements.length > 0) return 'CONFORME_AVEC_RESERVES';
    return 'CONFORME_SYSCOHADA';
  }

  /**
   * Génère des recommandations
   * @param {Array} erreurs - Liste des erreurs
   * @param {Array} avertissements - Liste des avertissements
   * @param {Array} suggestions - Liste des suggestions
   * @returns {Array} Recommandations
   */
  generateRecommendations(erreurs, avertissements, suggestions) {
    const recommandations = [];

    if (erreurs.length > 0) {
      recommandations.push('Corriger les erreurs critiques avant validation');
    }

    if (avertissements.length > 0) {
      recommandations.push('Vérifier la cohérence comptable des comptes utilisés');
    }

    if (suggestions.length > 0) {
      recommandations.push('Consulter les suggestions de comptes alternatifs');
    }

    return recommandations;
  }
}

module.exports = EntryValidationService;