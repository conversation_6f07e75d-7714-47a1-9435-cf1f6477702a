'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Ajout des champs tiers à la table ligne_ecritures
    await queryInterface.addColumn('ligne_ecritures', 'tiers_id', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'parties',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    await queryInterface.addColumn('ligne_ecritures', 'tiers_nom', {
      type: Sequelize.STRING(255),
      allowNull: true
    });

    // Ajout d'un index pour optimiser les requêtes sur les tiers
    await queryInterface.addIndex('ligne_ecritures', {
      fields: ['tiers_id'],
      name: 'ligne_ecritures_tiers_id_idx'
    });
  },

  async down(queryInterface, Sequelize) {
    // Suppression de l'index
    await queryInterface.removeIndex('ligne_ecritures', 'ligne_ecritures_tiers_id_idx');

    // Suppression des colonnes
    await queryInterface.removeColumn('ligne_ecritures', 'tiers_nom');
    await queryInterface.removeColumn('ligne_ecritures', 'tiers_id');
  }
};