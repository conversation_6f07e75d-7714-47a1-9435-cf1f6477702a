'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class AuditLog extends Model {
    /**
     * Associations du modèle AuditLog
     */
    static associate(models) {
      // Un log d'audit peut être lié à une société
      AuditLog.belongsTo(models.Societe, {
        foreignKey: 'societeId',
        as: 'societe'
      });
    }

    /**
     * Méthodes statiques
     */

    /**
     * Enregistre une action d'audit
     * @param {Object} params - Paramètres de l'audit
     * @returns {Promise<AuditLog>} Log créé
     */
    static async enregistrerAction(params) {
      const {
        action,
        table,
        recordId,
        anciensValeurs = null,
        nouvellesValeurs = null,
        utilisateurId = null,
        societeId = null,
        adresseIP = null,
        userAgent = null,
        details = null
      } = params;

      return await this.create({
        action,
        table,
        recordId,
        anciensValeurs: anciensValeurs ? JSON.stringify(anciensValeurs) : null,
        nouvellesValeurs: nouvellesValeurs ? JSON.stringify(nouvellesValeurs) : null,
        utilisateurId,
        societeId,
        adresseIP,
        userAgent,
        details
      });
    }

    /**
     * Recherche dans les logs d'audit
     * @param {Object} filtres - Critères de recherche
     * @returns {Promise<Object>} Résultats paginés
     */
    static async rechercherLogs(filtres = {}) {
      const {
        page = 1,
        limit = 50,
        action,
        table,
        utilisateurId,
        societeId,
        dateDebut,
        dateFin,
        recordId
      } = filtres;

      const offset = (page - 1) * limit;
      const where = {};

      if (action) where.action = action;
      if (table) where.table = table;
      if (utilisateurId) where.utilisateurId = utilisateurId;
      if (societeId) where.societeId = societeId;
      if (recordId) where.recordId = recordId;

      if (dateDebut || dateFin) {
        const { Op } = sequelize;
        where.createdAt = {};
        if (dateDebut) where.createdAt[Op.gte] = new Date(dateDebut);
        if (dateFin) where.createdAt[Op.lte] = new Date(dateFin);
      }

      const { count, rows } = await this.findAndCountAll({
        where,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: sequelize.models.Societe,
            as: 'societe',
            attributes: ['id', 'nom']
          }
        ]
      });

      return {
        logs: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      };
    }

    /**
     * Obtient l'historique d'un enregistrement
     * @param {string} table - Nom de la table
     * @param {string} recordId - ID de l'enregistrement
     * @returns {Promise<Array>} Historique complet
     */
    static async getHistoriqueRecord(table, recordId) {
      return await this.findAll({
        where: { table, recordId },
        order: [['createdAt', 'ASC']],
        include: [
          {
            model: sequelize.models.Societe,
            as: 'societe',
            attributes: ['id', 'nom']
          }
        ]
      });
    }

    /**
     * Méthodes d'instance
     */

    /**
     * Retourne les anciennes valeurs parsées
     */
    getAnciensValeursJSON() {
      return this.anciensValeurs ? JSON.parse(this.anciensValeurs) : null;
    }

    /**
     * Retourne les nouvelles valeurs parsées
     */
    getNouvellesValeursJSON() {
      return this.nouvellesValeurs ? JSON.parse(this.nouvellesValeurs) : null;
    }

    /**
     * Retourne un résumé des changements
     */
    getResumeChangements() {
      const anciens = this.getAnciensValeursJSON();
      const nouveaux = this.getNouvellesValeursJSON();
      
      if (!anciens || !nouveaux) return null;

      const changements = {};
      Object.keys(nouveaux).forEach(key => {
        if (anciens[key] !== nouveaux[key]) {
          changements[key] = {
            ancien: anciens[key],
            nouveau: nouveaux[key]
          };
        }
      });

      return changements;
    }
  }

  AuditLog.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    action: {
      type: DataTypes.ENUM('CREATE', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT', 'RESET'),
      allowNull: false
    },
    table: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    recordId: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    anciensValeurs: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'anciens_valeurs'
    },
    nouvellesValeurs: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'nouvelles_valeurs'
    },
    utilisateurId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'utilisateur_id'
    },
    societeId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'societe_id',
      references: {
        model: 'societes',
        key: 'id'
      }
    },
    adresseIP: {
      type: DataTypes.STRING(45),
      allowNull: true,
      field: 'adresse_ip'
    },
    userAgent: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'user_agent'
    },
    details: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'AuditLog',
    tableName: 'audit_logs',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['action']
      },
      {
        fields: ['table']
      },
      {
        fields: ['table', 'record_id']
      },
      {
        fields: ['utilisateur_id']
      },
      {
        fields: ['societe_id']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return AuditLog;
};
