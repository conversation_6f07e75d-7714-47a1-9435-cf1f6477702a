/**
 * Modèle pour les clés API
 * API Comptabilité SYSCOHADA
 */

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ApiKey = sequelize.define('ApiKey', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: 'Nom descriptif de la clé API'
    },
    key: {
      type: DataTypes.STRING(64),
      allowNull: false,
      unique: true,
      comment: 'Clé API (hashée)'
    },
    prefix: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: 'Préfixe visible de la clé (pour identification)'
    },
    permissions: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: ['read'],
      comment: 'Permissions accordées à cette clé'
    },
    lastUsedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Dernière utilisation de la clé'
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Date d\'expiration de la clé'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
      comment: 'Statut actif/inactif de la clé'
    },
    createdBy: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Créateur de la clé'
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Métadonnées additionnelles'
    }
  }, {
    tableName: 'api_keys',
    timestamps: true,
    underscored: true, // Utilise snake_case pour les noms de colonnes
    indexes: [
      {
        fields: ['key'],
        unique: true
      },
      {
        fields: ['isActive']
      },
      {
        fields: ['expiresAt']
      }
    ],
    hooks: {
      beforeCreate: (apiKey) => {
        // S'assurer que les permissions sont un tableau
        if (typeof apiKey.permissions === 'string') {
          apiKey.permissions = [apiKey.permissions];
        }
      },
      beforeUpdate: (apiKey) => {
        // S'assurer que les permissions sont un tableau
        if (typeof apiKey.permissions === 'string') {
          apiKey.permissions = [apiKey.permissions];
        }
      }
    }
  });

  return ApiKey;
};