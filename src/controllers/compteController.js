/**
 * Controller pour la gestion des comptes comptables
 * API Comptabilité SYSCOHADA
 */

const { CompteComptable, Societe } = require('../models');
const { AppError, NotFoundError, ValidationError, ConflictError, asyncHandler } = require('../middleware/errorHandler');
const { logger } = require('../config/logger');
const { Op } = require('sequelize');

/**
 * Récupérer tous les comptes avec filtres
 */
const getAllComptes = asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 50, 
    classe, 
    nature, 
    niveau,
    search,
    societeId 
  } = req.query;
  
  const offset = (page - 1) * limit;
  const where = {};

  // Filtres
  if (classe) where.classe = classe;
  if (nature) where.nature = nature;
  if (niveau) where.niveau = niveau;
  if (societeId) where.societeId = societeId;
  
  // Recherche textuelle
  if (search) {
    where[Op.or] = [
      { numero: { [Op.iLike]: `%${search}%` } },
      { libelle: { [Op.iLike]: `%${search}%` } }
    ];
  }

  const { count, rows: comptes } = await CompteComptable.findAndCountAll({
    where,
    limit: parseInt(limit),
    offset: parseInt(offset),
    include: [
      {
        model: CompteComptable,
        as: 'parent',
        attributes: ['numero', 'libelle']
      },
      {
        model: CompteComptable,
        as: 'sousComptes',
        attributes: ['numero', 'libelle', 'niveau']
      }
    ],
    order: [['numero', 'ASC']]
  });

  logger.info('Comptes comptables récupérés', {
    count,
    filtres: { classe, nature, niveau, search }
  });

  res.json({
    success: true,
    data: comptes,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count,
      pages: Math.ceil(count / limit)
    }
  });
});

/**
 * Récupérer un compte par numéro
 */
const getCompteByNumero = asyncHandler(async (req, res) => {
  const { numero } = req.params;

  const compte = await CompteComptable.findByPk(numero, {
    include: [
      {
        model: CompteComptable,
        as: 'parent',
        attributes: ['numero', 'libelle', 'classe']
      },
      {
        model: CompteComptable,
        as: 'sousComptes',
        attributes: ['numero', 'libelle', 'niveau']
      },
      {
        model: Societe,
        as: 'societe',
        attributes: ['id', 'nom']
      }
    ]
  });

  if (!compte) {
    throw new NotFoundError('Compte comptable');
  }

  // Ajouter des informations calculées
  const compteData = compte.toJSON();
  compteData.isCompteBilan = compte.isCompteBilan();
  compteData.isCompteGestion = compte.isCompteGestion();
  compteData.libelleClasse = compte.getLibelleClasse();

  logger.info('Compte comptable récupéré', { numero });

  res.json({
    success: true,
    data: compteData
  });
});

/**
 * Créer un nouveau compte comptable
 */
const createCompte = asyncHandler(async (req, res) => {
  const compteData = req.body;

  // Validation du numéro SYSCOHADA
  try {
    CompteComptable.validateNumeroSYCOHADA(compteData.numero);
  } catch (error) {
    throw new ValidationError(error.message);
  }

  // Vérifier si le compte existe déjà
  const existingCompte = await CompteComptable.findByPk(compteData.numero);
  if (existingCompte) {
    throw new ConflictError('Un compte avec ce numéro existe déjà');
  }

  // Vérifier que le compte parent existe si spécifié
  if (compteData.compteParent) {
    const parent = await CompteComptable.findByPk(compteData.compteParent);
    if (!parent) {
      throw new ValidationError('Le compte parent spécifié n\'existe pas');
    }
  }

  const compte = await CompteComptable.create(compteData);

  logger.info('Compte comptable créé', {
    numero: compte.numero,
    libelle: compte.libelle,
    classe: compte.classe
  });

  res.status(201).json({
    success: true,
    message: 'Compte comptable créé avec succès',
    data: compte
  });
});

/**
 * Mettre à jour un compte comptable
 */
const updateCompte = asyncHandler(async (req, res) => {
  const { numero } = req.params;
  const updateData = req.body;

  const compte = await CompteComptable.findByPk(numero);
  if (!compte) {
    throw new NotFoundError('Compte comptable');
  }

  // Ne pas permettre la modification du numéro
  if (updateData.numero && updateData.numero !== numero) {
    throw new ValidationError('Le numéro de compte ne peut pas être modifié');
  }

  // Vérifier le compte parent si modifié
  if (updateData.compteParent) {
    const parent = await CompteComptable.findByPk(updateData.compteParent);
    if (!parent) {
      throw new ValidationError('Le compte parent spécifié n\'existe pas');
    }
  }

  await compte.update(updateData);

  logger.info('Compte comptable mis à jour', {
    numero,
    modifications: Object.keys(updateData)
  });

  res.json({
    success: true,
    message: 'Compte comptable mis à jour avec succès',
    data: compte
  });
});

/**
 * Supprimer un compte comptable
 */
const deleteCompte = asyncHandler(async (req, res) => {
  const { numero } = req.params;

  const compte = await CompteComptable.findByPk(numero);
  if (!compte) {
    throw new NotFoundError('Compte comptable');
  }

  // Vérifier s'il y a des sous-comptes
  const sousComptesCount = await CompteComptable.count({
    where: { compteParent: numero }
  });

  if (sousComptesCount > 0) {
    throw new ValidationError('Impossible de supprimer un compte qui a des sous-comptes');
  }

  // TODO: Vérifier s'il y a des écritures liées quand le modèle sera disponible

  await compte.destroy();

  logger.warn('Compte comptable supprimé', {
    numero,
    libelle: compte.libelle
  });

  res.json({
    success: true,
    message: 'Compte comptable supprimé avec succès'
  });
});

/**
 * Récupérer les comptes par classe
 */
const getComptesByClasse = asyncHandler(async (req, res) => {
  const { classe } = req.params;
  const { niveau } = req.query;

  const where = { classe: parseInt(classe) };
  if (niveau) where.niveau = parseInt(niveau);

  const comptes = await CompteComptable.findAll({
    where,
    order: [['numero', 'ASC']],
    include: [
      {
        model: CompteComptable,
        as: 'sousComptes',
        attributes: ['numero', 'libelle']
      }
    ]
  });

  // Ajouter le libellé de la classe
  const libelleClasse = comptes.length > 0 ? comptes[0].getLibelleClasse() : null;

  res.json({
    success: true,
    data: {
      classe: parseInt(classe),
      libelleClasse,
      comptes
    }
  });
});

/**
 * Récupérer la hiérarchie des comptes
 */
const getHierarchieComptes = asyncHandler(async (req, res) => {
  const { classe } = req.query;
  const where = {};
  
  if (classe) where.classe = parseInt(classe);

  // Récupérer tous les comptes avec leurs relations
  const comptes = await CompteComptable.findAll({
    where,
    include: [
      {
        model: CompteComptable,
        as: 'sousComptes',
        include: [
          {
            model: CompteComptable,
            as: 'sousComptes'
          }
        ]
      }
    ],
    where: { compteParent: null }, // Seulement les comptes racines
    order: [['numero', 'ASC']]
  });

  res.json({
    success: true,
    data: comptes
  });
});

module.exports = {
  getAllComptes,
  getCompteByNumero,
  createCompte,
  updateCompte,
  deleteCompte,
  getComptesByClasse,
  getHierarchieComptes
};
