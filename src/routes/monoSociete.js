/**
 * Routes simplifiées pour architecture mono-société
 * Ces routes n'exigent pas de passer societeId dans les paramètres
 */

const express = require('express');
const router = express.Router();

// Middleware
const { protect } = require('../middleware/auth');
const { monoSocieteMiddleware } = require('../middleware/monoSociete');

// Controllers
const societeController = require('../controllers/societeController');
const compteController = require('../controllers/compteController');
const ecritureController = require('../controllers/ecritureController');
const journalController = require('../controllers/journalController');
const dashboardController = require('../controllers/dashboardController');
const calculController = require('../controllers/calculController');
const etatController = require('../controllers/etatController');
const lettrageController = require('../controllers/lettrageController');
const templateController = require('../controllers/templateController');
const importExportController = require('../controllers/importExportController');

// Appliquer les middlewares à toutes les routes
router.use(protect);
router.use(monoSocieteMiddleware);

/**
 * CONFIGURATION SOCIÉTÉ
 */

/**
 * @route GET /api/v1/mono/config
 * @desc Récupérer la configuration de la société de cette instance
 */
router.get('/config', async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        societe: req.societe,
        configuration: {
          societeId: req.societeId,
          nom: req.societe.nom,
          devise: req.societe.devise,
          exerciceActuel: req.societe.exercices?.[0] || null
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'CONFIG_ERROR',
        message: 'Erreur lors de la récupération de la configuration'
      }
    });
  }
});

/**
 * @route GET /api/v1/mono/stats
 * @desc Statistiques de la société
 */
router.get('/stats', societeController.getSocieteStats);

/**
 * PLAN COMPTABLE SIMPLIFIÉ
 */

/**
 * @route GET /api/v1/mono/comptes
 * @desc Lister tous les comptes de la société
 */
router.get('/comptes', compteController.getAllComptes);

/**
 * @route GET /api/v1/mono/comptes/hierarchie
 * @desc Hiérarchie des comptes
 */
router.get('/comptes/hierarchie', compteController.getHierarchieComptes);

/**
 * @route GET /api/v1/mono/comptes/classe/:classe
 * @desc Comptes par classe
 */
router.get('/comptes/classe/:classe', compteController.getComptesByClasse);

/**
 * @route POST /api/v1/mono/comptes
 * @desc Créer un compte (societeId injecté automatiquement)
 */
router.post('/comptes', compteController.createCompte);

/**
 * ÉCRITURES SIMPLIFIÉES
 */

/**
 * @route GET /api/v1/mono/ecritures
 * @desc Lister les écritures de la société
 */
router.get('/ecritures', (req, res, next) => {
  // Initialiser le controller si nécessaire
  if (!ecritureController.ecritureService) {
    ecritureController.init(req.app.get('models'));
  }
  next();
}, ecritureController.listerEcritures);

/**
 * @route POST /api/v1/mono/ecritures
 * @desc Créer une écriture (societeId injecté automatiquement)
 */
router.post('/ecritures', (req, res, next) => {
  if (!ecritureController.ecritureService) {
    ecritureController.init(req.app.get('models'));
  }
  next();
}, ecritureController.creerEcriture);

/**
 * JOURNAUX SIMPLIFIÉS
 */

/**
 * @route GET /api/v1/mono/journaux
 * @desc Lister les journaux de la société
 */
router.get('/journaux', journalController.getAllJournaux);

/**
 * @route POST /api/v1/mono/journaux
 * @desc Créer un journal (societeId injecté automatiquement)
 */
router.post('/journaux', journalController.createJournal);

/**
 * DASHBOARD SIMPLIFIÉ
 */

/**
 * @route GET /api/v1/mono/dashboard/kpi
 * @desc KPIs de la société
 */
router.get('/dashboard/kpi', dashboardController.getKPIFinanciers);

/**
 * @route GET /api/v1/mono/dashboard/alertes
 * @desc Alertes financières de la société
 */
router.get('/dashboard/alertes', dashboardController.getAlertes);

/**
 * ÉTATS COMPTABLES SIMPLIFIÉS
 */

/**
 * @route GET /api/v1/mono/etats/balance
 * @desc Balance générale de la société
 */
router.get('/etats/balance', calculController.getBalance);

/**
 * @route GET /api/v1/mono/etats/bilan
 * @desc Bilan de la société
 */
router.get('/etats/bilan', etatController.getBilan);

/**
 * @route GET /api/v1/mono/etats/compte-resultat
 * @desc Compte de résultat de la société
 */
router.get('/etats/compte-resultat', etatController.getCompteResultat);

/**
 * @route GET /api/v1/mono/etats/grand-livre/:compteNumero
 * @desc Grand livre d'un compte
 */
router.get('/etats/grand-livre/:compteNumero', calculController.getGrandLivre);

/**
 * LETTRAGE SIMPLIFIÉ
 */

/**
 * @route POST /api/v1/mono/lettrage/lettrer
 * @desc Lettrer des écritures
 */
router.post('/lettrage/lettrer', lettrageController.lettrerEcritures);

/**
 * @route POST /api/v1/mono/lettrage/auto
 * @desc Lettrage automatique
 */
router.post('/lettrage/auto', lettrageController.lettrageAutomatique);

/**
 * TEMPLATES SIMPLIFIÉS
 */

/**
 * @route GET /api/v1/mono/templates
 * @desc Lister les templates de la société
 */
router.get('/templates', templateController.getAllTemplates);

/**
 * @route POST /api/v1/mono/templates
 * @desc Créer un template (societeId injecté automatiquement)
 */
router.post('/templates', templateController.createTemplate);

/**
 * IMPORT/EXPORT SIMPLIFIÉ
 */

/**
 * @route POST /api/v1/mono/import/excel
 * @desc Importer des écritures Excel (societeId injecté automatiquement)
 */
router.post('/import/excel', 
  require('../middleware/upload').uploadWithErrorHandling,
  importExportController.importerExcel
);

/**
 * @route GET /api/v1/mono/export/ecritures
 * @desc Exporter les écritures de la société
 */
router.get('/export/ecritures', importExportController.exporterEcritures);

module.exports = router;
