'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Créer le type ENUM pour les types de configuration
    await queryInterface.sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE enum_configurations_type AS ENUM ('string', 'integer', 'float', 'boolean', 'json', 'array');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryInterface.createTable('configurations', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      cle: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'Clé unique de la configuration'
      },
      valeur: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Valeur de la configuration (stockée en string)'
      },
      type: {
        type: 'enum_configurations_type',
        allowNull: false,
        defaultValue: 'string',
        comment: 'Type de données de la valeur'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Description de la configuration'
      },
      categorie: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'general',
        comment: 'Catégorie de la configuration'
      },
      modifiable: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Indique si la configuration peut être modifiée'
      },
      societe_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'societes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'ID de la société (null pour config globale)'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Créer les index pour optimiser les performances
    await queryInterface.addIndex('configurations', ['cle', 'societe_id'], {
      unique: true,
      name: 'idx_configurations_cle_societe_unique'
    });

    await queryInterface.addIndex('configurations', ['categorie'], {
      name: 'idx_configurations_categorie'
    });

    await queryInterface.addIndex('configurations', ['societe_id'], {
      name: 'idx_configurations_societe'
    });

    await queryInterface.addIndex('configurations', ['cle'], {
      name: 'idx_configurations_cle'
    });

    // Insérer les configurations par défaut
    await queryInterface.bulkInsert('configurations', [
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        cle: 'app_nom',
        valeur: 'API Comptabilité SYSCOHADA',
        type: 'string',
        description: 'Nom de l\'application',
        categorie: 'general',
        modifiable: true,
        societe_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        cle: 'app_version',
        valeur: '1.0.0',
        type: 'string',
        description: 'Version de l\'application',
        categorie: 'general',
        modifiable: false,
        societe_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        cle: 'devise_principale',
        valeur: 'XOF',
        type: 'string',
        description: 'Devise principale du système',
        categorie: 'comptabilite',
        modifiable: true,
        societe_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        cle: 'format_date',
        valeur: 'DD/MM/YYYY',
        type: 'string',
        description: 'Format d\'affichage des dates',
        categorie: 'affichage',
        modifiable: true,
        societe_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        cle: 'format_nombre',
        valeur: 'fr-FR',
        type: 'string',
        description: 'Format d\'affichage des nombres',
        categorie: 'affichage',
        modifiable: true,
        societe_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        cle: 'exercice_duree_mois',
        valeur: '12',
        type: 'integer',
        description: 'Durée standard d\'un exercice en mois',
        categorie: 'comptabilite',
        modifiable: true,
        societe_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        cle: 'backup_auto',
        valeur: 'true',
        type: 'boolean',
        description: 'Sauvegarde automatique activée',
        categorie: 'maintenance',
        modifiable: true,
        societe_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        cle: 'backup_frequence_heures',
        valeur: '24',
        type: 'integer',
        description: 'Fréquence de sauvegarde en heures',
        categorie: 'maintenance',
        modifiable: true,
        societe_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        cle: 'audit_retention_jours',
        valeur: '365',
        type: 'integer',
        description: 'Durée de rétention des logs d\'audit en jours',
        categorie: 'securite',
        modifiable: true,
        societe_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        cle: 'session_duree_heures',
        valeur: '24',
        type: 'integer',
        description: 'Durée de session utilisateur en heures',
        categorie: 'securite',
        modifiable: true,
        societe_id: null,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    // Supprimer les index
    await queryInterface.removeIndex('configurations', 'idx_configurations_cle_societe_unique');
    await queryInterface.removeIndex('configurations', 'idx_configurations_categorie');
    await queryInterface.removeIndex('configurations', 'idx_configurations_societe');
    await queryInterface.removeIndex('configurations', 'idx_configurations_cle');

    // Supprimer la table
    await queryInterface.dropTable('configurations');

    // Supprimer le type ENUM
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS enum_configurations_type;
    `);
  }
};
