/**
 * Routes d'authentification
 * API Comptabilité SYSCOHADA
 */

const express = require('express');
const router = express.Router();

const {
  protect,
  verifyApiKeyRoute
} = require('../middleware/auth');


/**
 * @route   GET /api/v1/auth/verify
 * @desc    Vérifier la validité de la clé API
 * @access  Private
 */
router.get('/verify', 
  protect,
  verifyApiKeyRoute
);

/**
 * @route   GET /api/v1/auth/profile
 * @desc    Récupérer les informations de la clé API
 * @access  Private
 */
router.get('/profile', 
  protect,
  (req, res) => {
    res.json({
      success: true,
      data: {
        apiKey: {
          id: req.apiKey.id,
          name: req.apiKey.name,
          prefix: req.apiKey.prefix,
          permissions: req.apiKey.permissions
        }
      }
    });
  }
);

module.exports = router;
