'use strict';

const express = require('express');
const router = express.Router();
const { validate } = require('../middleware/validation');
const { protect, optionalAuth } = require('../middleware/auth');
const ExerciceController = require('../controllers/exerciceController');
const models = require('../models');

// Initialiser le controller
const exerciceController = new ExerciceController(models);

// Schémas de validation Joi
const Joi = require('joi');

const exerciceCreateSchema = Joi.object({
  societeId: Joi.string().uuid().required()
    .messages({
      'string.uuid': 'L\'ID de la société doit être un UUID valide',
      'any.required': 'L\'ID de la société est obligatoire'
    }),
  libelle: Joi.string().min(3).max(100).required()
    .messages({
      'string.min': 'Le libellé doit contenir au moins 3 caractères',
      'string.max': 'Le libellé ne peut dépasser 100 caractères',
      'any.required': 'Le libellé est obligatoire'
    }),
  dateDebut: Joi.date().iso().required()
    .messages({
      'date.base': 'La date de début doit être une date valide',
      'any.required': 'La date de début est obligatoire'
    }),
  dateFin: Joi.date().iso().greater(Joi.ref('dateDebut')).required()
    .messages({
      'date.base': 'La date de fin doit être une date valide',
      'date.greater': 'La date de fin doit être postérieure à la date de début',
      'any.required': 'La date de fin est obligatoire'
    }),
  exercicePrecedentId: Joi.string().uuid().optional()
    .messages({
      'string.uuid': 'L\'ID de l\'exercice précédent doit être un UUID valide'
    }),
  reportANouveau: Joi.number().precision(2).default(0)
    .messages({
      'number.base': 'Le report à nouveau doit être un nombre'
    })
});

const exerciceUpdateSchema = Joi.object({
  libelle: Joi.string().min(3).max(100).optional(),
  dateDebut: Joi.date().iso().optional(),
  dateFin: Joi.date().iso().optional(),
  exercicePrecedentId: Joi.string().uuid().optional(),
  reportANouveau: Joi.number().precision(2).optional(),
  commentaireCloture: Joi.string().max(500).optional()
}).custom((value, helpers) => {
  // Validation personnalisée : si dateDebut et dateFin sont présentes, dateFin > dateDebut
  if (value.dateDebut && value.dateFin && new Date(value.dateFin) <= new Date(value.dateDebut)) {
    return helpers.error('custom.dateFin');
  }
  return value;
}).messages({
  'custom.dateFin': 'La date de fin doit être postérieure à la date de début'
});

const clotureSchema = Joi.object({
  commentaire: Joi.string().max(500).optional()
    .messages({
      'string.max': 'Le commentaire ne peut dépasser 500 caractères'
    })
});

/**
 * @swagger
 * /api/v1/exercices:
 *   get:
 *     summary: Liste tous les exercices comptables
 *     tags: [Exercices]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: societeId
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: statut
 *         schema:
 *           type: string
 *           enum: [OUVERT, CLOTURE, ARCHIVE]
 *       - in: query
 *         name: annee
 *         schema:
 *           type: integer
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: includeStats
 *         schema:
 *           type: boolean
 *     responses:
 *       200:
 *         description: Liste des exercices comptables
 */
router.get('/', optionalAuth, (req, res, next) => {
  exerciceController.getAll(req, res, next);
});

/**
 * @swagger
 * /api/v1/exercices/{id}:
 *   get:
 *     summary: Récupère un exercice par son ID
 *     tags: [Exercices]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Détails de l'exercice comptable
 *       404:
 *         description: Exercice non trouvé
 */
router.get('/:id', optionalAuth, (req, res, next) => {
  exerciceController.getById(req, res, next);
});

/**
 * @swagger
 * /api/v1/exercices:
 *   post:
 *     summary: Crée un nouvel exercice comptable
 *     tags: [Exercices]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - societeId
 *               - libelle
 *               - dateDebut
 *               - dateFin
 *             properties:
 *               societeId:
 *                 type: string
 *                 format: uuid
 *               libelle:
 *                 type: string
 *               dateDebut:
 *                 type: string
 *                 format: date
 *               dateFin:
 *                 type: string
 *                 format: date
 *               exercicePrecedentId:
 *                 type: string
 *                 format: uuid
 *               reportANouveau:
 *                 type: number
 *     responses:
 *       201:
 *         description: Exercice créé avec succès
 *       400:
 *         description: Données invalides
 */
router.post('/', protect, validate(exerciceCreateSchema), (req, res, next) => {
  exerciceController.create(req, res, next);
});

/**
 * @swagger
 * /api/v1/exercices/{id}:
 *   put:
 *     summary: Met à jour un exercice comptable
 *     tags: [Exercices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Exercice mis à jour avec succès
 *       404:
 *         description: Exercice non trouvé
 */
router.put('/:id', protect, validate(exerciceUpdateSchema), (req, res, next) => {
  exerciceController.update(req, res, next);
});

/**
 * @swagger
 * /api/v1/exercices/{id}:
 *   delete:
 *     summary: Supprime un exercice comptable
 *     tags: [Exercices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Exercice supprimé avec succès
 *       404:
 *         description: Exercice non trouvé
 */
router.delete('/:id', protect, (req, res, next) => {
  exerciceController.delete(req, res, next);
});

/**
 * @swagger
 * /api/v1/exercices/{id}/verifier-cloture:
 *   get:
 *     summary: Vérifie les conditions de clôture d'un exercice
 *     tags: [Exercices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Conditions de clôture vérifiées
 */
router.get('/:id/verifier-cloture', protect, (req, res, next) => {
  exerciceController.verifierCloture(req, res, next);
});

/**
 * @swagger
 * /api/v1/exercices/{id}/cloturer:
 *   post:
 *     summary: Clôture un exercice comptable
 *     tags: [Exercices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               commentaire:
 *                 type: string
 *     responses:
 *       200:
 *         description: Exercice clôturé avec succès
 *       400:
 *         description: Conditions de clôture non remplies
 */
router.post('/:id/cloturer', protect, validate(clotureSchema), (req, res, next) => {
  exerciceController.cloturer(req, res, next);
});

/**
 * @swagger
 * /api/v1/exercices/societe/{societeId}/courant:
 *   get:
 *     summary: Récupère l'exercice courant d'une société
 *     tags: [Exercices]
 *     parameters:
 *       - in: path
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Exercice courant de la société
 */
router.get('/societe/:societeId/courant', optionalAuth, (req, res, next) => {
  exerciceController.getExerciceCourant(req, res, next);
});

module.exports = router;
