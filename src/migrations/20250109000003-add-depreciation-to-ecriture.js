'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Ajouter la colonne depreciationId à la table ecriture_comptables
    await queryInterface.addColumn('ecriture_comptables', 'depreciationId', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'depreciations',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // Ajouter un index pour les performances
    await queryInterface.addIndex('ecriture_comptables', ['depreciationId']);
  },

  async down(queryInterface, Sequelize) {
    // Supprimer l'index
    await queryInterface.removeIndex('ecriture_comptables', ['depreciationId']);
    
    // Supprimer la colonne
    await queryInterface.removeColumn('ecriture_comptables', 'depreciationId');
  }
};