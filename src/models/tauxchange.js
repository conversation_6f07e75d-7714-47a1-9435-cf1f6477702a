'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class TauxChange extends Model {
    /**
     * Associations du modèle TauxChange
     */
    static associate(models) {
      // Un taux de change appartient à une devise source
      TauxChange.belongsTo(models.Devise, {
        foreignKey: 'deviseSource',
        as: 'deviseSourceObj'
      });

      // Un taux de change appartient à une devise cible
      TauxChange.belongsTo(models.Devise, {
        foreignKey: 'deviseCible',
        as: 'deviseCibleObj'
      });
    }

    /**
     * Méthodes d'instance
     */
    toJSON() {
      const values = { ...this.get() };
      values.tauxNum = parseFloat(values.taux);
      return values;
    }

    /**
     * Convertit un montant avec ce taux
     */
    convertir(montant) {
      if (montant === null || montant === undefined) return null;
      
      const montantNum = parseFloat(montant);
      if (isNaN(montantNum)) return null;

      return montantNum * parseFloat(this.taux);
    }

    /**
     * Convertit un montant inverse (de cible vers source)
     */
    convertirInverse(montant) {
      if (montant === null || montant === undefined) return null;
      
      const montantNum = parseFloat(montant);
      if (isNaN(montantNum)) return null;

      const tauxNum = parseFloat(this.taux);
      if (tauxNum === 0) return null;

      return montantNum / tauxNum;
    }

    /**
     * Vérifie si le taux est encore valide
     */
    isValide(dateReference = null) {
      const dateRef = dateReference ? new Date(dateReference) : new Date();
      const dateApplication = new Date(this.dateApplication);
      
      return dateApplication <= dateRef;
    }

    /**
     * Obtient l'âge du taux en jours
     */
    getAge() {
      const maintenant = new Date();
      const dateApplication = new Date(this.dateApplication);
      
      return Math.floor((maintenant - dateApplication) / (1000 * 60 * 60 * 24));
    }

    /**
     * Vérifie si le taux est récent (moins de 7 jours)
     */
    isRecent() {
      return this.getAge() <= 7;
    }

    /**
     * Formate le taux pour affichage
     */
    formaterTaux() {
      return new Intl.NumberFormat('fr-FR', {
        minimumFractionDigits: 6,
        maximumFractionDigits: 6
      }).format(parseFloat(this.taux));
    }

    /**
     * Méthodes statiques
     */

    /**
     * Obtient le dernier taux entre deux devises
     */
    static async getDernierTaux(deviseSource, deviseCible, dateReference = null) {
      const whereClause = {
        deviseSource,
        deviseCible
      };

      if (dateReference) {
        whereClause.dateApplication = { [sequelize.Op.lte]: dateReference };
      }

      return await TauxChange.findOne({
        where: whereClause,
        order: [['dateApplication', 'DESC']],
        include: [
          {
            model: sequelize.models.Devise,
            as: 'deviseSourceObj'
          },
          {
            model: sequelize.models.Devise,
            as: 'deviseCibleObj'
          }
        ]
      });
    }

    /**
     * Convertit un montant entre deux devises
     */
    static async convertirMontant(montant, deviseSource, deviseCible, dateReference = null) {
      if (deviseSource === deviseCible) {
        return parseFloat(montant);
      }

      const taux = await TauxChange.getDernierTaux(deviseSource, deviseCible, dateReference);
      
      if (!taux) {
        throw new Error(`Aucun taux de change trouvé de ${deviseSource} vers ${deviseCible}`);
      }

      return taux.convertir(montant);
    }

    /**
     * Crée ou met à jour un taux de change
     */
    static async setTaux(deviseSource, deviseCible, taux, source = 'MANUEL', dateApplication = null) {
      const dateApp = dateApplication || new Date();
      
      // Vérifier que les devises existent
      const [devSource, devCible] = await Promise.all([
        sequelize.models.Devise.findByPk(deviseSource),
        sequelize.models.Devise.findByPk(deviseCible)
      ]);

      if (!devSource) {
        throw new Error(`Devise source ${deviseSource} non trouvée`);
      }
      if (!devCible) {
        throw new Error(`Devise cible ${deviseCible} non trouvée`);
      }

      // Créer le nouveau taux
      const nouveauTaux = await TauxChange.create({
        deviseSource,
        deviseCible,
        taux: parseFloat(taux),
        dateApplication: dateApp,
        source
      });

      // Créer automatiquement le taux inverse si ce n'est pas un taux vers soi-même
      if (deviseSource !== deviseCible && parseFloat(taux) !== 0) {
        const tauxInverse = 1 / parseFloat(taux);
        
        await TauxChange.create({
          deviseSource: deviseCible,
          deviseCible: deviseSource,
          taux: tauxInverse,
          dateApplication: dateApp,
          source
        });
      }

      return nouveauTaux;
    }

    /**
     * Crée les taux de change par défaut (EUR/USD vers XOF/XAF)
     */
    static async creerTauxDefaut() {
      const tauxDefaut = [
        // EUR vers devises CFA (taux approximatifs)
        { source: 'EUR', cible: 'XOF', taux: 655.957, source: 'BCEAO' },
        { source: 'EUR', cible: 'XAF', taux: 655.957, source: 'BEAC' },
        
        // USD vers devises CFA
        { source: 'USD', cible: 'XOF', taux: 600.0, source: 'MARCHE' },
        { source: 'USD', cible: 'XAF', taux: 600.0, source: 'MARCHE' },
        
        // EUR vers USD
        { source: 'EUR', cible: 'USD', taux: 1.10, source: 'MARCHE' },
        
        // XOF vers XAF (parité fixe)
        { source: 'XOF', cible: 'XAF', taux: 1.0, source: 'OFFICIEL' }
      ];

      const tauxCrees = [];
      for (const tauxData of tauxDefaut) {
        try {
          const taux = await TauxChange.setTaux(
            tauxData.source,
            tauxData.cible,
            tauxData.taux,
            tauxData.source
          );
          tauxCrees.push(taux);
        } catch (error) {
          // Ignorer les erreurs si les devises n'existent pas encore
          console.warn(`Impossible de créer le taux ${tauxData.source}/${tauxData.cible}:`, error.message);
        }
      }

      return tauxCrees;
    }

    /**
     * Obtient tous les taux actifs pour une devise
     */
    static async getTauxDevise(deviseCode) {
      return await TauxChange.findAll({
        where: {
          [sequelize.Op.or]: [
            { deviseSource: deviseCode },
            { deviseCible: deviseCode }
          ]
        },
        include: [
          {
            model: sequelize.models.Devise,
            as: 'deviseSourceObj'
          },
          {
            model: sequelize.models.Devise,
            as: 'deviseCibleObj'
          }
        ],
        order: [['dateApplication', 'DESC']]
      });
    }

    /**
     * Nettoie les anciens taux (garde seulement les 10 derniers par paire)
     */
    static async nettoyerAnciensTaux() {
      const pairesDevises = await TauxChange.findAll({
        attributes: ['deviseSource', 'deviseCible'],
        group: ['deviseSource', 'deviseCible']
      });

      let totalSupprimes = 0;

      for (const paire of pairesDevises) {
        const anciensTaux = await TauxChange.findAll({
          where: {
            deviseSource: paire.deviseSource,
            deviseCible: paire.deviseCible
          },
          order: [['dateApplication', 'DESC']],
          offset: 10 // Garder les 10 plus récents
        });

        if (anciensTaux.length > 0) {
          const idsASupprimer = anciensTaux.map(t => t.id);
          const nbSupprimes = await TauxChange.destroy({
            where: { id: idsASupprimer }
          });
          totalSupprimes += nbSupprimes;
        }
      }

      return totalSupprimes;
    }
  }

  TauxChange.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    deviseSource: {
      type: DataTypes.STRING(3),
      allowNull: false,
      references: {
        model: 'devises',
        key: 'code'
      }
    },
    deviseCible: {
      type: DataTypes.STRING(3),
      allowNull: false,
      references: {
        model: 'devises',
        key: 'code'
      }
    },
    taux: {
      type: DataTypes.DECIMAL(15, 6),
      allowNull: false,
      validate: {
        min: 0.000001,
        max: 999999.999999
      }
    },
    dateApplication: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    source: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'MANUEL',
      validate: {
        isIn: [['MANUEL', 'BCEAO', 'BEAC', 'BCE', 'FED', 'MARCHE', 'API', 'OFFICIEL']]
      }
    }
  }, {
    sequelize,
    modelName: 'TauxChange',
    tableName: 'taux_changes',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['devise_source', 'devise_cible', 'date_application']
      },
      {
        fields: ['devise_source', 'devise_cible'],
        name: 'idx_taux_paire_devises'
      },
      {
        fields: ['date_application']
      },
      {
        fields: ['source']
      }
    ],
    validate: {
      devisesDifferentes() {
        if (this.deviseSource === this.deviseCible) {
          throw new Error('Les devises source et cible doivent être différentes');
        }
      }
    }
  });

  return TauxChange;
};
