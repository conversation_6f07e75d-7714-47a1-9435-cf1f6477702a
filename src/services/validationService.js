'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Service pour la validation conforme aux normes SYSCOHADA
 */
class ValidationService {
  constructor(models) {
    this.models = models;
  }

  /**
   * Valide une écriture comptable selon les règles SYSCOHADA
   * @param {Object} ecriture - Écriture à valider
   * @param {Array} lignes - Lignes de l'écriture
   * @returns {Promise<Object>} Résultat de validation
   */
  async validerEcritureSYSCOHADA(ecriture, lignes) {
    const erreurs = [];
    const avertissements = [];

    try {
      // 1. Validation équilibre débit/crédit
      const equilibre = await this.validerEquilibre(lignes);
      if (!equilibre.valide) {
        erreurs.push(...equilibre.erreurs);
      }

      // 2. Validation cohérence dates
      const dates = await this.validerCoherenceDates(ecriture);
      if (!dates.valide) {
        erreurs.push(...dates.erreurs);
      }

      // 3. Validation comptes autorisés par journal
      const comptes = await this.validerComptesJournal(ecriture.journalCode, lignes);
      if (!comptes.valide) {
        erreurs.push(...comptes.erreurs);
      }
      if (comptes.avertissements) {
        avertissements.push(...comptes.avertissements);
      }

      // 4. Validation montants et devises
      const montants = await this.validerMontants(lignes, ecriture.societeId);
      if (!montants.valide) {
        erreurs.push(...montants.erreurs);
      }

      // 5. Validation références et pièces justificatives
      const references = await this.validerReferences(ecriture, lignes);
      if (!references.valide) {
        erreurs.push(...references.erreurs);
      }

      // 6. Validation spécifique par type de journal
      const typeJournal = await this.validerTypeJournal(ecriture, lignes);
      if (!typeJournal.valide) {
        erreurs.push(...typeJournal.erreurs);
      }
      if (typeJournal.avertissements) {
        avertissements.push(...typeJournal.avertissements);
      }

      const resultat = {
        valide: erreurs.length === 0,
        erreurs,
        avertissements,
        niveauValidation: erreurs.length === 0 ? 'CONFORME_SYSCOHADA' : 'NON_CONFORME'
      };

      logger.info('Validation SYSCOHADA effectuée', {
        ecritureId: ecriture.id,
        valide: resultat.valide,
        nombreErreurs: erreurs.length,
        nombreAvertissements: avertissements.length
      });

      return resultat;

    } catch (error) {
      logger.error('Erreur lors de la validation SYSCOHADA', {
        ecritureId: ecriture.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Valide l'équilibre débit/crédit
   * @param {Array} lignes - Lignes de l'écriture
   * @returns {Object} Résultat validation équilibre
   */
  async validerEquilibre(lignes) {
    const erreurs = [];
    
    let totalDebit = 0;
    let totalCredit = 0;

    lignes.forEach((ligne, index) => {
      const debit = parseFloat(ligne.debit || 0);
      const credit = parseFloat(ligne.credit || 0);

      // Vérifier que les montants sont positifs
      if (debit < 0) {
        erreurs.push(`Ligne ${index + 1}: Le montant débit ne peut pas être négatif`);
      }
      if (credit < 0) {
        erreurs.push(`Ligne ${index + 1}: Le montant crédit ne peut pas être négatif`);
      }

      // Vérifier exclusion mutuelle débit/crédit
      if (debit > 0 && credit > 0) {
        erreurs.push(`Ligne ${index + 1}: Une ligne ne peut pas avoir à la fois un débit et un crédit`);
      }

      // Vérifier qu'au moins un montant est présent
      if (debit === 0 && credit === 0) {
        erreurs.push(`Ligne ${index + 1}: Une ligne doit avoir soit un débit soit un crédit`);
      }

      totalDebit += debit;
      totalCredit += credit;
    });

    // Vérifier l'équilibre avec tolérance de 1 centime
    const difference = Math.abs(totalDebit - totalCredit);
    if (difference > 0.01) {
      erreurs.push(`Écriture non équilibrée: Débit ${totalDebit.toFixed(2)}, Crédit ${totalCredit.toFixed(2)}, Différence ${difference.toFixed(2)}`);
    }

    // Vérifier qu'il y a au moins 2 lignes
    if (lignes.length < 2) {
      erreurs.push('Une écriture doit avoir au moins 2 lignes');
    }

    return {
      valide: erreurs.length === 0,
      erreurs,
      totalDebit,
      totalCredit,
      difference
    };
  }

  /**
   * Valide la cohérence des dates
   * @param {Object} ecriture - Écriture à valider
   * @returns {Promise<Object>} Résultat validation dates
   */
  async validerCoherenceDates(ecriture) {
    const erreurs = [];

    // Vérifier que la date d'écriture est présente
    if (!ecriture.dateEcriture) {
      erreurs.push('La date d\'écriture est obligatoire');
      return { valide: false, erreurs };
    }

    const dateEcriture = new Date(ecriture.dateEcriture);
    const aujourd_hui = new Date();

    // Vérifier que la date n'est pas dans le futur
    if (dateEcriture > aujourd_hui) {
      erreurs.push('La date d\'écriture ne peut pas être dans le futur');
    }

    // Vérifier que l'exercice est ouvert pour cette date
    if (ecriture.exerciceId) {
      const exercice = await this.models.ExerciceComptable.findByPk(ecriture.exerciceId);
      if (exercice) {
        const dateDebut = new Date(exercice.dateDebut);
        const dateFin = new Date(exercice.dateFin);

        if (dateEcriture < dateDebut || dateEcriture > dateFin) {
          erreurs.push(`La date d'écriture doit être comprise entre ${dateDebut.toLocaleDateString()} et ${dateFin.toLocaleDateString()}`);
        }

        if (exercice.statut !== 'OUVERT') {
          erreurs.push(`L'exercice comptable est ${exercice.statut.toLowerCase()}, aucune écriture ne peut être saisie`);
        }
      }
    }

    return {
      valide: erreurs.length === 0,
      erreurs
    };
  }

  /**
   * Valide les comptes autorisés par journal
   * @param {string} journalCode - Code du journal
   * @param {Array} lignes - Lignes de l'écriture
   * @returns {Promise<Object>} Résultat validation comptes
   */
  async validerComptesJournal(journalCode, lignes) {
    const erreurs = [];
    const avertissements = [];

    // Récupérer le journal
    const journal = await this.models.Journal.findByPk(journalCode);
    if (!journal) {
      erreurs.push('Journal non trouvé');
      return { valide: false, erreurs };
    }

    // Règles par type de journal SYSCOHADA
    const reglesJournaux = {
      'VT': { // Ventes
        comptesObligatoires: ['7'], // Classe 7 (produits)
        comptesAutorises: ['4', '5', '7'], // Clients, trésorerie, produits
        comptesInterdits: ['6'] // Pas de charges dans journal ventes
      },
      'AC': { // Achats
        comptesObligatoires: ['6'], // Classe 6 (charges)
        comptesAutorises: ['4', '5', '6'], // Fournisseurs, trésorerie, charges
        comptesInterdits: ['7'] // Pas de produits dans journal achats
      },
      'BQ': { // Banque
        comptesObligatoires: ['52'], // Compte banque obligatoire
        comptesAutorises: ['1', '2', '3', '4', '5', '6', '7'], // Tous comptes
        comptesInterdits: []
      },
      'CA': { // Caisse
        comptesObligatoires: ['57'], // Compte caisse obligatoire
        comptesAutorises: ['1', '2', '3', '4', '5', '6', '7'], // Tous comptes
        comptesInterdits: []
      },
      'OD': { // Opérations diverses
        comptesObligatoires: [],
        comptesAutorises: ['1', '2', '3', '4', '5', '6', '7'], // Tous comptes
        comptesInterdits: []
      }
    };

    const regles = reglesJournaux[journalCode] || reglesJournaux['OD'];

    // Vérifier chaque ligne
    for (let i = 0; i < lignes.length; i++) {
      const ligne = lignes[i];
      const compteNumero = ligne.compteNumero;

      if (!compteNumero) {
        erreurs.push(`Ligne ${i + 1}: Compte comptable obligatoire`);
        continue;
      }

      // Vérifier que le compte existe
      const compte = await this.models.CompteComptable.findByPk(compteNumero);
      if (!compte) {
        erreurs.push(`Ligne ${i + 1}: Compte ${compteNumero} non trouvé`);
        continue;
      }

      // Vérifier que le compte est actif
      if (!compte.actif) {
        erreurs.push(`Ligne ${i + 1}: Compte ${compteNumero} inactif`);
        continue;
      }

      const premiereClasse = compteNumero.charAt(0);

      // Vérifier comptes interdits
      if (regles.comptesInterdits.includes(premiereClasse)) {
        erreurs.push(`Ligne ${i + 1}: Compte classe ${premiereClasse} interdit dans journal ${journalCode}`);
      }

      // Vérifier comptes autorisés
      if (regles.comptesAutorises.length > 0) {
        const autorise = regles.comptesAutorises.some(classe => 
          compteNumero.startsWith(classe)
        );
        if (!autorise) {
          avertissements.push(`Ligne ${i + 1}: Compte ${compteNumero} inhabituel pour journal ${journalCode}`);
        }
      }
    }

    // Vérifier comptes obligatoires
    if (regles.comptesObligatoires.length > 0) {
      const comptesPresents = lignes.map(l => l.compteNumero?.charAt(0) || l.compteNumero?.substring(0, 2));
      
      for (const compteObligatoire of regles.comptesObligatoires) {
        const present = comptesPresents.some(compte => 
          compte && compte.startsWith(compteObligatoire)
        );
        if (!present) {
          avertissements.push(`Journal ${journalCode}: Compte classe ${compteObligatoire} recommandé`);
        }
      }
    }

    return {
      valide: erreurs.length === 0,
      erreurs,
      avertissements
    };
  }

  /**
   * Valide les montants et devises
   * @param {Array} lignes - Lignes de l'écriture
   * @param {string} societeId - ID de la société
   * @returns {Promise<Object>} Résultat validation montants
   */
  async validerMontants(lignes, societeId) {
    const erreurs = [];

    // Récupérer la devise principale de la société
    const ConfigurationService = require('./configurationService');
    const configService = new ConfigurationService(this.models);
    const devisePrincipale = await configService.getConfig('devise_principale', societeId) || 'XOF';

    for (let i = 0; i < lignes.length; i++) {
      const ligne = lignes[i];
      const debit = parseFloat(ligne.debit || 0);
      const credit = parseFloat(ligne.credit || 0);
      const montant = debit + credit;

      // Vérifier montants minimum (1 centime)
      if (montant > 0 && montant < 0.01) {
        erreurs.push(`Ligne ${i + 1}: Montant trop faible (minimum 0.01 ${devisePrincipale})`);
      }

      // Vérifier montants maximum (selon devise)
      const montantMax = devisePrincipale === 'XOF' || devisePrincipale === 'XAF' ? 
        999999999999.99 : 99999999.99; // Limites selon devise africaine ou européenne

      if (montant > montantMax) {
        erreurs.push(`Ligne ${i + 1}: Montant trop élevé (maximum ${montantMax} ${devisePrincipale})`);
      }

      // Vérifier précision décimale (2 décimales max)
      const decimales = (montant.toString().split('.')[1] || '').length;
      if (decimales > 2) {
        erreurs.push(`Ligne ${i + 1}: Maximum 2 décimales autorisées`);
      }
    }

    return {
      valide: erreurs.length === 0,
      erreurs
    };
  }

  /**
   * Valide les références et pièces justificatives
   * @param {Object} ecriture - Écriture à valider
   * @param {Array} lignes - Lignes de l'écriture
   * @returns {Object} Résultat validation références
   */
  async validerReferences(ecriture, lignes) {
    const erreurs = [];

    // Vérifier libellé écriture
    if (!ecriture.libelle || ecriture.libelle.trim().length < 3) {
      erreurs.push('Le libellé de l\'écriture doit contenir au moins 3 caractères');
    }

    // Vérifier libellés des lignes
    lignes.forEach((ligne, index) => {
      if (!ligne.libelle || ligne.libelle.trim().length < 2) {
        erreurs.push(`Ligne ${index + 1}: Le libellé doit contenir au moins 2 caractères`);
      }
    });

    // Vérifier format référence si présente
    if (ecriture.reference) {
      if (ecriture.reference.length > 50) {
        erreurs.push('La référence ne peut pas dépasser 50 caractères');
      }
      
      // Format recommandé : LETTRES-CHIFFRES-ANNEE
      const formatReference = /^[A-Z0-9\-_\/]+$/i;
      if (!formatReference.test(ecriture.reference)) {
        erreurs.push('Format de référence non conforme (lettres, chiffres, tirets et underscores uniquement)');
      }
    }

    // Vérifier pièce justificative si présente
    if (ecriture.pieceJustificative) {
      if (ecriture.pieceJustificative.length > 100) {
        erreurs.push('La référence de pièce justificative ne peut pas dépasser 100 caractères');
      }
    }

    return {
      valide: erreurs.length === 0,
      erreurs
    };
  }

  /**
   * Validation spécifique par type de journal
   * @param {Object} ecriture - Écriture à valider
   * @param {Array} lignes - Lignes de l'écriture
   * @returns {Promise<Object>} Résultat validation type journal
   */
  async validerTypeJournal(ecriture, lignes) {
    const erreurs = [];
    const avertissements = [];

    const journalCode = ecriture.journalCode;

    switch (journalCode) {
      case 'VT': // Journal des ventes
        return await this.validerJournalVentes(ecriture, lignes);
      
      case 'AC': // Journal des achats
        return await this.validerJournalAchats(ecriture, lignes);
      
      case 'BQ': // Journal de banque
        return await this.validerJournalBanque(ecriture, lignes);
      
      case 'CA': // Journal de caisse
        return await this.validerJournalCaisse(ecriture, lignes);
      
      case 'OD': // Opérations diverses
        return await this.validerJournalOD(ecriture, lignes);
      
      default:
        avertissements.push(`Type de journal ${journalCode} non reconnu, validation générique appliquée`);
        return {
          valide: true,
          erreurs: [],
          avertissements
        };
    }
  }

  /**
   * Validation spécifique journal des ventes
   */
  async validerJournalVentes(ecriture, lignes) {
    const erreurs = [];
    const avertissements = [];

    // Vérifier présence compte client (classe 4)
    const compteClient = lignes.find(l => l.compteNumero?.startsWith('41'));
    if (!compteClient) {
      avertissements.push('Journal ventes: Compte client (classe 41) recommandé');
    }

    // Vérifier présence compte produit (classe 7)
    const compteProduit = lignes.find(l => l.compteNumero?.startsWith('7'));
    if (!compteProduit) {
      erreurs.push('Journal ventes: Compte produit (classe 7) obligatoire');
    }

    // Vérifier cohérence TVA si présente
    const compteTVA = lignes.find(l => l.compteNumero?.startsWith('4431'));
    if (compteTVA) {
      const montantTVA = parseFloat(compteTVA.credit || 0);
      const montantHT = lignes
        .filter(l => l.compteNumero?.startsWith('7'))
        .reduce((sum, l) => sum + parseFloat(l.credit || 0), 0);
      
      // Vérifier taux TVA standard (18% en zone UEMOA/CEMAC)
      const tauxTVAAttendu = montantHT * 0.18;
      const ecartTVA = Math.abs(montantTVA - tauxTVAAttendu);
      
      if (ecartTVA > 1) { // Tolérance 1 unité monétaire
        avertissements.push(`TVA: Montant ${montantTVA} semble incohérent avec base HT ${montantHT} (attendu: ${tauxTVAAttendu.toFixed(2)})`);
      }
    }

    return {
      valide: erreurs.length === 0,
      erreurs,
      avertissements
    };
  }

  /**
   * Validation spécifique journal des achats
   */
  async validerJournalAchats(ecriture, lignes) {
    const erreurs = [];
    const avertissements = [];

    // Vérifier présence compte fournisseur (classe 40)
    const compteFournisseur = lignes.find(l => l.compteNumero?.startsWith('40'));
    if (!compteFournisseur) {
      avertissements.push('Journal achats: Compte fournisseur (classe 40) recommandé');
    }

    // Vérifier présence compte charge (classe 6)
    const compteCharge = lignes.find(l => l.compteNumero?.startsWith('6'));
    if (!compteCharge) {
      erreurs.push('Journal achats: Compte charge (classe 6) obligatoire');
    }

    return {
      valide: erreurs.length === 0,
      erreurs,
      avertissements
    };
  }

  /**
   * Validation spécifique journal de banque
   */
  async validerJournalBanque(ecriture, lignes) {
    const erreurs = [];
    const avertissements = [];

    // Vérifier présence compte banque (classe 52)
    const compteBanque = lignes.find(l => l.compteNumero?.startsWith('52'));
    if (!compteBanque) {
      erreurs.push('Journal banque: Compte banque (classe 52) obligatoire');
    }

    return {
      valide: erreurs.length === 0,
      erreurs,
      avertissements
    };
  }

  /**
   * Validation spécifique journal de caisse
   */
  async validerJournalCaisse(ecriture, lignes) {
    const erreurs = [];
    const avertissements = [];

    // Vérifier présence compte caisse (classe 57)
    const compteCaisse = lignes.find(l => l.compteNumero?.startsWith('57'));
    if (!compteCaisse) {
      erreurs.push('Journal caisse: Compte caisse (classe 57) obligatoire');
    }

    return {
      valide: erreurs.length === 0,
      erreurs,
      avertissements
    };
  }

  /**
   * Validation spécifique opérations diverses
   */
  async validerJournalOD(ecriture, lignes) {
    const avertissements = [];

    // Pour les OD, validation plus souple mais recommandations
    if (lignes.length > 10) {
      avertissements.push('Opérations diverses: Écriture complexe avec plus de 10 lignes');
    }

    return {
      valide: true,
      erreurs: [],
      avertissements
    };
  }

  /**
   * Valide un lot d'écritures
   * @param {Array} ecritures - Écritures à valider
   * @returns {Promise<Object>} Résultat validation lot
   */
  async validerLotEcritures(ecritures) {
    const resultats = [];
    let totalErreurs = 0;
    let totalAvertissements = 0;

    for (const ecriture of ecritures) {
      const resultat = await this.validerEcritureSYSCOHADA(ecriture, ecriture.lignes);
      resultats.push({
        ecritureId: ecriture.id,
        ...resultat
      });
      
      totalErreurs += resultat.erreurs.length;
      totalAvertissements += resultat.avertissements.length;
    }

    return {
      resultats,
      resume: {
        totalEcritures: ecritures.length,
        ecrituresValides: resultats.filter(r => r.valide).length,
        totalErreurs,
        totalAvertissements
      }
    };
  }
}

module.exports = ValidationService;
