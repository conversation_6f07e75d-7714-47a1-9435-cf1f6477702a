/**
 * Service de gestion des clés API
 * API Comptabilité SYSCOHADA
 */

const crypto = require('crypto');
const bcrypt = require('bcrypt');
const { ApiKey } = require('../models');
const { logger } = require('../config/logger');
const { Op } = require('sequelize');

class ApiKeyService {
  /**
   * Générer une nouvelle clé API
   */
  static generateApiKey() {
    // Générer une clé aléatoire de 32 bytes (64 caractères hex)
    const key = crypto.randomBytes(32).toString('hex');
    
    // Créer un préfixe court pour identifier facilement la clé (max 10 caractères)
    const prefix = 'sk_' + crypto.randomBytes(3).toString('hex'); // sk_ + 6 chars = 9 chars total
    
    return {
      key: `${prefix}_${key}`,
      prefix
    };
  }

  /**
   * Hasher une clé API pour le stockage
   */
  static async hashApiKey(key) {
    const saltRounds = 12;
    return await bcrypt.hash(key, saltRounds);
  }

  /**
   * Vérifier une clé API
   */
  static async verifyApiKey(key, hashedKey) {
    return await bcrypt.compare(key, hashedKey);
  }

  /**
   * Créer une nouvelle clé API
   */
  static async createApiKey(data) {
    try {
      const { key, prefix } = this.generateApiKey();
      const hashedKey = await this.hashApiKey(key);

      const apiKey = await ApiKey.create({
        name: data.name,
        key: hashedKey,
        prefix,
        permissions: data.permissions || ['read'],
        expiresAt: data.expiresAt || null,
        createdBy: data.createdBy || 'system',
        metadata: data.metadata || {}
      });

      logger.info('Nouvelle clé API créée', {
        id: apiKey.id,
        name: apiKey.name,
        prefix: apiKey.prefix,
        permissions: apiKey.permissions
      });

      // Retourner la clé en clair (une seule fois)
      return {
        id: apiKey.id,
        name: apiKey.name,
        key: key, // Clé en clair (à afficher une seule fois)
        prefix: apiKey.prefix,
        permissions: apiKey.permissions,
        expiresAt: apiKey.expiresAt,
        createdAt: apiKey.createdAt
      };
    } catch (error) {
      logger.error('Erreur lors de la création de la clé API', {
        error: error.message,
        data
      });
      throw error;
    }
  }

  /**
   * Valider une clé API
   */
  static async validateApiKey(key) {
    try {
      if (!key || !key.startsWith('sk_')) {
        return null;
      }

      // Rechercher toutes les clés actives
      const apiKeys = await ApiKey.findAll({
        where: {
          isActive: true,
          [Op.or]: [
            { expiresAt: null },
            { expiresAt: { [Op.gt]: new Date() } }
          ]
        }
      });

      // Vérifier chaque clé
      for (const apiKey of apiKeys) {
        const isValid = await this.verifyApiKey(key, apiKey.key);
        if (isValid) {
          // Mettre à jour la dernière utilisation
          await apiKey.update({ lastUsedAt: new Date() });

          logger.info('Clé API validée avec succès', {
            id: apiKey.id,
            name: apiKey.name,
            prefix: apiKey.prefix
          });

          return {
            id: apiKey.id,
            name: apiKey.name,
            prefix: apiKey.prefix,
            permissions: apiKey.permissions,
            metadata: apiKey.metadata
          };
        }
      }

      logger.warn('Tentative d\'utilisation d\'une clé API invalide', {
        keyPrefix: key.substring(0, 10) + '...'
      });

      return null;
    } catch (error) {
      logger.error('Erreur lors de la validation de la clé API', {
        error: error.message
      });
      return null;
    }
  }

  /**
   * Lister les clés API
   */
  static async listApiKeys(options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        includeInactive = false
      } = options;

      const where = {};
      if (!includeInactive) {
        where.isActive = true;
      }

      const apiKeys = await ApiKey.findAndCountAll({
        where,
        attributes: [
          'id', 'name', 'prefix', 'permissions', 
          'lastUsedAt', 'expiresAt', 'isActive', 
          'createdBy', 'createdAt', 'updatedAt'
        ],
        order: [['createdAt', 'DESC']],
        limit,
        offset: (page - 1) * limit
      });

      return {
        apiKeys: apiKeys.rows,
        pagination: {
          total: apiKeys.count,
          page,
          limit,
          totalPages: Math.ceil(apiKeys.count / limit)
        }
      };
    } catch (error) {
      logger.error('Erreur lors de la récupération des clés API', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Récupérer une clé API par ID
   */
  static async getApiKeyById(id) {
    try {
      const apiKey = await ApiKey.findByPk(id, {
        attributes: [
          'id', 'name', 'prefix', 'permissions', 
          'lastUsedAt', 'expiresAt', 'isActive', 
          'createdBy', 'metadata', 'createdAt', 'updatedAt'
        ]
      });

      return apiKey;
    } catch (error) {
      logger.error('Erreur lors de la récupération de la clé API', {
        error: error.message,
        id
      });
      throw error;
    }
  }

  /**
   * Mettre à jour une clé API
   */
  static async updateApiKey(id, data) {
    try {
      const apiKey = await ApiKey.findByPk(id);
      if (!apiKey) {
        return null;
      }

      const updatedApiKey = await apiKey.update({
        name: data.name || apiKey.name,
        permissions: data.permissions || apiKey.permissions,
        expiresAt: data.expiresAt !== undefined ? data.expiresAt : apiKey.expiresAt,
        isActive: data.isActive !== undefined ? data.isActive : apiKey.isActive,
        metadata: data.metadata || apiKey.metadata
      });

      logger.info('Clé API mise à jour', {
        id: updatedApiKey.id,
        name: updatedApiKey.name,
        changes: data
      });

      return updatedApiKey;
    } catch (error) {
      logger.error('Erreur lors de la mise à jour de la clé API', {
        error: error.message,
        id,
        data
      });
      throw error;
    }
  }

  /**
   * Supprimer une clé API
   */
  static async deleteApiKey(id) {
    try {
      const apiKey = await ApiKey.findByPk(id);
      if (!apiKey) {
        return false;
      }

      await apiKey.destroy();

      logger.info('Clé API supprimée', {
        id: apiKey.id,
        name: apiKey.name,
        prefix: apiKey.prefix
      });

      return true;
    } catch (error) {
      logger.error('Erreur lors de la suppression de la clé API', {
        error: error.message,
        id
      });
      throw error;
    }
  }

  /**
   * Désactiver une clé API
   */
  static async deactivateApiKey(id) {
    try {
      return await this.updateApiKey(id, { isActive: false });
    } catch (error) {
      logger.error('Erreur lors de la désactivation de la clé API', {
        error: error.message,
        id
      });
      throw error;
    }
  }

  /**
   * Nettoyer les clés expirées
   */
  static async cleanupExpiredKeys() {
    try {
      const result = await ApiKey.destroy({
        where: {
          expiresAt: {
            [Op.lt]: new Date()
          }
        }
      });

      logger.info('Nettoyage des clés expirées', {
        deletedCount: result
      });

      return result;
    } catch (error) {
      logger.error('Erreur lors du nettoyage des clés expirées', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Vérifier les permissions d'une clé API
   */
  static hasPermission(apiKey, requiredPermission) {
    if (!apiKey || !apiKey.permissions) {
      return false;
    }

    // Si la clé a la permission 'admin', elle a toutes les permissions
    if (apiKey.permissions.includes('admin')) {
      return true;
    }

    // Vérifier si la permission spécifique est accordée
    return apiKey.permissions.includes(requiredPermission);
  }
}

module.exports = ApiKeyService;