'use strict';

const { DataTypes } = require('sequelize');

/**
 * <PERSON><PERSON><PERSON><PERSON> DepreciationPlan - Plan d'amortissement détaillé
 * Tableau d'amortissement année par année
 */
module.exports = (sequelize) => {
  const DepreciationPlan = sequelize.define('DepreciationPlan', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      comment: 'Identifiant unique de la ligne du plan'
    },

    // Période
    exercice: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Année d\'exercice'
    },

    dateDebut: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Date de début de la période'
    },

    dateFin: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Date de fin de la période'
    },

    // Valeurs en début de période
    valeurDebutPeriode: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Valeur nette en début de période'
    },

    cumulDebutPeriode: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0,
      comment: 'Cumul amortissements en début de période'
    },

    // Dotation de la période
    dotationPeriode: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Dotation aux amortissements de la période'
    },

    dotationTheorique: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Dotation théorique (sans prorata)'
    },

    prorataTemporisMois: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Nombre de mois pour prorata temporis'
    },

    // Valeurs en fin de période
    cumulFinPeriode: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Cumul amortissements en fin de période'
    },

    valeurFinPeriode: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Valeur nette en fin de période'
    },

    // Taux et calculs
    tauxPeriode: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      comment: 'Taux d\'amortissement appliqué'
    },

    baseCalcul: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Base de calcul pour la dotation'
    },

    // Statut
    statut: {
      type: DataTypes.ENUM(
        'PREVISIONNEL',
        'REALISE',
        'AJUSTE',
        'ANNULE'
      ),
      allowNull: false,
      defaultValue: 'PREVISIONNEL',
      comment: 'Statut de la ligne du plan'
    },

    ecritureGeneree: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Écriture comptable générée'
    },

    // Informations complémentaires
    methodeCalcul: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: 'Méthode de calcul utilisée'
    },

    observations: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Observations sur la période'
    }
  }, {
    tableName: 'depreciation_plans',
    timestamps: true,
    indexes: [
      {
        fields: ['depreciationId']
      },
      {
        fields: ['exercice']
      },
      {
        fields: ['depreciationId', 'exercice'],
        unique: true
      },
      {
        fields: ['statut']
      }
    ],
    hooks: {
      beforeValidate: (plan) => {
        // Calcul automatique du cumul fin de période
        plan.cumulFinPeriode = plan.cumulDebutPeriode + plan.dotationPeriode;
        
        // Calcul automatique de la valeur fin de période
        if (plan.valeurDebutPeriode !== undefined && plan.dotationPeriode !== undefined) {
          plan.valeurFinPeriode = plan.valeurDebutPeriode - plan.dotationPeriode;
        }
      }
    },
    validate: {
      datesPeriodeValides() {
        if (this.dateDebut && this.dateFin && this.dateDebut >= this.dateFin) {
          throw new Error('La date de début doit être antérieure à la date de fin');
        }
      },
      dotationPositive() {
        if (this.dotationPeriode < 0) {
          throw new Error('La dotation ne peut pas être négative');
        }
      },
      valeurFinPeriodePositive() {
        if (this.valeurFinPeriode < 0) {
          throw new Error('La valeur nette ne peut pas être négative');
        }
      }
    }
  });

  // Associations
  DepreciationPlan.associate = function(models) {
    // Relation avec Depreciation
    DepreciationPlan.belongsTo(models.Depreciation, {
      foreignKey: {
        name: 'depreciationId',
        allowNull: false
      },
      as: 'depreciation',
      onDelete: 'CASCADE'
    });

    // Relation avec l'écriture comptable générée
    DepreciationPlan.belongsTo(models.EcritureComptable, {
      foreignKey: 'ecritureId',
      as: 'ecriture',
      constraints: false
    });
  };

  // Méthodes d'instance
  DepreciationPlan.prototype.genererEcriture = async function(models) {
    if (this.ecritureGeneree) {
      throw new Error('L\'écriture a déjà été générée pour cette période');
    }

    const depreciation = await this.getDepreciation({
      include: [{ model: models.Societe, as: 'societe' }]
    });

    // Création de l'écriture d'amortissement
    const ecriture = await models.EcritureComptable.create({
      societeId: depreciation.societeId,
      journalCode: 'OD', // Journal des opérations diverses
      dateEcriture: this.dateFin,
      libelle: `Dotation amortissement ${depreciation.libelle} - Exercice ${this.exercice}`,
      reference: `AMORT-${depreciation.code}-${this.exercice}`,
      totalDebit: this.dotationPeriode,
      totalCredit: this.dotationPeriode,
      statut: 'BROUILLON',
      typeEcriture: 'AMORTISSEMENT',
      depreciationId: depreciation.id
    });

    // Lignes de l'écriture
    await models.LigneEcriture.bulkCreate([
      {
        ecritureId: ecriture.id,
        compteNumero: depreciation.compteDotation,
        libelle: `Dotation amortissement ${depreciation.libelle}`,
        montantDebit: this.dotationPeriode,
        montantCredit: 0,
        ordre: 1
      },
      {
        ecritureId: ecriture.id,
        compteNumero: depreciation.compteAmortissement,
        libelle: `Amortissement ${depreciation.libelle}`,
        montantDebit: 0,
        montantCredit: this.dotationPeriode,
        ordre: 2
      }
    ]);

    // Mise à jour du plan
    await this.update({
      ecritureGeneree: true,
      ecritureId: ecriture.id,
      statut: 'REALISE'
    });

    return ecriture;
  };

  DepreciationPlan.prototype.annulerEcriture = async function(models) {
    if (!this.ecritureGeneree || !this.ecritureId) {
      throw new Error('Aucune écriture à annuler pour cette période');
    }

    const ecriture = await models.EcritureComptable.findByPk(this.ecritureId);
    if (ecriture && ecriture.statut !== 'VALIDEE') {
      await ecriture.destroy();
      
      await this.update({
        ecritureGeneree: false,
        ecritureId: null,
        statut: 'PREVISIONNEL'
      });
    } else {
      throw new Error('Impossible d\'annuler une écriture validée');
    }
  };

  // Méthodes de classe
  DepreciationPlan.findByExercice = function(exercice, options = {}) {
    return this.findAll({
      where: { exercice, ...options.where },
      include: options.include || [
        { 
          model: this.sequelize.models.Depreciation, 
          as: 'depreciation',
          include: [{ model: this.sequelize.models.Societe, as: 'societe' }]
        }
      ],
      order: options.order || [['depreciation', 'code', 'ASC']]
    });
  };

  DepreciationPlan.findByDepreciation = function(depreciationId) {
    return this.findAll({
      where: { depreciationId },
      order: [['exercice', 'ASC']]
    });
  };

  DepreciationPlan.findAGenerer = function(exercice, societeId = null) {
    const where = {
      exercice,
      statut: 'PREVISIONNEL',
      ecritureGeneree: false
    };

    const include = [{
      model: this.sequelize.models.Depreciation,
      as: 'depreciation',
      where: societeId ? { societeId } : {},
      include: [{ model: this.sequelize.models.Societe, as: 'societe' }]
    }];

    return this.findAll({
      where,
      include,
      order: [['depreciation', 'code', 'ASC']]
    });
  };

  return DepreciationPlan;
};