'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Plan comptable SYSCOHADA - Comptes principaux
    const comptes = [
      // CLASSE 1 - COMPTES DE RESSOURCES DURABLES
      { numero: '10', libelle: 'CAPITAL ET RESERVES', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 1 },
      { numero: '101', libelle: 'Capital social', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '10' },
      { numero: '1011', libelle: 'Capital souscrit, non appelé', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 3, compte_parent: '101' },
      { numero: '1012', libelle: 'Capital souscrit, appelé, non versé', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 3, compte_parent: '101' },
      { numero: '1013', libelle: 'Capital souscrit, appelé, versé', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 3, compte_parent: '101' },

      { numero: '11', libelle: 'RESERVES', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 1 },
      { numero: '111', libelle: 'Réserve légale', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '11' },
      { numero: '112', libelle: 'Réserves statutaires', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '11' },
      { numero: '118', libelle: 'Autres réserves', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '11' },

      { numero: '12', libelle: 'REPORT A NOUVEAU', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 1 },
      { numero: '121', libelle: 'Report à nouveau créditeur', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '12' },
      { numero: '129', libelle: 'Report à nouveau débiteur', classe: 1, nature: 'PASSIF', sens: 'DEBIT', niveau: 2, compte_parent: '12' },

      { numero: '13', libelle: 'RESULTAT NET DE L\'EXERCICE', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 1 },
      { numero: '131', libelle: 'Résultat net : Bénéfice', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '13' },
      { numero: '139', libelle: 'Résultat net : Perte', classe: 1, nature: 'PASSIF', sens: 'DEBIT', niveau: 2, compte_parent: '13' },

      { numero: '16', libelle: 'EMPRUNTS ET DETTES ASSIMILEES', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 1 },
      { numero: '161', libelle: 'Emprunts obligataires', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '16' },
      { numero: '162', libelle: 'Emprunts et dettes auprès des établissements de crédit', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '16' },
      { numero: '163', libelle: 'Avances reçues de l\'Etat', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '16' },
      { numero: '164', libelle: 'Avances reçues et comptes courants bloqués', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '16' },
      { numero: '165', libelle: 'Dépôts et cautionnements reçus', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '16' },
      { numero: '166', libelle: 'Intérêts courus', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '16' },
      { numero: '167', libelle: 'Emprunts et dettes assortis de conditions particulières', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '16' },
      { numero: '168', libelle: 'Autres emprunts et dettes assimilées', classe: 1, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '16' },

      // CLASSE 2 - COMPTES D'ACTIF IMMOBILISE
      { numero: '20', libelle: 'CHARGES IMMOBILISEES', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '201', libelle: 'Frais de développement', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '20' },
      { numero: '202', libelle: 'Brevets, licences, logiciels', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '20' },
      { numero: '203', libelle: 'Fonds commercial', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '20' },
      { numero: '204', libelle: 'Frais de recherche et de développement', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '20' },

      { numero: '21', libelle: 'IMMOBILISATIONS INCORPORELLES', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '211', libelle: 'Frais de recherche et de développement', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '21' },
      { numero: '212', libelle: 'Brevets, licences, concessions et droits similaires', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '21' },
      { numero: '213', libelle: 'Logiciels', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '21' },
      { numero: '214', libelle: 'Marques', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '21' },
      { numero: '215', libelle: 'Fonds commercial', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '21' },
      { numero: '218', libelle: 'Autres droits et valeurs incorporels', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '21' },

      { numero: '22', libelle: 'TERRAINS', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '221', libelle: 'Terrains agricoles et forestiers', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '22' },
      { numero: '222', libelle: 'Terrains nus', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '22' },
      { numero: '223', libelle: 'Terrains bâtis', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '22' },
      { numero: '224', libelle: 'Travaux de mise en valeur des terrains', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '22' },
      { numero: '228', libelle: 'Autres terrains', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '22' },

      { numero: '23', libelle: 'BATIMENTS, INSTALLATIONS TECHNIQUES ET AGENCEMENTS', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '231', libelle: 'Bâtiments industriels, agricoles et commerciaux', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '23' },
      { numero: '232', libelle: 'Bâtiments administratifs et sociaux', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '23' },
      { numero: '233', libelle: 'Ouvrages d\'infrastructure', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '23' },
      { numero: '234', libelle: 'Installations techniques', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '23' },
      { numero: '235', libelle: 'Aménagements de bureaux', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '23' },
      { numero: '238', libelle: 'Autres installations et agencements', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '23' },

      { numero: '24', libelle: 'MATERIEL', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '241', libelle: 'Matériel et outillage industriel et commercial', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '24' },
      { numero: '242', libelle: 'Matériel et outillage agricole', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '24' },
      { numero: '243', libelle: 'Matériel d\'emballage récupérable et identifiable', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '24' },
      { numero: '244', libelle: 'Matériel et mobilier', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '24' },
      { numero: '245', libelle: 'Matériel de transport', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '24' },
      { numero: '246', libelle: 'Matériel informatique', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '24' },
      { numero: '248', libelle: 'Autres matériels', classe: 2, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '24' },

      // CLASSE 3 - COMPTES DE STOCKS
      { numero: '31', libelle: 'MARCHANDISES', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '311', libelle: 'Marchandises A', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '31' },
      { numero: '312', libelle: 'Marchandises B', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '31' },

      { numero: '32', libelle: 'MATIERES PREMIERES ET FOURNITURES LIEES', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '321', libelle: 'Matières premières', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '32' },
      { numero: '322', libelle: 'Matières et fournitures consommables', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '32' },

      { numero: '33', libelle: 'AUTRES APPROVISIONNEMENTS', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '331', libelle: 'Matières consommables', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '33' },
      { numero: '332', libelle: 'Fournitures d\'atelier et d\'usine', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '33' },
      { numero: '333', libelle: 'Fournitures de magasin', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '33' },
      { numero: '334', libelle: 'Fournitures de bureau', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '33' },

      { numero: '34', libelle: 'PRODUITS EN COURS', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '341', libelle: 'Études en cours', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '34' },
      { numero: '342', libelle: 'Travaux en cours', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '34' },

      { numero: '35', libelle: 'SERVICES EN COURS', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '351', libelle: 'Études en cours de services', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '35' },
      { numero: '352', libelle: 'Prestations de services en cours', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '35' },

      { numero: '36', libelle: 'PRODUITS FINIS', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '361', libelle: 'Produits finis A', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '36' },
      { numero: '362', libelle: 'Produits finis B', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '36' },

      { numero: '37', libelle: 'PRODUITS INTERMEDIAIRES ET RESIDUS', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '371', libelle: 'Produits intermédiaires A', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '37' },
      { numero: '372', libelle: 'Produits intermédiaires B', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '37' },
      { numero: '375', libelle: 'Produits résiduels', classe: 3, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '37' },

      // CLASSE 4 - COMPTES DE TIERS
      { numero: '40', libelle: 'FOURNISSEURS ET COMPTES RATTACHES', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 1 },
      { numero: '401', libelle: 'Fournisseurs', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '40' },
      { numero: '4011', libelle: 'Fournisseurs - Achats de biens', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 3, compte_parent: '401' },
      { numero: '4012', libelle: 'Fournisseurs - Achats de prestations de services', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 3, compte_parent: '401' },
      { numero: '402', libelle: 'Fournisseurs - Effets à payer', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '40' },
      { numero: '403', libelle: 'Fournisseurs - Retenues de garantie', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '40' },
      { numero: '408', libelle: 'Fournisseurs - Factures non parvenues', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '40' },
      { numero: '409', libelle: 'Fournisseurs débiteurs', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '40' },

      { numero: '41', libelle: 'CLIENTS ET COMPTES RATTACHES', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '411', libelle: 'Clients', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '41' },
      { numero: '4111', libelle: 'Clients - Ventes de biens', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 3, compte_parent: '411' },
      { numero: '4112', libelle: 'Clients - Prestations de services', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 3, compte_parent: '411' },
      { numero: '412', libelle: 'Clients - Effets à recevoir', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '41' },
      { numero: '413', libelle: 'Clients - Retenues de garantie', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '41' },
      { numero: '418', libelle: 'Clients - Produits non encore facturés', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '41' },
      { numero: '419', libelle: 'Clients créditeurs', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '41' },

      { numero: '42', libelle: 'PERSONNEL', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 1 },
      { numero: '421', libelle: 'Personnel - Rémunérations dues', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '42' },
      { numero: '422', libelle: 'Personnel - Œuvres sociales', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '42' },
      { numero: '423', libelle: 'Personnel - Participation aux bénéfices', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '42' },
      { numero: '424', libelle: 'Personnel - Œuvres sociales du comité d\'entreprise', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '42' },
      { numero: '425', libelle: 'Personnel - Avances et acomptes', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '42' },
      { numero: '426', libelle: 'Personnel - Dépôts', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '42' },
      { numero: '427', libelle: 'Personnel - Oppositions', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '42' },
      { numero: '428', libelle: 'Personnel - Charges à payer et produits à recevoir', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '42' },

      { numero: '43', libelle: 'ORGANISMES SOCIAUX', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 1 },
      { numero: '431', libelle: 'Sécurité sociale', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '43' },
      { numero: '432', libelle: 'Autres organismes sociaux', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '43' },

      { numero: '44', libelle: 'ETAT ET COLLECTIVITES PUBLIQUES', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 1 },
      { numero: '441', libelle: 'État - Subventions à recevoir', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '44' },
      { numero: '442', libelle: 'État - Impôts et taxes recouvrables sur des tiers', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '44' },
      { numero: '443', libelle: 'État - TVA facturée', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '44' },
      { numero: '444', libelle: 'État - TVA due (intra-communautaire)', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '44' },
      { numero: '445', libelle: 'État - TVA récupérable', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '44' },
      { numero: '446', libelle: 'État - TVA déductible', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '44' },
      { numero: '447', libelle: 'État - Autres impôts, taxes et versements assimilés', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '44' },
      { numero: '448', libelle: 'État - Charges fiscales sur congés à payer', classe: 4, nature: 'PASSIF', sens: 'CREDIT', niveau: 2, compte_parent: '44' },
      { numero: '449', libelle: 'État - Quotas d\'émission alloués par l\'État', classe: 4, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '44' },

      // CLASSE 5 - COMPTES DE TRESORERIE
      { numero: '50', libelle: 'VALEURS MOBILIERES DE PLACEMENT', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '501', libelle: 'Parts dans des entreprises liées', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '50' },
      { numero: '502', libelle: 'Actions', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '50' },
      { numero: '503', libelle: 'Obligations', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '50' },
      { numero: '504', libelle: 'Bons du Trésor et bons de caisse à court terme', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '50' },
      { numero: '505', libelle: 'Bons de souscription d\'actions', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '50' },

      { numero: '52', libelle: 'BANQUES', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '521', libelle: 'Banques locales', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '52' },
      { numero: '522', libelle: 'Banques étrangères', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '52' },
      { numero: '523', libelle: 'Chèques postaux', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '52' },

      { numero: '53', libelle: 'ETABLISSEMENTS FINANCIERS ET ASSIMILES', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '531', libelle: 'Trésor', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '53' },
      { numero: '532', libelle: 'Régies d\'avances', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '53' },
      { numero: '533', libelle: 'Autres établissements financiers', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '53' },

      { numero: '54', libelle: 'INSTRUMENTS DE TRESORERIE', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '541', libelle: 'Escompte de crédit ordinaire', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '54' },
      { numero: '542', libelle: 'Escompte de crédit de campagne', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '54' },
      { numero: '543', libelle: 'Crédits de trésorerie', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '54' },
      { numero: '544', libelle: 'Escompte de crédit relais', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '54' },

      { numero: '57', libelle: 'CAISSE', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 1 },
      { numero: '571', libelle: 'Caisse siège social', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '57' },
      { numero: '572', libelle: 'Caisse succursale A', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '57' },
      { numero: '573', libelle: 'Caisse succursale B', classe: 5, nature: 'ACTIF', sens: 'DEBIT', niveau: 2, compte_parent: '57' }
    ];

    await queryInterface.bulkInsert('compte_comptables', comptes.map(compte => ({
      ...compte,
      created_at: new Date(),
      updated_at: new Date()
    })));
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('compte_comptables', null, {});
  }
};
