'use strict';

/**
 * Tests pour les fonctionnalités de templates - Jour 6 Phase 3
 */

const TemplateService = require('../services/templateService');

// Mock des modèles pour les tests
const mockModels = {
  TemplateEcriture: {
    validateTemplateData: jest.fn(() => ({ valide: true, erreurs: [] })),
    findOne: jest.fn(() => null),
    create: jest.fn(() => ({ id: 'template-uuid' })),
    findByPk: jest.fn(),
    findAndCountAll: jest.fn()
  },
  TemplateLigneEcriture: {
    validateLigneTemplateData: jest.fn(() => ({ valide: true, erreurs: [] })),
    create: jest.fn()
  },
  EcritureComptable: {
    findByPk: jest.fn()
  },
  LigneEcriture: {},
  CompteComptable: {},
  Journal: {},
  Societe: {},
  sequelize: {
    transaction: jest.fn(() => ({
      commit: jest.fn(),
      rollback: jest.fn()
    }))
  },
  Sequelize: {
    Op: {
      iLike: Symbol('iLike'),
      or: Symbol('or'),
      in: Symbol('in')
    }
  }
};

describe('🧪 Tests Jour 6 - Templates d\'Écritures', () => {
  let templateService;

  beforeEach(() => {
    // Réinitialiser les mocks
    jest.clearAllMocks();
    
    // Créer le service
    templateService = new TemplateService(mockModels);
  });

  describe('📝 Test 1: Création de template', () => {
    test('Devrait créer un template avec ses lignes', async () => {
      const donnees = {
        nom: 'Vente avec TVA',
        description: 'Template pour vente avec TVA',
        libelle: 'Vente {{client}} - Facture {{numero}}',
        journalCode: 'VT',
        categorie: 'VENTE',
        societeId: 'societe-uuid',
        public: true
      };

      const lignes = [
        {
          compteNumero: '411000',
          libelle: 'Client {{client}}',
          sens: 'DEBIT',
          formuleMontant: '{{montant_ht}} * 1.18',
          ordre: 1
        },
        {
          compteNumero: '701000',
          libelle: 'Vente de marchandises',
          sens: 'CREDIT',
          formuleMontant: '{{montant_ht}}',
          ordre: 2
        },
        {
          compteNumero: '443100',
          libelle: 'TVA collectée',
          sens: 'CREDIT',
          formuleMontant: '{{montant_ht}} * 0.18',
          ordre: 3
        }
      ];

      // Mock du template créé avec ses lignes
      const mockTemplate = {
        id: 'template-uuid',
        nom: donnees.nom,
        formater: jest.fn(() => ({
          id: 'template-uuid',
          nom: donnees.nom,
          categorie: donnees.categorie,
          parametresVariables: ['client', 'numero', 'montant_ht'],
          nombreLignes: 3
        }))
      };

      mockModels.TemplateEcriture.findByPk.mockResolvedValue(mockTemplate);

      const template = await templateService.creerTemplate(donnees, lignes);

      expect(mockModels.TemplateEcriture.validateTemplateData).toHaveBeenCalledWith(donnees);
      expect(mockModels.TemplateLigneEcriture.validateLigneTemplateData).toHaveBeenCalledTimes(3);
      expect(mockModels.TemplateEcriture.create).toHaveBeenCalledWith(donnees, expect.any(Object));
      expect(mockModels.TemplateLigneEcriture.create).toHaveBeenCalledTimes(3);

      console.log('✅ Test création template: RÉUSSI');
    });

    test('Devrait rejeter un template avec moins de 2 lignes', async () => {
      const donnees = {
        nom: 'Template invalide',
        societeId: 'societe-uuid',
        journalCode: 'VT',
        categorie: 'VENTE'
      };

      const lignes = [
        {
          compteNumero: '411000',
          libelle: 'Une seule ligne',
          sens: 'DEBIT',
          montantFixe: 1000
        }
      ];

      await expect(templateService.creerTemplate(donnees, lignes))
        .rejects
        .toThrow('Un template doit avoir au moins 2 lignes');

      console.log('✅ Test validation nombre lignes: RÉUSSI');
    });
  });

  describe('🔄 Test 2: Application de template', () => {
    test('Devrait appliquer un template avec paramètres', async () => {
      const mockTemplate = {
        id: 'template-uuid',
        nom: 'Vente avec TVA',
        journalCode: 'VT',
        societeId: 'societe-uuid',
        actif: true,
        appliquerParametres: jest.fn((params) => ({
          libelle: `Vente ${params.client} - Facture ${params.numero}`
        })),
        lignes: [
          {
            toLigneEcriture: jest.fn((params) => ({
              compteNumero: '411000',
              libelle: `Client ${params.client}`,
              debit: params.montant_ht * 1.18,
              credit: 0,
              ordre: 1
            }))
          },
          {
            toLigneEcriture: jest.fn((params) => ({
              compteNumero: '701000',
              libelle: 'Vente de marchandises',
              debit: 0,
              credit: params.montant_ht,
              ordre: 2
            }))
          },
          {
            toLigneEcriture: jest.fn((params) => ({
              compteNumero: '443100',
              libelle: 'TVA collectée',
              debit: 0,
              credit: params.montant_ht * 0.18,
              ordre: 3
            }))
          }
        ]
      };

      mockModels.TemplateEcriture.findByPk.mockResolvedValue(mockTemplate);

      const parametres = {
        client: 'ACME Corp',
        numero: 'F2025-001',
        montant_ht: 1000
      };

      const donneesEcriture = {
        dateEcriture: '2025-08-07',
        reference: 'REF-001'
      };

      const resultat = await templateService.appliquerTemplate('template-uuid', parametres, donneesEcriture);

      expect(resultat).toHaveProperty('donnees');
      expect(resultat).toHaveProperty('lignes');
      expect(resultat).toHaveProperty('template');
      expect(resultat.lignes).toHaveLength(3);

      // Vérifier l'équilibre
      const totalDebit = resultat.lignes.reduce((sum, ligne) => sum + ligne.debit, 0);
      const totalCredit = resultat.lignes.reduce((sum, ligne) => sum + ligne.credit, 0);
      expect(Math.abs(totalDebit - totalCredit)).toBeLessThan(0.01);

      console.log('✅ Test application template: RÉUSSI');
    });

    test('Devrait rejeter un template inactif', async () => {
      const mockTemplate = {
        id: 'template-uuid',
        actif: false
      };

      mockModels.TemplateEcriture.findByPk.mockResolvedValue(mockTemplate);

      await expect(templateService.appliquerTemplate('template-uuid', {}, {}))
        .rejects
        .toThrow('Ce template n\'est pas actif');

      console.log('✅ Test template inactif: RÉUSSI');
    });
  });

  describe('📋 Test 3: Création depuis écriture existante', () => {
    test('Devrait créer un template depuis une écriture', async () => {
      const mockEcriture = {
        id: 'ecriture-uuid',
        numeroEcriture: 'VT000001',
        libelle: 'Vente ACME Corp',
        journalCode: 'VT',
        societeId: 'societe-uuid',
        lignes: [
          {
            compteNumero: '411000',
            libelle: 'Client ACME Corp',
            debit: 1180,
            credit: 0
          },
          {
            compteNumero: '701000',
            libelle: 'Vente de marchandises',
            debit: 0,
            credit: 1000
          },
          {
            compteNumero: '443100',
            libelle: 'TVA collectée',
            debit: 0,
            credit: 180
          }
        ]
      };

      mockModels.EcritureComptable.findByPk.mockResolvedValue(mockEcriture);

      // Mock de la création du template
      templateService.creerTemplate = jest.fn().mockResolvedValue({
        id: 'nouveau-template-uuid',
        nom: 'Template depuis écriture'
      });

      const donneesTemplate = {
        nom: 'Template depuis écriture',
        description: 'Créé depuis VT000001',
        categorie: 'VENTE'
      };

      const template = await templateService.creerTemplateDepuisEcriture('ecriture-uuid', donneesTemplate);

      expect(mockModels.EcritureComptable.findByPk).toHaveBeenCalledWith('ecriture-uuid', expect.any(Object));
      expect(templateService.creerTemplate).toHaveBeenCalledWith(
        expect.objectContaining({
          nom: donneesTemplate.nom,
          journalCode: 'VT',
          societeId: 'societe-uuid'
        }),
        expect.arrayContaining([
          expect.objectContaining({
            compteNumero: '411000',
            sens: 'DEBIT',
            montantFixe: 1180
          }),
          expect.objectContaining({
            compteNumero: '701000',
            sens: 'CREDIT',
            montantFixe: 1000
          }),
          expect.objectContaining({
            compteNumero: '443100',
            sens: 'CREDIT',
            montantFixe: 180
          })
        ])
      );

      console.log('✅ Test création depuis écriture: RÉUSSI');
    });
  });

  describe('🔍 Test 4: Recherche et filtrage', () => {
    test('Devrait récupérer les templates avec filtres', async () => {
      const mockTemplates = [
        {
          id: 'template1',
          nom: 'Vente avec TVA',
          categorie: 'VENTE',
          formater: jest.fn(() => ({ id: 'template1', nom: 'Vente avec TVA' }))
        },
        {
          id: 'template2',
          nom: 'Achat avec TVA',
          categorie: 'ACHAT',
          formater: jest.fn(() => ({ id: 'template2', nom: 'Achat avec TVA' }))
        }
      ];

      mockModels.TemplateEcriture.findAndCountAll.mockResolvedValue({
        count: 2,
        rows: mockTemplates
      });

      const filtres = {
        categorie: 'VENTE',
        recherche: 'TVA',
        page: 1,
        limit: 10
      };

      const resultats = await templateService.getTemplates('societe-uuid', filtres);

      expect(resultats).toHaveProperty('templates');
      expect(resultats).toHaveProperty('pagination');
      expect(resultats.templates).toHaveLength(2);
      expect(resultats.pagination.total).toBe(2);

      console.log('✅ Test recherche templates: RÉUSSI');
    });
  });

  describe('🗑️ Test 5: Suppression de template', () => {
    test('Devrait supprimer un template (logiquement)', async () => {
      const mockTemplate = {
        id: 'template-uuid',
        nom: 'Template à supprimer',
        update: jest.fn()
      };

      mockModels.TemplateEcriture.findByPk.mockResolvedValue(mockTemplate);

      const resultat = await templateService.supprimerTemplate('template-uuid');

      expect(mockTemplate.update).toHaveBeenCalledWith({ actif: false });
      expect(resultat).toBe(true);

      console.log('✅ Test suppression template: RÉUSSI');
    });
  });
});

// Fonction de test principal
async function testerTemplates() {
  console.log('🧪 Test des fonctionnalités de templates - Jour 6\n');

  try {
    // Les tests Jest s'exécutent automatiquement
    console.log('✅ Tous les tests de templates sont configurés');
    console.log('🎯 Fonctionnalités testées:');
    console.log('   - Création de templates avec validation');
    console.log('   - Application de templates avec paramètres variables');
    console.log('   - Création de templates depuis écritures existantes');
    console.log('   - Recherche et filtrage de templates');
    console.log('   - Suppression logique de templates');
    console.log('\n🚀 Système de templates opérationnel !');

  } catch (error) {
    console.error('❌ Erreur lors des tests:', error.message);
    throw error;
  }
}

// Export pour utilisation
module.exports = {
  testerTemplates
};

// Exécution si appelé directement
if (require.main === module) {
  testerTemplates().catch(console.error);
}
