'use strict';

/**
 * Utilitaires de calcul pour les analyses financières
 * Conforme aux normes SYSCOHADA
 */

/**
 * Calcule la tendance d'évolution d'une série de valeurs
 * @param {Array<number>} valeurs - Série de valeurs à analyser
 * @returns {Object} Tendance d'évolution
 */
function calculerTendance(valeurs) {
  if (!Array.isArray(valeurs) || valeurs.length < 2) {
    return {
      tendance: 'STABLE',
      variation: 0,
      variationPourcentage: 0
    };
  }

  // Calculer la variation entre la première et la dernière valeur
  const premiereValeur = valeurs[0];
  const derniereValeur = valeurs[valeurs.length - 1];
  const variation = derniereValeur - premiereValeur;
  const variationPourcentage = premiereValeur !== 0 
    ? (variation / premiereValeur) * 100 
    : 0;

  // Déterminer la tendance
  let tendance = 'STABLE';
  if (variationPourcentage > 5) {
    tendance = 'HAUSSE';
  } else if (variationPourcentage < -5) {
    tendance = 'BAISSE';
  }

  return {
    tendance,
    variation,
    variationPourcentage
  };
}

/**
 * Détecte les anomalies dans une série de valeurs
 * @param {Array<number>} valeurs - Série de valeurs à analyser
 * @param {Object} options - Options de détection
 * @returns {Array<Object>} Anomalies détectées
 */
function detecterAnomalies(valeurs, options = {}) {
  if (!Array.isArray(valeurs) || valeurs.length < 3) {
    return [];
  }

  const {
    seuilEcartType = 2,
    seuilVariationBrusque = 50
  } = options;

  const anomalies = [];

  // Calculer la moyenne et l'écart-type
  const moyenne = valeurs.reduce((sum, val) => sum + val, 0) / valeurs.length;
  const ecartType = Math.sqrt(
    valeurs.reduce((sum, val) => sum + Math.pow(val - moyenne, 2), 0) / valeurs.length
  );

  // Détecter les valeurs aberrantes (écart-type)
  valeurs.forEach((valeur, index) => {
    const ecart = Math.abs(valeur - moyenne);
    if (ecart > seuilEcartType * ecartType) {
      anomalies.push({
        type: 'VALEUR_ABERRANTE',
        index,
        valeur,
        moyenne,
        ecart,
        seuil: seuilEcartType * ecartType
      });
    }
  });

  // Détecter les variations brusques
  for (let i = 1; i < valeurs.length; i++) {
    const valeurPrecedente = valeurs[i - 1];
    const valeurActuelle = valeurs[i];
    
    if (valeurPrecedente !== 0) {
      const variationPourcentage = Math.abs((valeurActuelle - valeurPrecedente) / valeurPrecedente * 100);
      
      if (variationPourcentage > seuilVariationBrusque) {
        anomalies.push({
          type: 'VARIATION_BRUSQUE',
          index: i,
          valeurPrecedente,
          valeurActuelle,
          variationPourcentage,
          seuil: seuilVariationBrusque
        });
      }
    }
  }

  return anomalies;
}

/**
 * Calcule les prévisions financières basées sur une série historique
 * @param {Array<number>} historique - Série historique de valeurs
 * @param {number} horizon - Nombre de périodes à prévoir
 * @param {string} methode - Méthode de prévision ('moyenne_mobile', 'regression_lineaire')
 * @returns {Array<number>} Prévisions
 */
function calculerPrevisions(historique, horizon = 3, methode = 'regression_lineaire') {
  if (!Array.isArray(historique) || historique.length < 2 || horizon < 1) {
    return [];
  }

  switch (methode) {
  case 'moyenne_mobile':
    return calculerPrevisionsMoyenneMobile(historique, horizon);
  case 'regression_lineaire':
    return calculerPrevisionsRegressionLineaire(historique, horizon);
  default:
    return calculerPrevisionsRegressionLineaire(historique, horizon);
  }
}

/**
 * Calcule les prévisions par moyenne mobile
 * @param {Array<number>} historique - Série historique de valeurs
 * @param {number} horizon - Nombre de périodes à prévoir
 * @returns {Array<number>} Prévisions
 */
function calculerPrevisionsMoyenneMobile(historique, horizon) {
  const fenetreMobile = Math.min(3, historique.length);
  const previsions = [];

  // Calculer la moyenne des dernières valeurs
  const derniereMoyenne = historique
    .slice(-fenetreMobile)
    .reduce((sum, val) => sum + val, 0) / fenetreMobile;

  // Utiliser cette moyenne pour les prévisions
  for (let i = 0; i < horizon; i++) {
    previsions.push(derniereMoyenne);
  }

  return previsions;
}

/**
 * Calcule les prévisions par régression linéaire
 * @param {Array<number>} historique - Série historique de valeurs
 * @param {number} horizon - Nombre de périodes à prévoir
 * @returns {Array<number>} Prévisions
 */
function calculerPrevisionsRegressionLineaire(historique, horizon) {
  const n = historique.length;
  
  // Calculer les sommes nécessaires pour la régression
  let sommeX = 0;
  let sommeY = 0;
  let sommeXY = 0;
  let sommeXX = 0;
  
  for (let i = 0; i < n; i++) {
    sommeX += i;
    sommeY += historique[i];
    sommeXY += i * historique[i];
    sommeXX += i * i;
  }
  
  // Calculer les coefficients de la droite y = a + bx
  const b = (n * sommeXY - sommeX * sommeY) / (n * sommeXX - sommeX * sommeX);
  const a = (sommeY - b * sommeX) / n;
  
  // Calculer les prévisions
  const previsions = [];
  for (let i = 0; i < horizon; i++) {
    const x = n + i;
    const prevision = a + b * x;
    previsions.push(Math.max(0, prevision)); // Éviter les valeurs négatives
  }
  
  return previsions;
}

/**
 * Compare les valeurs réelles avec un budget
 * @param {Array<number>} valeursReelles - Valeurs réelles
 * @param {Array<number>} valeursBudget - Valeurs budgétées
 * @returns {Object} Comparaison budgétaire
 */
function comparerBudget(valeursReelles, valeursBudget) {
  if (!Array.isArray(valeursReelles) || !Array.isArray(valeursBudget) || 
      valeursReelles.length !== valeursBudget.length || valeursReelles.length === 0) {
    return {
      ecartTotal: 0,
      ecartPourcentage: 0,
      ecarts: []
    };
  }

  const ecarts = [];
  let totalReel = 0;
  let totalBudget = 0;

  // Calculer les écarts pour chaque période
  for (let i = 0; i < valeursReelles.length; i++) {
    const reel = valeursReelles[i];
    const budget = valeursBudget[i];
    const ecart = reel - budget;
    const ecartPourcentage = budget !== 0 ? (ecart / budget) * 100 : 0;

    ecarts.push({
      reel,
      budget,
      ecart,
      ecartPourcentage
    });

    totalReel += reel;
    totalBudget += budget;
  }

  // Calculer l'écart total
  const ecartTotal = totalReel - totalBudget;
  const ecartPourcentage = totalBudget !== 0 ? (ecartTotal / totalBudget) * 100 : 0;

  return {
    ecartTotal,
    ecartPourcentage,
    ecarts
  };
}

module.exports = {
  calculerTendance,
  detecterAnomalies,
  calculerPrevisions,
  calculerPrevisionsMoyenneMobile,
  calculerPrevisionsRegressionLineaire,
  comparerBudget
};