'use strict';

const express = require('express');
const router = express.Router();
const calculController = require('../controllers/calculController');
const { protect } = require('../middleware/auth');
const { param, query } = require('express-validator');
const validation = require('../middleware/validation');

/**
 * Routes pour les calculs comptables et gestion des soldes
 * Toutes les routes nécessitent une authentification
 */

// ==========================================
// ROUTES DE CALCULS DE SOLDES
// ==========================================

/**
 * @route GET /api/v1/calculs/solde/:compteNumero
 * @desc Calcule le solde d'un compte à une date donnée
 * @access Private
 */
router.get('/solde/:compteNumero',
  protect,
  [
    param('compteNumero')
      .notEmpty()
      .withMessage('Le numéro de compte est obligatoire')
      .isLength({ min: 1, max: 10 })
      .withMessage('Le numéro de compte doit faire entre 1 et 10 caractères'),
    query('dateFin')
      .notEmpty()
      .withMessage('La date de fin est obligatoire')
      .isISO8601()
      .withMessage('La date de fin doit être au format ISO 8601'),
    query('dateDebut')
      .optional()
      .isISO8601()
      .withMessage('La date de début doit être au format ISO 8601'),
    query('societeId')
      .optional()
      .isUUID()
      .withMessage('L\'ID de société doit être un UUID valide'),
    query('exerciceId')
      .optional()
      .isUUID()
      .withMessage('L\'ID d\'exercice doit être un UUID valide'),
    query('includeNonValidees')
      .optional()
      .isBoolean()
      .withMessage('includeNonValidees doit être un booléen'),
    query('useCache')
      .optional()
      .isBoolean()
      .withMessage('useCache doit être un booléen')
  ],
  validation.handleValidationErrors,
  (req, res, next) => calculController.calculerSoldeCompte(req, res, next)
);

/**
 * @route GET /api/v1/calculs/soldes-progressifs/:compteNumero
 * @desc Calcule les soldes progressifs d'un compte sur une période
 * @access Private
 */
router.get('/soldes-progressifs/:compteNumero',
  protect,
  [
    param('compteNumero')
      .notEmpty()
      .withMessage('Le numéro de compte est obligatoire')
      .isLength({ min: 1, max: 10 })
      .withMessage('Le numéro de compte doit faire entre 1 et 10 caractères'),
    query('dateDebut')
      .notEmpty()
      .withMessage('La date de début est obligatoire')
      .isISO8601()
      .withMessage('La date de début doit être au format ISO 8601'),
    query('dateFin')
      .notEmpty()
      .withMessage('La date de fin est obligatoire')
      .isISO8601()
      .withMessage('La date de fin doit être au format ISO 8601'),
    query('societeId')
      .optional()
      .isUUID()
      .withMessage('L\'ID de société doit être un UUID valide'),
    query('exerciceId')
      .optional()
      .isUUID()
      .withMessage('L\'ID d\'exercice doit être un UUID valide'),
    query('includeNonValidees')
      .optional()
      .isBoolean()
      .withMessage('includeNonValidees doit être un booléen'),
    query('groupeParMois')
      .optional()
      .isBoolean()
      .withMessage('groupeParMois doit être un booléen')
  ],
  validation.handleValidationErrors,
  (req, res, next) => calculController.calculerSoldesProgressifs(req, res, next)
);

// ==========================================
// ROUTES DE CALCULS DE JOURNAUX
// ==========================================

/**
 * @route GET /api/v1/calculs/totaux-journal/:journalCode
 * @desc Calcule les totaux d'un journal sur une période
 * @access Private
 */
router.get('/totaux-journal/:journalCode',
  protect,
  [
    param('journalCode')
      .notEmpty()
      .withMessage('Le code journal est obligatoire')
      .isLength({ min: 1, max: 10 })
      .withMessage('Le code journal doit faire entre 1 et 10 caractères'),
    query('dateDebut')
      .notEmpty()
      .withMessage('La date de début est obligatoire')
      .isISO8601()
      .withMessage('La date de début doit être au format ISO 8601'),
    query('dateFin')
      .notEmpty()
      .withMessage('La date de fin est obligatoire')
      .isISO8601()
      .withMessage('La date de fin doit être au format ISO 8601'),
    query('societeId')
      .optional()
      .isUUID()
      .withMessage('L\'ID de société doit être un UUID valide'),
    query('exerciceId')
      .optional()
      .isUUID()
      .withMessage('L\'ID d\'exercice doit être un UUID valide'),
    query('includeNonValidees')
      .optional()
      .isBoolean()
      .withMessage('includeNonValidees doit être un booléen'),
    query('groupeParCompte')
      .optional()
      .isBoolean()
      .withMessage('groupeParCompte doit être un booléen')
  ],
  validation.handleValidationErrors,
  (req, res, next) => calculController.calculerTotauxJournal(req, res, next)
);

// ==========================================
// ROUTES DE CALCULS DE BALANCES
// ==========================================

/**
 * @route GET /api/v1/calculs/balance
 * @desc Calcule la balance générale
 * @access Private
 */
router.get('/balance',
  protect,
  [
    query('dateDebut')
      .notEmpty()
      .withMessage('La date de début est obligatoire')
      .isISO8601()
      .withMessage('La date de début doit être au format ISO 8601'),
    query('dateFin')
      .notEmpty()
      .withMessage('La date de fin est obligatoire')
      .isISO8601()
      .withMessage('La date de fin doit être au format ISO 8601'),
    query('societeId')
      .optional()
      .isUUID()
      .withMessage('L\'ID de société doit être un UUID valide'),
    query('exerciceId')
      .optional()
      .isUUID()
      .withMessage('L\'ID d\'exercice doit être un UUID valide'),
    query('includeNonValidees')
      .optional()
      .isBoolean()
      .withMessage('includeNonValidees doit être un booléen'),
    query('niveauDetail')
      .optional()
      .isIn(['TOUS', 'DETAIL', 'COLLECTIF'])
      .withMessage('niveauDetail doit être TOUS, DETAIL ou COLLECTIF'),
    query('classeComptes')
      .optional()
      .matches(/^[1-8](,[1-8])*$/)
      .withMessage('classeComptes doit être une liste de classes séparées par des virgules (1-8)'),
    query('seulementAvecMouvement')
      .optional()
      .isBoolean()
      .withMessage('seulementAvecMouvement doit être un booléen')
  ],
  validation.handleValidationErrors,
  (req, res, next) => calculController.calculerBalanceGenerale(req, res, next)
);

/**
 * @route GET /api/v1/calculs/balance-auxiliaire/:compteCollectif
 * @desc Calcule la balance auxiliaire pour un compte collectif
 * @access Private
 */
router.get('/balance-auxiliaire/:compteCollectif',
  protect,
  [
    param('compteCollectif')
      .notEmpty()
      .withMessage('Le numéro de compte collectif est obligatoire')
      .isLength({ min: 1, max: 10 })
      .withMessage('Le numéro de compte collectif doit faire entre 1 et 10 caractères'),
    query('dateDebut')
      .notEmpty()
      .withMessage('La date de début est obligatoire')
      .isISO8601()
      .withMessage('La date de début doit être au format ISO 8601'),
    query('dateFin')
      .notEmpty()
      .withMessage('La date de fin est obligatoire')
      .isISO8601()
      .withMessage('La date de fin doit être au format ISO 8601'),
    query('societeId')
      .optional()
      .isUUID()
      .withMessage('L\'ID de société doit être un UUID valide'),
    query('exerciceId')
      .optional()
      .isUUID()
      .withMessage('L\'ID d\'exercice doit être un UUID valide'),
    query('includeNonValidees')
      .optional()
      .isBoolean()
      .withMessage('includeNonValidees doit être un booléen'),
    query('seulementAvecMouvement')
      .optional()
      .isBoolean()
      .withMessage('seulementAvecMouvement doit être un booléen')
  ],
  validation.handleValidationErrors,
  (req, res, next) => calculController.calculerBalanceAuxiliaire(req, res, next)
);

// ==========================================
// ROUTES DE STATISTIQUES
// ==========================================

/**
 * @route GET /api/v1/calculs/statistiques
 * @desc Calcule les statistiques générales d'une société
 * @access Private
 */
router.get('/statistiques',
  protect,
  [
    query('dateDebut')
      .notEmpty()
      .withMessage('La date de début est obligatoire')
      .isISO8601()
      .withMessage('La date de début doit être au format ISO 8601'),
    query('dateFin')
      .notEmpty()
      .withMessage('La date de fin est obligatoire')
      .isISO8601()
      .withMessage('La date de fin doit être au format ISO 8601'),
    query('societeId')
      .notEmpty()
      .withMessage('L\'ID de société est obligatoire')
      .isUUID()
      .withMessage('L\'ID de société doit être un UUID valide')
  ],
  validation.handleValidationErrors,
  (req, res, next) => calculController.calculerStatistiquesGenerales(req, res, next)
);

// ==========================================
// ROUTES DE GESTION DU CACHE
// ==========================================

/**
 * @route GET /api/v1/calculs/cache/stats
 * @desc Obtient les statistiques du cache des calculs
 * @access Private
 */
router.get('/cache/stats',
  protect,
  (req, res, next) => calculController.gererCache(req, res, next)
);

/**
 * @route DELETE /api/v1/calculs/cache
 * @desc Vide le cache des calculs
 * @access Private
 */
router.delete('/cache',
  protect,
  (req, res, next) => calculController.gererCache(req, res, next)
);

// ==========================================
// ROUTES UTILITAIRES
// ==========================================

/**
 * @route GET /api/v1/calculs/info
 * @desc Retourne les informations sur les calculs disponibles
 * @access Private
 */
router.get('/info',
  protect,
  (req, res) => {
    res.json({
      success: true,
      data: {
        calculsDisponibles: {
          soldes: {
            endpoint: '/api/v1/calculs/solde/:compteNumero',
            description: 'Calcule le solde d\'un compte à une date donnée',
            parametres: {
              obligatoires: ['compteNumero', 'dateFin'],
              optionnels: ['dateDebut', 'societeId', 'exerciceId', 'includeNonValidees', 'useCache']
            }
          },
          soldesProgressifs: {
            endpoint: '/api/v1/calculs/soldes-progressifs/:compteNumero',
            description: 'Calcule les soldes progressifs d\'un compte sur une période',
            parametres: {
              obligatoires: ['compteNumero', 'dateDebut', 'dateFin'],
              optionnels: ['societeId', 'exerciceId', 'includeNonValidees', 'groupeParMois']
            }
          },
          totauxJournal: {
            endpoint: '/api/v1/calculs/totaux-journal/:journalCode',
            description: 'Calcule les totaux d\'un journal sur une période',
            parametres: {
              obligatoires: ['journalCode', 'dateDebut', 'dateFin'],
              optionnels: ['societeId', 'exerciceId', 'includeNonValidees', 'groupeParCompte']
            }
          },
          balanceGenerale: {
            endpoint: '/api/v1/calculs/balance',
            description: 'Calcule la balance générale',
            parametres: {
              obligatoires: ['dateDebut', 'dateFin'],
              optionnels: ['societeId', 'exerciceId', 'includeNonValidees', 'niveauDetail', 'classeComptes', 'seulementAvecMouvement']
            }
          },
          balanceAuxiliaire: {
            endpoint: '/api/v1/calculs/balance-auxiliaire/:compteCollectif',
            description: 'Calcule la balance auxiliaire pour un compte collectif',
            parametres: {
              obligatoires: ['compteCollectif', 'dateDebut', 'dateFin'],
              optionnels: ['societeId', 'exerciceId', 'includeNonValidees', 'seulementAvecMouvement']
            }
          },
          statistiques: {
            endpoint: '/api/v1/calculs/statistiques',
            description: 'Calcule les statistiques générales d\'une société',
            parametres: {
              obligatoires: ['societeId', 'dateDebut', 'dateFin'],
              optionnels: []
            }
          }
        },
        cache: {
          stats: '/api/v1/calculs/cache/stats',
          vider: 'DELETE /api/v1/calculs/cache'
        },
        formatsDates: 'ISO 8601 (YYYY-MM-DD)',
        classesComptes: {
          1: 'Comptes de capitaux',
          2: 'Comptes d\'immobilisations',
          3: 'Comptes de stocks',
          4: 'Comptes de tiers',
          5: 'Comptes de trésorerie',
          6: 'Comptes de charges',
          7: 'Comptes de produits',
          8: 'Comptes spéciaux'
        }
      }
    });
  }
);

module.exports = router;
