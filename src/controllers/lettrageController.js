'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');
const LettrageService = require('../services/lettrageService');

/**
 * Controller pour la gestion du lettrage et des rapprochements
 */
class LettrageController {
  constructor() {
    this.lettrageService = null;
  }

  /**
   * Initialise le service avec les modèles
   * @param {Object} models - Modèles Sequelize
   */
  init(models) {
    this.lettrageService = new LettrageService(models);
  }

  /**
   * Effectue un lettrage manuel
   * POST /api/v1/lettrage/manuel
   */
  async lettrageManuel(req, res, next) {
    try {
      const { compteNumero, ligneIds, codeLettrage } = req.body;

      // Validation des données requises
      if (!compteNumero || !ligneIds || !Array.isArray(ligneIds)) {
        return res.status(400).json({
          success: false,
          message: 'Le numéro de compte et la liste des IDs de lignes sont obligatoires',
          code: 'DONNEES_MANQUANTES'
        });
      }

      if (ligneIds.length < 2) {
        return res.status(400).json({
          success: false,
          message: 'Le lettrage nécessite au moins 2 lignes d\'écriture',
          code: 'LIGNES_INSUFFISANTES'
        });
      }

      const utilisateurId = req.user?.id;
      const resultat = await this.lettrageService.lettrageManuel(
        compteNumero,
        ligneIds,
        codeLettrage,
        utilisateurId
      );

      logger.info('Lettrage manuel effectué via API', {
        compteNumero,
        codeLettrage: resultat.codeLettrage,
        nombreLignes: resultat.nombreLignes,
        utilisateur: utilisateurId
      });

      res.json({
        success: true,
        message: 'Lettrage effectué avec succès',
        data: resultat
      });

    } catch (error) {
      logger.error('Erreur lors du lettrage manuel via API', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Effectue un lettrage automatique
   * POST /api/v1/lettrage/automatique
   */
  async lettrageAutomatique(req, res, next) {
    try {
      const { compteNumero, criteres = {} } = req.body;

      // Validation des données requises
      if (!compteNumero) {
        return res.status(400).json({
          success: false,
          message: 'Le numéro de compte est obligatoire',
          code: 'COMPTE_MANQUANT'
        });
      }

      const utilisateurId = req.user?.id;
      const resultat = await this.lettrageService.lettrageAutomatique(
        compteNumero,
        criteres,
        utilisateurId
      );

      logger.info('Lettrage automatique effectué via API', {
        compteNumero,
        lettragesEffectues: resultat.lettragesEffectues,
        lignesTraitees: resultat.lignesTraitees,
        utilisateur: utilisateurId
      });

      res.json({
        success: true,
        message: 'Lettrage automatique terminé',
        data: resultat
      });

    } catch (error) {
      logger.error('Erreur lors du lettrage automatique via API', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Effectue un délettrage
   * POST /api/v1/lettrage/delettrage
   */
  async delettrage(req, res, next) {
    try {
      const { ligneIds } = req.body;

      // Validation des données requises
      if (!ligneIds || !Array.isArray(ligneIds) || ligneIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'La liste des IDs de lignes est obligatoire',
          code: 'LIGNES_MANQUANTES'
        });
      }

      const utilisateurId = req.user?.id;
      const resultat = await this.lettrageService.delettrage(ligneIds, utilisateurId);

      logger.info('Délettrage effectué via API', {
        nombreLignes: resultat.nombreLignes,
        codesLettrage: resultat.codesLettrage,
        utilisateur: utilisateurId
      });

      res.json({
        success: true,
        message: 'Délettrage effectué avec succès',
        data: resultat
      });

    } catch (error) {
      logger.error('Erreur lors du délettrage via API', {
        error: error.message,
        body: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Récupère les lignes à lettrer pour un compte
   * GET /api/v1/lettrage/lignes/:compteNumero
   */
  async getLignesALettrer(req, res, next) {
    try {
      const { compteNumero } = req.params;
      const filtres = {
        dateDebut: req.query.dateDebut,
        dateFin: req.query.dateFin,
        montantMin: req.query.montantMin ? parseFloat(req.query.montantMin) : undefined,
        montantMax: req.query.montantMax ? parseFloat(req.query.montantMax) : undefined,
        page: req.query.page ? parseInt(req.query.page) : 1,
        limit: req.query.limit ? parseInt(req.query.limit) : 50,
        seulement_non_lettrees: req.query.seulement_non_lettrees !== 'false'
      };

      const resultat = await this.lettrageService.getLignesALettrer(compteNumero, filtres);

      res.json({
        success: true,
        message: 'Lignes récupérées avec succès',
        data: resultat
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des lignes à lettrer via API', {
        error: error.message,
        compteNumero: req.params.compteNumero,
        filtres: req.query
      });
      next(error);
    }
  }

  /**
   * Récupère les soldes lettrés et non lettrés d'un compte
   * GET /api/v1/lettrage/soldes/:compteNumero
   */
  async getSoldes(req, res, next) {
    try {
      const { compteNumero } = req.params;
      const periode = {
        dateDebut: req.query.dateDebut,
        dateFin: req.query.dateFin
      };

      const [soldeLettre, soldeNonLettre] = await Promise.all([
        this.lettrageService.getSoldeLettre(compteNumero, periode),
        this.lettrageService.getSoldeNonLettre(compteNumero, periode)
      ]);

      const soldeTotal = soldeLettre + soldeNonLettre;

      res.json({
        success: true,
        message: 'Soldes calculés avec succès',
        data: {
          compteNumero,
          periode,
          soldes: {
            lettre: soldeLettre,
            nonLettre: soldeNonLettre,
            total: soldeTotal
          },
          pourcentageLettrage: soldeTotal !== 0 ? (soldeLettre / soldeTotal) * 100 : 0
        }
      });

    } catch (error) {
      logger.error('Erreur lors du calcul des soldes via API', {
        error: error.message,
        compteNumero: req.params.compteNumero,
        periode: req.query
      });
      next(error);
    }
  }

  /**
   * Récupère les statistiques de lettrage pour un compte
   * GET /api/v1/lettrage/statistiques/:compteNumero
   */
  async getStatistiques(req, res, next) {
    try {
      const { compteNumero } = req.params;
      const periode = {
        dateDebut: req.query.dateDebut,
        dateFin: req.query.dateFin
      };

      // Récupérer les lignes pour calculer les statistiques
      const lignesLettrees = await this.lettrageService.getLignesALettrer(compteNumero, {
        ...periode,
        seulement_non_lettrees: false,
        limit: 1000 // Limite élevée pour les statistiques
      });

      const lignesNonLettrees = await this.lettrageService.getLignesALettrer(compteNumero, {
        ...periode,
        seulement_non_lettrees: true,
        limit: 1000
      });

      // Calculer les statistiques
      const totalLignes = lignesLettrees.pagination.total;
      const lignesLettreesCount = totalLignes - lignesNonLettrees.pagination.total;
      const lignesNonLettreesCount = lignesNonLettrees.pagination.total;

      // Grouper les lettrages par code
      const codesLettrage = new Set();
      lignesLettrees.lignes.forEach(ligne => {
        if (ligne.lettrage) {
          codesLettrage.add(ligne.lettrage);
        }
      });

      res.json({
        success: true,
        message: 'Statistiques calculées avec succès',
        data: {
          compteNumero,
          periode,
          statistiques: {
            totalLignes,
            lignesLettrees: lignesLettreesCount,
            lignesNonLettrees: lignesNonLettreesCount,
            nombreLettrages: codesLettrage.size,
            pourcentageLettrage: totalLignes > 0 ? (lignesLettreesCount / totalLignes) * 100 : 0
          }
        }
      });

    } catch (error) {
      logger.error('Erreur lors du calcul des statistiques via API', {
        error: error.message,
        compteNumero: req.params.compteNumero,
        periode: req.query
      });
      next(error);
    }
  }
}

module.exports = new LettrageController();
