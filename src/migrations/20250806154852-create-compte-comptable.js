'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('compte_comptables', {
      numero: {
        type: Sequelize.STRING(10),
        primaryKey: true,
        allowNull: false
      },
      libelle: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      classe: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      nature: {
        type: Sequelize.ENUM('ACTIF', 'PASSIF', 'CHARGE', 'PRODUIT'),
        allowNull: false
      },
      sens: {
        type: Sequelize.ENUM('DEBIT', 'CREDIT'),
        allowNull: false
      },
      niveau: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      compte_parent: {
        type: Sequelize.STRING(10),
        allowNull: true,
        references: {
          model: 'compte_comptables',
          key: 'numero'
        }
      },
      societe_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'societes',
          key: 'id'
        }
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Index pour améliorer les performances
    await queryInterface.addIndex('compte_comptables', ['classe']);
    await queryInterface.addIndex('compte_comptables', ['nature']);
    await queryInterface.addIndex('compte_comptables', ['societe_id']);
    await queryInterface.addIndex('compte_comptables', ['compte_parent']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('compte_comptables');
  }
};