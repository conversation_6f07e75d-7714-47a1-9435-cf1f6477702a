'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Controller pour la gestion des amortissements
 * Conforme aux normes SYSCOHADA
 */
class DepreciationController {
  constructor() {
    this.models = null;
  }

  /**
   * Initialise le controller avec les modèles
   * @param {Object} models - Modèles Sequelize
   */
  init(models) {
    this.models = models;
  }

  /**
   * Liste les amortissements d'une société
   * GET /api/v1/depreciations
   */
  async listerAmortissements(req, res, next) {
    try {
      const {
        societeId,
        categorie,
        statut,
        page = 1,
        limit = 50,
        search
      } = req.query;

      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'ID société obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      // Construction des filtres
      const where = { societeId };
      if (categorie) where.categorie = categorie;
      if (statut) where.statut = statut;
      
      if (search) {
        where[this.models.Sequelize.Op.or] = [
          { code: { [this.models.Sequelize.Op.iLike]: `%${search}%` } },
          { libelle: { [this.models.Sequelize.Op.iLike]: `%${search}%` } }
        ];
      }

      // Pagination
      const offset = (page - 1) * limit;

      const { count, rows: amortissements } = await this.models.Depreciation.findAndCountAll({
        where,
        include: [
          {
            model: this.models.CompteComptable,
            as: 'compteImmobilisationDetail',
            attributes: ['numero', 'intitule']
          },
          {
            model: this.models.CompteComptable,
            as: 'compteAmortissementDetail',
            attributes: ['numero', 'intitule']
          }
        ],
        limit: parseInt(limit),
        offset,
        order: [['code', 'ASC']]
      });

      // Calcul des statistiques
      const statistiques = await this.calculerStatistiques(societeId);

      logger.info('Liste amortissements récupérée', {
        societeId,
        nombreAmortissements: count,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Amortissements récupérés avec succès',
        data: {
          amortissements,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            pages: Math.ceil(count / limit)
          },
          statistiques
        }
      });

    } catch (error) {
      logger.error('Erreur récupération amortissements', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Récupère un amortissement par ID
   * GET /api/v1/depreciations/:id
   */
  async obtenirAmortissement(req, res, next) {
    try {
      const { id } = req.params;

      const amortissement = await this.models.Depreciation.findByPk(id, {
        include: [
          {
            model: this.models.Societe,
            as: 'societe',
            attributes: ['id', 'nom']
          },
          {
            model: this.models.CompteComptable,
            as: 'compteImmobilisationDetail',
            attributes: ['numero', 'intitule']
          },
          {
            model: this.models.CompteComptable,
            as: 'compteAmortissementDetail',
            attributes: ['numero', 'intitule']
          },
          {
            model: this.models.CompteComptable,
            as: 'compteDotationDetail',
            attributes: ['numero', 'intitule']
          },
          {
            model: this.models.DepreciationPlan,
            as: 'planAmortissement',
            order: [['exercice', 'ASC']]
          }
        ]
      });

      if (!amortissement) {
        throw new NotFoundError('Amortissement inexistant');
      }

      // Calcul des informations complémentaires
      const informationsComplementaires = {
        pourcentageAmortissement: amortissement.getPourcentageAmortissement(),
        estTotalementAmorti: amortissement.estTotalementAmorti(),
        restantAAmortir: amortissement.valeurAmortissable - amortissement.cumulAmortissements,
        dureeRestante: this.calculerDureeRestante(amortissement),
        prochaineDotation: await this.calculerProchaineDotation(amortissement)
      };

      logger.info('Amortissement récupéré', {
        amortissementId: id,
        code: amortissement.code,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Amortissement récupéré avec succès',
        data: {
          amortissement,
          informationsComplementaires
        }
      });

    } catch (error) {
      logger.error('Erreur récupération amortissement', {
        error: error.message,
        amortissementId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Crée un nouvel amortissement
   * POST /api/v1/depreciations
   */
  async creerAmortissement(req, res, next) {
    const transaction = await this.models.sequelize.transaction();

    try {
      const donneesAmortissement = req.body;

      // Validation de la société
      const societe = await this.models.Societe.findByPk(donneesAmortissement.societeId);
      if (!societe) {
        throw new NotFoundError('Société inexistante');
      }

      // Validation de l'unicité du code
      const amortissementExistant = await this.models.Depreciation.findOne({
        where: {
          code: donneesAmortissement.code,
          societeId: donneesAmortissement.societeId
        }
      });

      if (amortissementExistant) {
        throw new ValidationError('Un amortissement avec ce code existe déjà');
      }

      // Validation des comptes comptables
      await this.validerComptesComptables(donneesAmortissement);

      // Création de l'amortissement
      const amortissement = await this.models.Depreciation.create(donneesAmortissement, {
        transaction
      });

      // Génération du plan d'amortissement
      await this.genererPlanAmortissement(amortissement, transaction);

      await transaction.commit();

      // Récupération complète pour la réponse
      const amortissementComplet = await this.models.Depreciation.findByPk(amortissement.id, {
        include: [
          {
            model: this.models.CompteComptable,
            as: 'compteImmobilisationDetail',
            attributes: ['numero', 'intitule']
          },
          {
            model: this.models.DepreciationPlan,
            as: 'planAmortissement',
            order: [['exercice', 'ASC']]
          }
        ]
      });

      logger.info('Amortissement créé', {
        amortissementId: amortissement.id,
        code: amortissement.code,
        societeId: donneesAmortissement.societeId,
        utilisateur: req.user?.id
      });

      res.status(201).json({
        success: true,
        message: 'Amortissement créé avec succès',
        data: amortissementComplet
      });

    } catch (error) {
      await transaction.rollback();
      logger.error('Erreur création amortissement', {
        error: error.message,
        donnees: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Met à jour un amortissement
   * PUT /api/v1/depreciations/:id
   */
  async modifierAmortissement(req, res, next) {
    const transaction = await this.models.sequelize.transaction();

    try {
      const { id } = req.params;
      const donneesModification = req.body;

      const amortissement = await this.models.Depreciation.findByPk(id);
      if (!amortissement) {
        throw new NotFoundError('Amortissement inexistant');
      }

      // Vérification des contraintes de modification
      if (amortissement.cumulAmortissements > 0 && 
          (donneesModification.valeurAcquisition || donneesModification.dureeAmortissement)) {
        throw new ValidationError('Impossible de modifier la valeur ou la durée d\'un amortissement déjà commencé');
      }

      // Validation des comptes si modifiés
      if (donneesModification.compteImmobilisation || 
          donneesModification.compteAmortissement || 
          donneesModification.compteDotation) {
        await this.validerComptesComptables(donneesModification);
      }

      // Mise à jour
      await amortissement.update(donneesModification, { transaction });

      // Régénération du plan si nécessaire
      if (donneesModification.valeurAcquisition || 
          donneesModification.dureeAmortissement || 
          donneesModification.methodeAmortissement) {
        await this.regenererPlanAmortissement(amortissement, transaction);
      }

      await transaction.commit();

      // Récupération complète pour la réponse
      const amortissementModifie = await this.models.Depreciation.findByPk(id, {
        include: [
          {
            model: this.models.CompteComptable,
            as: 'compteImmobilisationDetail',
            attributes: ['numero', 'intitule']
          },
          {
            model: this.models.DepreciationPlan,
            as: 'planAmortissement',
            order: [['exercice', 'ASC']]
          }
        ]
      });

      logger.info('Amortissement modifié', {
        amortissementId: id,
        modifications: Object.keys(donneesModification),
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Amortissement modifié avec succès',
        data: amortissementModifie
      });

    } catch (error) {
      await transaction.rollback();
      logger.error('Erreur modification amortissement', {
        error: error.message,
        amortissementId: req.params.id,
        donnees: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Supprime un amortissement
   * DELETE /api/v1/depreciations/:id
   */
  async supprimerAmortissement(req, res, next) {
    const transaction = await this.models.sequelize.transaction();

    try {
      const { id } = req.params;

      const amortissement = await this.models.Depreciation.findByPk(id);
      if (!amortissement) {
        throw new NotFoundError('Amortissement inexistant');
      }

      // Vérification des contraintes de suppression
      if (amortissement.cumulAmortissements > 0) {
        throw new ValidationError('Impossible de supprimer un amortissement avec des dotations réalisées');
      }

      // Vérification des écritures liées
      const ecrituresLiees = await this.models.EcritureComptable.count({
        where: { depreciationId: id }
      });

      if (ecrituresLiees > 0) {
        throw new ValidationError('Impossible de supprimer un amortissement avec des écritures comptables liées');
      }

      // Suppression du plan d'amortissement
      await this.models.DepreciationPlan.destroy({
        where: { depreciationId: id },
        transaction
      });

      // Suppression de l'amortissement
      await amortissement.destroy({ transaction });

      await transaction.commit();

      logger.info('Amortissement supprimé', {
        amortissementId: id,
        code: amortissement.code,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Amortissement supprimé avec succès'
      });

    } catch (error) {
      await transaction.rollback();
      logger.error('Erreur suppression amortissement', {
        error: error.message,
        amortissementId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Calcule les dotations d'un exercice
   * POST /api/v1/depreciations/calculate-dotations
   */
  async calculerDotations(req, res, next) {
    try {
      const { societeId, exercice } = req.body;

      if (!societeId || !exercice) {
        return res.status(400).json({
          success: false,
          message: 'Société et exercice obligatoires',
          code: 'PARAMETRES_MANQUANTS'
        });
      }

      // Récupération des amortissements en service
      const amortissements = await this.models.Depreciation.findAll({
        where: {
          societeId,
          statut: 'EN_SERVICE',
          amortissementTermine: false
        },
        include: [{
          model: this.models.DepreciationPlan,
          as: 'planAmortissement',
          where: { exercice },
          required: false
        }]
      });

      const resultats = [];

      for (const amortissement of amortissements) {
        const dotation = amortissement.calculerDotationAnnuelle(new Date(exercice, 11, 31));
        
        resultats.push({
          amortissementId: amortissement.id,
          code: amortissement.code,
          libelle: amortissement.libelle,
          dotationCalculee: dotation,
          planExistant: amortissement.planAmortissement.length > 0
        });
      }

      const totalDotations = resultats.reduce((sum, r) => sum + r.dotationCalculee, 0);

      logger.info('Dotations calculées', {
        societeId,
        exercice,
        nombreAmortissements: resultats.length,
        totalDotations,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Dotations calculées avec succès',
        data: {
          exercice,
          amortissements: resultats,
          totalDotations,
          nombreAmortissements: resultats.length
        }
      });

    } catch (error) {
      logger.error('Erreur calcul dotations', {
        error: error.message,
        donnees: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère les écritures d'amortissement
   * POST /api/v1/depreciations/generate-entries
   */
  async genererEcritures(req, res, next) {
    const transaction = await this.models.sequelize.transaction();

    try {
      const { societeId, exercice, amortissementIds = [] } = req.body;

      if (!societeId || !exercice) {
        return res.status(400).json({
          success: false,
          message: 'Société et exercice obligatoires',
          code: 'PARAMETRES_MANQUANTS'
        });
      }

      // Récupération des plans à traiter
      const where = { exercice, statut: 'PREVISIONNEL', ecritureGeneree: false };
      
      const plansAmortissement = await this.models.DepreciationPlan.findAll({
        where,
        include: [{
          model: this.models.Depreciation,
          as: 'depreciation',
          where: {
            societeId,
            ...(amortissementIds.length > 0 && { id: amortissementIds })
          }
        }]
      });

      if (plansAmortissement.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Aucun plan d\'amortissement à traiter',
          code: 'AUCUN_PLAN_TROUVE'
        });
      }

      const ecrituresGenerees = [];
      let totalDotations = 0;

      for (const plan of plansAmortissement) {
        try {
          const ecriture = await plan.genererEcriture(this.models);
          ecrituresGenerees.push({
            planId: plan.id,
            ecritureId: ecriture.id,
            amortissement: plan.depreciation.code,
            dotation: plan.dotationPeriode
          });
          totalDotations += parseFloat(plan.dotationPeriode);
        } catch (error) {
          logger.warn('Erreur génération écriture plan', {
            planId: plan.id,
            error: error.message
          });
        }
      }

      await transaction.commit();

      logger.info('Écritures amortissement générées', {
        societeId,
        exercice,
        nombreEcritures: ecrituresGenerees.length,
        totalDotations,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Écritures d\'amortissement générées avec succès',
        data: {
          exercice,
          ecrituresGenerees,
          nombreEcritures: ecrituresGenerees.length,
          totalDotations
        }
      });

    } catch (error) {
      await transaction.rollback();
      logger.error('Erreur génération écritures amortissement', {
        error: error.message,
        donnees: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Récupère le plan d'amortissement
   * GET /api/v1/depreciations/:id/plan
   */
  async obtenirPlanAmortissement(req, res, next) {
    try {
      const { id } = req.params;

      const amortissement = await this.models.Depreciation.findByPk(id, {
        include: [{
          model: this.models.DepreciationPlan,
          as: 'planAmortissement',
          order: [['exercice', 'ASC']]
        }]
      });

      if (!amortissement) {
        throw new NotFoundError('Amortissement inexistant');
      }

      // Calcul des totaux
      const totaux = {
        totalDotations: 0,
        totalCumul: 0,
        nombreAnnees: amortissement.planAmortissement.length
      };

      amortissement.planAmortissement.forEach(plan => {
        totaux.totalDotations += parseFloat(plan.dotationPeriode);
        totaux.totalCumul = parseFloat(plan.cumulFinPeriode);
      });

      logger.info('Plan amortissement récupéré', {
        amortissementId: id,
        nombreAnnees: totaux.nombreAnnees,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Plan d\'amortissement récupéré avec succès',
        data: {
          amortissement: {
            id: amortissement.id,
            code: amortissement.code,
            libelle: amortissement.libelle,
            valeurAcquisition: amortissement.valeurAcquisition,
            valeurAmortissable: amortissement.valeurAmortissable,
            dureeAmortissement: amortissement.dureeAmortissement,
            methodeAmortissement: amortissement.methodeAmortissement
          },
          plan: amortissement.planAmortissement,
          totaux
        }
      });

    } catch (error) {
      logger.error('Erreur récupération plan amortissement', {
        error: error.message,
        amortissementId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  // === MÉTHODES UTILITAIRES ===

  /**
   * Valide les comptes comptables
   */
  async validerComptesComptables(donnees) {
    const { compteImmobilisation, compteAmortissement, compteDotation, societeId } = donnees;

    // Validation compte immobilisation
    if (compteImmobilisation) {
      const compte = await this.models.CompteComptable.findOne({
        where: { numero: compteImmobilisation, societeId }
      });
      if (!compte) {
        throw new ValidationError('Compte d\'immobilisation inexistant');
      }
      if (!compte.numero.startsWith('2')) {
        throw new ValidationError('Le compte d\'immobilisation doit appartenir à la classe 2');
      }
    }

    // Validation compte amortissement
    if (compteAmortissement) {
      const compte = await this.models.CompteComptable.findOne({
        where: { numero: compteAmortissement, societeId }
      });
      if (!compte) {
        throw new ValidationError('Compte d\'amortissement inexistant');
      }
      if (!compte.numero.startsWith('28')) {
        throw new ValidationError('Le compte d\'amortissement doit appartenir à la classe 28');
      }
    }

    // Validation compte dotation
    if (compteDotation) {
      const compte = await this.models.CompteComptable.findOne({
        where: { numero: compteDotation, societeId }
      });
      if (!compte) {
        throw new ValidationError('Compte de dotation inexistant');
      }
      if (!compte.numero.startsWith('681')) {
        throw new ValidationError('Le compte de dotation doit appartenir au compte 681');
      }
    }
  }

  /**
   * Génère le plan d'amortissement
   */
  async genererPlanAmortissement(amortissement, transaction) {
    const plans = [];
    const dateDebut = new Date(amortissement.dateMiseEnService);
    let cumulAmortissement = 0;

    for (let annee = 0; annee < amortissement.dureeAmortissement; annee++) {
      const exercice = dateDebut.getFullYear() + annee;
      const dateDebutPeriode = new Date(exercice, 0, 1);
      const dateFinPeriode = new Date(exercice, 11, 31);

      // Ajustement pour la première année
      if (annee === 0) {
        dateDebutPeriode.setTime(dateDebut.getTime());
      }

      const dotation = amortissement.calculerDotationAnnuelle(dateFinPeriode);
      cumulAmortissement += dotation;

      plans.push({
        depreciationId: amortissement.id,
        exercice,
        dateDebut: dateDebutPeriode,
        dateFin: dateFinPeriode,
        valeurDebutPeriode: amortissement.valeurAcquisition - (cumulAmortissement - dotation),
        cumulDebutPeriode: cumulAmortissement - dotation,
        dotationPeriode: dotation,
        dotationTheorique: amortissement.valeurAmortissable * (amortissement.tauxAmortissement / 100),
        cumulFinPeriode: cumulAmortissement,
        valeurFinPeriode: amortissement.valeurAcquisition - cumulAmortissement,
        tauxPeriode: amortissement.tauxAmortissement,
        baseCalcul: amortissement.valeurAmortissable,
        statut: 'PREVISIONNEL'
      });
    }

    await this.models.DepreciationPlan.bulkCreate(plans, { transaction });
  }

  /**
   * Régénère le plan d'amortissement
   */
  async regenererPlanAmortissement(amortissement, transaction) {
    // Suppression de l'ancien plan (seulement les lignes prévisionnelles)
    await this.models.DepreciationPlan.destroy({
      where: {
        depreciationId: amortissement.id,
        statut: 'PREVISIONNEL'
      },
      transaction
    });

    // Génération du nouveau plan
    await this.genererPlanAmortissement(amortissement, transaction);
  }

  /**
   * Calcule les statistiques des amortissements
   */
  async calculerStatistiques(societeId) {
    const stats = await this.models.Depreciation.findAll({
      where: { societeId },
      attributes: [
        'statut',
        [this.models.sequelize.fn('COUNT', this.models.sequelize.col('id')), 'nombre'],
        [this.models.sequelize.fn('SUM', this.models.sequelize.col('valeurAcquisition')), 'valeurTotale'],
        [this.models.sequelize.fn('SUM', this.models.sequelize.col('cumulAmortissements')), 'cumulTotal']
      ],
      group: ['statut'],
      raw: true
    });

    const statistiques = {
      parStatut: {},
      totaux: {
        nombre: 0,
        valeurTotale: 0,
        cumulTotal: 0,
        valeurNette: 0
      }
    };

    stats.forEach(stat => {
      statistiques.parStatut[stat.statut] = {
        nombre: parseInt(stat.nombre),
        valeurTotale: parseFloat(stat.valeurTotale || 0),
        cumulTotal: parseFloat(stat.cumulTotal || 0)
      };

      statistiques.totaux.nombre += parseInt(stat.nombre);
      statistiques.totaux.valeurTotale += parseFloat(stat.valeurTotale || 0);
      statistiques.totaux.cumulTotal += parseFloat(stat.cumulTotal || 0);
    });

    statistiques.totaux.valeurNette = statistiques.totaux.valeurTotale - statistiques.totaux.cumulTotal;

    return statistiques;
  }

  /**
   * Calcule la durée restante d'amortissement
   */
  calculerDureeRestante(amortissement) {
    if (amortissement.estTotalementAmorti()) {
      return 0;
    }

    const maintenant = new Date();
    const dateFin = new Date(amortissement.dateFinAmortissement);
    
    if (dateFin <= maintenant) {
      return 0;
    }

    const diffTime = dateFin.getTime() - maintenant.getTime();
    const diffYears = diffTime / (1000 * 60 * 60 * 24 * 365.25);
    
    return Math.max(0, Math.ceil(diffYears));
  }

  /**
   * Calcule la prochaine dotation
   */
  async calculerProchaineDotation(amortissement) {
    if (amortissement.estTotalementAmorti()) {
      return 0;
    }

    const prochaineAnnee = new Date().getFullYear() + 1;
    return amortissement.calculerDotationAnnuelle(new Date(prochaineAnnee, 11, 31));
  }
}

module.exports = new DepreciationController();