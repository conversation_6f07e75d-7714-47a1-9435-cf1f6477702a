/**
 * Point d'entrée principal de l'API Comptabilité SYSCOHADA
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

const app = require('./src/app');
const { sequelize } = require('./src/config/database');
const { config } = require('./src/config/env');
const { logger } = require('./src/config/logger');

const PORT = config.PORT;

/**
 * Démarre le serveur après avoir testé la connexion à la base de données
 */
async function startServer() {
  try {
    // Test de la connexion à la base de données
    await sequelize.authenticate();
    logger.info('Connexion à la base de données établie avec succès');

    // Synchronisation des modèles (en développement uniquement)
    if (config.NODE_ENV === 'development') {
      await sequelize.sync({ alter: false });
      logger.info('Modèles synchronisés avec la base de données');
    }

    // Démarrage du serveur
    const server = app.listen(PORT, () => {
      logger.info(`Serveur API Comptabilité SYSCOHADA démarré`, {
        port: PORT,
        environment: config.NODE_ENV,
        url: `http://localhost:${PORT}`,
        documentation: `http://localhost:${PORT}/api-docs`
      });

      console.log(`🚀 Serveur démarré sur le port ${PORT}`);
      console.log(`📊 Environnement: ${config.NODE_ENV}`);
      console.log(`🔗 URL: http://localhost:${PORT}`);
    });

    // Gestion propre de l'arrêt du serveur
    const gracefulShutdown = (signal) => {
      logger.info(`Signal ${signal} reçu. Arrêt du serveur...`);

      server.close(async () => {
        logger.info('Serveur HTTP fermé');

        try {
          await sequelize.close();
          logger.info('Connexion base de données fermée');
          process.exit(0);
        } catch (error) {
          logger.error('Erreur lors de la fermeture de la base de données', error);
          process.exit(1);
        }
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Impossible de démarrer le serveur', error);
    console.error('❌ Impossible de démarrer le serveur:', error.message);
    process.exit(1);
  }
}

// Démarrage du serveur
startServer();
