# Guide d'Intégration Django - Architecture Mono-Société

## Configuration Django

### 1. Variables d'environnement (.env)

```bash
# Configuration API Comptabilité
COMPTABILITE_API_URL=http://localhost:3000/api/v1
COMPTABILITE_API_KEY=sk_your_mono_societe_key
COMPTABILITE_SOCIETE_ID=uuid-de-votre-societe
COMPTABILITE_SOCIETE_NOM="Ma Société SARL"

# Optionnel : Configuration cache
COMPTABILITE_CACHE_TIMEOUT=300  # 5 minutes
```

### 2. Settings Django (settings/base.py)

```python
import os
from decouple import config

# Configuration API Comptabilité
COMPTABILITE_API_URL = config('COMPTABILITE_API_URL', default='http://localhost:3000/api/v1')
COMPTABILITE_API_KEY = config('COMPTABILITE_API_KEY')
COMPTABILITE_SOCIETE_ID = config('COMPTABILITE_SOCIETE_ID')
COMPTABILITE_SOCIETE_NOM = config('COMPTABILITE_SOCIETE_NOM', default='Ma Société')

# Cache pour les données comptables
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    },
    'comptabilite': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/2',
        'TIMEOUT': config('COMPTABILITE_CACHE_TIMEOUT', default=300, cast=int),
    }
}

# Logging pour l'API comptabilité
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'comptabilite_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/comptabilite.log',
        },
    },
    'loggers': {
        'apps.accounting': {
            'handlers': ['comptabilite_file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### 3. Structure de l'app accounting

```
apps/accounting/
├── __init__.py
├── apps.py
├── urls.py
├── views/
│   ├── __init__.py
│   ├── dashboard.py
│   ├── plan_comptable.py
│   ├── ecritures.py
│   ├── journaux.py
│   ├── etats.py
│   └── lettrage.py
├── services/
│   ├── __init__.py
│   ├── api_client.py      # Client API principal
│   ├── cache_service.py   # Gestion du cache
│   ├── helpers.py         # Fonctions utilitaires
│   └── validators.py      # Validations métier
├── forms/
│   ├── __init__.py
│   ├── ecriture_forms.py
│   ├── compte_forms.py
│   └── journal_forms.py
├── templates/accounting/
│   ├── base.html
│   ├── dashboard.html
│   ├── plan_comptable.html
│   ├── ecritures/
│   ├── etats/
│   └── components/
├── static/accounting/
│   ├── css/
│   ├── js/
│   └── img/
└── templatetags/
    ├── __init__.py
    └── accounting_tags.py
```

## Exemples d'Implémentation

### 1. Vue Dashboard

```python
# apps/accounting/views/dashboard.py
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.cache import caches
from ..services.api_client import api_client
from ..services.helpers import handle_api_response
import logging

logger = logging.getLogger(__name__)
cache = caches['comptabilite']

@login_required
def dashboard(request):
    """Dashboard comptable avec KPIs"""
    
    # Essayer de récupérer depuis le cache
    cache_key = 'dashboard_kpis'
    kpis = cache.get(cache_key)
    
    if not kpis:
        # Récupérer les KPIs depuis l'API
        from datetime import datetime, date
        date_debut = date(datetime.now().year, 1, 1).isoformat()
        date_fin = date.today().isoformat()
        
        response = api_client.get_kpi_financiers(date_debut, date_fin)
        kpis = handle_api_response(response, request)
        
        if kpis:
            cache.set(cache_key, kpis, timeout=300)  # Cache 5 minutes
    
    # Récupérer les alertes
    alertes_response = api_client.get_alertes()
    alertes = handle_api_response(alertes_response, request) or []
    
    # Récupérer la config société
    config_response = api_client.get_config()
    config = handle_api_response(config_response, request) or {}
    
    context = {
        'kpis': kpis or {},
        'alertes': alertes,
        'societe': config.get('societe', {}),
        'page_title': 'Dashboard Comptable'
    }
    
    return render(request, 'accounting/dashboard.html', context)
```

### 2. Vue Plan Comptable

```python
# apps/accounting/views/plan_comptable.py
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.paginator import Paginator
from ..services.api_client import api_client
from ..forms.compte_forms import CompteForm

@login_required
def plan_comptable(request):
    """Affichage hiérarchique du plan comptable"""
    
    # Récupérer la hiérarchie des comptes
    response = api_client.get_hierarchie_comptes()
    
    if response.success:
        comptes = response.data
    else:
        messages.error(request, "Erreur lors du chargement du plan comptable")
        comptes = []
    
    context = {
        'comptes': comptes,
        'page_title': 'Plan Comptable SYSCOHADA'
    }
    
    return render(request, 'accounting/plan_comptable.html', context)

@login_required
def compte_create(request):
    """Création d'un nouveau compte"""
    
    if request.method == 'POST':
        form = CompteForm(request.POST)
        if form.is_valid():
            response = api_client.create_compte(form.cleaned_data)
            
            if response.success:
                messages.success(request, "Compte créé avec succès")
                return redirect('accounting:plan_comptable')
            else:
                error_msg = response.error.get('message', 'Erreur lors de la création')
                messages.error(request, error_msg)
    else:
        form = CompteForm()
    
    context = {
        'form': form,
        'page_title': 'Nouveau Compte'
    }
    
    return render(request, 'accounting/compte_form.html', context)

@login_required
def comptes_by_classe(request, classe):
    """API endpoint pour récupérer les comptes par classe (AJAX)"""
    
    response = api_client.get_comptes_by_classe(classe)
    
    if response.success:
        return JsonResponse({
            'success': True,
            'comptes': response.data
        })
    else:
        return JsonResponse({
            'success': False,
            'error': response.error.get('message', 'Erreur')
        })
```

### 3. Formulaire Écriture

```python
# apps/accounting/forms/ecriture_forms.py
from django import forms
from django.core.exceptions import ValidationError
from ..services.validators import validate_equilibrage, validate_syscohada_account

class LigneEcritureForm(forms.Form):
    """Formulaire pour une ligne d'écriture"""
    
    compte_numero = forms.CharField(
        max_length=10,
        widget=forms.TextInput(attrs={
            'class': 'form-control compte-autocomplete',
            'placeholder': 'Ex: 601000'
        })
    )
    
    libelle = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Libellé de la ligne'
        })
    )
    
    debit = forms.DecimalField(
        max_digits=15,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control montant-input',
            'step': '0.01',
            'min': '0'
        })
    )
    
    credit = forms.DecimalField(
        max_digits=15,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control montant-input',
            'step': '0.01',
            'min': '0'
        })
    )
    
    def clean_compte_numero(self):
        numero = self.cleaned_data['compte_numero']
        if not validate_syscohada_account(numero):
            raise ValidationError("Numéro de compte SYSCOHADA invalide")
        return numero
    
    def clean(self):
        cleaned_data = super().clean()
        debit = cleaned_data.get('debit') or 0
        credit = cleaned_data.get('credit') or 0
        
        # Vérifier qu'on a soit débit soit crédit, pas les deux
        if debit > 0 and credit > 0:
            raise ValidationError("Une ligne ne peut pas avoir à la fois un débit et un crédit")
        
        # Vérifier qu'on a au moins un montant
        if debit == 0 and credit == 0:
            raise ValidationError("Une ligne doit avoir soit un débit soit un crédit")
        
        return cleaned_data

class EcritureForm(forms.Form):
    """Formulaire principal pour une écriture comptable"""
    
    date_ecriture = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    libelle = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Libellé de l\'écriture'
        })
    )
    
    journal_code = forms.CharField(
        max_length=2,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    reference = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Référence (optionnel)'
        })
    )
    
    piece_justificative = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Pièce justificative (optionnel)'
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Charger les journaux depuis l'API
        from ..services.api_client import api_client
        response = api_client.get_journaux()
        
        if response.success:
            choices = [(j['code'], f"{j['code']} - {j['libelle']}") 
                      for j in response.data]
            self.fields['journal_code'].widget.choices = choices
```

### 4. Template Dashboard

```html
<!-- apps/accounting/templates/accounting/dashboard.html -->
{% extends 'accounting/base.html' %}
{% load accounting_tags %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête avec infos société -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">{{ societe.nom }}</h4>
                    <p class="text-muted">
                        Devise: {{ societe.devise }} | 
                        Régime: {{ societe.regimeFiscal|default:"Non défini" }}
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- KPIs Financiers -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-primary">Chiffre d'Affaires</h5>
                    <h3 class="text-primary">{{ kpis.chiffreAffaires|format_currency }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">Résultat Net</h5>
                    <h3 class="text-success">{{ kpis.resultatNet|format_currency }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-info">Marge Commerciale</h5>
                    <h3 class="text-info">{{ kpis.margeCommerciale|floatformat:1 }}%</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">Ratio Liquidité</h5>
                    <h3 class="text-warning">{{ kpis.ratioLiquidite|floatformat:2 }}</h3>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Alertes -->
    {% if alertes %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Alertes Financières
                    </h5>
                </div>
                <div class="card-body">
                    {% for alerte in alertes %}
                    <div class="alert alert-{% if alerte.niveau == 'CRITICAL' %}danger{% elif alerte.niveau == 'WARNING' %}warning{% else %}info{% endif %}" role="alert">
                        <strong>{{ alerte.type }}:</strong> {{ alerte.message }}
                        {% if alerte.valeur %}
                        <br><small>Valeur: {{ alerte.valeur|format_currency }}</small>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Actions rapides -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Actions Rapides</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'accounting:ecriture_create' %}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>Nouvelle Écriture
                        </a>
                        <a href="{% url 'accounting:balance' %}" class="btn btn-outline-primary">
                            <i class="bi bi-table me-2"></i>Balance Générale
                        </a>
                        <a href="{% url 'accounting:lettrage' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-link-45deg me-2"></i>Lettrage
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">États Comptables</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'accounting:bilan' %}" class="btn btn-outline-success">
                            <i class="bi bi-graph-up me-2"></i>Bilan Comptable
                        </a>
                        <a href="{% url 'accounting:compte_resultat' %}" class="btn btn-outline-info">
                            <i class="bi bi-bar-chart me-2"></i>Compte de Résultat
                        </a>
                        <a href="{% url 'accounting:grand_livre' %}" class="btn btn-outline-warning">
                            <i class="bi bi-book me-2"></i>Grand Livre
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Actualisation automatique des KPIs toutes les 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
```

Cette intégration vous donne une base solide pour votre frontend Django avec l'architecture mono-société optimisée !
