# 🖥️ **CLI SYSCOHADA - Guide d'utilisation complet**

## 📋 **Vue d'ensemble**

Le CLI SYSCOHADA est un outil en ligne de commande puissant qui permet d'interagir avec l'API de comptabilité SYSCOHADA. Il offre une interface intuitive pour gérer tous les aspects de votre comptabilité directement depuis le terminal.

## Installation

### Prérequis

- Node.js (v18 ou supérieur)
- npm (v8 ou supérieur)

### Installation locale

```bash
# Installer les dépendances
npm install --save-dev

# Installer l'outil globalement
npm install -g .
```

### Installation depuis le fichier cli-package.json

```bash
# Créer un nouveau dossier pour l'outil CLI
mkdir syscohada-cli
cd syscohada-cli

# Copier le fichier cli.js et cli-package.json
cp /chemin/vers/api-compta-generale/cli.js .
cp /chemin/vers/api-compta-generale/cli-package.json ./package.json

# Installer les dépendances
npm install

# Installer l'outil globalement
npm install -g .
```

## Configuration

Avant d'utiliser l'outil, vous devez configurer l'URL de l'API:

```bash
syscohada config
```

Par défaut, l'URL est `http://localhost:3000/api/v1`.

## Authentification par Clé API

L'API utilise maintenant un système de clés API au lieu des tokens JWT. Pour utiliser la plupart des commandes, vous devez configurer une clé API:

```bash
syscohada auth:setup
```

Vous serez invité à saisir votre clé API. La clé doit commencer par `sk_` et vous devez l'obtenir d'un administrateur ou la générer si vous avez les permissions admin.

## Commandes disponibles

### Général

- `syscohada --help` - Afficher l'aide
- `syscohada config` - Configurer l'URL de l'API
- `syscohada status` - Vérifier le statut de l'API

### Authentification

- `syscohada auth:setup` - Configurer la clé API
- `syscohada auth:verify` - Vérifier la validité de la clé API
- `syscohada auth:clear` - Supprimer la clé API configurée

### Gestion des Clés API (Admin uniquement)

- `syscohada apikeys:list [options]` - Lister toutes les clés API
  - `-i, --include-inactive` - Inclure les clés inactives
  - `-l, --limit <number>` - Nombre de clés à afficher
- `syscohada apikeys:create` - Créer une nouvelle clé API
- `syscohada apikeys:deactivate <prefix>` - Désactiver une clé API par son préfixe
- `syscohada apikeys:delete <prefix>` - Supprimer définitivement une clé API

### Sociétés

- `syscohada societes:list` - Lister toutes les sociétés
- `syscohada societes:create` - Créer une nouvelle société
- `syscohada societes:select` - Sélectionner une société comme contexte courant

### Exercices comptables

- `syscohada exercices:list` - Lister les exercices comptables
- `syscohada exercices:create` - Créer un nouvel exercice comptable

### Écritures comptables

- `syscohada ecritures:list [options]` - Lister les écritures comptables
  - `-j, --journal <code>` - Filtrer par code journal
  - `-d, --debut <date>` - Date de début (YYYY-MM-DD)
  - `-f, --fin <date>` - Date de fin (YYYY-MM-DD)
  - `-s, --statut <statut>` - Filtrer par statut (BROUILLARD, VALIDEE, CLOTUREE)
  - `-p, --page <number>` - Numéro de page
  - `-l, --limit <number>` - Nombre d'éléments par page
- `syscohada ecritures:create` - Créer une nouvelle écriture comptable
- `syscohada ecritures:show <id>` - Afficher les détails d'une écriture
- `syscohada ecritures:valider <id>` - Valider une écriture (passage de BROUILLARD à VALIDEE)

### Plan comptable

- `syscohada comptes:list [options]` - Lister les comptes du plan comptable
  - `-c, --classe <classe>` - Filtrer par classe (1-9)
  - `-p, --page <number>` - Numéro de page
  - `-l, --limit <number>` - Nombre d'éléments par page

### États financiers

- `syscohada etats:generer` - Générer un état financier (Grand Livre, Balance, Journal, Bilan, Compte de Résultat)

### Analyses financières

- `syscohada analyses:generer` - Générer une analyse financière (liquidité, rentabilité, endettement, complète)

### Tableau de bord

- `syscohada dashboard` - Afficher le tableau de bord financier

## Exemples d'utilisation

### Workflow de base

```bash
# Configurer l'API
syscohada config

# Configurer la clé API
syscohada auth:setup

# Vérifier la clé API
syscohada auth:verify

# Lister les sociétés
syscohada societes:list

# Sélectionner une société
syscohada societes:select

# Créer un exercice comptable
syscohada exercices:create

# Créer une écriture comptable
syscohada ecritures:create

# Lister les écritures
syscohada ecritures:list

# Valider une écriture
syscohada ecritures:valider <id>

# Générer un état financier
syscohada etats:generer

# Afficher le tableau de bord
syscohada dashboard
```

### Workflow pour les administrateurs

```bash
# Lister les clés API existantes
syscohada apikeys:list

# Créer une nouvelle clé API
syscohada apikeys:create

# Désactiver une clé API
syscohada apikeys:deactivate sk_abc123

# Supprimer une clé API
syscohada apikeys:delete sk_abc123
```

## Dépannage

### L'outil ne se connecte pas à l'API

Vérifiez que l'API est en cours d'exécution et que l'URL configurée est correcte:

```bash
syscohada status
```

Si l'API n'est pas accessible, vérifiez que le serveur est en cours d'exécution:

```bash
cd /chemin/vers/api-compta-generale
npm run dev
```

### Erreur "Vous devez configurer une clé API"

Si vous voyez cette erreur, vous devez configurer une clé API:

```bash
syscohada auth:setup
```

### Erreur "Clé API invalide"

Si votre clé API est invalide ou expirée:

1. Vérifiez que la clé est correcte et n'a pas expiré
2. Contactez un administrateur pour obtenir une nouvelle clé
3. Si vous êtes administrateur, créez une nouvelle clé:

```bash
syscohada apikeys:create
```

### Erreur "Permission admin requise"

Certaines commandes nécessitent des permissions administrateur. Contactez un administrateur pour obtenir une clé avec les bonnes permissions.

### Erreur "Aucune société sélectionnée"

Pour la plupart des commandes, vous devez d'abord sélectionner une société:

```bash
syscohada societes:select
```

## Sécurité

### Bonnes pratiques

1. **Protection de la clé API**
   - Ne partagez jamais votre clé API
   - Stockez-la de manière sécurisée
   - Utilisez des clés avec les permissions minimales nécessaires

2. **Rotation des clés**
   - Changez régulièrement vos clés API
   - Désactivez immédiatement les clés compromises

3. **Monitoring**
   - Surveillez l'utilisation de vos clés
   - Vérifiez régulièrement les clés actives

### En cas de compromission

Si vous pensez que votre clé API a été compromise:

1. Contactez immédiatement un administrateur
2. La clé sera désactivée
3. Une nouvelle clé vous sera fournie

```bash
# Pour les administrateurs - désactiver une clé compromise
syscohada apikeys:deactivate sk_compromised_key
```