'use strict';

const { logger } = require('../config/logger');
const { ValidationError, NotFoundError } = require('../middleware/errorHandler');
const dashboardCalculations = require('../utils/dashboardCalculations');

/**
 * Service pour la génération des tableaux de bord financiers
 * Conforme aux normes SYSCOHADA
 */
class DashboardService {
  constructor(models) {
    this.models = models || require('../models');
    this.calculService = new (require('./calculService'))(this.models);
  }

  /**
   * Récupère les indicateurs clés de performance financiers
   * @param {string} societeId - ID de la société
   * @param {Object} periode - Période d'analyse {dateDebut, dateFin}
   * @param {Object} options - Options supplémentaires
   * @returns {Promise<Object>} KPIs financiers
   */
  async getKPIFinanciers(societeId, periode, options = {}) {
    try {
      const { dateDebut, dateFin } = periode;
      const { exerciceId } = options;

      // Valider les paramètres
      this.calculService.validerPeriode(dateDebut, dateFin);

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Calculer la balance générale pour obtenir les totaux par classe
      const balance = await this.calculService.calculerBalanceGenerale(
        dateDebut,
        dateFin,
        {
          societeId,
          exerciceId,
          niveauDetail: 'TOUS'
        }
      );

      // Extraire les données par classe de comptes
      const classeComptes = {};
      balance.totauxParClasse.forEach(classe => {
        classeComptes[classe.classe] = {
          totalDebit: classe.totalDebit,
          totalCredit: classe.totalCredit,
          soldeNet: classe.totalDebit - classe.totalCredit
        };
      });

      // Calculer les KPIs
      const kpis = {
        chiffreAffaires: dashboardCalculations.calculerChiffreAffaires(classeComptes),
        resultatNet: dashboardCalculations.calculerResultatNet(classeComptes),
        margeCommerciale: dashboardCalculations.calculerMargeCommerciale(classeComptes),
        tresorerie: dashboardCalculations.calculerTresorerie(classeComptes),
        totalActif: dashboardCalculations.calculerTotalActif(classeComptes),
        totalPassif: dashboardCalculations.calculerTotalPassif(classeComptes),
        totalCharges: dashboardCalculations.calculerTotalCharges(classeComptes),
        totalProduits: dashboardCalculations.calculerTotalProduits(classeComptes)
      };

      // Calculer les ratios
      kpis.ratios = {
        rentabilite: kpis.resultatNet / kpis.chiffreAffaires || 0,
        liquidite: dashboardCalculations.calculerRatioLiquidite(classeComptes),
        endettement: dashboardCalculations.calculerRatioEndettement(classeComptes),
        autonomieFinanciere: dashboardCalculations.calculerRatioAutonomieFinanciere(classeComptes)
      };

      logger.info('KPIs financiers calculés', {
        societeId,
        periode: { dateDebut, dateFin },
        chiffreAffaires: kpis.chiffreAffaires,
        resultatNet: kpis.resultatNet
      });

      return {
        societe: {
          id: societe.id,
          raisonSociale: societe.raisonSociale
        },
        periode: { dateDebut, dateFin },
        kpis,
        dateCalcul: new Date()
      };
    } catch (error) {
      logger.error('Erreur lors du calcul des KPIs financiers', {
        societeId,
        periode,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Analyse l'évolution du chiffre d'affaires sur plusieurs périodes
   * @param {string} societeId - ID de la société
   * @param {Array<Object>} periodes - Liste des périodes à analyser [{dateDebut, dateFin, libelle}]
   * @param {Object} options - Options supplémentaires
   * @returns {Promise<Object>} Évolution du chiffre d'affaires
   */
  async getEvolutionChiffreAffaires(societeId, periodes, _options = {}) {
    try {
      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Valider les périodes
      if (!Array.isArray(periodes) || periodes.length === 0) {
        throw new ValidationError('Au moins une période doit être spécifiée');
      }

      const resultats = [];
      let chiffreAffairesTotal = 0;

      // Calculer le chiffre d'affaires pour chaque période
      for (const periode of periodes) {
        const { dateDebut, dateFin, libelle } = periode;
        
        // Valider la période
        this.calculService.validerPeriode(dateDebut, dateFin);

        // Calculer la balance pour cette période
        const balance = await this.calculService.calculerBalanceGenerale(
          dateDebut,
          dateFin,
          {
            societeId,
            classeComptes: [7], // Classe 7 = Produits
            niveauDetail: 'TOUS'
          }
        );

        // Extraire le chiffre d'affaires (comptes 70)
        let chiffreAffaires = 0;
        for (const ligne of balance.lignesBalance) {
          if (ligne.compteNumero.startsWith('70')) {
            chiffreAffaires += ligne.totalCredit - ligne.totalDebit;
          }
        }

        resultats.push({
          periode: {
            dateDebut,
            dateFin,
            libelle: libelle || `${dateDebut.toISOString().split('T')[0]} - ${dateFin.toISOString().split('T')[0]}`
          },
          chiffreAffaires,
          nombreTransactions: balance.lignesBalance.reduce((sum, ligne) => sum + ligne.nombreLignes, 0)
        });

        chiffreAffairesTotal += chiffreAffaires;
      }

      // Calculer les variations entre périodes
      if (resultats.length > 1) {
        for (let i = 1; i < resultats.length; i++) {
          const periodeActuelle = resultats[i];
          const periodePrecedente = resultats[i - 1];
          
          const variation = periodeActuelle.chiffreAffaires - periodePrecedente.chiffreAffaires;
          const variationPourcentage = periodePrecedente.chiffreAffaires !== 0 
            ? (variation / periodePrecedente.chiffreAffaires) * 100 
            : 0;
          
          periodeActuelle.variation = {
            valeur: variation,
            pourcentage: variationPourcentage
          };
        }
      }

      logger.info('Évolution du chiffre d\'affaires calculée', {
        societeId,
        nombrePeriodes: periodes.length,
        chiffreAffairesTotal
      });

      return {
        societe: {
          id: societe.id,
          raisonSociale: societe.raisonSociale
        },
        periodes: resultats,
        chiffreAffairesTotal,
        dateCalcul: new Date()
      };
    } catch (error) {
      logger.error('Erreur lors du calcul de l\'évolution du chiffre d\'affaires', {
        societeId,
        periodes,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Analyse la répartition des charges et produits
   * @param {string} societeId - ID de la société
   * @param {Object} periode - Période d'analyse {dateDebut, dateFin}
   * @param {Object} options - Options supplémentaires
   * @returns {Promise<Object>} Analyse des charges et produits
   */
  async getAnalyseChargesProduits(societeId, periode, options = {}) {
    try {
      const { dateDebut, dateFin } = periode;
      const { exerciceId, niveauDetail = 2 } = options;

      // Valider les paramètres
      this.calculService.validerPeriode(dateDebut, dateFin);

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Calculer la balance générale
      const balance = await this.calculService.calculerBalanceGenerale(
        dateDebut,
        dateFin,
        {
          societeId,
          exerciceId,
          niveauDetail: 'TOUS'
        }
      );

      // Analyser les charges (classe 6)
      const charges = this._analyserComptes(balance.lignesBalance, '6', niveauDetail);
      
      // Analyser les produits (classe 7)
      const produits = this._analyserComptes(balance.lignesBalance, '7', niveauDetail);

      logger.info('Analyse charges/produits calculée', {
        societeId,
        periode: { dateDebut, dateFin },
        totalCharges: charges.total,
        totalProduits: produits.total
      });

      return {
        societe: {
          id: societe.id,
          raisonSociale: societe.raisonSociale
        },
        periode: { dateDebut, dateFin },
        charges,
        produits,
        resultatNet: produits.total - charges.total,
        dateCalcul: new Date()
      };
    } catch (error) {
      logger.error('Erreur lors de l\'analyse des charges et produits', {
        societeId,
        periode,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Calcule les ratios financiers
   * @param {string} societeId - ID de la société
   * @param {Object} periode - Période d'analyse {dateDebut, dateFin}
   * @param {Object} options - Options supplémentaires
   * @returns {Promise<Object>} Ratios financiers
   */
  async getRatiosFinanciers(societeId, periode, options = {}) {
    try {
      const { dateDebut, dateFin } = periode;
      // Utiliser l'exerciceId si fourni dans les options
      const { exerciceId } = options;

      // Valider les paramètres
      this.calculService.validerPeriode(dateDebut, dateFin);

      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Récupérer les KPIs pour calculer les ratios
      const kpisResult = await this.getKPIFinanciers(societeId, periode, { exerciceId });
      const kpis = kpisResult.kpis;

      // Calculer les ratios supplémentaires
      const ratios = {
        ...kpis.ratios,
        // Ratios de rentabilité
        rentabiliteCommerciale: kpis.resultatNet / kpis.chiffreAffaires || 0,
        margeNette: kpis.resultatNet / kpis.chiffreAffaires || 0,
        margeBrute: kpis.margeCommerciale / kpis.chiffreAffaires || 0,
        
        // Ratios de structure
        couvertureActifFixe: kpis.totalPassif > 0 ? kpis.totalActif / kpis.totalPassif : 0,
        
        // Ratios d'activité
        rotationStocks: kpis.chiffreAffaires / (kpis.totalActif - kpis.tresorerie) || 0
      };

      logger.info('Ratios financiers calculés', {
        societeId,
        periode: { dateDebut, dateFin }
      });

      return {
        societe: {
          id: societe.id,
          raisonSociale: societe.raisonSociale
        },
        periode: { dateDebut, dateFin },
        ratios,
        dateCalcul: new Date()
      };
    } catch (error) {
      logger.error('Erreur lors du calcul des ratios financiers', {
        societeId,
        periode,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Génère des alertes financières
   * @param {string} societeId - ID de la société
   * @param {Object} options - Options supplémentaires
   * @returns {Promise<Object>} Alertes financières
   */
  async getAlertes(societeId, _options = {}) {
    try {
      // Récupérer la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Récupérer l'exercice en cours
      const exerciceEnCours = await this.models.ExerciceComptable.findOne({
        where: {
          societeId,
          statut: 'OUVERT'
        },
        order: [['dateFin', 'DESC']]
      });

      if (!exerciceEnCours) {
        throw new NotFoundError('Exercice comptable ouvert');
      }

      const dateDebut = exerciceEnCours.dateDebut;
      const dateFin = new Date(); // Date actuelle

      // Récupérer les KPIs et ratios
      const kpisResult = await this.getKPIFinanciers(societeId, { dateDebut, dateFin }, { exerciceId: exerciceEnCours.id });
      const kpis = kpisResult.kpis;
      const ratios = kpis.ratios;

      // Générer les alertes
      const alertes = [];

      // Alerte de trésorerie
      if (kpis.tresorerie < 0) {
        alertes.push({
          type: 'TRESORERIE_NEGATIVE',
          niveau: 'CRITIQUE',
          message: 'La trésorerie est négative',
          valeur: kpis.tresorerie,
          seuil: 0
        });
      } else if (kpis.tresorerie < kpis.totalCharges * 0.1) {
        alertes.push({
          type: 'TRESORERIE_FAIBLE',
          niveau: 'AVERTISSEMENT',
          message: 'La trésorerie est inférieure à 10% des charges',
          valeur: kpis.tresorerie,
          seuil: kpis.totalCharges * 0.1
        });
      }

      // Alerte de résultat
      if (kpis.resultatNet < 0) {
        alertes.push({
          type: 'RESULTAT_NEGATIF',
          niveau: 'AVERTISSEMENT',
          message: 'Le résultat net est négatif',
          valeur: kpis.resultatNet,
          seuil: 0
        });
      }

      // Alerte d'endettement
      if (ratios.endettement > 1.5) {
        alertes.push({
          type: 'ENDETTEMENT_ELEVE',
          niveau: 'AVERTISSEMENT',
          message: 'Le ratio d\'endettement est supérieur à 1.5',
          valeur: ratios.endettement,
          seuil: 1.5
        });
      }

      // Alerte de liquidité
      if (ratios.liquidite < 1) {
        alertes.push({
          type: 'LIQUIDITE_FAIBLE',
          niveau: 'AVERTISSEMENT',
          message: 'Le ratio de liquidité est inférieur à 1',
          valeur: ratios.liquidite,
          seuil: 1
        });
      }

      logger.info('Alertes financières générées', {
        societeId,
        nombreAlertes: alertes.length
      });

      return {
        societe: {
          id: societe.id,
          raisonSociale: societe.raisonSociale
        },
        exercice: {
          id: exerciceEnCours.id,
          annee: exerciceEnCours.annee
        },
        alertes,
        dateCalcul: new Date()
      };
    } catch (error) {
      logger.error('Erreur lors de la génération des alertes', {
        societeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Analyse les comptes d'une classe donnée
   * @private
   * @param {Array} lignesBalance - Lignes de la balance
   * @param {string} prefixeClasse - Préfixe de la classe (ex: '6' pour les charges)
   * @param {number} niveauDetail - Niveau de détail (nombre de chiffres du compte)
   * @returns {Object} Analyse des comptes
   */
  _analyserComptes(lignesBalance, prefixeClasse, niveauDetail) {
    const comptes = lignesBalance.filter(ligne => 
      ligne.compteNumero.startsWith(prefixeClasse)
    );

    // Regrouper par préfixe selon le niveau de détail
    const groupes = {};
    let total = 0;

    comptes.forEach(compte => {
      const prefixe = compte.compteNumero.substring(0, niveauDetail);
      const solde = compte.sensActuel === 'DEBIT' 
        ? compte.solde 
        : -compte.solde;

      if (!groupes[prefixe]) {
        groupes[prefixe] = {
          prefixe,
          libelle: `Comptes ${prefixe}`,
          montant: 0,
          details: []
        };
      }

      groupes[prefixe].montant += solde;
      groupes[prefixe].details.push({
        compte: compte.compteNumero,
        libelle: compte.libelle,
        montant: solde
      });

      total += solde;
    });

    // Convertir en tableau et calculer les pourcentages
    const groupesArray = Object.values(groupes).map(groupe => {
      return {
        ...groupe,
        pourcentage: total !== 0 ? (groupe.montant / total) * 100 : 0
      };
    });

    // Trier par montant décroissant
    groupesArray.sort((a, b) => b.montant - a.montant);

    return {
      total: Math.abs(total),
      groupes: groupesArray
    };
  }
}

module.exports = DashboardService;