'use strict';

const request = require('supertest');
const app = require('../../src/app');
const { models, sequelize } = require('../../src/models');
const { v4: uuidv4 } = require('uuid');

// Données de test
let testExerciceId;
let testSocieteId;
let testJournalCode;
let testToken;

// Configuration avant les tests
beforeAll(async () => {
  // Créer une société de test
  const societe = await models.Societe.create({
    nom: 'Société Test Clôture',
    siret: '98765432100001',
    adresse: '123 Rue de Test',
    codePostal: '75000',
    ville: 'Paris',
    pays: 'France',
    telephone: '0123456789',
    email: '<EMAIL>'
  });
  testSocieteId = societe.id;

  // Créer un journal OD
  const journal = await models.Journal.create({
    code: 'OD-TEST',
    libelle: 'Journal OD Test',
    type: 'OD',
    societeId: testSocieteId
  });
  testJournalCode = journal.code;

  // Créer un exercice comptable
  const exercice = await models.ExerciceComptable.create({
    libelle: 'Exercice Test Clôture',
    dateDebut: '2025-01-01',
    dateFin: '2025-12-31',
    statut: 'OUVERT',
    societeId: testSocieteId
  });
  testExerciceId = exercice.id;

  // Créer quelques comptes comptables
  await models.CompteComptable.bulkCreate([
    {
      numero: '401000',
      libelle: 'Fournisseurs',
      classe: 4,
      nature: 'PASSIF',
      sens: 'CREDIT',
      niveau: 3,
      societeId: testSocieteId,
      obligatoireLettrage: true
    },
    {
      numero: '512000',
      libelle: 'Banque',
      classe: 5,
      nature: 'ACTIF',
      sens: 'DEBIT',
      niveau: 3,
      societeId: testSocieteId
    },
    {
      numero: '607000',
      libelle: 'Achats de marchandises',
      classe: 6,
      nature: 'CHARGE',
      sens: 'DEBIT',
      niveau: 3,
      societeId: testSocieteId
    },
    {
      numero: '707000',
      libelle: 'Ventes de marchandises',
      classe: 7,
      nature: 'PRODUIT',
      sens: 'CREDIT',
      niveau: 3,
      societeId: testSocieteId
    }
  ]);

  // Créer quelques écritures comptables
  const ecriture1 = await models.EcritureComptable.create({
    numeroEcriture: `${testJournalCode}-2025-001`,
    dateEcriture: '2025-02-15',
    journalCode: testJournalCode,
    libelle: 'Achat de marchandises',
    exerciceId: testExerciceId,
    statut: 'VALIDE',
    societeId: testSocieteId
  });

  await models.LigneEcriture.bulkCreate([
    {
      ecritureId: ecriture1.id,
      compteNumero: '607000',
      libelle: 'Achat de marchandises',
      montantDebit: 1000,
      montantCredit: 0
    },
    {
      ecritureId: ecriture1.id,
      compteNumero: '401000',
      libelle: 'Fournisseur XYZ',
      montantDebit: 0,
      montantCredit: 1000
    }
  ]);

  const ecriture2 = await models.EcritureComptable.create({
    numeroEcriture: `${testJournalCode}-2025-002`,
    dateEcriture: '2025-03-20',
    journalCode: testJournalCode,
    libelle: 'Vente de marchandises',
    exerciceId: testExerciceId,
    statut: 'VALIDE',
    societeId: testSocieteId
  });

  await models.LigneEcriture.bulkCreate([
    {
      ecritureId: ecriture2.id,
      compteNumero: '707000',
      libelle: 'Vente de marchandises',
      montantDebit: 0,
      montantCredit: 1500
    },
    {
      ecritureId: ecriture2.id,
      compteNumero: '512000',
      libelle: 'Banque',
      montantDebit: 1500,
      montantCredit: 0
    }
  ]);

  // Simuler un token d'authentification
  testToken = 'test-token-cloture';
});

// Nettoyage après les tests
afterAll(async () => {
  await models.LigneEcriture.destroy({ where: {} });
  await models.EcritureComptable.destroy({ where: {} });
  await models.CompteComptable.destroy({ where: { societeId: testSocieteId } });
  await models.ExerciceComptable.destroy({ where: { id: testExerciceId } });
  await models.Journal.destroy({ where: { code: testJournalCode } });
  await models.Societe.destroy({ where: { id: testSocieteId } });
});

// Mock du middleware d'authentification
jest.mock('../../src/middleware/auth', () => ({
  authMiddleware: (req, res, next) => {
    req.user = { id: 'test-user-id' };
    next();
  }
}));

describe('Routes de clôture', () => {
  describe('GET /api/v1/cloture/verifier/:exerciceId', () => {
    it('devrait vérifier la complétude des saisies', async () => {
      const response = await request(app)
        .get(`/api/v1/cloture/verifier/${testExerciceId}`)
        .set('Authorization', `Bearer ${testToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.exerciceId).toBe(testExerciceId);
    });

    it('devrait retourner 400 pour un ID d\'exercice invalide', async () => {
      const response = await request(app)
        .get('/api/v1/cloture/verifier/invalid-id')
        .set('Authorization', `Bearer ${testToken}`);

      expect(response.status).toBe(400);
    });
  });

  describe('GET /api/v1/cloture/resultat/:exerciceId', () => {
    it('devrait calculer le résultat de l\'exercice', async () => {
      const response = await request(app)
        .get(`/api/v1/cloture/resultat/${testExerciceId}`)
        .set('Authorization', `Bearer ${testToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.exerciceId).toBe(testExerciceId);
      expect(response.body.data.resultatExercice).toBeDefined();
    });
  });

  describe('POST /api/v1/cloture/ecritures/:exerciceId', () => {
    it('devrait générer les écritures de clôture', async () => {
      const response = await request(app)
        .post(`/api/v1/cloture/ecritures/${testExerciceId}`)
        .set('Authorization', `Bearer ${testToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.ecritures).toBeInstanceOf(Array);
    });
  });

  describe('POST /api/v1/cloture/exercice/:exerciceId', () => {
    it('devrait clôturer un exercice', async () => {
      // Créer un nouvel exercice pour le test de clôture
      const exerciceACloturer = await models.ExerciceComptable.create({
        libelle: 'Exercice à Clôturer',
        dateDebut: '2024-01-01',
        dateFin: '2024-12-31',
        statut: 'OUVERT',
        societeId: testSocieteId
      });

      const response = await request(app)
        .post(`/api/v1/cloture/exercice/${exerciceACloturer.id}`)
        .send({
          commentaire: 'Clôture de test',
          genererEcritures: true
        })
        .set('Authorization', `Bearer ${testToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.exercice).toBeDefined();
      expect(response.body.data.exercice.statut).toBe('CLOTURE');

      // Nettoyer
      await models.ExerciceComptable.destroy({ where: { id: exerciceACloturer.id } });
    });
  });

  describe('POST /api/v1/cloture/a-nouveaux/:exerciceId', () => {
    it('devrait générer les écritures d\'à-nouveaux', async () => {
      // Créer un exercice clôturé et un nouvel exercice
      const exerciceSource = await models.ExerciceComptable.create({
        libelle: 'Exercice Source',
        dateDebut: '2023-01-01',
        dateFin: '2023-12-31',
        statut: 'CLOTURE',
        dateCloture: new Date(),
        societeId: testSocieteId
      });

      const nouvelExercice = await models.ExerciceComptable.create({
        libelle: 'Nouvel Exercice',
        dateDebut: '2024-01-01',
        dateFin: '2024-12-31',
        statut: 'OUVERT',
        societeId: testSocieteId
      });

      const response = await request(app)
        .post(`/api/v1/cloture/a-nouveaux/${exerciceSource.id}`)
        .send({
          nouvelExerciceId: nouvelExercice.id
        })
        .set('Authorization', `Bearer ${testToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.ecritureANouveaux).toBeDefined();

      // Nettoyer
      await models.ExerciceComptable.destroy({ 
        where: { 
          id: [exerciceSource.id, nouvelExercice.id] 
        } 
      });
    });

    it('devrait retourner 400 si l\'ID du nouvel exercice est manquant', async () => {
      const response = await request(app)
        .post(`/api/v1/cloture/a-nouveaux/${testExerciceId}`)
        .send({})
        .set('Authorization', `Bearer ${testToken}`);

      expect(response.status).toBe(400);
    });
  });
});