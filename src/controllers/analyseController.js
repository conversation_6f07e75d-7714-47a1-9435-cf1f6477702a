'use strict';

const { ValidationError } = require('../middleware/errorHandler');

/**
 * Contrôleur pour les analyses financières avancées
 */
class AnalyseController {
  constructor() {
    this.models = require('../models');
    this.analyseService = new (require('../services/analyseService'))(this.models);
  }

  /**
   * Analyse l'évolution des comptes sur plusieurs périodes
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async analyseEvolutionComptes(req, res, next) {
    try {
      // Récupérer les paramètres
      const { comptes, periodes } = req.body;
      
      // Valider les paramètres obligatoires
      if (!comptes || !Array.isArray(comptes) || comptes.length === 0) {
        throw new ValidationError('Au moins un compte doit être spécifié');
      }
      
      if (!periodes || !Array.isArray(periodes) || periodes.length < 2) {
        throw new ValidationError('Au moins deux périodes doivent être spécifiées');
      }
      
      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;
      
      // Récupérer l'exercice si fourni
      const exerciceId = req.query.exerciceId;
      
      // Formater les périodes
      const periodesFormatees = periodes.map(periode => ({
        dateDebut: new Date(periode.dateDebut),
        dateFin: new Date(periode.dateFin),
        libelle: periode.libelle
      }));
      
      // Analyser l'évolution des comptes
      const resultat = await this.analyseService.analyseEvolutionComptes(
        comptes,
        periodesFormatees,
        { societeId, exerciceId }
      );
      
      res.json(resultat);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Détecte les anomalies dans les écritures comptables
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async detectionAnomalies(req, res, next) {
    try {
      const { dateDebut, dateFin, seuilMontant, seuilEcartType, seuilFrequence } = req.query;
      
      // Valider les paramètres obligatoires
      if (!dateDebut || !dateFin) {
        throw new ValidationError('Les dates de début et de fin sont obligatoires');
      }
      
      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;
      
      // Récupérer l'exercice si fourni
      const exerciceId = req.query.exerciceId;
      
      // Préparer les options
      const options = { 
        exerciceId,
        seuilMontant: seuilMontant ? parseFloat(seuilMontant) : undefined,
        seuilEcartType: seuilEcartType ? parseFloat(seuilEcartType) : undefined,
        seuilFrequence: seuilFrequence ? parseInt(seuilFrequence) : undefined
      };
      
      // Détecter les anomalies
      const resultat = await this.analyseService.detectionAnomalies(
        societeId,
        {
          dateDebut: new Date(dateDebut),
          dateFin: new Date(dateFin)
        },
        options
      );
      
      res.json(resultat);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Calcule des prévisions financières
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async previsionsFinancieres(req, res, next) {
    try {
      const { horizon, methode, periodeReference, nombrePeriodesPasses } = req.query;
      const { comptes } = req.body;
      
      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;
      
      // Préparer les options
      const options = {
        methode: methode || 'regression_lineaire',
        periodeReference: periodeReference || 'mois',
        nombrePeriodesPasses: nombrePeriodesPasses ? parseInt(nombrePeriodesPasses) : 12,
        comptes: comptes || []
      };
      
      // Calculer les prévisions
      const resultat = await this.analyseService.previsionsFinancieres(
        societeId,
        horizon ? parseInt(horizon) : 3,
        options
      );
      
      res.json(resultat);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Compare les résultats réels avec les budgets
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async comparaisonsBudgetaires(req, res, next) {
    try {
      const { dateDebut, dateFin } = req.query;
      
      // Valider les paramètres obligatoires
      if (!dateDebut || !dateFin) {
        throw new ValidationError('Les dates de début et de fin sont obligatoires');
      }
      
      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;
      
      // Récupérer l'exercice si fourni
      const exerciceId = req.query.exerciceId;
      
      // Comparer avec les budgets
      const resultat = await this.analyseService.comparaisonsBudgetaires(
        societeId,
        {
          dateDebut: new Date(dateDebut),
          dateFin: new Date(dateFin)
        },
        { exerciceId }
      );
      
      res.json(resultat);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Effectue un benchmarking sectoriel
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   * @param {Function} next - Middleware suivant
   */
  async benchmarkingSectoriel(req, res, next) {
    try {
      const { secteur } = req.params;
      const { ratios } = req.query;
      
      // Valider les paramètres obligatoires
      if (!secteur) {
        throw new ValidationError('Le secteur est obligatoire');
      }
      
      // Récupérer l'ID de la société depuis le token JWT
      const societeId = req.user.societeId;
      
      // Préparer les ratios
      const ratiosArray = ratios ? ratios.split(',') : null;
      
      // Effectuer le benchmarking
      const resultat = await this.analyseService.benchmarkingSectoriel(
        secteur,
        ratiosArray,
        { societeId }
      );
      
      res.json(resultat);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new AnalyseController();