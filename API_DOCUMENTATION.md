# Documentation API - Système de Comptabilité Générale SYSCOHADA

## Vue d'ensemble

Cette API REST fournit un système complet de comptabilité générale conforme aux normes SYSCOHADA (Système Comptable Ouest Africain). Elle est développée en Node.js avec Express et utilise PostgreSQL comme base de données.

**URL de base :** `http://localhost:3000/api/v1`

**Version :** 1.0.0

## Authentification

L'API utilise un système de clés API pour l'authentification. Toutes les requêtes (sauf les routes publiques) doivent inclure l'en-tête :

```
Authorization: Bearer YOUR_API_KEY
```

### Permissions disponibles :
- `read` : Lecture seule
- `write` : Lecture et écriture
- `admin` : Accès complet

## Structure des réponses

### Réponse de succès
```json
{
  "success": true,
  "data": { ... },
  "message": "Opération réussie"
}
```

### Réponse d'erreur
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Description de l'erreur",
    "details": { ... }
  }
}
```

## Codes de statut HTTP

- `200` : Succès
- `201` : Créé avec succès
- `400` : Requête invalide
- `401` : Non autorisé
- `403` : Accès interdit
- `404` : Ressource non trouvée
- `422` : Erreur de validation
- `500` : Erreur serveur

---

## 1. AUTHENTIFICATION ET CLÉS API

### 1.1 Vérifier une clé API

**GET** `/auth/verify`

Vérifie la validité de la clé API actuelle.

**Headers requis :**
- `Authorization: Bearer YOUR_API_KEY`

**Réponse :**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "apiKey": {
      "id": "uuid",
      "name": "Nom de la clé",
      "permissions": ["read", "write"]
    }
  }
}
```

### 1.2 Profil de la clé API

**GET** `/auth/profile`

Récupère les informations de la clé API actuelle.

**Réponse :**
```json
{
  "success": true,
  "data": {
    "apiKey": {
      "id": "uuid",
      "name": "Nom de la clé",
      "prefix": "sk_",
      "permissions": ["read", "write", "admin"]
    }
  }
}
```

### 1.3 Gestion des clés API (Admin uniquement)

#### Créer une clé API
**POST** `/api-keys`

**Permissions requises :** `admin`

**Corps de la requête :**
```json
{
  "name": "Nom de la clé",
  "permissions": ["read", "write"],
  "expiresAt": "2024-12-31T23:59:59.000Z",
  "metadata": {}
}
```

#### Lister les clés API
**GET** `/api-keys`

**Paramètres de requête :**
- `page` (optionnel) : Numéro de page (défaut: 1)
- `limit` (optionnel) : Nombre d'éléments par page (défaut: 20, max: 100)
- `includeInactive` (optionnel) : Inclure les clés inactives (défaut: false)

#### Récupérer une clé API
**GET** `/api-keys/:id`

#### Mettre à jour une clé API
**PUT** `/api-keys/:id`

#### Supprimer une clé API
**DELETE** `/api-keys/:id`

#### Désactiver une clé API
**POST** `/api-keys/:id/deactivate`

---

## 2. GESTION DES SOCIÉTÉS

### 2.1 Lister les sociétés

**GET** `/societes`

**Réponse :**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "nom": "Nom de la société",
      "adresse": "Adresse complète",
      "telephone": "+225 XX XX XX XX",
      "email": "<EMAIL>",
      "numeroContribuable": "123456789",
      "formeJuridique": "SARL",
      "capital": 1000000.00,
      "exerciceDebut": "2024-01-01",
      "exerciceFin": "2024-12-31",
      "devise": "XOF",
      "numeroRccm": "CI-ABJ-2024-B12-12345",
      "regimeFiscal": "REEL_NORMAL",
      "statut": "ACTIF",
      "logoUrl": "https://...",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### 2.2 Récupérer une société

**GET** `/societes/:id`

### 2.3 Créer une société

**POST** `/societes`

**Corps de la requête :**
```json
{
  "nom": "Nom de la société",
  "adresse": "Adresse complète",
  "telephone": "+225 XX XX XX XX",
  "email": "<EMAIL>",
  "numeroContribuable": "123456789",
  "formeJuridique": "SARL",
  "capital": 1000000.00,
  "exerciceDebut": "2024-01-01",
  "exerciceFin": "2024-12-31",
  "devise": "XOF",
  "numeroRccm": "CI-ABJ-2024-B12-12345",
  "regimeFiscal": "REEL_NORMAL"
}
```

**Champs obligatoires :**
- `nom` : Nom de la société (2-255 caractères)
- `exerciceDebut` : Date de début d'exercice
- `exerciceFin` : Date de fin d'exercice

**Valeurs autorisées :**
- `devise` : XOF, EUR, USD, GBP
- `regimeFiscal` : REEL_NORMAL, REEL_SIMPLIFIE, SYNTHETIQUE
- `statut` : ACTIF, SUSPENDU, FERME

### 2.4 Mettre à jour une société

**PUT** `/societes/:id`

### 2.5 Supprimer une société

**DELETE** `/societes/:id`

### 2.6 Statistiques d'une société

**GET** `/societes/:id/stats`

**Réponse :**
```json
{
  "success": true,
  "data": {
    "nombreComptes": 150,
    "nombreJournaux": 8,
    "nombreEcritures": 1250,
    "nombreExercices": 3,
    "chiffreAffaires": 2500000.00,
    "resultatNet": 125000.00
  }
}
```

---

## 3. GESTION DES TIERS (PARTIES)

### 3.1 Lister les tiers d'une société

**GET** `/societes/:societeId/parties`

**Paramètres de requête :**
- `type` (optionnel) : CLIENT, FOURNISSEUR, CLIENT_FOURNISSEUR
- `actif` (optionnel) : true/false
- `page` (optionnel) : Numéro de page
- `limit` (optionnel) : Nombre d'éléments par page

### 3.2 Créer un tiers

**POST** `/societes/:societeId/parties`

**Corps de la requête :**
```json
{
  "nom": "Nom du tiers",
  "type": "CLIENT",
  "email": "<EMAIL>",
  "telephone": "+225 XX XX XX XX",
  "adresse": "Adresse du tiers",
  "numeroContribuable": "123456789",
  "compteClient": "411001",
  "compteFournisseur": "401001",
  "conditionsPaiement": "30 jours",
  "actif": true
}
```

### 3.3 Récupérer un tiers

**GET** `/societes/:societeId/parties/:id`

### 3.4 Mettre à jour un tiers

**PUT** `/societes/:societeId/parties/:id`

### 3.5 Supprimer un tiers

**DELETE** `/societes/:societeId/parties/:id`

---

## 4. PLAN COMPTABLE

### 4.1 Lister les comptes

**GET** `/comptes`

**Paramètres de requête :**
- `societeId` (optionnel) : UUID de la société
- `classe` (optionnel) : Classe de compte (1-8)
- `actif` (optionnel) : true/false
- `page` (optionnel) : Numéro de page
- `limit` (optionnel) : Nombre d'éléments par page

### 4.2 Hiérarchie des comptes

**GET** `/comptes/hierarchie`

### 4.3 Comptes par classe

**GET** `/comptes/classe/:classe`

### 4.4 Récupérer un compte

**GET** `/comptes/:numero`

### 4.5 Créer un compte

**POST** `/comptes`

**Corps de la requête :**
```json
{
  "numero": "601001",
  "libelle": "Achats de marchandises",
  "classe": 6,
  "nature": "CHARGE",
  "sens": "DEBIT",
  "niveau": 3,
  "compteParent": "601000",
  "societeId": "uuid",
  "actif": true,
  "obligatoireLettrage": false,
  "typeAnalytique": "AUCUN"
}
```

**Champs obligatoires :**
- `numero` : Numéro de compte (3-10 chiffres)
- `libelle` : Libellé du compte (2-100 caractères)
- `nature` : ACTIF, PASSIF, CHARGE, PRODUIT
- `sens` : DEBIT, CREDIT

### 4.6 Mettre à jour un compte

**PUT** `/comptes/:numero`

### 4.7 Supprimer un compte

**DELETE** `/comptes/:numero`

---

## 5. GESTION DES JOURNAUX

### 5.1 Lister les journaux

**GET** `/journaux`

### 5.2 Créer un journal

**POST** `/journaux`

**Corps de la requête :**
```json
{
  "code": "VE",
  "libelle": "Journal des ventes",
  "type": "VENTE",
  "societeId": "uuid",
  "prefixeNumero": "VE",
  "prochainNumero": 1,
  "actif": true
}
```

### 5.3 Récupérer un journal

**GET** `/journaux/:code`

### 5.4 Mettre à jour un journal

**PUT** `/journaux/:code`

### 5.5 Supprimer un journal

**DELETE** `/journaux/:code`

---

## 6. GESTION DES EXERCICES COMPTABLES

### 6.1 Lister les exercices

**GET** `/exercices`

### 6.2 Créer un exercice

**POST** `/exercices`

**Corps de la requête :**
```json
{
  "libelle": "Exercice 2024",
  "dateDebut": "2024-01-01",
  "dateFin": "2024-12-31",
  "societeId": "uuid",
  "statut": "OUVERT"
}
```

### 6.3 Récupérer un exercice

**GET** `/exercices/:id`

### 6.4 Mettre à jour un exercice

**PUT** `/exercices/:id`

### 6.5 Clôturer un exercice

**POST** `/exercices/:id/cloturer`

---

## 7. GESTION DES ÉCRITURES COMPTABLES

### 7.1 Lister les écritures

**GET** `/ecritures`

**Paramètres de requête :**
- `page` (optionnel) : Numéro de page (défaut: 1)
- `limit` (optionnel) : Nombre d'éléments par page (défaut: 50, max: 100)
- `societeId` (optionnel) : UUID de la société
- `journalCode` (optionnel) : Code du journal (2 caractères)
- `statut` (optionnel) : BROUILLARD, VALIDEE, CLOTUREE
- `dateDebut` (optionnel) : Date de début (format ISO)
- `dateFin` (optionnel) : Date de fin (format ISO)
- `compteNumero` (optionnel) : Numéro de compte
- `recherche` (optionnel) : Recherche textuelle

### 7.2 Créer une écriture

**POST** `/ecritures`

**Corps de la requête :**
```json
{
  "donnees": {
    "dateEcriture": "2024-01-15",
    "libelle": "Achat marchandises",
    "reference": "FAC-2024-001",
    "pieceJustificative": "Facture n°001",
    "journalCode": "AC",
    "exerciceId": "uuid",
    "societeId": "uuid"
  },
  "lignes": [
    {
      "compteNumero": "601000",
      "libelle": "Achats de marchandises",
      "debit": 100000.00,
      "credit": 0.00,
      "reference": "FAC-001"
    },
    {
      "compteNumero": "445700",
      "libelle": "TVA déductible",
      "debit": 18000.00,
      "credit": 0.00
    },
    {
      "compteNumero": "401000",
      "libelle": "Fournisseur ABC",
      "debit": 0.00,
      "credit": 118000.00,
      "tiersId": "uuid"
    }
  ]
}
```

**Règles de validation :**
- Une écriture doit avoir au moins 2 lignes
- Le total des débits doit égaler le total des crédits
- Une ligne ne peut pas avoir à la fois débit et crédit
- Chaque ligne doit avoir soit un débit soit un crédit > 0

### 7.3 Récupérer une écriture

**GET** `/ecritures/:id`

### 7.4 Mettre à jour une écriture

**PUT** `/ecritures/:id`

**Note :** Seules les écritures en statut BROUILLARD peuvent être modifiées.

### 7.5 Supprimer une écriture

**DELETE** `/ecritures/:id`

### 7.6 Valider une écriture

**POST** `/ecritures/:id/valider`

Change le statut de BROUILLARD à VALIDEE.

### 7.7 Dupliquer une écriture

**POST** `/ecritures/:id/dupliquer`

**Corps de la requête :**
```json
{
  "nouvelleDate": "2024-02-15",
  "nouveauLibelle": "Achat marchandises - Février"
}
```

### 7.8 Recherche avancée d'écritures

**POST** `/ecritures/recherche`

**Corps de la requête :**
```json
{
  "criteres": {
    "dateDebut": "2024-01-01",
    "dateFin": "2024-12-31",
    "comptes": ["601000", "701000"],
    "journaux": ["AC", "VE"],
    "montantMin": 1000.00,
    "montantMax": 100000.00,
    "texte": "facture"
  },
  "options": {
    "page": 1,
    "limit": 50,
    "tri": "dateEcriture",
    "ordre": "DESC"
  }
}
```

### 7.9 Validation SYSCOHADA

**POST** `/ecritures/valider-syscohada`

Valide une écriture selon les règles SYSCOHADA avant création.

---

## 8. LETTRAGE

### 8.1 Lettrer des écritures

**POST** `/lettrage/lettrer`

**Corps de la requête :**
```json
{
  "lignesIds": ["uuid1", "uuid2", "uuid3"],
  "lettrage": "A001"
}
```

### 8.2 Délettrer des écritures

**POST** `/lettrage/delettrer`

**Corps de la requête :**
```json
{
  "lettrage": "A001"
}
```

### 8.3 Lettrage automatique

**POST** `/lettrage/auto`

**Corps de la requête :**
```json
{
  "compteNumero": "411000",
  "dateDebut": "2024-01-01",
  "dateFin": "2024-12-31",
  "tolerance": 0.01
}
```

### 8.4 Propositions de lettrage

**GET** `/lettrage/propositions`

**Paramètres de requête :**
- `compteNumero` : Numéro de compte
- `montant` (optionnel) : Montant à lettrer
- `tolerance` (optionnel) : Tolérance (défaut: 0.01)

---

## 9. TEMPLATES D'ÉCRITURES

### 9.1 Lister les templates

**GET** `/templates`

### 9.2 Créer un template

**POST** `/templates`

**Corps de la requête :**
```json
{
  "nom": "Achat avec TVA",
  "description": "Template pour achats avec TVA déductible",
  "journalCode": "AC",
  "societeId": "uuid",
  "lignes": [
    {
      "compteNumero": "601000",
      "libelle": "Achats de marchandises",
      "typeOperation": "DEBIT",
      "formule": "montant_ht",
      "ordre": 1
    },
    {
      "compteNumero": "445700",
      "libelle": "TVA déductible",
      "typeOperation": "DEBIT",
      "formule": "montant_ht * 0.18",
      "ordre": 2
    },
    {
      "compteNumero": "401000",
      "libelle": "Fournisseur",
      "typeOperation": "CREDIT",
      "formule": "montant_ht * 1.18",
      "ordre": 3
    }
  ]
}
```

### 9.3 Utiliser un template

**POST** `/templates/:id/utiliser`

**Corps de la requête :**
```json
{
  "dateEcriture": "2024-01-15",
  "libelle": "Achat marchandises Janvier",
  "variables": {
    "montant_ht": 100000.00,
    "fournisseur": "Fournisseur ABC"
  }
}
```

### 9.4 Récupérer un template

**GET** `/templates/:id`

### 9.5 Mettre à jour un template

**PUT** `/templates/:id`

### 9.6 Supprimer un template

**DELETE** `/templates/:id`

---

## 10. IMPORT/EXPORT

### 10.1 Importer des écritures depuis Excel

**POST** `/import-export/excel`

**Content-Type :** `multipart/form-data`

**Champs du formulaire :**
- `file` : Fichier Excel (.xlsx)
- `societeId` : UUID de la société
- `exerciceId` (optionnel) : UUID de l'exercice
- `remplacerExistants` (optionnel) : true/false
- `validerUniquement` (optionnel) : true/false
- `validerEcritures` (optionnel) : true/false

**Format du fichier Excel :**
| Date | Journal | Libellé | Référence | Compte | Libellé Ligne | Débit | Crédit | Tiers |
|------|---------|---------|-----------|---------|---------------|-------|--------|-------|
| 2024-01-15 | AC | Achat | FAC-001 | 601000 | Achats | 100000 | 0 | |
| 2024-01-15 | AC | Achat | FAC-001 | 401000 | Fournisseur | 0 | 100000 | FOURNISSEUR_ABC |

### 10.2 Importer des écritures depuis CSV

**POST** `/import-export/csv`

**Champs supplémentaires :**
- `separateur` (optionnel) : Caractère séparateur (défaut: ',')

### 10.3 Exporter des écritures vers Excel

**GET** `/import-export/export/excel`

**Paramètres de requête :**
- `societeId` : UUID de la société
- `dateDebut` : Date de début
- `dateFin` : Date de fin
- `journalCode` (optionnel) : Code du journal
- `compteNumero` (optionnel) : Numéro de compte

### 10.4 Exporter des écritures vers CSV

**GET** `/import-export/export/csv`

### 10.5 Télécharger un template d'import

**GET** `/import-export/template`

**Paramètres de requête :**
- `format` : excel ou csv

### 10.6 Statut d'un import

**GET** `/import-export/statut/:jobId`

---

## 11. CALCULS ET ÉTATS COMPTABLES

### 11.1 Balance générale

**GET** `/calculs/balance`

**Paramètres de requête :**
- `societeId` : UUID de la société
- `dateDebut` : Date de début
- `dateFin` : Date de fin
- `niveau` (optionnel) : Niveau de détail (1-5)
- `format` (optionnel) : json, excel, pdf

### 11.2 Grand livre

**GET** `/calculs/grand-livre`

**Paramètres de requête :**
- `societeId` : UUID de la société
- `compteNumero` : Numéro de compte
- `dateDebut` : Date de début
- `dateFin` : Date de fin

### 11.3 Journal comptable

**GET** `/calculs/journal`

**Paramètres de requête :**
- `societeId` : UUID de la société
- `journalCode` : Code du journal
- `dateDebut` : Date de début
- `dateFin` : Date de fin

### 11.4 Bilan comptable

**GET** `/etats/bilan`

**Paramètres de requête :**
- `societeId` : UUID de la société
- `dateArrete` : Date d'arrêté
- `format` (optionnel) : json, excel, pdf

### 11.5 Compte de résultat

**GET** `/etats/compte-resultat`

### 11.6 Tableau de financement

**GET** `/etats/tableau-financement`

### 11.7 Soldes intermédiaires de gestion

**GET** `/etats/soldes-intermediaires`

---

## 12. DASHBOARD ET ANALYSES

### 12.1 KPIs financiers

**GET** `/dashboard/kpi`

**Paramètres de requête :**
- `dateDebut` : Date de début
- `dateFin` : Date de fin
- `exerciceId` (optionnel) : UUID de l'exercice

**Réponse :**
```json
{
  "success": true,
  "data": {
    "chiffreAffaires": 2500000.00,
    "resultatNet": 125000.00,
    "margeCommerciale": 15.5,
    "ratioLiquidite": 1.8,
    "rotationStocks": 12.5,
    "delaiPaiementClients": 45,
    "delaiPaiementFournisseurs": 30
  }
}
```

### 12.2 Évolution du chiffre d'affaires

**POST** `/dashboard/evolution`

**Corps de la requête :**
```json
{
  "periodes": [
    {
      "dateDebut": "2024-01-01",
      "dateFin": "2024-01-31",
      "libelle": "Janvier 2024"
    },
    {
      "dateDebut": "2024-02-01",
      "dateFin": "2024-02-29",
      "libelle": "Février 2024"
    }
  ]
}
```

### 12.3 Analyse charges et produits

**GET** `/dashboard/analyse`

**Paramètres de requête :**
- `dateDebut` : Date de début
- `dateFin` : Date de fin
- `exerciceId` (optionnel) : UUID de l'exercice
- `niveauDetail` (optionnel) : Niveau de détail (1-8)

### 12.4 Ratios financiers

**GET** `/dashboard/ratios`

### 12.5 Alertes financières

**GET** `/dashboard/alertes`

**Réponse :**
```json
{
  "success": true,
  "data": {
    "alertes": [
      {
        "type": "TRESORERIE",
        "niveau": "WARNING",
        "message": "Trésorerie faible",
        "valeur": 15000.00,
        "seuil": 50000.00
      },
      {
        "type": "CREANCES",
        "niveau": "CRITICAL",
        "message": "Créances en retard",
        "valeur": 125000.00,
        "delai": 60
      }
    ]
  }
}
```

---

## 13. ANALYSES FINANCIÈRES

### 13.1 Analyse de rentabilité

**GET** `/analyses/rentabilite`

### 13.2 Analyse de liquidité

**GET** `/analyses/liquidite`

### 13.3 Analyse de solvabilité

**GET** `/analyses/solvabilite`

### 13.4 Analyse de l'activité

**GET** `/analyses/activite`

### 13.5 Comparaison d'exercices

**POST** `/analyses/comparaison`

**Corps de la requête :**
```json
{
  "exercices": ["uuid1", "uuid2"],
  "indicateurs": ["CA", "RESULTAT", "MARGE"]
}
```

---

## 14. CLÔTURE D'EXERCICE

### 14.1 Préparer la clôture

**POST** `/cloture/preparer`

**Corps de la requête :**
```json
{
  "exerciceId": "uuid",
  "dateClotureProvisoire": "2024-12-31"
}
```

### 14.2 Vérifications pré-clôture

**GET** `/cloture/verifications/:exerciceId`

**Réponse :**
```json
{
  "success": true,
  "data": {
    "verifications": [
      {
        "type": "EQUILIBRE_BALANCE",
        "statut": "OK",
        "message": "Balance équilibrée"
      },
      {
        "type": "ECRITURES_NON_VALIDEES",
        "statut": "WARNING",
        "message": "15 écritures en brouillard",
        "details": { "nombre": 15 }
      }
    ]
  }
}
```

### 14.3 Générer les écritures de clôture

**POST** `/cloture/generer-ecritures`

### 14.4 Clôturer définitivement

**POST** `/cloture/cloturer`

### 14.5 Annuler une clôture

**POST** `/cloture/annuler`

---

## 15. PARAMÈTRES ET CONFIGURATION

### 15.1 Lister les paramètres

**GET** `/parametres`

### 15.2 Récupérer un paramètre

**GET** `/parametres/:cle`

### 15.3 Mettre à jour un paramètre

**PUT** `/parametres/:cle`

**Corps de la requête :**
```json
{
  "valeur": "nouvelle_valeur",
  "description": "Description du paramètre"
}
```

### 15.4 Paramètres par catégorie

**GET** `/parametres/categorie/:categorie`

**Catégories disponibles :**
- `GENERAL` : Paramètres généraux
- `COMPTABILITE` : Paramètres comptables
- `FISCALITE` : Paramètres fiscaux
- `REPORTING` : Paramètres de reporting

---

## 16. MOUVEMENTS ET RAPPORTS

### 16.1 Créer un mouvement automatique

**POST** `/movements/create`

### 16.2 Générer un rapport

**POST** `/reports/generate`

**Corps de la requête :**
```json
{
  "type": "BALANCE",
  "societeId": "uuid",
  "dateDebut": "2024-01-01",
  "dateFin": "2024-12-31",
  "format": "PDF",
  "options": {
    "niveau": 3,
    "inclureNonMouvementes": false
  }
}
```

### 16.3 Télécharger un rapport

**GET** `/reports/download/:reportId`

---

## 17. AMORTISSEMENTS

### 17.1 Créer un plan d'amortissement

**POST** `/depreciations`

**Corps de la requête :**
```json
{
  "libelle": "Matériel informatique",
  "compteImmobilisation": "218400",
  "compteAmortissement": "281840",
  "compteDotation": "681140",
  "valeurAcquisition": 50000.00,
  "dureeAmortissement": 3,
  "methode": "LINEAIRE",
  "dateAcquisition": "2024-01-01",
  "societeId": "uuid"
}
```

### 17.2 Calculer les dotations

**POST** `/depreciations/:id/calculer`

### 17.3 Générer les écritures d'amortissement

**POST** `/depreciations/generer-ecritures`

---

## CODES D'ERREUR SPÉCIFIQUES

### Erreurs de validation
- `VALIDATION_ERROR` : Erreur de validation des données
- `SYSCOHADA_VIOLATION` : Violation des règles SYSCOHADA
- `BALANCE_NOT_EQUILIBRATED` : Balance non équilibrée

### Erreurs métier
- `EXERCICE_CLOSED` : Exercice clôturé
- `ENTRY_ALREADY_VALIDATED` : Écriture déjà validée
- `INSUFFICIENT_PERMISSIONS` : Permissions insuffisantes
- `ACCOUNT_NOT_FOUND` : Compte non trouvé
- `JOURNAL_NOT_FOUND` : Journal non trouvé

### Erreurs d'import
- `IMPORT_FILE_INVALID` : Fichier d'import invalide
- `IMPORT_DATA_ERROR` : Erreur dans les données d'import
- `IMPORT_DUPLICATE_ENTRY` : Écriture dupliquée lors de l'import

---

## EXEMPLES D'UTILISATION

### Créer une société et son plan comptable
```bash
# 1. Créer une société
curl -X POST http://localhost:3000/api/v1/societes \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "nom": "Ma Société SARL",
    "exerciceDebut": "2024-01-01",
    "exerciceFin": "2024-12-31",
    "devise": "XOF"
  }'

# 2. Initialiser le plan comptable SYSCOHADA
curl -X POST http://localhost:3000/api/v1/plan-comptable/initialiser \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{"societeId": "uuid-de-la-societe"}'
```

### Saisir une écriture d'achat
```bash
curl -X POST http://localhost:3000/api/v1/ecritures \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "donnees": {
      "dateEcriture": "2024-01-15",
      "libelle": "Achat marchandises",
      "journalCode": "AC",
      "societeId": "uuid"
    },
    "lignes": [
      {
        "compteNumero": "601000",
        "libelle": "Achats",
        "debit": 100000,
        "credit": 0
      },
      {
        "compteNumero": "401000",
        "libelle": "Fournisseur",
        "debit": 0,
        "credit": 100000
      }
    ]
  }'
```

---

## SUPPORT ET CONTACT

Pour toute question ou support technique concernant cette API, veuillez consulter :

- **Documentation technique** : `/docs` (si disponible)
- **Tests d'intégration** : Voir les fichiers de test dans le projet
- **Logs** : Consultez les logs de l'application pour le débogage

---

## 18. ROUTES MONO-SOCIÉTÉ (NOUVELLES)

Pour simplifier l'intégration dans des architectures mono-société (1 instance = 1 société), l'API propose des routes simplifiées sous `/api/v1/mono/*` qui injectent automatiquement l'ID de société configuré.

### Configuration requise

L'ID de société doit être configuré dans les métadonnées de la clé API ou dans la configuration serveur.

### Avantages des routes mono-société :
- ✅ Pas besoin de passer `societeId` dans chaque requête
- ✅ Validation automatique de l'existence de la société
- ✅ Sécurité renforcée (accès uniquement aux données de la société configurée)
- ✅ Code frontend simplifié

### 18.1 Configuration

**GET** `/mono/config`

Récupère la configuration de la société de cette instance.

**Réponse :**
```json
{
  "success": true,
  "data": {
    "societe": {
      "id": "uuid",
      "nom": "Ma Société SARL",
      "devise": "XOF",
      "regimeFiscal": "REEL_NORMAL"
    },
    "configuration": {
      "societeId": "uuid",
      "nom": "Ma Société SARL",
      "devise": "XOF",
      "exerciceActuel": {
        "id": "uuid",
        "libelle": "Exercice 2024"
      }
    }
  }
}
```

### 18.2 Routes simplifiées disponibles

| Route originale | Route mono-société | Description |
|----------------|-------------------|-------------|
| `GET /comptes?societeId=uuid` | `GET /mono/comptes` | Liste des comptes |
| `POST /ecritures` (avec societeId) | `POST /mono/ecritures` | Créer écriture |
| `GET /dashboard/kpi?societeId=uuid` | `GET /mono/dashboard/kpi` | KPIs |
| `GET /etats/balance?societeId=uuid` | `GET /mono/etats/balance` | Balance |
| `POST /lettrage/auto` (avec societeId) | `POST /mono/lettrage/auto` | Lettrage auto |

### 18.3 Exemples d'utilisation

#### Récupérer les comptes (simplifié)
```bash
# Avant (route classique)
curl -X GET "http://localhost:3000/api/v1/comptes?societeId=uuid-societe" \
  -H "Authorization: Bearer YOUR_API_KEY"

# Maintenant (route mono-société)
curl -X GET "http://localhost:3000/api/v1/mono/comptes" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

#### Créer une écriture (simplifié)
```bash
# Avant
curl -X POST http://localhost:3000/api/v1/ecritures \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "donnees": {
      "societeId": "uuid-societe",
      "dateEcriture": "2024-01-15",
      "libelle": "Achat"
    },
    "lignes": [...]
  }'

# Maintenant
curl -X POST http://localhost:3000/api/v1/mono/ecritures \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "donnees": {
      "dateEcriture": "2024-01-15",
      "libelle": "Achat"
    },
    "lignes": [...]
  }'
```

### 18.4 Configuration de la clé API

Pour utiliser les routes mono-société, configurez l'ID de société dans les métadonnées de votre clé API :

```bash
curl -X POST http://localhost:3000/api/v1/api-keys \
  -H "Authorization: Bearer ADMIN_API_KEY" \
  -d '{
    "name": "Frontend Django - Ma Société",
    "permissions": ["read", "write"],
    "metadata": {
      "societeId": "uuid-de-ma-societe",
      "type": "mono-societe",
      "frontend": "django"
    }
  }'
```

---

*Cette documentation couvre la version 1.0.0 de l'API SYSCOHADA. Elle est mise à jour régulièrement selon les évolutions du système.*

