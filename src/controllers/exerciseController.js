'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');
const ExerciseService = require('../services/exerciseService');

/**
 * Controller pour la gestion des exercices comptables et clôtures
 * Conforme aux normes SYSCOHADA
 */
class ExerciseController {
  constructor() {
    this.exerciseService = null;
  }

  /**
   * Initialise le controller avec les modèles
   * @param {Object} models - Modèles Sequelize
   */
  init(models) {
    this.models = models;
    this.exerciseService = new ExerciseService(models);
  }

  /**
   * Valide les pré-requis pour la clôture
   * POST /api/v1/exercises/:id/validate-closure
   */
  async validerPrerequisCloture(req, res, next) {
    try {
      const { id: exerciceId } = req.params;

      const validation = await this.exerciseService.validerPrerequisCloture(exerciceId);

      logger.info('Validation pré-requis clôture', {
        exerciceId,
        validationGlobale: validation.validationGlobale,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Validation des pré-requis terminée',
        data: validation
      });

    } catch (error) {
      logger.error('Erreur validation pré-requis clôture', {
        error: error.message,
        exerciceId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Lance la clôture d'exercice
   * POST /api/v1/exercises/:id/close
   */
  async cloturerExercice(req, res, next) {
    try {
      const { id: exerciceId } = req.params;
      const options = req.body;

      // Validation de l'exercice
      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId);
      if (!exercice) {
        throw new NotFoundError('Exercice inexistant');
      }

      if (exercice.statut === 'CLOTURE') {
        return res.status(400).json({
          success: false,
          message: 'Exercice déjà clôturé',
          code: 'EXERCICE_DEJA_CLOTURE'
        });
      }

      // Lancement de la clôture
      const resultatCloture = await this.exerciseService.cloturerExercice(exerciceId, options);

      logger.info('Clôture exercice terminée', {
        exerciceId,
        resultatNet: resultatCloture.resultatCalcul.resultatNet,
        nombreEtapes: resultatCloture.etapes.length,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Clôture d\'exercice terminée avec succès',
        data: resultatCloture
      });

    } catch (error) {
      logger.error('Erreur clôture exercice', {
        error: error.message,
        exerciceId: req.params.id,
        options: req.body,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Rouvre un exercice clôturé
   * POST /api/v1/exercises/:id/reopen
   */
  async rouvrirExercice(req, res, next) {
    try {
      const { id: exerciceId } = req.params;

      const resultatReouverture = await this.exerciseService.rouvrirExercice(exerciceId);

      logger.info('Exercice rouvert', {
        exerciceId,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Exercice rouvert avec succès',
        data: resultatReouverture
      });

    } catch (error) {
      logger.error('Erreur réouverture exercice', {
        error: error.message,
        exerciceId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère le rapport de clôture
   * GET /api/v1/exercises/:id/closure-report
   */
  async genererRapportCloture(req, res, next) {
    try {
      const { id: exerciceId } = req.params;
      const { format = 'JSON' } = req.query;

      const rapport = await this.exerciseService.genererRapportCloture(exerciceId);

      // Génération selon le format demandé
      if (format.toUpperCase() === 'PDF') {
        // TODO: Implémenter la génération PDF
        return res.status(501).json({
          success: false,
          message: 'Génération PDF non encore implémentée',
          code: 'FORMAT_NON_IMPLEMENTE'
        });
      }

      logger.info('Rapport de clôture généré', {
        exerciceId,
        format,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Rapport de clôture généré avec succès',
        data: rapport
      });

    } catch (error) {
      logger.error('Erreur génération rapport clôture', {
        error: error.message,
        exerciceId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Récupère le statut de clôture d'un exercice
   * GET /api/v1/exercises/:id/closure-status
   */
  async obtenirStatutCloture(req, res, next) {
    try {
      const { id: exerciceId } = req.params;

      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId, {
        include: [{ model: this.models.Societe, as: 'societe' }]
      });

      if (!exercice) {
        throw new NotFoundError('Exercice inexistant');
      }

      let validation = null;
      let peutCloture = false;

      if (exercice.statut === 'OUVERT') {
        validation = await this.exerciseService.validerPrerequisCloture(exerciceId);
        peutCloture = validation.validationGlobale;
      }

      const statut = {
        exercice: {
          id: exercice.id,
          libelle: exercice.libelle,
          dateDebut: exercice.dateDebut,
          dateFin: exercice.dateFin,
          statut: exercice.statut,
          dateCloture: exercice.dateCloture,
          resultatExercice: exercice.resultatExercice
        },
        peutCloture,
        peutRouvrir: exercice.statut === 'CLOTURE',
        validation,
        actions: this.determinerActionsDisponibles(exercice, peutCloture)
      };

      logger.info('Statut clôture récupéré', {
        exerciceId,
        statut: exercice.statut,
        peutCloture,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Statut de clôture récupéré avec succès',
        data: statut
      });

    } catch (error) {
      logger.error('Erreur récupération statut clôture', {
        error: error.message,
        exerciceId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Liste les exercices avec leur statut de clôture
   * GET /api/v1/exercises/closure-overview
   */
  async obtenirVueEnsembleClotures(req, res, next) {
    try {
      const { societeId } = req.query;

      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'ID société obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      const exercices = await this.models.ExerciceComptable.findAll({
        where: { societeId },
        include: [{ model: this.models.Societe, as: 'societe' }],
        order: [['dateDebut', 'DESC']]
      });

      const vueEnsemble = [];

      for (const exercice of exercices) {
        let validation = null;
        let peutCloture = false;

        if (exercice.statut === 'OUVERT') {
          try {
            validation = await this.exerciseService.validerPrerequisCloture(exercice.id);
            peutCloture = validation.validationGlobale;
          } catch (error) {
            logger.warn('Erreur validation exercice', {
              exerciceId: exercice.id,
              error: error.message
            });
          }
        }

        vueEnsemble.push({
          exercice: {
            id: exercice.id,
            libelle: exercice.libelle,
            dateDebut: exercice.dateDebut,
            dateFin: exercice.dateFin,
            statut: exercice.statut,
            dateCloture: exercice.dateCloture,
            resultatExercice: exercice.resultatExercice
          },
          peutCloture,
          peutRouvrir: exercice.statut === 'CLOTURE',
          validation: validation ? {
            validationGlobale: validation.validationGlobale,
            nombreRecommandations: validation.recommandations.length
          } : null,
          actions: this.determinerActionsDisponibles(exercice, peutCloture)
        });
      }

      // Statistiques globales
      const statistiques = {
        total: exercices.length,
        ouverts: exercices.filter(e => e.statut === 'OUVERT').length,
        clotures: exercices.filter(e => e.statut === 'CLOTURE').length,
        brouillons: exercices.filter(e => e.statut === 'BROUILLON').length,
        pretsPourCloture: vueEnsemble.filter(v => v.peutCloture).length
      };

      logger.info('Vue ensemble clôtures récupérée', {
        societeId,
        nombreExercices: exercices.length,
        pretsPourCloture: statistiques.pretsPourCloture,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Vue d\'ensemble des clôtures récupérée avec succès',
        data: {
          exercices: vueEnsemble,
          statistiques
        }
      });

    } catch (error) {
      logger.error('Erreur vue ensemble clôtures', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Simule une clôture d'exercice
   * POST /api/v1/exercises/:id/simulate-closure
   */
  async simulerCloture(req, res, next) {
    try {
      const { id: exerciceId } = req.params;

      const exercice = await this.models.ExerciceComptable.findByPk(exerciceId);
      if (!exercice) {
        throw new NotFoundError('Exercice inexistant');
      }

      if (exercice.statut === 'CLOTURE') {
        return res.status(400).json({
          success: false,
          message: 'Exercice déjà clôturé',
          code: 'EXERCICE_DEJA_CLOTURE'
        });
      }

      // Validation des pré-requis
      const validation = await this.exerciseService.validerPrerequisCloture(exerciceId);

      // Calcul du résultat (sans transaction)
      const resultatCalcul = await this.exerciseService.calculerResultatExercice(exercice);

      // Simulation des étapes
      const simulation = {
        exercice: {
          id: exercice.id,
          libelle: exercice.libelle,
          dateDebut: exercice.dateDebut,
          dateFin: exercice.dateFin
        },
        validation,
        resultatCalcul,
        etapesSimulees: [
          {
            etape: 'CALCUL_RESULTAT',
            description: 'Calcul du résultat de l\'exercice',
            resultat: resultatCalcul,
            dureeEstimee: '2 minutes'
          },
          {
            etape: 'ECRITURES_RESULTAT',
            description: 'Génération des écritures de détermination du résultat',
            nombreEcritures: 2,
            dureeEstimee: '1 minute'
          },
          {
            etape: 'ECRITURES_CLOTURE',
            description: 'Génération des écritures de clôture',
            nombreEcritures: 1,
            dureeEstimee: '3 minutes'
          },
          {
            etape: 'EXERCICE_SUIVANT',
            description: 'Création de l\'exercice suivant et écritures de réouverture',
            dureeEstimee: '2 minutes'
          },
          {
            etape: 'FINALISATION',
            description: 'Finalisation de la clôture',
            dureeEstimee: '1 minute'
          }
        ],
        dureeEstimeeTotal: '9 minutes',
        recommandations: validation.recommandations
      };

      logger.info('Simulation clôture générée', {
        exerciceId,
        resultatNet: resultatCalcul.resultatNet,
        validationGlobale: validation.validationGlobale,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Simulation de clôture générée avec succès',
        data: simulation
      });

    } catch (error) {
      logger.error('Erreur simulation clôture', {
        error: error.message,
        exerciceId: req.params.id,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  // === MÉTHODES UTILITAIRES ===

  /**
   * Détermine les actions disponibles pour un exercice
   */
  determinerActionsDisponibles(exercice, peutCloture) {
    const actions = [];

    switch (exercice.statut) {
      case 'BROUILLON':
        actions.push({
          code: 'OUVRIR',
          libelle: 'Ouvrir l\'exercice',
          description: 'Ouvrir l\'exercice pour commencer la saisie'
        });
        break;

      case 'OUVERT':
        if (peutCloture) {
          actions.push({
            code: 'CLOTURER',
            libelle: 'Clôturer l\'exercice',
            description: 'Lancer le processus de clôture'
          });
        }
        actions.push({
          code: 'SIMULER_CLOTURE',
          libelle: 'Simuler la clôture',
          description: 'Simuler le processus de clôture sans l\'exécuter'
        });
        actions.push({
          code: 'VALIDER_PREREQUIS',
          libelle: 'Valider les pré-requis',
          description: 'Vérifier les conditions de clôture'
        });
        break;

      case 'CLOTURE':
        actions.push({
          code: 'ROUVRIR',
          libelle: 'Rouvrir l\'exercice',
          description: 'Rouvrir l\'exercice pour modifications'
        });
        actions.push({
          code: 'RAPPORT_CLOTURE',
          libelle: 'Rapport de clôture',
          description: 'Générer le rapport de clôture'
        });
        break;
    }

    return actions;
  }
}

module.exports = new ExerciseController();