# Guide Complet - Backend Node.js pour Logiciel de Comptabilité SYSCOHADA

## Table des Matières
1. [Architecture Backend](#architecture-backend)
2. [Modèles de Données](#modèles-de-données)
3. [Controllers Métier](#controllers-métier)
4. [Logiques Métier](#logiques-métier-importantes)
5. [API Endpoints](#api-endpoints)
6. [Contraintes Techniques](#contraintes-techniques)
7. [Structure des Fichiers](#structure-des-fichiers)
8. [Installation et Configuration](#installation-et-configuration)

## Architecture Backend

### Structure du projet recommandée
```
syscohada-backend/
├── src/
│   ├── models/          # Modèles de données (Sequelize/Mongoose)
│   ├── routes/          # Routes API Express
│   ├── controllers/     # Logique métier
│   ├── middleware/      # Authentification, validation, logs
│   ├── utils/           # Fonctions utilitaires
│   ├── services/        # Services métier (calculs, rapports)
│   └── config/          # Configuration DB, JWT, etc.
├── migrations/          # Scripts de création/modification DB
├── seeds/              # Données initiales (plan comptable SYSCOHADA)
├── tests/              # Tests unitaires et d'intégration
├── docs/               # Documentation API
└── uploads/            # Fichiers uploadés (justificatifs)
```

### Technologies recommandées
- **Framework** : Express.js
- **Base de données** : PostgreSQL (support décimal précis)
- **ORM** : Sequelize ou Prisma
- **Authentification** : JWT + bcrypt
- **Validation** : Joi ou Yup
- **Documentation** : Swagger/OpenAPI
- **Tests** : Jest + Supertest

## Modèles de Données

### 1. Société (Company)
```javascript
// models/Company.js
{
  id: UUID PRIMARY KEY,
  nom: VARCHAR(255) NOT NULL,
  adresse: TEXT,
  telephone: VARCHAR(50),
  email: VARCHAR(255),
  forme_juridique: VARCHAR(100), // SARL, SA, EI, etc.
  numero_rccm: VARCHAR(100),     // Registre Commerce
  numero_contribuable: VARCHAR(100), // N° fiscal
  exercice_debut: DATE,          // 01/01/2024
  exercice_fin: DATE,            // 31/12/2024
  monnaie: VARCHAR(3) DEFAULT 'XOF',
  regime_fiscal: ENUM('REEL_NORMAL', 'REEL_SIMPLIFIE', 'SYNTHETIQUE'),
  statut: ENUM('ACTIF', 'SUSPENDU', 'FERME') DEFAULT 'ACTIF',
  logo_url: VARCHAR(500),
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
}

// Index
CREATE INDEX idx_company_statut ON companies(statut);
```

### 2. Plan Comptable (Accounts)
```javascript
// models/Account.js
{
  id: UUID PRIMARY KEY,
  numero: VARCHAR(10) NOT NULL,     // "101", "21310", "6011001"
  intitule: VARCHAR(255) NOT NULL,  // "Capital social"
  classe: INTEGER,                  // 1-8
  nature: ENUM('ACTIF', 'PASSIF', 'CHARGE', 'PRODUIT', 'RESULTAT'),
  sens_normal: ENUM('DEBIT', 'CREDIT'),
  type_compte: ENUM('COLLECTIF', 'INDIVIDUEL', 'AUXILIAIRE'),
  compte_parent: VARCHAR(10),       // Compte de niveau supérieur
  niveau: INTEGER,                  // 1,2,3,4 (profondeur hiérarchique)
  actif: BOOLEAN DEFAULT TRUE,
  bloque_saisie: BOOLEAN DEFAULT FALSE, // Comptes de totalisation
  tva_applicable: BOOLEAN DEFAULT FALSE,
  tiers_obligatoire: BOOLEAN DEFAULT FALSE, // Pour comptes 411, 401
  echeance_obligatoire: BOOLEAN DEFAULT FALSE,
  company_id: UUID REFERENCES companies(id),
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP,
  
  // Contraintes
  UNIQUE(numero, company_id)
}

// Index
CREATE INDEX idx_account_numero ON accounts(numero);
CREATE INDEX idx_account_classe ON accounts(classe);
CREATE INDEX idx_account_company ON accounts(company_id);
```

### 3. Journaux (Journals)
```javascript
// models/Journal.js
{
  id: UUID PRIMARY KEY,
  code: VARCHAR(10) NOT NULL,       // "VE", "AC", "BQ", "OD"
  libelle: VARCHAR(255) NOT NULL,   // "Journal des ventes"
  type: ENUM('VENTE', 'ACHAT', 'BANQUE', 'CAISSE', 'OD'),
  compte_contrepartie: VARCHAR(10), // Compte par défaut
  derniere_piece: INTEGER DEFAULT 0, // Auto-incrément numéro pièce
  prefixe_piece: VARCHAR(10),       // "VE2024-", "FAC-"
  protection_saisie: BOOLEAN DEFAULT FALSE, // Journal système
  actif: BOOLEAN DEFAULT TRUE,
  company_id: UUID REFERENCES companies(id),
  created_at: TIMESTAMP,
  
  // Contraintes
  UNIQUE(code, company_id)
}
```

### 4. Tiers - Clients/Fournisseurs (Parties)
```javascript
// models/Party.js
{
  id: UUID PRIMARY KEY,
  code: VARCHAR(20) NOT NULL,       // "CLI001", "FOUR001"
  nom: VARCHAR(255) NOT NULL,
  type: ENUM('CLIENT', 'FOURNISSEUR', 'CLIENT_FOURNISSEUR', 'AUTRE'),
  civilite: ENUM('M', 'MME', 'MLLE', 'SA', 'SARL'),
  adresse: TEXT,
  ville: VARCHAR(100),
  pays: VARCHAR(100) DEFAULT 'Togo',
  telephone: VARCHAR(50),
  email: VARCHAR(255),
  site_web: VARCHAR(255),
  
  // Informations comptables
  compte_comptable: VARCHAR(10),    // 411001 ou 401001
  conditions_paiement: INTEGER DEFAULT 30, // Délai en jours
  plafond_credit: DECIMAL(15,2) DEFAULT 0,
  taux_escompte: DECIMAL(5,2) DEFAULT 0,
  
  // Informations fiscales
  numero_contribuable: VARCHAR(100),
  assujetti_tva: BOOLEAN DEFAULT TRUE,
  
  // Statut
  actif: BOOLEAN DEFAULT TRUE,
  date_creation: DATE,
  
  // Relations
  company_id: UUID REFERENCES companies(id),
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP,
  
  // Contraintes
  UNIQUE(code, company_id)
}

// Index
CREATE INDEX idx_party_type ON parties(type);
CREATE INDEX idx_party_compte ON parties(compte_comptable);
```

### 5. Écritures Comptables (Entries)
```javascript
// models/Entry.js
{
  id: UUID PRIMARY KEY,
  date_ecriture: DATE NOT NULL,     // Date comptable
  date_piece: DATE,                 // Date du document source
  date_echeance: DATE,              // Pour créances/dettes
  
  // Références
  journal_id: UUID REFERENCES journals(id),
  numero_piece: VARCHAR(50),        // N° facture, chèque, etc.
  reference_externe: VARCHAR(100),  // Référence client/fournisseur
  
  // Description
  libelle: TEXT NOT NULL,           // Description de l'opération
  observations: TEXT,
  
  // Montants (contrôle)
  total_debit: DECIMAL(15,2) NOT NULL DEFAULT 0,
  total_credit: DECIMAL(15,2) NOT NULL DEFAULT 0,
  
  // Workflow
  statut: ENUM('BROUILLARD', 'VALIDE', 'CLOTURE', 'ANNULE') DEFAULT 'BROUILLARD',
  date_validation: TIMESTAMP,
  user_validation_id: UUID,
  
  // Lettrage
  lettrage_global: VARCHAR(10),     // Code de lettrage
  lettre: BOOLEAN DEFAULT FALSE,    // Entièrement lettré
  
  // Métadonnées
  user_id: UUID NOT NULL,           // Qui a saisi
  company_id: UUID REFERENCES companies(id),
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP,
  
  // Contraintes
  CHECK (total_debit = total_credit),
  UNIQUE(journal_id, numero_piece)
}

// Index
CREATE INDEX idx_entry_date ON entries(date_ecriture);
CREATE INDEX idx_entry_journal ON entries(journal_id);
CREATE INDEX idx_entry_statut ON entries(statut);
CREATE INDEX idx_entry_company ON entries(company_id);
```

### 6. Lignes d'écriture (EntryLines)
```javascript
// models/EntryLine.js
{
  id: UUID PRIMARY KEY,
  entry_id: UUID REFERENCES entries(id) ON DELETE CASCADE,
  ligne_numero: INTEGER,            // Ordre dans l'écriture
  
  // Imputation comptable
  compte_numero: VARCHAR(10) NOT NULL,
  compte_libelle: VARCHAR(255),     // Cache pour performance
  
  // Montants
  libelle: VARCHAR(255),            // Libellé de ligne
  debit: DECIMAL(15,2) DEFAULT 0,
  credit: DECIMAL(15,2) DEFAULT 0,
  
  // Tiers
  tiers_id: UUID REFERENCES parties(id),
  tiers_nom: VARCHAR(255),          // Cache
  
  // Échéances
  date_echeance: DATE,
  mode_reglement: ENUM('ESPECES', 'CHEQUE', 'VIREMENT', 'CARTE', 'TRAITE'),
  
  // Lettrage
  lettrage: VARCHAR(10),            // A, B, C, AA, AB...
  date_lettrage: DATE,
  
  // Analytique (optionnel)
  section_analytique: VARCHAR(20),
  
  // Quantités (pour stocks)
  quantite: DECIMAL(12,3),
  unite: VARCHAR(10),
  prix_unitaire: DECIMAL(12,2),
  
  // Multi-devises (optionnel)
  devise: VARCHAR(3) DEFAULT 'XOF',
  cours: DECIMAL(12,6) DEFAULT 1,
  montant_devise: DECIMAL(15,2),
  
  created_at: TIMESTAMP,
  
  // Contraintes
  CHECK (debit >= 0 AND credit >= 0),
  CHECK (debit = 0 OR credit = 0), -- Pas les deux à la fois
  CHECK (debit > 0 OR credit > 0)  -- Au moins un des deux
}

// Index
CREATE INDEX idx_entryline_entry ON entry_lines(entry_id);
CREATE INDEX idx_entryline_compte ON entry_lines(compte_numero);
CREATE INDEX idx_entryline_tiers ON entry_lines(tiers_id);
CREATE INDEX idx_entryline_lettrage ON entry_lines(lettrage);
CREATE INDEX idx_entryline_echeance ON entry_lines(date_echeance);
```

### 7. Paramètres système (Settings)
```javascript
// models/Setting.js
{
  id: UUID PRIMARY KEY,
  cle: VARCHAR(100) NOT NULL,       // "compte_achat_defaut"
  valeur: TEXT,                     // "601000"
  type: ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON'),
  description: TEXT,
  category: VARCHAR(50),            // "ACHAT", "VENTE", "GENERAL"
  company_id: UUID REFERENCES companies(id),
  
  UNIQUE(cle, company_id)
}
```

### 8. Amortissements (Depreciations)
```javascript
// models/Depreciation.js
{
  id: UUID PRIMARY KEY,
  immobilisation_id: UUID,          // Référence bien
  compte_immobilisation: VARCHAR(10), // 21xxx
  compte_amortissement: VARCHAR(10),  // 28xxx
  compte_dotation: VARCHAR(10),       // 681xx
  
  designation: VARCHAR(255),
  date_acquisition: DATE,
  date_mise_en_service: DATE,
  valeur_origine: DECIMAL(15,2),
  valeur_residuelle: DECIMAL(15,2) DEFAULT 0,
  duree_amortissement: INTEGER,     // En mois
  methode: ENUM('LINEAIRE', 'DEGRESSIF', 'UNITE_OEUVRE'),
  coefficient_degressif: DECIMAL(5,2),
  
  // État
  totaL_amorti: DECIMAL(15,2) DEFAULT 0,
  valeur_nette: DECIMAL(15,2),
  termine: BOOLEAN DEFAULT FALSE,
  
  company_id: UUID REFERENCES companies(id),
  created_at: TIMESTAMP
}
```

## Controllers Métier

### 1. CompanyController
```javascript
// controllers/CompanyController.js
class CompanyController {
  
  // Créer une nouvelle société
  async create(req, res) {
    // 1. Valider les données
    // 2. Créer la société
    // 3. Initialiser le plan comptable SYSCOHADA
    // 4. Créer les journaux de base
    // 5. Paramètres par défaut
  }
  
  // Modifier les informations société
  async update(req, res) {
    // Contrôler les champs modifiables
    // Historiser les changements importants
  }
  
  // Clôturer un exercice
  async closeExercise(req, res) {
    // 1. Vérifier que toutes écritures sont validées
    // 2. Calculer le résultat
    // 3. Écriture de clôture (comptes 6 et 7 → 131)
    // 4. Écriture de réouverture (bilan)
    // 5. Changer exercice
  }
}
```

### 2. AccountController
```javascript
// controllers/AccountController.js
class AccountController {
  
  // Initialiser le plan comptable SYSCOHADA standard
  async initializeSyscohada(companyId) {
    // Charger le référentiel SYSCOHADA depuis seeds/
    // Créer tous les comptes de base
    // Respecter la hiérarchie
  }
  
  // Ajouter un compte personnalisé
  async create(req, res) {
    // 1. Valider le numéro de compte
    // 2. Vérifier l'unicité
    // 3. Contrôler la cohérence avec la classe
    // 4. Créer le compte
  }
  
  // Rechercher des comptes
  async search(req, res) {
    // Recherche par numéro, intitulé
    // Filtrage par classe
    // Pagination
  }
  
  // Valider la cohérence d'un numéro de compte
  validateAccountNumber(numero, classe) {
    // Le premier chiffre doit correspondre à la classe
    // Longueur appropriée selon le niveau
    // Pas de doublons
  }
}
```

### 3. JournalController
```javascript
// controllers/JournalController.js
class JournalController {
  
  // Créer les journaux de base
  async initializeDefault(companyId) {
    const defaultJournals = [
      { code: 'VE', libelle: 'Journal des ventes', type: 'VENTE' },
      { code: 'AC', libelle: 'Journal des achats', type: 'ACHAT' },
      { code: 'BQ', libelle: 'Journal de banque', type: 'BANQUE' },
      { code: 'CA', libelle: 'Journal de caisse', type: 'CAISSE' },
      { code: 'OD', libelle: 'Opérations diverses', type: 'OD' }
    ];
    // Créer chaque journal
  }
  
  // Générer le prochain numéro de pièce
  async getNextPieceNumber(journalId) {
    // Incrémenter le compteur
    // Formatter avec préfixe si configuré
    // Gérer l'année dans la numérotation
  }
}
```

### 4. EntryController
```javascript
// controllers/EntryController.js
class EntryController {
  
  // Saisir une écriture manuelle
  async create(req, res) {
    const transaction = await db.transaction();
    try {
      // 1. Valider l'équilibre débit/crédit
      // 2. Contrôler l'existence des comptes
      // 3. Vérifier la date (exercice ouvert)
      // 4. Créer l'en-tête d'écriture
      // 5. Créer les lignes
      // 6. Calculer les totaux
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  // Valider une écriture (passage brouillard → validé)
  async validate(req, res) {
    // 1. Vérifier le statut actuel
    // 2. Contrôles de cohérence
    // 3. Marquer comme validé
    // 4. Tracer l'utilisateur et la date
  }
  
  // Modifier une écriture en brouillard
  async update(req, res) {
    // Autorisé seulement si statut = BROUILLARD
    // Recalculer les totaux
  }
  
  // Supprimer une écriture
  async delete(req, res) {
    // Autorisé seulement si BROUILLARD et non lettrée
    // Supprimer en cascade les lignes
  }
  
  // Contrôles métier
  validateEntry(entryData) {
    const totalDebit = entryData.lines.reduce((sum, line) => sum + line.debit, 0);
    const totalCredit = entryData.lines.reduce((sum, line) => sum + line.credit, 0);
    
    if (Math.abs(totalDebit - totalCredit) > 0.01) {
      throw new Error('L\'écriture n\'est pas équilibrée');
    }
    
    // Autres contrôles...
  }
}
```

### 5. MovementController (Écritures automatiques)
```javascript
// controllers/MovementController.js
class MovementController {
  
  // Écriture de vente automatique
  async createSale(req, res) {
    const { clientId, montantHT, tauxTVA, numeroFacture } = req.body;
    
    const montantTVA = montantHT * (tauxTVA / 100);
    const montantTTC = montantHT + montantTVA;
    
    const entry = {
      date_ecriture: new Date(),
      journal_id: await this.getJournalByCode('VE'),
      numero_piece: numeroFacture,
      libelle: `Facture ${numeroFacture}`,
      lines: [
        {
          compte_numero: await this.getClientAccount(clientId),
          libelle: 'Vente de marchandises',
          debit: montantTTC,
          credit: 0,
          tiers_id: clientId
        },
        {
          compte_numero: '701000', // Ventes de marchandises
          libelle: 'Vente de marchandises',
          debit: 0,
          credit: montantHT
        },
        {
          compte_numero: '445510', // TVA collectée
          libelle: 'TVA sur ventes',
          debit: 0,
          credit: montantTVA
        }
      ]
    };
    
    return await this.entryController.create({ body: entry }, res);
  }
  
  // Écriture d'achat automatique
  async createPurchase(req, res) {
    // Logique similaire mais inversée
    // Débit 601xxx + 445520 / Crédit 401xxx
  }
  
  // Écriture de paiement
  async createPayment(req, res) {
    // Débit compte fournisseur / Crédit compte banque
    // Gestion des escomptes si applicable
  }
  
  // Écriture d'encaissement
  async createReceipt(req, res) {
    // Débit compte banque / Crédit compte client
  }
}
```

### 6. ReportController
```javascript
// controllers/ReportController.js
class ReportController {
  
  // Journal détaillé
  async getJournal(req, res) {
    const { journalId, dateDebut, dateFin } = req.params;
    
    const entries = await Entry.findAll({
      where: {
        journal_id: journalId,
        date_ecriture: {
          [Op.between]: [dateDebut, dateFin]
        }
      },
      include: [{ model: EntryLine }],
      order: [['date_ecriture', 'ASC'], ['numero_piece', 'ASC']]
    });
    
    return res.json(entries);
  }
  
  // Grand livre d'un compte
  async getLedger(req, res) {
    const { compteNumero, dateDebut, dateFin } = req.params;
    
    const lines = await EntryLine.findAll({
      where: {
        compte_numero: compteNumero
      },
      include: [{
        model: Entry,
        where: {
          date_ecriture: {
            [Op.between]: [dateDebut, dateFin]
          },
          statut: 'VALIDE'
        }
      }],
      order: [['entry', 'date_ecriture', 'ASC']]
    });
    
    // Calculer le solde progressif
    let solde = 0;
    const ledgerLines = lines.map(line => {
      const natureCcompte = this.getAccountNature(line.compte_numero);
      if (dotationAnnuelle > 0) {
        // Créer l'écriture d'amortissement
        const entry = {
          date_ecriture: new Date(`${exercice}-12-31`),
          journal_id: await this.getJournalByCode('OD'),
          numero_piece: `AMORT-${amort.id}-${exercice}`,
          libelle: `Dotation amortissement ${amort.designation}`,
          lines: [
            {
              compte_numero: amort.compte_dotation,
              libelle: `Dotation amortissement ${amort.designation}`,
              debit: dotationAnnuelle,
              credit: 0
            },
            {
              compte_numero: amort.compte_amortissement,
              libelle: `Amortissement ${amort.designation}`,
              debit: 0,
              credit: dotationAnnuelle
            }
          ]
        };
        
        await this.entryController.create({ body: entry });
        
        // Mettre à jour le cumul
        await amort.update({
          total_amorti: amort.total_amorti + dotationAnnuelle,
          valeur_nette: amort.valeur_origine - (amort.total_amorti + dotationAnnuelle)
        });
      }
    }
  }
}
```

## Logiques Métier Importantes

### 1. Validation des Écritures Comptables

```javascript
// services/EntryValidationService.js
class EntryValidationService {
  
  static validateEntry(entryData, companyId) {
    const errors = [];
    
    // 1. Équilibre débit/crédit
    const totalDebit = entryData.lines.reduce((sum, line) => sum + parseFloat(line.debit || 0), 0);
    const totalCredit = entryData.lines.reduce((sum, line) => sum + parseFloat(line.credit || 0), 0);
    
    if (Math.abs(totalDebit - totalCredit) > 0.01) {
      errors.push(`Écriture non équilibrée: Débit=${totalDebit}, Crédit=${totalCredit}`);
    }
    
    // 2. Date dans l'exercice ouvert
    const exercice = await this.getExerciceOuvert(companyId);
    const dateEcriture = new Date(entryData.date_ecriture);
    
    if (dateEcriture < exercice.debut || dateEcriture > exercice.fin) {
      errors.push('Date d\'écriture hors exercice comptable ouvert');
    }
    
    // 3. Existence des comptes
    for (const line of entryData.lines) {
      const compte = await Account.findOne({
        where: { numero: line.compte_numero, company_id: companyId }
      });
      
      if (!compte) {
        errors.push(`Compte ${line.compte_numero} inexistant`);
      } else if (compte.bloque_saisie) {
        errors.push(`Compte ${line.compte_numero} bloqué en saisie`);
      }
    }
    
    // 4. Montants positifs
    entryData.lines.forEach((line, index) => {
      if (line.debit < 0 || line.credit < 0) {
        errors.push(`Ligne ${index + 1}: montants négatifs interdits`);
      }
      
      if (line.debit > 0 && line.credit > 0) {
        errors.push(`Ligne ${index + 1}: débit ET crédit saisis`);
      }
      
      if (line.debit === 0 && line.credit === 0) {
        errors.push(`Ligne ${index + 1}: aucun montant saisi`);
      }
    });
    
    // 5. Numéro de pièce unique dans le journal
    if (entryData.numero_piece) {
      const existing = await Entry.findOne({
        where: {
          journal_id: entryData.journal_id,
          numero_piece: entryData.numero_piece,
          id: { [Op.ne]: entryData.id || null }
        }
      });
      
      if (existing) {
        errors.push(`Numéro de pièce ${entryData.numero_piece} déjà utilisé dans ce journal`);
      }
    }
    
    return errors;
  }
  
  // Validation spécifique par type de mouvement
  static validateSaleEntry(saleData) {
    const errors = [];
    
    if (!saleData.client_id) {
      errors.push('Client obligatoire pour une vente');
    }
    
    if (saleData.montant_ht <= 0) {
      errors.push('Montant HT doit être positif');
    }
    
    if (saleData.taux_tva < 0 || saleData.taux_tva > 100) {
      errors.push('Taux TVA invalide');
    }
    
    return errors;
  }
}
```

### 2. Calculs Automatiques

```javascript
// services/AccountingCalculationService.js
class AccountingCalculationService {
  
  // Calculer le solde d'un compte
  static async calculateAccountBalance(compteNumero, dateDebut, dateFin, companyId) {
    const result = await db.query(`
      SELECT 
        SUM(el.debit) as total_debit,
        SUM(el.credit) as total_credit,
        a.sens_normal,
        a.nature
      FROM entry_lines el
      JOIN entries e ON el.entry_id = e.id
      JOIN accounts a ON el.compte_numero = a.numero
      WHERE el.compte_numero = :compte
        AND e.date_ecriture BETWEEN :dateDebut AND :dateFin
        AND e.company_id = :companyId
        AND e.statut = 'VALIDE'
      GROUP BY a.sens_normal, a.nature
    `, {
      replacements: { compte: compteNumero, dateDebut, dateFin, companyId },
      type: QueryTypes.SELECT
    });
    
    if (!result.length) return 0;
    
    const { total_debit, total_credit, sens_normal } = result[0];
    
    // Calcul selon la nature du compte
    if (sens_normal === 'DEBIT') {
      return total_debit - total_credit; // Actif, charges
    } else {
      return total_credit - total_debit; // Passif, produits
    }
  }
  
  // Calculer le résultat de l'exercice
  static async calculateExerciseResult(exerciceDebut, exerciceFin, companyId) {
    // Total des produits (classe 7)
    const produits = await db.query(`
      SELECT SUM(el.credit) - SUM(el.debit) as total
      FROM entry_lines el
      JOIN entries e ON el.entry_id = e.id
      WHERE el.compte_numero LIKE '7%'
        AND e.date_ecriture BETWEEN :dateDebut AND :dateFin
        AND e.company_id = :companyId
        AND e.statut = 'VALIDE'
    `, {
      replacements: { dateDebut: exerciceDebut, dateFin: exerciceFin, companyId },
      type: QueryTypes.SELECT
    });
    
    // Total des charges (classe 6)
    const charges = await db.query(`
      SELECT SUM(el.debit) - SUM(el.credit) as total
      FROM entry_lines el
      JOIN entries e ON el.entry_id = e.id
      WHERE el.compte_numero LIKE '6%'
        AND e.date_ecriture BETWEEN :dateDebut AND :dateFin
        AND e.company_id = :companyId
        AND e.statut = 'VALIDE'
    `, {
      replacements: { dateDebut: exerciceDebut, dateFin: exerciceFin, companyId },
      type: QueryTypes.SELECT
    });
    
    const totalProduits = produits[0]?.total || 0;
    const totalCharges = charges[0]?.total || 0;
    
    return {
      produits: totalProduits,
      charges: totalCharges,
      resultat: totalProduits - totalCharges,
      benefice: totalProduits > totalCharges,
      perte: totalProduits < totalCharges
    };
  }
  
  // Calculer les ratios financiers
  static async calculateFinancialRatios(dateClotüre, companyId) {
    const bilan = await this.getBalanceSheet(dateClotüre, companyId);
    
    // Ratios de liquidité
    const activeCcirculant = this.sumAccounts(bilan.actif.circulant);
    const dettesCourtTerme = this.sumAccounts(bilan.passif.dettes, '4%');
    const liquiditeGenerale = activeCcirculant / dettesCourtTerme;
    
    // Ratio d'autonomie financière
    const capitauxPropres = this.sumAccounts(bilan.passif.capitaux_propres);
    const totalPassif = this.sumAccounts(bilan.passif.capitaux_propres) + 
                       this.sumAccounts(bilan.passif.dettes);
    const autonomieFinanciere = capitauxPropres / totalPassif;
    
    return {
      liquidite_generale: liquiditeGenerale,
      autonomie_financiere: autonomieFinanciere,
      // Autres ratios...
    };
  }
}
```

### 3. Gestion des Exercices Comptables

```javascript
// services/ExerciseService.js
class ExerciseService {
  
  // Clôturer un exercice
  static async closeExercise(companyId, exerciceFin) {
    const transaction = await db.transaction();
    
    try {
      // 1. Vérifier les pré-requis
      await this.validateClosurePrerequisites(companyId, exerciceFin);
      
      // 2. Calculer le résultat
      const resultat = await AccountingCalculationService.calculateExerciseResult(
        `${exerciceFin.getFullYear()}-01-01`,
        exerciceFin,
        companyId
      );
      
      // 3. Écriture de détermination du résultat
      await this.createResultEntry(resultat, exerciceFin, companyId, transaction);
      
      // 4. Écritures de clôture (soldes → bilan)
      await this.createClosureEntries(exerciceFin, companyId, transaction);
      
      // 5. Écritures de réouverture (nouvel exercice)
      const nouveauExerciceDebut = new Date(exerciceFin);
      nouveauExerciceDebut.setDate(nouveauExerciceDebut.getDate() + 1);
      await this.createReopeningEntries(nouveauExerciceDebut, companyId, transaction);
      
      // 6. Mettre à jour les dates d'exercice
      await Company.update({
        exercice_debut: nouveauExerciceDebut,
        exercice_fin: new Date(nouveauExerciceDebut.getFullYear(), 11, 31)
      }, {
        where: { id: companyId },
        transaction
      });
      
      await transaction.commit();
      
      return {
        success: true,
        resultat: resultat.resultat,
        nouveau_exercice: {
          debut: nouveauExerciceDebut,
          fin: new Date(nouveauExerciceDebut.getFullYear(), 11, 31)
        }
      };
      
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  // Créer l'écriture de résultat
  static async createResultEntry(resultat, dateCloture, companyId, transaction) {
    const entry = {
      date_ecriture: dateCloture,
      journal_id: await this.getJournalByCode('OD', companyId),
      numero_piece: `RESULT-${dateCloture.getFullYear()}`,
      libelle: `Détermination du résultat exercice ${dateCloture.getFullYear()}`,
      company_id: companyId,
      statut: 'VALIDE',
      lines: []
    };
    
    if (resultat.benefice) {
      // Bénéfice : Débit comptes de produits, Crédit résultat
      entry.lines.push({
        compte_numero: '131000', // Résultat net - Bénéfice
        libelle: 'Bénéfice de l\'exercice',
        debit: 0,
        credit: resultat.resultat
      });
      
      // Solder les comptes de produits (classe 7)
      const comptesProqddquits = await this.getAccountsWithBalance('7%', companyId);
      comptesProqddquits.forEach(compte => {
        entry.lines.push({
          compte_numero: compte.numero,
          libelle: 'Clôture compte de produit',
          debit: compte.solde_credit,
          credit: 0
        });
      });
      
    } else {
      // Perte : Crédit comptes de charges, Débit résultat
      entry.lines.push({
        compte_numero: '139000', // Résultat net - Perte
        libelle: 'Perte de l\'exercice',
        debit: Math.abs(resultat.resultat),
        credit: 0
      });
    }
    
    return await Entry.create(entry, { 
      include: [EntryLine], 
      transaction 
    });
  }
}
```

## API Endpoints

### Routes d'authentification
```javascript
// routes/auth.js
POST   /api/auth/login          # Connexion utilisateur
POST   /api/auth/register       # Inscription (admin seulement)
POST   /api/auth/logout         # Déconnexion
POST   /api/auth/refresh        # Renouveler token JWT
POST   /api/auth/forgot-password # Mot de passe oublié
POST   /api/auth/reset-password  # Réinitialiser mot de passe
```

### Routes Société (Fichier)
```javascript
// routes/companies.js
GET    /api/companies           # Liste des sociétés (admin)
POST   /api/companies           # Créer nouvelle société
GET    /api/companies/:id       # Détails d'une société
PUT    /api/companies/:id       # Modifier société
DELETE /api/companies/:id       # Supprimer société
POST   /api/companies/:id/close-exercise # Clôturer exercice
GET    /api/companies/:id/parameters     # Paramètres société
PUT    /api/companies/:id/parameters     # Modifier paramètres
```

### Routes Plan Comptable
```javascript
// routes/accounts.js
GET    /api/accounts            # Liste des comptes
POST   /api/accounts            # Créer un compte
GET    /api/accounts/:numero    # Détail d'un compte
PUT    /api/accounts/:numero    # Modifier un compte
DELETE /api/accounts/:numero    # Supprimer un compte
POST   /api/accounts/initialize-syscohada # Initialiser plan SYSCOHADA
GET    /api/accounts/search     # Rechercher des comptes
GET    /api/accounts/hierarchy  # Hiérarchie des comptes
```

### Routes Journaux
```javascript
// routes/journals.js
GET    /api/journals            # Liste des journaux
POST   /api/journals            # Créer un journal
GET    /api/journals/:id        # Détail d'un journal
PUT    /api/journals/:id        # Modifier un journal
DELETE /api/journals/:id        # Supprimer un journal
POST   /api/journals/initialize-default # Créer journaux par défaut
GET    /api/journals/:id/next-piece     # Prochain numéro de pièce
```

### Routes Tiers
```javascript
// routes/parties.js
GET    /api/parties             # Liste des tiers
POST   /api/parties             # Créer un tiers
GET    /api/parties/:id         # Détail d'un tiers
PUT    /api/parties/:id         # Modifier un tiers
DELETE /api/parties/:id         # Supprimer un tiers
GET    /api/parties/clients     # Liste des clients uniquement
GET    /api/parties/fournisseurs # Liste des fournisseurs
GET    /api/parties/:id/balance # Solde d'un tiers
```

### Routes Saisie
```javascript
// routes/entries.js
GET    /api/entries             # Liste des écritures
POST   /api/entries             # Saisir une écriture
GET    /api/entries/:id         # Détail d'une écriture
PUT    /api/entries/:id         # Modifier écriture (si brouillard)
DELETE /api/entries/:id         # Supprimer écriture (si brouillard)
POST   /api/entries/:id/validate # Valider une écriture
POST   /api/entries/:id/duplicate # Dupliquer une écriture
GET    /api/entries/draft       # Écritures en brouillard
POST   /api/entries/validate-batch # Valider en lot
```

### Routes Mouvements Automatiques
```javascript
// routes/movements.js
POST   /api/movements/sale      # Écriture de vente
POST   /api/movements/purchase  # Écriture d'achat
POST   /api/movements/payment   # Écriture de paiement
POST   /api/movements/receipt   # Écriture d'encaissement
POST   /api/movements/transfer  # Virement interne
POST   /api/movements/salary    # Écriture de salaire
```

### Routes Éditions/Rapports
```javascript
// routes/reports.js
GET    /api/reports/journal/:journal_id      # Journal détaillé
GET    /api/reports/ledger/:account          # Grand livre
GET    /api/reports/trial-balance           # Balance générale
GET    /api/reports/balance-sheet           # Bilan
GET    /api/reports/income-statement        # Compte de résultat
GET    /api/reports/cash-flow               # Tableau de flux
GET    /api/reports/draft                   # Brouillard
GET    /api/reports/movement               # Liste des mouvements
POST   /api/reports/custom                 # Rapport personnalisé
GET    /api/reports/:id/export/:format     # Export PDF/Excel
```

### Routes Lettrage
```javascript
// routes/lettrage.js
GET    /api/lettrage/unlettred            # Écritures non lettrées
POST   /api/lettrage/auto                # Lettrage automatique
POST   /api/lettrage/manual              # Lettrage manuel
DELETE /api/lettrage/:code               # Délettrage
GET    /api/lettrage/tiers/:id           # Écritures non lettrées d'un tiers
POST   /api/lettrage/tiers/:id/auto     # Auto-lettrage pour un tiers
```

### Routes Amortissements
```javascript
// routes/depreciations.js
GET    /api/depreciations                # Liste des amortissements
POST   /api/depreciations                # Créer un amortissement
GET    /api/depreciations/:id            # Détail amortissement
PUT    /api/depreciations/:id            # Modifier amortissement
DELETE /api/depreciations/:id            # Supprimer amortissement
GET    /api/depreciations/:id/plan       # Plan d'amortissement
POST   /api/depreciations/calculate      # Calculer dotations exercice
POST   /api/depreciations/generate-entries # Générer écritures dotations
```

### Routes Outils de Calcul
```javascript
// routes/calculations.js
POST   /api/calculations/loan-payment    # Calcul mensualités emprunt
POST   /api/calculations/loan-amount     # Calcul montant empruntable
POST   /api/calculations/investment-return # Calcul rendement placement
POST   /api/calculations/depreciation-linear # Amortissement linéaire
POST   /api/calculations/depreciation-declining # Amortissement dégressif
```

## Contraintes Techniques

### 1. Base de Données

**PostgreSQL - Configuration recommandée :**
```sql
-- Contraintes métier essentielles
ALTER TABLE entries ADD CONSTRAINT check_balance 
  CHECK (ABS(total_debit - total_credit) < 0.01);

ALTER TABLE entry_lines ADD CONSTRAINT check_debit_or_credit 
  CHECK ((debit > 0 AND credit = 0) OR (debit = 0 AND credit > 0));

ALTER TABLE accounts ADD CONSTRAINT check_account_number_class
  CHECK (LEFT(numero, 1)::INTEGER = classe);

-- Index pour performance
CREATE INDEX CONCURRENTLY idx_entries_company_date 
  ON entries(company_id, date_ecriture);

CREATE INDEX CONCURRENTLY idx_entry_lines_account_date 
  ON entry_lines(compte_numero) INCLUDE (debit, credit);

CREATE INDEX CONCURRENTLY idx_entry_lines_tiers 
  ON entry_lines(tiers_id) WHERE tiers_id IS NOT NULL;

-- Contraintes d'intégrité référentielle
ALTER TABLE entries ADD CONSTRAINT fk_entries_company 
  FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;

ALTER TABLE entry_lines ADD CONSTRAINT fk_entry_lines_entry 
  FOREIGN KEY (entry_id) REFERENCES entries(id) ON DELETE CASCADE;
```

### 2. Sécurité

**Authentification et Autorisation :**
```javascript
// middleware/auth.js
const authenticateJWT = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({ error: 'Token manquant' });
  }

  const token = authHeader.split(' ')[1];
  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Token invalide' });
    }
    req.user = user;
    next();
  });
};

// Middleware de contrôle d'accès société
const checkCompanyAccess = async (req, res, next) => {
  const companyId = req.params.companyId || req.body.company_id;
  
  // Vérifier que l'utilisateur a accès à cette société
  const access = await UserCompanyAccess.findOne({
    where: {
      user_id: req.user.id,
      company_id: companyId,
      active: true
    }
  });
  
  if (!access) {
    return res.status(403).json({ error: 'Accès interdit à cette société' });
  }
  
  req.company = access.company;
  next();
};
```

**Validation des entrées :**
```javascript
// middleware/validation.js
const Joi = require('joi');

const entryValidationSchema = Joi.object({
  date_ecriture: Joi.date().required(),
  journal_id: Joi.string().uuid().required(),
  numero_piece: Joi.string().max(50),
  libelle: Joi.string().required().max(255),
  lines: Joi.array().items(
    Joi.object({
      compte_numero: Joi.string().required().max(10),
      libelle: Joi.string().max(255),
      debit: Joi.number().min(0).precision(2),
      credit: Joi.number().min(0).precision(2),
      tiers_id: Joi.string().uuid().allow(null)
    })
  ).min(2).required()
});

const validateEntry = (req, res, next) => {
  const { error } = entryValidationSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ 
      error: error.details[0].message 
    });
  }
  next();
};
```

### 3. Performance et Optimisation

**Cache Redis :**
```javascript
// services/CacheService.js
const redis = require('redis');
const client = redis.createClient();

class CacheService {
  
  // Cache des balances (recalcul lourd)
  static async getTrialBalance(companyId, dateDebut, dateFin) {
    const cacheKey = `balance:${companyId}:${dateDebut}:${dateFin}`;
    
    // Vérifier le cache
    const cached = await client.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }
    
    // Calculer et mettre en cache (expire dans 1h)
    const balance = await this.calculateTrialBalance(companyId, dateDebut, dateFin);
    await client.setex(cacheKey, 3600, JSON.stringify(balance));
    
    return balance;
  }
  
  // Invalider le cache lors de modifications
  static async invalidateCompanyCache(companyId) {
    const keys = await client.keys(`*:${companyId}:*`);
    if (keys.length > 0) {
      await client.del(keys);
    }
  }
}
```

**Jobs en arrière-plan :**
```javascript
// jobs/AccountingJobs.js
const Queue = require('bull');
const reportQueue = new Queue('report processing');

// Job de génération de rapport lourd
reportQueue.process('generate-annual-report', async (job) => {
  const { companyId, exerciceFin } = job.data;
  
  // Générer le rapport annuel complet
  const report = await ReportService.generateAnnualReport(companyId, exerciceFin);
  
  // Sauvegarder en base
  await GeneratedReport.create({
    company_id: companyId,
    type: 'ANNUAL',
    file_path: report.filePath,
    generated_at: new Date()
  });
  
  return { success: true, filePath: report.filePath };
});

// Programmer la génération
app.post('/api/reports/annual/:companyId', async (req, res) => {
  const job = await reportQueue.add('generate-annual-report', {
    companyId: req.params.companyId,
    exerciceFin: req.body.exerciceFin
  });
  
  res.json({ jobId: job.id, status: 'En cours de génération...' });
});
```

### 4. Logging et Audit

```javascript
// middleware/audit.js
const auditLog = (action, entity) => {
  return async (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // Logger l'action après succès
      if (res.statusCode < 400) {
        AuditLog.create({
          user_id: req.user.id,
          company_id: req.company?.id,
          action: action,
          entity_type: entity,
          entity_id: req.params.id,
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          request_data: JSON.stringify(req.body),
          timestamp: new Date()
        }).catch(console.error);
      }
      
      originalSend.call(this, data);
    };
    
    next();
  };
};

// Usage dans les routes
app.post('/api/entries', 
  authenticateJWT, 
  checkCompanyAccess, 
  validateEntry,
  auditLog('CREATE', 'ENTRY'),
  EntryController.create
);
```

## Structure des Fichiers

### Package.json
```json
{
  "name": "syscohada-backend",
  "version": "1.0.0",
  "description": "API Backend pour logiciel comptabilité SYSCOHADA",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "test": "jest",
    "migrate": "sequelize-cli db:migrate",
    "seed": "sequelize-cli db:seed:all",
    "build": "docker build -t syscohada-api ."
  },
  "dependencies": {
    "express": "^4.18.2",
    "sequelize": "^6.32.1",
    "pg": "^8.11.0",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.1",
    "joi": "^17.9.2",
    "multer": "^1.4.5",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "morgan": "^1.10.0",
    "redis": "^4.6.7",
    "bull": "^4.10.4",
    "nodemailer": "^6.9.3",
    "puppeteer": "^20.7.1",
    "exceljs": "^4.3.0",
    "moment": "^2.29.4"
  },
  "devDependencies": {
    "nodemon": "^3.0.1",
    "jest": "^29.6.1",
    "supertest": "^6.3.3",
    "sequelize-cli": "^6.6.1"
  }
}
```

### Variables d'environnement (.env)
```env
# Base de données
DB_HOST=localhost
DB_PORT=5432
DB_NAME=syscohada_db
DB_USER=postgres
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Application
NODE_ENV=development
PORT=3000
API_VERSION=v1

# Upload
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads

# Logs
LOG_LEVEL=info
LOG_FILE=./logs/app.log
```

## Installation et Configuration

### 1. Initialisation du projet
```bash
# Créer le projet
mkdir syscohada-backend
cd syscohada-backend
npm init -y

# Installer les dépendances
npm install express sequelize pg bcryptjs jsonwebtoken joi multer cors helmet morgan redis bull nodemailer puppeteer exceljs moment

# Dépendances de développement
npm install --save-dev nodemon jest supertest sequelize-cli

# Initialiser Sequelize
npx sequelize-cli init
```

### 2. Configuration de base
```javascript
// src/config/database.js
require('dotenv').config();

module.exports = {
  development: {
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgresql',
    timezone: '+00:00',
    logging: console.log,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true
    },
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  },
  production: {
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgresql',
    timezone: '+00:00',
    logging: false,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true
    },
    pool: {
      max: 20,
      min: 5,
      acquire: 30000,
      idle: 10000
    }
  }
};
```

### 3. Fichier principal de l'application
```javascript
// src/app.js
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const { sequelize } = require('./models');
const routes = require('./routes');
const errorHandler = require('./middleware/errorHandler');

const app = express();

// Middlewares de sécurité
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));

// Logging
app.use(morgan('combined'));

// Parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/v1', routes);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Gestion des erreurs
app.use(errorHandler);

// 404 Handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route non trouvée',
    path: req.originalUrl
  });
});

const PORT = process.env.PORT || 3000;

// Démarrage du serveur
const startServer = async () => {
  try {
    // Test de connexion à la DB
    await sequelize.authenticate();
    console.log('✅ Connexion à la base de données établie');
    
    // Synchronisation des modèles (dev uniquement)
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true });
      console.log('✅ Modèles synchronisés');
    }
    
    app.listen(PORT, () => {
      console.log(`🚀 Serveur démarré sur le port ${PORT}`);
      console.log(`📖 API disponible sur http://localhost:${PORT}/api/v1`);
    });
    
  } catch (error) {
    console.error('❌ Erreur de démarrage:', error);
    process.exit(1);
  }
};

startServer();

module.exports = app;
```

### 4. Modèles Sequelize
```javascript
// src/models/index.js
const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
const basename = path.basename(__filename);
const env = process.env.NODE_ENV || 'development';
const config = require('../config/database')[env];

const db = {};

let sequelize;
if (config.use_env_variable) {
  sequelize = new Sequelize(process.env[config.use_env_variable], config);
} else {
  sequelize = new Sequelize(config.database, config.username, config.password, config);
}

// Charger tous les modèles
fs
  .readdirSync(__dirname)
  .filter(file => {
    return (
      file.indexOf('.') !== 0 &&
      file !== basename &&
      file.slice(-3) === '.js' &&
      file.indexOf('.test.js') === -1
    );
  })
  .forEach(file => {
    const model = require(path.join(__dirname, file))(sequelize, Sequelize.DataTypes);
    db[model.name] = model;
  });

// Associations
Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

db.sequelize = sequelize;
db.Sequelize = Sequelize;

module.exports = db;
```

### 5. Modèle Company
```javascript
// src/models/Company.js
module.exports = (sequelize, DataTypes) => {
  const Company = sequelize.define('Company', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    nom: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 255]
      }
    },
    adresse: DataTypes.TEXT,
    telephone: {
      type: DataTypes.STRING(50),
      validate: {
        isPhoneNumber(value) {
          if (value && !/^[\+]?[0-9\s\-\(\)]{8,}$/.test(value)) {
            throw new Error('Format de téléphone invalide');
          }
        }
      }
    },
    email: {
      type: DataTypes.STRING(255),
      validate: {
        isEmail: true
      }
    },
    forme_juridique: DataTypes.STRING(100),
    numero_rccm: DataTypes.STRING(100),
    numero_contribuable: DataTypes.STRING(100),
    exercice_debut: {
      type: DataTypes.DATEONLY,
      defaultValue: () => new Date().getFullYear() + '-01-01'
    },
    exercice_fin: {
      type: DataTypes.DATEONLY,
      defaultValue: () => new Date().getFullYear() + '-12-31'
    },
    monnaie: {
      type: DataTypes.STRING(3),
      defaultValue: 'XOF'
    },
    regime_fiscal: {
      type: DataTypes.ENUM('REEL_NORMAL', 'REEL_SIMPLIFIE', 'SYNTHETIQUE'),
      defaultValue: 'REEL_NORMAL'
    },
    statut: {
      type: DataTypes.ENUM('ACTIF', 'SUSPENDU', 'FERME'),
      defaultValue: 'ACTIF'
    },
    logo_url: DataTypes.STRING(500)
  }, {
    tableName: 'companies',
    indexes: [
      {
        fields: ['statut']
      },
      {
        fields: ['exercice_debut', 'exercice_fin']
      }
    ]
  });

  Company.associate = function(models) {
    Company.hasMany(models.Account, { foreignKey: 'company_id', as: 'accounts' });
    Company.hasMany(models.Journal, { foreignKey: 'company_id', as: 'journals' });
    Company.hasMany(models.Entry, { foreignKey: 'company_id', as: 'entries' });
    Company.hasMany(models.Party, { foreignKey: 'company_id', as: 'parties' });
  };

  return Company;
};
```

### 6. Routes principales
```javascript
// src/routes/index.js
const express = require('express');
const router = express.Router();

// Import des routes
const authRoutes = require('./auth');
const companyRoutes = require('./companies');
const accountRoutes = require('./accounts');
const journalRoutes = require('./journals');
const partyRoutes = require('./parties');
const entryRoutes = require('./entries');
const movementRoutes = require('./movements');
const reportRoutes = require('./reports');
const lettrageRoutes = require('./lettrage');
const depreciationRoutes = require('./depreciations');
const calculationRoutes = require('./calculations');

// Middleware d'authentification global (sauf pour auth)
const { authenticateJWT } = require('../middleware/auth');

// Routes publiques
router.use('/auth', authRoutes);

// Routes protégées
router.use('/companies', authenticateJWT, companyRoutes);
router.use('/accounts', authenticateJWT, accountRoutes);
router.use('/journals', authenticateJWT, journalRoutes);
router.use('/parties', authenticateJWT, partyRoutes);
router.use('/entries', authenticateJWT, entryRoutes);
router.use('/movements', authenticateJWT, movementRoutes);
router.use('/reports', authenticateJWT, reportRoutes);
router.use('/lettrage', authenticateJWT, lettrageRoutes);
router.use('/depreciations', authenticateJWT, depreciationRoutes);
router.use('/calculations', calculationRoutes);

module.exports = router;
```

### 7. Gestion des erreurs
```javascript
// src/middleware/errorHandler.js
const errorHandler = (err, req, res, next) => {
  console.error('Erreur capturée:', err);

  // Erreur de validation Sequelize
  if (err.name === 'SequelizeValidationError') {
    return res.status(400).json({
      error: 'Erreur de validation',
      details: err.errors.map(e => ({
        field: e.path,
        message: e.message
      }))
    });
  }

  // Erreur de contrainte unique
  if (err.name === 'SequelizeUniqueConstraintError') {
    return res.status(409).json({
      error: 'Violation de contrainte d\'unicité',
      field: err.errors[0]?.path,
      message: 'Cette valeur existe déjà'
    });
  }

  // Erreur de contrainte FK
  if (err.name === 'SequelizeForeignKeyConstraintError') {
    return res.status(400).json({
      error: 'Référence invalide',
      message: 'L\'élément référencé n\'existe pas'
    });
  }

  // Erreur JWT
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      error: 'Token invalide',
      message: 'Veuillez vous reconnecter'
    });
  }

  // Erreur de validation Joi
  if (err.isJoi) {
    return res.status(400).json({
      error: 'Données invalides',
      details: err.details.map(d => ({
        field: d.path.join('.'),
        message: d.message
      }))
    });
  }

  // Erreur métier personnalisée
  if (err.type === 'BUSINESS_ERROR') {
    return res.status(400).json({
      error: err.message,
      code: err.code
    });
  }

  // Erreur 404
  if (err.status === 404) {
    return res.status(404).json({
      error: 'Ressource non trouvée'
    });
  }

  // Erreur générique
  res.status(500).json({
    error: process.env.NODE_ENV === 'development' 
      ? err.message 
      : 'Erreur interne du serveur',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

module.exports = errorHandler;
```

### 8. Seeders - Plan comptable SYSCOHADA
```javascript
// src/seeders/**************-syscohada-accounts.js
'use strict';

const { v4: uuidv4 } = require('uuid');

const syscohadaAccounts = [
  // CLASSE 1 - COMPTES DE RESSOURCES DURABLES
  { numero: '10', intitule: 'Capital et réserves', classe: 1, nature: 'PASSIF', sens_normal: 'CREDIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '101', intitule: 'Capital social', classe: 1, nature: 'PASSIF', sens_normal: 'CREDIT', niveau: 2, compte_parent: '10', type_compte: 'COLLECTIF' },
  { numero: '1011', intitule: 'Capital souscrit, non appelé', classe: 1, nature: 'PASSIF', sens_normal: 'CREDIT', niveau: 3, compte_parent: '101', type_compte: 'INDIVIDUEL' },
  { numero: '1012', intitule: 'Capital souscrit, appelé, non versé', classe: 1, nature: 'PASSIF', sens_normal: 'CREDIT', niveau: 3, compte_parent: '101', type_compte: 'INDIVIDUEL' },
  { numero: '1013', intitule: 'Capital souscrit, appelé, versé, non amorti', classe: 1, nature: 'PASSIF', sens_normal: 'CREDIT', niveau: 3, compte_parent: '101', type_compte: 'INDIVIDUEL' },

  // CLASSE 2 - COMPTES D'ACTIF IMMOBILISE
  { numero: '20', intitule: 'Immobilisations incorporelles', classe: 2, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '21', intitule: 'Immobilisations corporelles', classe: 2, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '211', intitule: 'Terrains', classe: 2, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 2, compte_parent: '21', type_compte: 'COLLECTIF' },
  { numero: '2111', intitule: 'Terrains nus', classe: 2, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 3, compte_parent: '211', type_compte: 'INDIVIDUEL' },
  { numero: '212', intitule: 'Agencements et aménagements de terrains', classe: 2, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 2, compte_parent: '21', type_compte: 'INDIVIDUEL' },
  { numero: '213', intitule: 'Bâtiments, installations techniques et agencements', classe: 2, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 2, compte_parent: '21', type_compte: 'COLLECTIF' },
  { numero: '2131', intitule: 'Bâtiments industriels, agricoles, administratifs et commerciaux', classe: 2, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 3, compte_parent: '213', type_compte: 'INDIVIDUEL' },

  // CLASSE 3 - COMPTES DE STOCKS
  { numero: '30', intitule: 'Marchandises', classe: 3, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '31', intitule: 'Matières premières et fournitures liées', classe: 3, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '32', intitule: 'Autres approvisionnements', classe: 3, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },

  // CLASSE 4 - COMPTES DE TIERS
  { numero: '40', intitule: 'Fournisseurs et comptes rattachés', classe: 4, nature: 'PASSIF', sens_normal: 'CREDIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '401', intitule: 'Fournisseurs', classe: 4, nature: 'PASSIF', sens_normal: 'CREDIT', niveau: 2, compte_parent: '40', type_compte: 'COLLECTIF', tiers_obligatoire: true },
  { numero: '4011', intitule: 'Fournisseurs - catégorie A', classe: 4, nature: 'PASSIF', sens_normal: 'CREDIT', niveau: 3, compte_parent: '401', type_compte: 'AUXILIAIRE', tiers_obligatoire: true },
  
  { numero: '41', intitule: 'Clients et comptes rattachés', classe: 4, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '411', intitule: 'Clients', classe: 4, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 2, compte_parent: '41', type_compte: 'COLLECTIF', tiers_obligatoire: true },
  { numero: '4111', intitule: 'Clients - catégorie A', classe: 4, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 3, compte_parent: '411', type_compte: 'AUXILIAIRE', tiers_obligatoire: true },

  { numero: '44', intitule: 'État et collectivités publiques', classe: 4, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '445', intitule: 'État, taxes sur le chiffre d\'affaires', classe: 4, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 2, compte_parent: '44', type_compte: 'COLLECTIF' },
  { numero: '4451', intitule: 'État, TVA facturée', classe: 4, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 3, compte_parent: '445', type_compte: 'INDIVIDUEL' },
  { numero: '4452', intitule: 'État, TVA due', classe: 4, nature: 'PASSIF', sens_normal: 'CREDIT', niveau: 3, compte_parent: '445', type_compte: 'INDIVIDUEL' },

  // CLASSE 5 - COMPTES DE TRESORERIE
  { numero: '50', intitule: 'Valeurs mobilières de placement', classe: 5, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '52', intitule: 'Banques', classe: 5, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '521', intitule: 'Banques locales', classe: 5, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 2, compte_parent: '52', type_compte: 'COLLECTIF' },
  { numero: '5211', intitule: 'Banques en francs', classe: 5, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 3, compte_parent: '521', type_compte: 'INDIVIDUEL' },
  
  { numero: '53', intitule: 'Établissements financiers et assimilés', classe: 5, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '57', intitule: 'Caisse', classe: 5, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '571', intitule: 'Caisse siège social', classe: 5, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 2, compte_parent: '57', type_compte: 'INDIVIDUEL' },

  // CLASSE 6 - COMPTES DE CHARGES
  { numero: '60', intitule: 'Achats et variations de stocks', classe: 6, nature: 'CHARGE', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '601', intitule: 'Achats de marchandises', classe: 6, nature: 'CHARGE', sens_normal: 'DEBIT', niveau: 2, compte_parent: '60', type_compte: 'COLLECTIF' },
  { numero: '6011', intitule: 'Achats de marchandises - groupe A', classe: 6, nature: 'CHARGE', sens_normal: 'DEBIT', niveau: 3, compte_parent: '601', type_compte: 'INDIVIDUEL' },
  
  { numero: '61', intitule: 'Transports', classe: 6, nature: 'CHARGE', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '62', intitule: 'Services extérieurs A', classe: 6, nature: 'CHARGE', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '63', intitule: 'Services extérieurs B', classe: 6, nature: 'CHARGE', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '64', intitule: 'Impôts et taxes', classe: 6, nature: 'CHARGE', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '66', intitule: 'Charges de personnel', classe: 6, nature: 'CHARGE', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '68', intitule: 'Dotations aux amortissements', classe: 6, nature: 'CHARGE', sens_normal: 'DEBIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '681', intitule: 'Dotations aux amortissements d\'exploitation', classe: 6, nature: 'CHARGE', sens_normal: 'DEBIT', niveau: 2, compte_parent: '68', type_compte: 'COLLECTIF' },

  // CLASSE 7 - COMPTES DE PRODUITS
  { numero: '70', intitule: 'Ventes', classe: 7, nature: 'PRODUIT', sens_normal: 'CREDIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '701', intitule: 'Ventes de marchandises', classe: 7, nature: 'PRODUIT', sens_normal: 'CREDIT', niveau: 2, compte_parent: '70', type_compte: 'COLLECTIF' },
  { numero: '7011', intitule: 'Ventes de marchandises - groupe A', classe: 7, nature: 'PRODUIT', sens_normal: 'CREDIT', niveau: 3, compte_parent: '701', type_compte: 'INDIVIDUEL' },
  
  { numero: '71', intitule: 'Subventions d\'exploitation', classe: 7, nature: 'PRODUIT', sens_normal: 'CREDIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '75', intitule: 'Autres produits', classe: 7, nature: 'PRODUIT', sens_normal: 'CREDIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '78', intitule: 'Reprises d\'amortissements et de provisions', classe: 7, nature: 'PRODUIT', sens_normal: 'CREDIT', niveau: 1, type_compte: 'COLLECTIF' },

  // CLASSE 8 - COMPTES DE RESULTATS
  { numero: '13', intitule: 'Résultat net de l\'exercice', classe: 1, nature: 'PASSIF', sens_normal: 'CREDIT', niveau: 1, type_compte: 'COLLECTIF' },
  { numero: '131', intitule: 'Résultat net : bénéfice', classe: 1, nature: 'PASSIF', sens_normal: 'CREDIT', niveau: 2, compte_parent: '13', type_compte: 'INDIVIDUEL' },
  { numero: '139', intitule: 'Résultat net : perte', classe: 1, nature: 'ACTIF', sens_normal: 'DEBIT', niveau: 2, compte_parent: '13', type_compte: 'INDIVIDUEL' }
];

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Créer les comptes pour chaque société existante
    const companies = await queryInterface.sequelize.query(
      'SELECT id FROM companies',
      { type: Sequelize.QueryTypes.SELECT }
    );

    const accountsToInsert = [];

    companies.forEach(company => {
      syscohadaAccounts.forEach(account => {
        accountsToInsert.push({
          id: uuidv4(),
          numero: account.numero,
          intitule: account.intitule,
          classe: account.classe,
          nature: account.nature,
          sens_normal: account.sens_normal,
          niveau: account.niveau,
          compte_parent: account.compte_parent || null,
          type_compte: account.type_compte,
          tiers_obligatoire: account.tiers_obligatoire || false,
          bloque_saisie: account.type_compte === 'COLLECTIF',
          actif: true,
          company_id: company.id,
          created_at: new Date(),
          updated_at: new Date()
        });
      });
    });

    if (accountsToInsert.length > 0) {
      await queryInterface.bulkInsert('accounts', accountsToInsert);
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('accounts', null, {});
  }
};
```

### 9. Tests unitaires
```javascript
// tests/entry.test.js
const request = require('supertest');
const app = require('../src/app');
const { sequelize } = require('../src/models');
const { createTestCompany, createTestUser, getAuthToken } = require('./helpers');

describe('API des écritures', () => {
  let company, user, authToken;

  beforeAll(async () => {
    await sequelize.sync({ force: true });
    company = await createTestCompany();
    user = await createTestUser();
    authToken = getAuthToken(user);
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('POST /api/v1/entries', () => {
    test('devrait créer une écriture équilibrée', async () => {
      const entryData = {
        date_ecriture: '2024-01-15',
        journal_id: company.journalVente.id,
        numero_piece: 'TEST001',
        libelle: 'Test écriture',
        company_id: company.id,
        lines: [
          {
            compte_numero: '411001',
            libelle: 'Client test',
            debit: 1000,
            credit: 0
          },
          {
            compte_numero: '701001',
            libelle: 'Vente test',
            debit: 0,
            credit: 1000
          }
        ]
      };

      const response = await request(app)
        .post('/api/v1/entries')
        .set('Authorization', `Bearer ${authToken}`)
        .send(entryData);

      expect(response.status).toBe(201);
      expect(response.body.total_debit).toBe(1000);
      expect(response.body.total_credit).toBe(1000);
      expect(response.body.statut).toBe('BROUILLARD');
    });

    test('devrait rejeter une écriture non équilibrée', async () => {
      const entryData = {
        date_ecriture: '2024-01-15',
        journal_id: company.journalVente.id,
        numero_piece: 'TEST002',
        libelle: 'Test écriture déséquilibrée',
        company_id: company.id,
        lines: [
          {
            compte_numero: '411001',
            libelle: 'Client test',
            debit: 1000,
            credit: 0
          },
          {
            compte_numero: '701001',
            libelle: 'Vente test',
            debit: 0,
            credit: 900 // Déséquilibrée !
          }
        ]
      };

      const response = await request(app)
        .post('/api/v1/entries')
        .set('Authorization', `Bearer ${authToken}`)
        .send(entryData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('équilibr');
    });
  });

  describe('POST /api/v1/entries/:id/validate', () => {
    test('devrait valider une écriture en brouillard', async () => {
      // Créer d'abord une écriture
      const entry = await createTestEntry(company, user);

      const response = await request(app)
        .post(`/api/v1/entries/${entry.id}/validate`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.statut).toBe('VALIDE');
      expect(response.body.date_validation).toBeTruthy();
    });
  });
});
```

### 10. Documentation API (Swagger)
```javascript
// src/swagger.js
const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'API Comptabilité SYSCOHADA',
      version: '1.0.0',
      description: 'API REST pour logiciel de comptabilité conforme au référentiel SYSCOHADA',
      contact: {
        name: 'Support API',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: 'http://localhost:3000/api/v1',
        description: 'Serveur de développement'
      },
      {
        url: 'https://api.syscohada.com/v1',
        description: 'Serveur de production'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        Company: {
          type: 'object',
          required: ['nom'],
          properties: {
            id: { type: 'string', format: 'uuid' },
            nom: { type: 'string', minLength: 2, maxLength: 255 },
            adresse: { type: 'string' },
            telephone: { type: 'string' },
            email: { type: 'string', format: 'email' },
            exercice_debut: { type: 'string', format: 'date' },
            exercice_fin: { type: 'string', format: 'date' },
            monnaie: { type: 'string', enum: ['XOF', 'XAF', 'EUR'] },
            regime_fiscal: { type: 'string', enum: ['REEL_NORMAL', 'REEL_SIMPLIFIE', 'SYNTHETIQUE'] }
          }
        },
        Account: {
          type: 'object',
          required: ['numero', 'intitule', 'classe'],
          properties: {
            id: { type: 'string', format: 'uuid' },
            numero: { type: 'string', maxLength: 10 },
            intitule: { type: 'string', maxLength: 255 },
            classe: { type: 'integer', minimum: 1, maximum: 8 },
            nature: { type: 'string', enum: ['ACTIF', 'PASSIF', 'CHARGE', 'PRODUIT', 'RESULTAT'] },
            sens_normal: { type: 'string', enum: ['DEBIT', 'CREDIT'] },
            type_compte: { type: 'string', enum: ['COLLECTIF', 'INDIVIDUEL', 'AUXILIAIRE'] }
          }
        },
        Entry: {
          type: 'object',
          required: ['date_ecriture', 'journal_id', 'libelle', 'lines'],
          properties: {
            id: { type: 'string', format: 'uuid' },
            date_ecriture: { type: 'string', format: 'date' },
            journal_id: { type: 'string', format: 'uuid' },
            numero_piece: { type: 'string', maxLength: 50 },
            libelle: { type: 'string', maxLength: 255 },
            total_debit: { type: 'number', format: 'decimal' },
            total_credit: { type: 'number', format: 'decimal' },
            statut: { type: 'string', enum: ['BROUILLARD', 'VALIDE', 'CLOTURE', 'ANNULE'] },
            lines: {
              type: 'array',
              minItems: 2,
              items: { $ref: '#/components/schemas/EntryLine' }
            }
          }
        },
        EntryLine: {
          type: 'object',
          required: ['compte_numero'],
          properties: {
            compte_numero: { type: 'string', maxLength: 10 },
            libelle: { type: 'string', maxLength: 255 },
            debit: { type: 'number', format: 'decimal', minimum: 0 },
            credit: { type: 'number', format: 'decimal', minimum: 0 },
            tiers_id: { type: 'string', format: 'uuid' }
          }
        },
        Error: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
            details: { type: 'array', items: { type: 'object' } }
          }
        }
      }
    },
    security: [
      { bearerAuth: [] }
    ]
  },
  apis: ['./src/routes/*.js'] // Chemins vers les fichiers contenant les annotations
};

const specs = swaggerJsdoc(options);

module.exports = { specs, swaggerUi };
```

### 11. Middleware de validation avancée
```javascript
// src/middleware/validation.js
const Joi = require('joi');

// Schémas de validation
const schemas = {
  company: Joi.object({
    nom: Joi.string().min(2).max(255).required(),
    adresse: Joi.string().allow(''),
    telephone: Joi.string().pattern(/^[\+]?[0-9\s\-\(\)]{8,}$/).allow(''),
    email: Joi.string().email().allow(''),
    forme_juridique: Joi.string().max(100),
    numero_rccm: Joi.string().max(100),
    numero_contribuable: Joi.string().max(100),
    exercice_debut: Joi.date().required(),
    exercice_fin: Joi.date().greater(Joi.ref('exercice_debut')).required(),
    monnaie: Joi.string().valid('XOF', 'XAF', 'EUR').default('XOF'),
    regime_fiscal: Joi.string().valid('REEL_NORMAL', 'REEL_SIMPLIFIE', 'SYNTHETIQUE').default('REEL_NORMAL')
  }),

  account: Joi.object({
    numero: Joi.string().max(10).required()
      .custom((value, helpers) => {
        // Validation personnalisée du numéro de compte
        if (!/^[1-8]\d*$/.test(value)) {
          return helpers.error('any.invalid');
        }
        return value;
      }, 'Numéro de compte SYSCOHADA'),
    intitule: Joi.string().min(1).max(255).required(),
    classe: Joi.number().integer().min(1).max(8).required(),
    nature: Joi.string().valid('ACTIF', 'PASSIF', 'CHARGE', 'PRODUIT', 'RESULTAT').required(),
    sens_normal: Joi.string().valid('DEBIT', 'CREDIT').required(),
    type_compte: Joi.string().valid('COLLECTIF', 'INDIVIDUEL', 'AUXILIAIRE').default('INDIVIDUEL'),
    compte_parent: Joi.string().max(10).allow(null),
    tiers_obligatoire: Joi.boolean().default(false),
    bloque_saisie: Joi.boolean().default(false)
  }),

  entry: Joi.object({
    date_ecriture: Joi.date().required(),
    date_piece: Joi.date().allow(null),
    journal_id: Joi.string().uuid().required(),
    numero_piece: Joi.string().max(50).allow(''),
    libelle: Joi.string().min(1).max(255).required(),
    reference_externe: Joi.string().max(100).allow(''),
    observations: Joi.string().allow(''),
    lines: Joi.array().items(
      Joi.object({
        compte_numero: Joi.string().max(10).required(),
        libelle: Joi.string().max(255).default(''),
        debit: Joi.number().precision(2).min(0).default(0),
        credit: Joi.number().precision(2).min(0).default(0),
        tiers_id: Joi.string().uuid().allow(null),
        date_echeance: Joi.date().allow(null),
        mode_reglement: Joi.string().valid('ESPECES', 'CHEQUE', 'VIREMENT', 'CARTE', 'TRAITE').allow(null),
        quantite: Joi.number().precision(3).allow(null),
        prix_unitaire: Joi.number().precision(2).allow(null)
      }).custom((line, helpers) => {
        // Une seule des deux colonnes peut être non nulle
        if ((line.debit > 0 && line.credit > 0) || (line.debit === 0 && line.credit === 0)) {
          return helpers.error('entry.line.debit_xor_credit');
        }
        return line;
      })
    ).min(2).required().custom((lines, helpers) => {
      // Vérifier l'équilibre débit/crédit
      const totalDebit = lines.reduce((sum, line) => sum + (line.debit || 0), 0);
      const totalCredit = lines.reduce((sum, line) => sum + (line.credit || 0), 0);
      
      if (Math.abs(totalDebit - totalCredit) > 0.01) {
        return helpers.error('entry.unbalanced', { totalDebit, totalCredit });
      }
      return lines;
    })
  }),

  movement: {
    sale: Joi.object({
      client_id: Joi.string().uuid().required(),
      numero_facture: Joi.string().required(),
      date_facture: Joi.date().default(new Date()),
      montant_ht: Joi.number().precision(2).positive().required(),
      taux_tva: Joi.number().precision(2).min(0).max(100).default(18),
      remise: Joi.number().precision(2).min(0).default(0),
      description: Joi.string().default(''),
      date_echeance: Joi.date().allow(null),
      articles: Joi.array().items(
        Joi.object({
          designation: Joi.string().required(),
          quantite: Joi.number().positive().required(),
          prix_unitaire: Joi.number().precision(2).positive().required(),
          taux_tva: Joi.number().precision(2).min(0).max(100).default(18)
        })
      ).min(1)
    }),
    
    purchase: Joi.object({
      fournisseur_id: Joi.string().uuid().required(),
      numero_facture: Joi.string().required(),
      date_facture: Joi.date().default(new Date()),
      montant_ht: Joi.number().precision(2).positive().required(),
      taux_tva: Joi.number().precision(2).min(0).max(100).default(18),
      description: Joi.string().default(''),
      date_echeance: Joi.date().allow(null)
    })
  }
};

// Messages d'erreur personnalisés
const customMessages = {
  'entry.unbalanced': 'L\'écriture n\'est pas équilibrée : Débit={{#totalDebit}}, Crédit={{#totalCredit}}',
  'entry.line.debit_xor_credit': 'Une ligne d\'écriture doit avoir soit un débit soit un crédit, mais pas les deux',
  'any.invalid': 'Le numéro de compte doit commencer par un chiffre de 1 à 8'
};

// Middleware générique de validation
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true,
      messages: customMessages
    });

    if (error) {
      return res.status(400).json({
        error: 'Données invalides',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }))
      });
    }

    req[property] = value;
    next();
  };
};

// Middleware spécialisés
const validateCompany = validate(schemas.company);
const validateAccount = validate(schemas.account);
const validateEntry = validate(schemas.entry);
const validateSale = validate(schemas.movement.sale);
const validatePurchase = validate(schemas.movement.purchase);

module.exports = {
  schemas,
  validate,
  validateCompany,
  validateAccount,
  validateEntry,
  validateSale,
  validatePurchase
};
```

### 12. Service de génération de rapports PDF
```javascript
// src/services/ReportService.js
const puppeteer = require('puppeteer');
const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs');

class ReportService {
  
  // Générer un journal en PDF
  static async generateJournalPDF(journalData, options = {}) {
    const browser = await puppeteer.launch({ headless: true });
    const page = await browser.newPage();
    
    const html = this.generateJournalHTML(journalData, options);
    
    await page.setContent(html, { waitUntil: 'networkidle0' });
    
    const pdfBuffer = await page.pdf({
      format: 'A4',
      margin: {
        top: '1cm',
        right: '1cm',
        bottom: '1cm',
        left: '1cm'
      },
      displayHeaderFooter: true,
      headerTemplate: this.getHeaderTemplate(journalData.company),
      footerTemplate: this.getFooterTemplate()
    });
    
    await browser.close();
    
    return pdfBuffer;
  }
  
  // Template HTML pour journal
  static generateJournalHTML(journalData, options) {
    const { journal, entries, company, dateDebut, dateFin } = journalData;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Journal ${journal.libelle}</title>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            font-size: 12px;
            margin: 0;
            padding: 20px;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
          }
          .company-info {
            text-align: left;
            margin-bottom: 20px;
          }
          .period-info {
            text-align: right;
            margin-bottom: 20px;
            font-weight: bold;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
          }
          th {
            background-color: #f2f2f2;
            font-weight: bold;
          }
          .number-cell {
            text-align: right;
          }
          .total-row {
            font-weight: bold;
            background-color: #f9f9f9;
          }
          .entry-group {
            border-top: 2px solid #666;
          }
          .page-break {
            page-break-before: always;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${journal.libelle}</h1>
          <h2>${company.nom}</h2>
        </div>
        
        <div class="company-info">
          <p><strong>Adresse :</strong> ${company.adresse || 'Non renseignée'}</p>
          <p><strong>Téléphone :</strong> ${company.telephone || 'Non renseigné'}</p>
          <p><strong>Email :</strong> ${company.email || 'Non renseigné'}</p>
        </div>
        
        <div class="period-info">
          Période du ${this.formatDate(dateDebut)} au ${this.formatDate(dateFin)}
        </div>
        
        <table>
          <thead>
            <tr>
              <th>Date</th>
              <th>Pièce</th>
              <th>Libellé</th>
              <th>Compte</th>
              <th>Intitulé compte</th>
              <th>Débit</th>
              <th>Crédit</th>
            </tr>
          </thead>
          <tbody>
            ${this.generateJournalRows(entries)}
          </tbody>
        </table>
        
        <div class="summary">
          <p><strong>Nombre d'écritures :</strong> ${entries.length}</p>
          <p><strong>Total Débit :</strong> ${this.formatAmount(this.calculateTotal(entries, 'debit'))} XOF</p>
          <p><strong>Total Crédit :</strong> ${this.formatAmount(this.calculateTotal(entries, 'credit'))} XOF</p>
        </div>
      </body>
      </html>
    `;
  }
  
  // Générer les lignes du journal
  static generateJournalRows(entries) {
    let html = '';
    
    entries.forEach((entry, entryIndex) => {
      entry.lines.forEach((line, lineIndex) => {
        html += `
          <tr ${lineIndex === 0 ? 'class="entry-group"' : ''}>
            <td>${lineIndex === 0 ? this.formatDate(entry.date_ecriture) : ''}</td>
            <td>${lineIndex === 0 ? entry.numero_piece || '' : ''}</td>
            <td>${lineIndex === 0 ? entry.libelle : line.libelle}</td>
            <td>${line.compte_numero}</td>
            <td>${line.compte_libelle || ''}</td>
            <td class="number-cell">${line.debit > 0 ? this.formatAmount(line.debit) : ''}</td>
            <td class="number-cell">${line.credit > 0 ? this.formatAmount(line.credit) : ''}</td>
          </tr>
        `;
      });
    });
    
    return html;
  }
  
  // Générer balance en Excel
  static async generateTrialBalanceExcel(balanceData, options = {}) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Balance Générale');
    
    // En-têtes
    const headers = [
      'N° Compte',
      'Intitulé',
      'Débit',
      'Crédit',
      'Solde Débiteur',
      'Solde Créditeur'
    ];
    
    worksheet.addRow(headers);
    
    // Styles des en-têtes
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6E6FA' }
    };
    
    // Données
    let totalDebit = 0;
    let totalCredit = 0;
    let totalSoldeDebiteur = 0;
    let totalSoldeCrediteur = 0;
    
    balanceData.forEach(account => {
      const soldeDebiteur = account.solde > 0 ? account.solde : 0;
      const soldeCrediteur = account.solde < 0 ? Math.abs(account.solde) : 0;
      
      worksheet.addRow([
        account.numero,
        account.intitule,
        account.total_debit,
        account.total_credit,
        soldeDebiteur || '',
        soldeCrediteur || ''
      ]);
      
      totalDebit += account.total_debit;
      totalCredit += account.total_credit;
      totalSoldeDebiteur += soldeDebiteur;
      totalSoldeCrediteur += soldeCrediteur;
    });
    
    // Ligne de total
    const totalRow = worksheet.addRow([
      '',
      'TOTAUX',
      totalDebit,
      totalCredit,
      totalSoldeDebiteur,
      totalSoldeCrediteur
    ]);
    
    totalRow.font = { bold: true };
    totalRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFFE4B5' }
    };
    
    // Format des colonnes
    worksheet.getColumn('A').width = 12; // N° Compte
    worksheet.getColumn('B').width = 40; // Intitulé
    worksheet.getColumn('C').width = 15; // Débit
    worksheet.getColumn('D').width = 15; // Crédit
    worksheet.getColumn('E').width = 15; // Solde Débiteur
    worksheet.getColumn('F').width = 15; // Solde Créditeur
    
    // Format des nombres
    ['C', 'D', 'E', 'F'].forEach(col => {
      worksheet.getColumn(col).numFmt = '#,##0.00';
      worksheet.getColumn(col).alignment = { horizontal: 'right' };
    });
    
    // Bordures
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
    });
    
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  
  // Générer bilan SYSCOHADA
  static async generateBalanceSheetPDF(bilanData, options = {}) {
    const browser = await puppeteer.launch({ headless: true });
    const page = await browser.newPage();
    
    const html = this.generateBalanceSheetHTML(bilanData, options);
    
    await page.setContent(html, { waitUntil: 'networkidle0' });
    
    const pdfBuffer = await page.pdf({
      format: 'A4',
      landscape: true, // Bilan en mode paysage
      margin: { top: '1cm', right: '1cm', bottom: '1cm', left: '1cm' }
    });
    
    await browser.close();
    
    return pdfBuffer;
  }
  
  // Template HTML pour bilan SYSCOHADA
  static generateBalanceSheetHTML(bilanData, options) {
    const { actif, passif, company, dateClotüre } = bilanData;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Bilan - ${company.nom}</title>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            font-size: 11px;
            margin: 0;
            padding: 15px;
          }
          .header {
            text-align: center;
            margin-bottom: 20px;
          }
          .bilan-container {
            display: flex;
            justify-content: space-between;
          }
          .actif, .passif {
            width: 48%;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
          }
          th, td {
            border: 1px solid #333;
            padding: 6px;
            font-size: 10px;
          }
          th {
            background-color: #e6e6e6;
            font-weight: bold;
            text-align: center;
          }
          .section-header {
            background-color: #cccccc;
            font-weight: bold;
            text-align: center;
          }
          .subsection-header {
            background-color: #f0f0f0;
            font-weight: bold;
            padding-left: 10px;
          }
          .account-line {
            padding-left: 20px;
          }
          .number-cell {
            text-align: right;
            font-family: 'Courier New', monospace;
          }
          .total-line {
            font-weight: bold;
            background-color: #e6e6e6;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h2>${company.nom}</h2>
          <h3>BILAN AU ${this.formatDate(dateClotüre)}</h3>
          <p>Exercice du ${this.formatDate(company.exercice_debut)} au ${this.formatDate(company.exercice_fin)}</p>
        </div>
        
        <div class="bilan-container">
          <div class="actif">
            <h3 style="text-align: center; margin-bottom: 10px;">ACTIF</h3>
            ${this.generateActifTable(actif)}
          </div>
          
          <div class="passif">
            <h3 style="text-align: center; margin-bottom: 10px;">PASSIF</h3>
            ${this.generatePassifTable(passif)}
          </div>
        </div>
      </body>
      </html>
    `;
  }
  
  // Fonctions utilitaires
  static formatDate(date) {
    if (!date) return '';
    return new Date(date).toLocaleDateString('fr-FR');
  }
  
  static formatAmount(amount) {
    if (!amount) return '0';
    return new Intl.NumberFormat('fr-FR', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(Math.round(amount));
  }
  
  static calculateTotal(entries, type) {
    return entries.reduce((total, entry) => {
      return total + entry.lines.reduce((lineTotal, line) => {
        return lineTotal + (line[type] || 0);
      }, 0);
    }, 0);
  }
  
  // Templates pour en-tête et pied de page
  static getHeaderTemplate(company) {
    return `
      <div style="font-size: 10px; margin: 0 1cm; width: calc(100% - 2cm);">
        <div style="display: flex; justify-content: space-between;">
          <span>${company.nom}</span>
          <span>Page <span class="pageNumber"></span> sur <span class="totalPages"></span></span>
        </div>
      </div>
    `;
  }
  
  static getFooterTemplate() {
    return `
      <div style="font-size: 9px; margin: 0 1cm; width: calc(100% - 2cm); text-align: center;">
        <span>Édité le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}</span>
      </div>
    `;
  }
}

module.exports = ReportService;
```

### 13. Gestion des sauvegardes automatiques
```javascript
// src/services/BackupService.js
const cron = require('node-cron');
const { exec } = require('child_process');
const path = require('path');
const fs = require('fs').promises;
const archiver = require('archiver');

class BackupService {
  
  static init() {
    // Sauvegarde quotidienne à 2h du matin
    cron.schedule('0 2 * * *', () => {
      this.performDailyBackup();
    });
    
    // Sauvegarde hebdomadaire le dimanche à 1h
    cron.schedule('0 1 * * 0', () => {
      this.performWeeklyBackup();
    });
    
    console.log('✅ Services de sauvegarde initialisés');
  }
  
  static async performDailyBackup() {
    try {
      console.log('🔄 Début de la sauvegarde quotidienne...');
      
      const backupDir = path.join(process.cwd(), 'backups', 'daily');
      await this.ensureDirectoryExists(backupDir);
      
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `backup-daily-${timestamp}.sql`;
      const filepath = path.join(backupDir, filename);
      
      // Sauvegarde de la base de données
      await this.dumpDatabase(filepath);
      
      // Compresser les fichiers uploadés
      const uploadBackup = path.join(backupDir, `uploads-${timestamp}.zip`);
      await this.backupUploads(uploadBackup);
      
      // Nettoyer les anciennes sauvegardes (garder 7 jours)
      await this.cleanOldBackups(backupDir, 7);
      
      console.log('✅ Sauvegarde quotidienne terminée');
      
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde quotidienne:', error);
    }
  }
  
  static async performWeeklyBackup() {
    try {
      console.log('🔄 Début de la sauvegarde hebdomadaire...');
      
      const backupDir = path.join(process.cwd(), 'backups', 'weekly');
      await this.ensureDirectoryExists(backupDir);
      
      const timestamp = this.getWeekNumber();
      const filename = `backup-weekly-${timestamp}.sql`;
      const filepath = path.join(backupDir, filename);
      
      await this.dumpDatabase(filepath);
      
      // Garder 4 semaines de sauvegarde
      await this.cleanOldBackups(backupDir, 4);
      
      console.log('✅ Sauvegarde hebdomadaire terminée');
      
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde hebdomadaire:', error);
    }
  }
  
  static async dumpDatabase(filepath) {
    return new Promise((resolve, reject) => {
      const command = `pg_dump -h ${process.env.DB_HOST} -p ${process.env.DB_PORT} -U ${process.env.DB_USER} -d ${process.env.DB_NAME} -f ${filepath}`;
      
      exec(command, { 
        env: { ...process.env, PGPASSWORD: process.env.DB_PASSWORD } 
      }, (error, stdout, stderr) => {
        if (error) {
          reject(new Error(`Erreur pg_dump:natureCcompte === 'DEBIT') {
        solde += line.debit - line.credit;
      } else {
        solde += line.credit - line.debit;
      }
      
      return {
        ...line.toJSON(),
        solde: solde
      };
    });
    
    return res.json(ledgerLines);
  }
  
  // Balance générale
  async getTrialBalance(req, res) {
    const { dateDebut, dateFin, companyId } = req.params;
    
    const balanceData = await db.query(`
      SELECT 
        el.compte_numero,
        a.intitule,
        SUM(el.debit) as total_debit,
        SUM(el.credit) as total_credit,
        CASE 
          WHEN a.sens_normal = 'DEBIT' 
          THEN SUM(el.debit) - SUM(el.credit)
          ELSE SUM(el.credit) - SUM(el.debit)
        END as solde
      FROM entry_lines el
      JOIN entries e ON el.entry_id = e.id
      JOIN accounts a ON el.compte_numero = a.numero
      WHERE e.date_ecriture BETWEEN ? AND ?
        AND e.statut = 'VALIDE'
        AND e.company_id = ?
      GROUP BY el.compte_numero, a.intitule, a.sens_normal
      HAVING SUM(el.debit) > 0 OR SUM(el.credit) > 0
      ORDER BY el.compte_numero
    `, {
      replacements: [dateDebut, dateFin, companyId],
      type: QueryTypes.SELECT
    });
    
    return res.json(balanceData);
  }
  
  // Bilan comptable
  async getBalanceSheet(req, res) {
    const { dateClotüre, companyId } = req.params;
    
    // Calculer les soldes de tous les comptes de bilan (classes 1-5)
    const bilanComptes = await this.getTrialBalance({
      params: { dateDebut: '2024-01-01', dateFin: dateClotüre, companyId }
    });
    
    // Structurer selon le modèle SYSCOHADA
    const actif = {
      immobilise: bilanComptes.filter(c => c.compte_numero.startsWith('2')),
      circulant: bilanComptes.filter(c => c.compte_numero.startsWith('3')),
      tresorerie: bilanComptes.filter(c => c.compte_numero.startsWith('5'))
    };
    
    const passif = {
      capitaux_propres: bilanComptes.filter(c => c.compte_numero.startsWith('1')),
      dettes: bilanComptes.filter(c => c.compte_numero.startsWith('4'))
    };
    
    return res.json({ actif, passif });
  }
}
```

### 7. LettrageController
```javascript
// controllers/LettrageController.js
class LettrageController {
  
  // Lettrage automatique facture-règlement
  async autoLettrage(companyId, tiersId = null) {
    // 1. Récupérer les écritures non lettrées
    const ecrituresNonLettrees = await EntryLine.findAll({
      where: {
        lettrage: null,
        tiers_id: tiersId || { [Op.ne]: null }
      },
      include: [{
        model: Entry,
        where: {
          company_id: companyId,
          statut: 'VALIDE'
        }
      }]
    });
    
    // 2. Grouper par tiers et compte
    const groupes = this.groupByTiersCompte(ecrituresNonLettrees);
    
    // 3. Pour chaque groupe, chercher les couples qui s'équilibrent
    for (const groupe of groupes) {
      await this.lettrerGroupe(groupe);
    }
  }
  
  // Lettrage manuel
  async lettrerManuellement(req, res) {
    const { entryLineIds } = req.body;
    
    // 1. Vérifier que les lignes peuvent être lettrées
    const lignes = await EntryLine.findAll({
      where: { id: entryLineIds }
    });
    
    // 2. Contrôler l'équilibre
    const totalDebit = lignes.reduce((sum, l) => sum + l.debit, 0);
    const totalCredit = lignes.reduce((sum, l) => sum + l.credit, 0);
    
    if (Math.abs(totalDebit - totalCredit) > 0.01) {
      return res.status(400).json({ error: 'Lignes non équilibrées' });
    }
    
    // 3. Générer un code de lettrage unique
    const codeLettrage = await this.genererCodeLettrage();
    
    // 4. Appliquer le lettrage
    await EntryLine.update(
      { lettrage: codeLettrage, date_lettrage: new Date() },
      { where: { id: entryLineIds } }
    );
    
    return res.json({ success: true, code: codeLettrage });
  }
  
  // Délettrage
  async delettrer(req, res) {
    const { codeLettrage } = req.params;
    
    await EntryLine.update(
      { lettrage: null, date_lettrage: null },
      { where: { lettrage: codeLettrage } }
    );
    
    return res.json({ success: true });
  }
  
  // Générer code lettrage (A, B, ..., Z, AA, AB, ...)
  async genererCodeLettrage() {
    const dernierCode = await EntryLine.findOne({
      attributes: [[db.fn('MAX', db.col('lettrage')), 'maxLettrage']],
      raw: true
    });
    
    return this.incrementerCodeLettrage(dernierCode.maxLettrage || null);
  }
}
```

### 8. DepreciationController (Amortissements)
```javascript
// controllers/DepreciationController.js
class DepreciationController {
  
  // Calculer amortissement linéaire
  calculerLineaire(valeurOrigine, valeurResiduelle, dureeAns) {
    const baseAmortissable = valeurOrigine - valeurResiduelle;
    const annuite = baseAmortissable / dureeAns;
    
    const plan = [];
    for (let annee = 1; annee <= dureeAns; annee++) {
      plan.push({
        annee,
        dotation: annuite,
        cumul_amortissement: annuite * annee,
        valeur_nette: valeurOrigine - (annuite * annee)
      });
    }
    
    return plan;
  }
  
  // Calculer amortissement dégressif
  calculerDegressif(valeurOrigine, dureeAns, coefficient) {
    const tauxDegressif = (100 / dureeAns) * coefficient / 100;
    const plan = [];
    let valeurNette = valeurOrigine;
    let cumulAmortissement = 0;
    
    for (let annee = 1; annee <= dureeAns; annee++) {
      const dotationDegressive = valeurNette * tauxDegressif;
      const dotationLineaire = (valeurOrigine - cumulAmortissement) / (dureeAns - annee + 1);
      
      // Prendre le max entre dégressif et linéaire
      const dotation = Math.max(dotationDegressive, dotationLineaire);
      
      cumulAmortissement += dotation;
      valeurNette -= dotation;
      
      plan.push({
        annee,
        dotation,
        cumul_amortissement: cumulAmortissement,
        valeur_nette: valeurNette
      });
    }
    
    return plan;
  }
  
  // Générer les écritures d'amortissement
  async genererEcrituresAmortissement(exercice, companyId) {
    const amortissements = await Depreciation.findAll({
      where: { company_id: companyId, termine: false }
    });
    
    for (const amort of amortissements) {
      const dotationAnnuelle = this.calculerDotationAnnee(amort, exercice);
      
      if (