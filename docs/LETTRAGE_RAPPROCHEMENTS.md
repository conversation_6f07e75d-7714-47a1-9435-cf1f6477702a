# Module Lettrage et Rapprochements - API Comptabilité SYSCOHADA

## 📋 Vue d'ensemble

Le module de lettrage et rapprochements permet de gérer les opérations de lettrage comptable conformément aux normes SYSCOHADA. Il offre des fonctionnalités de lettrage manuel, automatique et de délettrage pour faciliter le rapprochement des comptes.

## 🎯 Fonctionnalités

### ✅ Lettrage Manuel
- Lettrage de lignes d'écriture sélectionnées manuellement
- Vérification automatique de l'équilibre
- Génération automatique de codes de lettrage
- Traçabilité complète (utilisateur, date)

### ✅ Lettrage Automatique
- Lettrage par montant exact (avec tolérance configurable)
- Lettrage par référence d'écriture
- Algorithmes optimisés pour gros volumes
- Rapport détaillé des opérations effectuées

### ✅ Délettrage
- Délettrage sélectif de lignes
- Suppression complète des informations de lettrage
- Historique des opérations

### ✅ Consultation et Statistiques
- Consultation des lignes à lettrer
- Calcul des soldes lettrés/non lettrés
- Statistiques de lettrage par compte
- Filtres avancés par période et montant

## 🏗️ Architecture

### Modèle de Données

Les champs de lettrage sont ajoutés au modèle `LigneEcriture` :

```javascript
{
  lettrage: STRING(10),           // Code de lettrage
  dateLettrage: DATE,             // Date du lettrage
  utilisateurLettrage: UUID       // Utilisateur ayant effectué le lettrage
}
```

### Services

#### LettrageService
Service principal gérant toutes les opérations de lettrage :

- `lettrageManuel(compteNumero, ligneIds, codeLettrage, utilisateurId)`
- `lettrageAutomatique(compteNumero, criteres, utilisateurId)`
- `delettrage(ligneIds, utilisateurId)`
- `getLignesALettrer(compteNumero, filtres)`
- `getSoldeLettre(compteNumero, periode)`
- `getSoldeNonLettre(compteNumero, periode)`

## 🔌 API REST

### Endpoints Disponibles

#### POST /api/v1/lettrage/manuel
Effectue un lettrage manuel de lignes d'écriture.

**Paramètres :**
```json
{
  "compteNumero": "411000",
  "ligneIds": ["uuid1", "uuid2"],
  "codeLettrage": "AA001" // Optionnel
}
```

**Réponse :**
```json
{
  "success": true,
  "message": "Lettrage effectué avec succès",
  "data": {
    "codeLettrage": "AA001",
    "nombreLignes": 2,
    "dateLettrage": "2024-06-15T10:30:00Z",
    "solde": 0
  }
}
```

#### POST /api/v1/lettrage/automatique
Lance un lettrage automatique sur un compte.

**Paramètres :**
```json
{
  "compteNumero": "411000",
  "criteres": {
    "dateDebut": "2024-01-01",
    "dateFin": "2024-12-31",
    "toleranceMontant": 0.01,
    "toleranceDate": 0,
    "lettrerParMontant": true,
    "lettrerParReference": true
  }
}
```

**Réponse :**
```json
{
  "success": true,
  "message": "Lettrage automatique terminé",
  "data": {
    "lettragesEffectues": 5,
    "lignesTraitees": 12,
    "details": [...]
  }
}
```

#### POST /api/v1/lettrage/delettrage
Effectue un délettrage de lignes.

**Paramètres :**
```json
{
  "ligneIds": ["uuid1", "uuid2"]
}
```

#### GET /api/v1/lettrage/lignes/:compteNumero
Récupère les lignes à lettrer pour un compte.

**Paramètres de requête :**
- `dateDebut` : Date de début (ISO 8601)
- `dateFin` : Date de fin (ISO 8601)
- `montantMin` : Montant minimum
- `montantMax` : Montant maximum
- `page` : Numéro de page (défaut: 1)
- `limit` : Nombre d'éléments par page (défaut: 50)
- `seulement_non_lettrees` : Afficher seulement les non lettrées (défaut: true)

#### GET /api/v1/lettrage/soldes/:compteNumero
Calcule les soldes lettrés et non lettrés d'un compte.

**Réponse :**
```json
{
  "success": true,
  "data": {
    "compteNumero": "411000",
    "periode": {...},
    "soldes": {
      "lettre": 15000.00,
      "nonLettre": 5000.00,
      "total": 20000.00
    },
    "pourcentageLettrage": 75.0
  }
}
```

#### GET /api/v1/lettrage/statistiques/:compteNumero
Fournit des statistiques détaillées de lettrage.

## 💡 Utilisation Pratique

### Exemple 1 : Lettrage Manuel de Factures Clients

```javascript
// 1. Récupérer les lignes à lettrer
const lignes = await fetch('/api/v1/lettrage/lignes/411000?seulement_non_lettrees=true');

// 2. Sélectionner les lignes à lettrer (facture + règlement)
const lignesSelectionnees = ['uuid-facture', 'uuid-reglement'];

// 3. Effectuer le lettrage
const resultat = await fetch('/api/v1/lettrage/manuel', {
  method: 'POST',
  body: JSON.stringify({
    compteNumero: '411000',
    ligneIds: lignesSelectionnees
  })
});
```

### Exemple 2 : Lettrage Automatique Mensuel

```javascript
// Lettrage automatique pour le mois en cours
const resultat = await fetch('/api/v1/lettrage/automatique', {
  method: 'POST',
  body: JSON.stringify({
    compteNumero: '411000',
    criteres: {
      dateDebut: '2024-06-01',
      dateFin: '2024-06-30',
      toleranceMontant: 0.01,
      lettrerParMontant: true,
      lettrerParReference: true
    }
  })
});

console.log(`${resultat.data.lettragesEffectues} lettrages effectués`);
console.log(`${resultat.data.lignesTraitees} lignes traitées`);
```

### Exemple 3 : Suivi des Soldes

```javascript
// Obtenir les soldes d'un compte
const soldes = await fetch('/api/v1/lettrage/soldes/411000?dateDebut=2024-01-01&dateFin=2024-12-31');

console.log(`Solde lettré: ${soldes.data.soldes.lettre}`);
console.log(`Solde non lettré: ${soldes.data.soldes.nonLettre}`);
console.log(`Pourcentage de lettrage: ${soldes.data.pourcentageLettrage}%`);
```

## 🔧 Configuration

### Paramètres de Lettrage Automatique

- **toleranceMontant** : Tolérance en valeur absolue pour les montants (défaut: 0.01)
- **toleranceDate** : Tolérance en jours pour les dates (défaut: 0)
- **lettrerParMontant** : Activer le lettrage par montant exact (défaut: true)
- **lettrerParReference** : Activer le lettrage par référence (défaut: true)

### Codes de Lettrage

Les codes de lettrage sont générés automatiquement selon le format :
- **Format** : `{2 premières lettres du compte}{numéro séquentiel sur 3 chiffres}`
- **Exemple** : `41001`, `41002`, `41003` pour le compte 411000

## 🧪 Tests

### Exécution des Tests

```bash
# Tests unitaires du service
npm test -- --grep "LettrageService"

# Tests d'intégration API
npm test -- --grep "API Lettrage"

# Tests complets du module
npm test src/tests/lettrage.test.js
```

### Couverture de Tests

- ✅ Lettrage manuel avec validation
- ✅ Lettrage automatique par montant
- ✅ Lettrage automatique par référence
- ✅ Délettrage et vérifications
- ✅ Calculs de soldes
- ✅ API REST complète
- ✅ Gestion des erreurs

## 📊 Performance

### Optimisations Implémentées

- **Index de base de données** sur les champs de lettrage
- **Requêtes optimisées** avec jointures efficaces
- **Pagination** pour les gros volumes
- **Transactions** pour la cohérence des données

### Recommandations

- Utiliser le lettrage automatique pour les gros volumes
- Effectuer les lettrages par petits lots (< 1000 lignes)
- Programmer les lettrages automatiques en heures creuses
- Surveiller les performances avec les statistiques

## 🔒 Sécurité

### Contrôles d'Accès

- Authentification requise pour toutes les opérations
- Traçabilité complète des utilisateurs
- Validation des permissions par compte
- Audit des opérations de lettrage/délettrage

### Validation des Données

- Vérification de l'équilibre avant lettrage
- Validation des UUIDs des lignes
- Contrôle de l'existence des comptes
- Vérification du statut des écritures (VALIDEE uniquement)

## 🚀 Prochaines Évolutions

- [ ] Lettrage par échéance
- [ ] Lettrage partiel avec reliquats
- [ ] Interface graphique de lettrage
- [ ] Rapports de lettrage avancés
- [ ] Intégration avec les workflows de validation
- [ ] Lettrage inter-comptes
- [ ] Lettrage automatique par IA

---

*Ce module respecte intégralement les normes SYSCOHADA et les bonnes pratiques comptables.*
