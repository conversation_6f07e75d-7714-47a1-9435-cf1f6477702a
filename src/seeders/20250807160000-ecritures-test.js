'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Récupérer une société existante
    const [societes] = await queryInterface.sequelize.query(
      'SELECT id FROM societes LIMIT 1'
    );
    
    if (societes.length === 0) {
      console.log('Aucune société trouvée, création d\'une société de test');
      await queryInterface.bulkInsert('societes', [{
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        nom: 'SARL TEST ECRITURES',
        adresse: '123 Rue de Test, Dakar',
        telephone: '+221 33 123 45 67',
        email: '<EMAIL>',
        created_at: new Date(),
        updated_at: new Date()
      }]);
      
      const [newSocietes] = await queryInterface.sequelize.query(
        'SELECT id FROM societes WHERE nom = \'SARL TEST ECRITURES\' LIMIT 1'
      );
      societes.push(newSocietes[0]);
    }

    const societeId = societes[0].id;

    // Récupérer un exercice existant ou en créer un
    const [exercices] = await queryInterface.sequelize.query(
      `SELECT id FROM exercice_comptables WHERE societe_id = '${societeId}' LIMIT 1`
    );
    
    let exerciceId;
    if (exercices.length === 0) {
      await queryInterface.bulkInsert('exercice_comptables', [{
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        date_debut: '2025-01-01',
        date_fin: '2025-12-31',
        libelle: 'Exercice 2025',
        statut: 'OUVERT',
        societe_id: societeId,
        created_at: new Date(),
        updated_at: new Date()
      }]);
      
      const [newExercices] = await queryInterface.sequelize.query(
        `SELECT id FROM exercice_comptables WHERE societe_id = '${societeId}' LIMIT 1`
      );
      exerciceId = newExercices[0].id;
    } else {
      exerciceId = exercices[0].id;
    }

    // Créer des écritures de test
    const ecritures = [
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        numero_ecriture: 'VT000001',
        date_ecriture: '2025-08-01',
        libelle: 'Vente marchandises client Dupont',
        reference: 'FACT-2025-001',
        piece_justificative: 'Facture 001',
        statut: 'VALIDEE',
        date_validation: new Date(),
        journal_code: 'VT',
        exercice_id: exerciceId,
        societe_id: societeId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        numero_ecriture: 'AC000001',
        date_ecriture: '2025-08-02',
        libelle: 'Achat fournitures bureau',
        reference: 'FACT-FOUR-001',
        piece_justificative: 'Facture fournisseur 001',
        statut: 'VALIDEE',
        date_validation: new Date(),
        journal_code: 'AC',
        exercice_id: exerciceId,
        societe_id: societeId,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        numero_ecriture: null, // En brouillard
        date_ecriture: '2025-08-07',
        libelle: 'Règlement client Martin',
        reference: 'REG-001',
        statut: 'BROUILLARD',
        journal_code: 'BQ',
        exercice_id: exerciceId,
        societe_id: societeId,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('ecriture_comptables', ecritures);

    // Récupérer les IDs des écritures créées
    const [ecrituresCreees] = await queryInterface.sequelize.query(
      `SELECT id, libelle FROM ecriture_comptables WHERE societe_id = '${societeId}' ORDER BY created_at DESC LIMIT 3`
    );

    // Créer les lignes d'écriture
    const lignes = [];

    // Lignes pour la vente (écriture 1)
    const venteLignes = [
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        ecriture_id: ecrituresCreees.find(e => e.libelle.includes('Vente marchandises')).id,
        compte_numero: '411000', // Clients
        libelle: 'Client Dupont',
        debit: 118000, // 100000 + 18000 TVA
        credit: 0,
        reference: 'FACT-2025-001',
        ordre: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        ecriture_id: ecrituresCreees.find(e => e.libelle.includes('Vente marchandises')).id,
        compte_numero: '701000', // Ventes de marchandises
        libelle: 'Vente marchandises',
        debit: 0,
        credit: 100000,
        reference: 'FACT-2025-001',
        ordre: 2,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        ecriture_id: ecrituresCreees.find(e => e.libelle.includes('Vente marchandises')).id,
        compte_numero: '443100', // TVA collectée
        libelle: 'TVA collectée 18%',
        debit: 0,
        credit: 18000,
        reference: 'FACT-2025-001',
        ordre: 3,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // Lignes pour l'achat (écriture 2)
    const achatLignes = [
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        ecriture_id: ecrituresCreees.find(e => e.libelle.includes('Achat fournitures')).id,
        compte_numero: '606000', // Achats de fournitures
        libelle: 'Fournitures de bureau',
        debit: 50000,
        credit: 0,
        reference: 'FACT-FOUR-001',
        ordre: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        ecriture_id: ecrituresCreees.find(e => e.libelle.includes('Achat fournitures')).id,
        compte_numero: '443200', // TVA déductible
        libelle: 'TVA déductible 18%',
        debit: 9000,
        credit: 0,
        reference: 'FACT-FOUR-001',
        ordre: 2,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        ecriture_id: ecrituresCreees.find(e => e.libelle.includes('Achat fournitures')).id,
        compte_numero: '401000', // Fournisseurs
        libelle: 'Fournisseur Papeterie Moderne',
        debit: 0,
        credit: 59000,
        reference: 'FACT-FOUR-001',
        ordre: 3,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // Lignes pour le règlement en brouillard (écriture 3)
    const reglementLignes = [
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        ecriture_id: ecrituresCreees.find(e => e.libelle.includes('Règlement client')).id,
        compte_numero: '521000', // Banque
        libelle: 'Virement client Martin',
        debit: 75000,
        credit: 0,
        reference: 'REG-001',
        ordre: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: queryInterface.sequelize.fn('gen_random_uuid'),
        ecriture_id: ecrituresCreees.find(e => e.libelle.includes('Règlement client')).id,
        compte_numero: '411000', // Clients
        libelle: 'Client Martin',
        debit: 0,
        credit: 75000,
        reference: 'REG-001',
        lettrage: 'A001', // Exemple de lettrage
        date_lettrage: new Date(),
        ordre: 2,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    lignes.push(...venteLignes, ...achatLignes, ...reglementLignes);

    await queryInterface.bulkInsert('ligne_ecritures', lignes);

    console.log('✅ Données de test créées avec succès :');
    console.log('- 3 écritures comptables (2 validées, 1 en brouillard)');
    console.log('- 8 lignes d\'écriture avec exemples de lettrage');
    console.log('- Écritures de vente, achat et règlement');
  },

  async down(queryInterface, Sequelize) {
    // Supprimer les lignes d'écriture de test
    await queryInterface.bulkDelete('ligne_ecritures', {
      reference: ['FACT-2025-001', 'FACT-FOUR-001', 'REG-001']
    });

    // Supprimer les écritures de test
    await queryInterface.bulkDelete('ecriture_comptables', {
      reference: ['FACT-2025-001', 'FACT-FOUR-001', 'REG-001']
    });

    // Supprimer la société de test si elle a été créée
    await queryInterface.bulkDelete('societes', {
      nom: 'SARL TEST ECRITURES'
    });
  }
};
