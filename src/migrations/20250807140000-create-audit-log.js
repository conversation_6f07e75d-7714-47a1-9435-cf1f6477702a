'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Créer le type ENUM pour les actions d'audit
    await queryInterface.sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE enum_audit_logs_action AS ENUM ('CREATE', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT', 'RESET');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryInterface.createTable('audit_logs', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      action: {
        type: 'enum_audit_logs_action',
        allowNull: false,
        comment: 'Type d\'action effectuée'
      },
      table: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: 'Nom de la table concernée'
      },
      record_id: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: 'ID de l\'enregistrement concerné'
      },
      anciens_valeurs: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Valeurs avant modification (JSON)'
      },
      nouvelles_valeurs: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Valeurs après modification (JSON)'
      },
      utilisateur_id: {
        type: Sequelize.UUID,
        allowNull: true,
        comment: 'ID de l\'utilisateur ayant effectué l\'action'
      },
      societe_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'societes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: 'ID de la société concernée'
      },
      adresse_ip: {
        type: Sequelize.STRING(45),
        allowNull: true,
        comment: 'Adresse IP de l\'utilisateur'
      },
      user_agent: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'User Agent du navigateur'
      },
      details: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Détails supplémentaires de l\'action'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Créer les index pour optimiser les performances
    await queryInterface.addIndex('audit_logs', ['action'], {
      name: 'idx_audit_logs_action'
    });

    await queryInterface.addIndex('audit_logs', ['table'], {
      name: 'idx_audit_logs_table'
    });

    await queryInterface.addIndex('audit_logs', ['table', 'record_id'], {
      name: 'idx_audit_logs_table_record'
    });

    await queryInterface.addIndex('audit_logs', ['utilisateur_id'], {
      name: 'idx_audit_logs_utilisateur'
    });

    await queryInterface.addIndex('audit_logs', ['societe_id'], {
      name: 'idx_audit_logs_societe'
    });

    await queryInterface.addIndex('audit_logs', ['created_at'], {
      name: 'idx_audit_logs_created_at'
    });

    // Index composé pour les recherches fréquentes
    await queryInterface.addIndex('audit_logs', ['societe_id', 'created_at'], {
      name: 'idx_audit_logs_societe_date'
    });

    await queryInterface.addIndex('audit_logs', ['table', 'action', 'created_at'], {
      name: 'idx_audit_logs_table_action_date'
    });
  },

  async down(queryInterface, Sequelize) {
    // Supprimer les index
    await queryInterface.removeIndex('audit_logs', 'idx_audit_logs_action');
    await queryInterface.removeIndex('audit_logs', 'idx_audit_logs_table');
    await queryInterface.removeIndex('audit_logs', 'idx_audit_logs_table_record');
    await queryInterface.removeIndex('audit_logs', 'idx_audit_logs_utilisateur');
    await queryInterface.removeIndex('audit_logs', 'idx_audit_logs_societe');
    await queryInterface.removeIndex('audit_logs', 'idx_audit_logs_created_at');
    await queryInterface.removeIndex('audit_logs', 'idx_audit_logs_societe_date');
    await queryInterface.removeIndex('audit_logs', 'idx_audit_logs_table_action_date');

    // Supprimer la table
    await queryInterface.dropTable('audit_logs');

    // Supprimer le type ENUM
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS enum_audit_logs_action;
    `);
  }
};
