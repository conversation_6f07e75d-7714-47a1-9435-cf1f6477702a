'use strict';

const express = require('express');
const router = express.Router();
const Joi = require('joi');

const exerciseController = require('../controllers/exerciseController');
const { protect } = require('../middleware/auth');
const { validate } = require('../middleware/validation');

/**
 * Schémas de validation Joi pour les exercices
 */

// Schéma pour les options de clôture
const closureOptionsSchema = Joi.object({
  forcerCloture: Joi.boolean().default(false),
  genererEcrituresResultat: Joi.boolean().default(true),
  genererEcrituresReouverture: Joi.boolean().default(true),
  archiverDonnees: Joi.boolean().default(false)
});

/**
 * Routes pour la gestion des exercices et clôtures
 */

/**
 * @swagger
 * /api/v1/exercises/{id}/validate-closure:
 *   post:
 *     summary: Valide les pré-requis pour la clôture d'exercice
 *     tags: [Exercices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice
 *     responses:
 *       200:
 *         description: Validation des pré-requis terminée
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     validationGlobale:
 *                       type: boolean
 *                     validations:
 *                       type: object
 *                     recommandations:
 *                       type: array
 *       404:
 *         description: Exercice inexistant
 */
router.post('/:id/validate-closure',
  protect,
  validate(Joi.object({
    id: Joi.string().uuid().required()
  }), 'params'),
  exerciseController.validerPrerequisCloture.bind(exerciseController)
);

/**
 * @swagger
 * /api/v1/exercises/{id}/close:
 *   post:
 *     summary: Lance la clôture d'exercice
 *     tags: [Exercices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               forcerCloture:
 *                 type: boolean
 *                 default: false
 *                 description: Forcer la clôture même si les pré-requis ne sont pas satisfaits
 *               genererEcrituresResultat:
 *                 type: boolean
 *                 default: true
 *                 description: Générer les écritures de détermination du résultat
 *               genererEcrituresReouverture:
 *                 type: boolean
 *                 default: true
 *                 description: Générer l'exercice suivant et les écritures de réouverture
 *               archiverDonnees:
 *                 type: boolean
 *                 default: false
 *                 description: Archiver les données de l'exercice
 *     responses:
 *       200:
 *         description: Clôture d'exercice terminée avec succès
 *       400:
 *         description: Exercice déjà clôturé ou paramètres invalides
 *       404:
 *         description: Exercice inexistant
 *       422:
 *         description: Pré-requis de clôture non satisfaits
 */
router.post('/:id/close',
  protect,
  validate(Joi.object({
    id: Joi.string().uuid().required()
  }), 'params'),
  validate(closureOptionsSchema),
  exerciseController.cloturerExercice.bind(exerciseController)
);

/**
 * @swagger
 * /api/v1/exercises/{id}/reopen:
 *   post:
 *     summary: Rouvre un exercice clôturé
 *     tags: [Exercices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice
 *     responses:
 *       200:
 *         description: Exercice rouvert avec succès
 *       400:
 *         description: Exercice non clôturé
 *       404:
 *         description: Exercice inexistant
 *       422:
 *         description: Impossible de rouvrir (contraintes métier)
 */
router.post('/:id/reopen',
  protect,
  validate(Joi.object({
    id: Joi.string().uuid().required()
  }), 'params'),
  exerciseController.rouvrirExercice.bind(exerciseController)
);

/**
 * @swagger
 * /api/v1/exercises/{id}/simulate-closure:
 *   post:
 *     summary: Simule une clôture d'exercice
 *     tags: [Exercices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice
 *     responses:
 *       200:
 *         description: Simulation de clôture générée avec succès
 *       400:
 *         description: Exercice déjà clôturé
 *       404:
 *         description: Exercice inexistant
 */
router.post('/:id/simulate-closure',
  protect,
  validate(Joi.object({
    id: Joi.string().uuid().required()
  }), 'params'),
  exerciseController.simulerCloture.bind(exerciseController)
);

/**
 * @swagger
 * /api/v1/exercises/{id}/closure-status:
 *   get:
 *     summary: Récupère le statut de clôture d'un exercice
 *     tags: [Exercices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice
 *     responses:
 *       200:
 *         description: Statut de clôture récupéré avec succès
 *       404:
 *         description: Exercice inexistant
 */
router.get('/:id/closure-status',
  protect,
  validate(Joi.object({
    id: Joi.string().uuid().required()
  }), 'params'),
  exerciseController.obtenirStatutCloture.bind(exerciseController)
);

/**
 * @swagger
 * /api/v1/exercises/{id}/closure-report:
 *   get:
 *     summary: Génère le rapport de clôture
 *     tags: [Exercices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [JSON, PDF]
 *           default: JSON
 *         description: Format du rapport
 *     responses:
 *       200:
 *         description: Rapport de clôture généré avec succès
 *       404:
 *         description: Exercice inexistant
 *       501:
 *         description: Format non encore implémenté
 */
router.get('/:id/closure-report',
  protect,
  validate(Joi.object({
    id: Joi.string().uuid().required()
  }), 'params'),
  validate(Joi.object({
    format: Joi.string().valid('JSON', 'PDF').default('JSON')
  }), 'query'),
  exerciseController.genererRapportCloture.bind(exerciseController)
);

/**
 * @swagger
 * /api/v1/exercises/closure-overview:
 *   get:
 *     summary: Vue d'ensemble des clôtures d'exercices
 *     tags: [Exercices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: societeId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de la société
 *     responses:
 *       200:
 *         description: Vue d'ensemble des clôtures récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     exercices:
 *                       type: array
 *                       items:
 *                         type: object
 *                     statistiques:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         ouverts:
 *                           type: integer
 *                         clotures:
 *                           type: integer
 *                         pretsPourCloture:
 *                           type: integer
 *       400:
 *         description: ID société manquant
 */
router.get('/closure-overview',
  protect,
  validate(Joi.object({
    societeId: Joi.string().uuid().required()
  }), 'query'),
  exerciseController.obtenirVueEnsembleClotures.bind(exerciseController)
);

module.exports = router;