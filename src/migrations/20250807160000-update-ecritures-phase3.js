'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Mettre à jour l'ENUM statut pour ajouter CLOTUREE (si elle n'existe pas)
    try {
      await queryInterface.sequelize.query(`
        ALTER TYPE enum_ecriture_comptables_statut ADD VALUE 'CLOTUREE';
      `);
    } catch (error) {
      console.log('Valeur CLOTUREE existe déjà dans l\'ENUM');
    }

    // Ajouter les nouveaux champs à la table ecriture_comptables (si ils n'existent pas)
    try {
      await queryInterface.addColumn('ecriture_comptables', 'piece_justificative', {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Référence de la pièce justificative'
      });
    } catch (error) {
      console.log('Colonne piece_justificative existe déjà');
    }

    try {
      await queryInterface.addColumn('ecriture_comptables', 'date_validation', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Date de validation de l\'écriture'
      });
    } catch (error) {
      console.log('Colonne date_validation existe déjà');
    }

    try {
      await queryInterface.addColumn('ecriture_comptables', 'utilisateur_creation', {
        type: Sequelize.UUID,
        allowNull: true,
        comment: 'Utilisateur ayant créé l\'écriture'
      });
    } catch (error) {
      console.log('Colonne utilisateur_creation existe déjà');
    }

    try {
      await queryInterface.addColumn('ecriture_comptables', 'utilisateur_validation', {
        type: Sequelize.UUID,
        allowNull: true,
        comment: 'Utilisateur ayant validé l\'écriture'
      });
    } catch (error) {
      console.log('Colonne utilisateur_validation existe déjà');
    }

    // Ajouter les nouveaux champs à la table ligne_ecritures (si ils n'existent pas)
    try {
      await queryInterface.addColumn('ligne_ecritures', 'date_lettrage', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Date du lettrage'
      });
    } catch (error) {
      console.log('Colonne date_lettrage existe déjà');
    }

    try {
      await queryInterface.addColumn('ligne_ecritures', 'ordre', {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: 'Ordre d\'affichage de la ligne dans l\'écriture'
      });
    } catch (error) {
      console.log('Colonne ordre existe déjà');
    }

    // Renommer les colonnes pour correspondre au modèle
    try {
      await queryInterface.renameColumn('ligne_ecritures', 'montant_debit', 'debit');
    } catch (error) {
      console.log('Colonne montant_debit déjà renommée ou n\'existe pas');
    }

    try {
      await queryInterface.renameColumn('ligne_ecritures', 'montant_credit', 'credit');
    } catch (error) {
      console.log('Colonne montant_credit déjà renommée ou n\'existe pas');
    }

    // Créer des index pour optimiser les performances (si ils n'existent pas)
    const indexes = [
      { table: 'ecriture_comptables', fields: ['statut'], name: 'idx_ecriture_statut' },
      { table: 'ecriture_comptables', fields: ['date_ecriture'], name: 'idx_ecriture_date' },
      { table: 'ecriture_comptables', fields: ['journal_code', 'date_ecriture'], name: 'idx_ecriture_journal_date' },
      { table: 'ecriture_comptables', fields: ['societe_id', 'statut'], name: 'idx_ecriture_societe_statut' },
      { table: 'ecriture_comptables', fields: ['exercice_id'], name: 'idx_ecriture_exercice' },
      { table: 'ligne_ecritures', fields: ['compte_numero'], name: 'idx_ligne_compte' },
      { table: 'ligne_ecritures', fields: ['lettrage'], name: 'idx_ligne_lettrage' },
      { table: 'ligne_ecritures', fields: ['compte_numero', 'lettrage'], name: 'idx_ligne_compte_lettrage' },
      { table: 'ligne_ecritures', fields: ['ecriture_id', 'ordre'], name: 'idx_ligne_ecriture_ordre' }
    ];

    for (const index of indexes) {
      try {
        await queryInterface.addIndex(index.table, index.fields, {
          name: index.name
        });
      } catch (error) {
        console.log(`Index ${index.name} existe déjà`);
      }
    }

    // Ajouter la valeur VALIDEE à l'ENUM si elle n'existe pas
    try {
      await queryInterface.sequelize.query(`
        ALTER TYPE enum_ecriture_comptables_statut ADD VALUE 'VALIDEE';
      `);
    } catch (error) {
      console.log('Valeur VALIDEE existe déjà dans l\'ENUM');
    }

    // Mettre à jour les données existantes
    await queryInterface.sequelize.query(`
      UPDATE ecriture_comptables
      SET statut = 'VALIDEE'
      WHERE statut = 'VALIDE';
    `);

    // Ajouter des contraintes de validation (si elles n'existent pas)
    try {
      await queryInterface.addConstraint('ligne_ecritures', {
        fields: ['debit'],
        type: 'check',
        name: 'check_debit_positive',
        where: {
          debit: {
            [Sequelize.Op.gte]: 0
          }
        }
      });
    } catch (error) {
      console.log('Contrainte check_debit_positive existe déjà');
    }

    try {
      await queryInterface.addConstraint('ligne_ecritures', {
        fields: ['credit'],
        type: 'check',
        name: 'check_credit_positive',
        where: {
          credit: {
            [Sequelize.Op.gte]: 0
          }
        }
      });
    } catch (error) {
      console.log('Contrainte check_credit_positive existe déjà');
    }
  },

  async down(queryInterface, Sequelize) {
    // Supprimer les contraintes
    await queryInterface.removeConstraint('ligne_ecritures', 'check_debit_positive');
    await queryInterface.removeConstraint('ligne_ecritures', 'check_credit_positive');

    // Supprimer les index
    await queryInterface.removeIndex('ecriture_comptables', 'idx_ecriture_statut');
    await queryInterface.removeIndex('ecriture_comptables', 'idx_ecriture_date');
    await queryInterface.removeIndex('ecriture_comptables', 'idx_ecriture_journal_date');
    await queryInterface.removeIndex('ecriture_comptables', 'idx_ecriture_societe_statut');
    await queryInterface.removeIndex('ecriture_comptables', 'idx_ecriture_exercice');
    await queryInterface.removeIndex('ligne_ecritures', 'idx_ligne_compte');
    await queryInterface.removeIndex('ligne_ecritures', 'idx_ligne_lettrage');
    await queryInterface.removeIndex('ligne_ecritures', 'idx_ligne_compte_lettrage');
    await queryInterface.removeIndex('ligne_ecritures', 'idx_ligne_ecriture_ordre');

    // Supprimer les colonnes ajoutées
    await queryInterface.removeColumn('ecriture_comptables', 'piece_justificative');
    await queryInterface.removeColumn('ecriture_comptables', 'date_validation');
    await queryInterface.removeColumn('ecriture_comptables', 'utilisateur_creation');
    await queryInterface.removeColumn('ecriture_comptables', 'utilisateur_validation');

    // Remettre les anciens noms de colonnes
    try {
      await queryInterface.renameColumn('ligne_ecritures', 'debit', 'montant_debit');
      await queryInterface.renameColumn('ligne_ecritures', 'credit', 'montant_credit');
    } catch (error) {
      console.log('Erreur lors du renommage des colonnes');
    }

    await queryInterface.removeColumn('ligne_ecritures', 'date_lettrage');
    await queryInterface.removeColumn('ligne_ecritures', 'ordre');

    // Remettre les données comme avant
    await queryInterface.sequelize.query(`
      UPDATE ecriture_comptables
      SET statut = 'VALIDE'
      WHERE statut = 'VALIDEE';
    `);
  }
};
