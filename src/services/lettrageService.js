'use strict';

const { logger } = require('../config/logger');
const {
  AppError,
  ValidationError,
  NotFoundError
} = require('../middleware/errorHandler');

/**
 * Service pour la gestion du lettrage et des rapprochements
 */
class LettrageService {
  constructor(models) {
    this.models = models;
  }

  /**
   * Effectue un lettrage manuel de lignes d'écriture
   * @param {string} compteNumero - Numéro du compte
   * @param {Array<string>} ligneIds - IDs des lignes à lettrer
   * @param {string} codeLettrage - Code de lettrage (optionnel, généré automatiquement si non fourni)
   * @param {string} utilisateurId - ID de l'utilisateur effectuant le lettrage
   * @returns {Promise<Object>} Résultat du lettrage
   */
  async lettrageManuel(compteNumero, ligneIds, codeLettrage = null, utilisateurId) {
    const transaction = await this.models.sequelize.transaction();
    
    try {
      // Validation des paramètres
      if (!compteNumero || !ligneIds || !Array.isArray(ligneIds) || ligneIds.length < 2) {
        throw new ValidationError('Le lettrage nécessite au moins 2 lignes d\'écriture');
      }

      // Vérifier que le compte existe
      const compte = await this.models.CompteComptable.findOne({
        where: { numero: compteNumero }
      });

      if (!compte) {
        throw new NotFoundError('Compte comptable');
      }

      // Récupérer les lignes à lettrer
      const lignes = await this.models.LigneEcriture.findAll({
        where: {
          id: ligneIds,
          compteNumero: compteNumero,
          lettrage: null // Seulement les lignes non lettrées
        },
        include: [
          {
            model: this.models.EcritureComptable,
            as: 'ecriture',
            where: { statut: 'VALIDEE' } // Seulement les écritures validées
          }
        ],
        transaction
      });

      if (lignes.length !== ligneIds.length) {
        throw new ValidationError('Certaines lignes sont déjà lettrées ou n\'existent pas');
      }

      // Vérifier l'équilibre du lettrage
      const solde = this.calculerSoldeLignes(lignes);
      if (Math.abs(solde) > 0.01) { // Tolérance de 1 centime
        throw new ValidationError(`Le lettrage n'est pas équilibré. Solde: ${solde}`);
      }

      // Générer un code de lettrage si non fourni
      if (!codeLettrage) {
        codeLettrage = await this.genererCodeLettrage(compteNumero, transaction);
      }

      // Effectuer le lettrage
      const dateLettrage = new Date();
      await this.models.LigneEcriture.update(
        {
          lettrage: codeLettrage,
          dateLettrage: dateLettrage,
          utilisateurLettrage: utilisateurId
        },
        {
          where: { id: ligneIds },
          transaction
        }
      );

      await transaction.commit();

      logger.info('Lettrage manuel effectué', {
        compteNumero,
        codeLettrage,
        nombreLignes: lignes.length,
        utilisateur: utilisateurId
      });

      return {
        success: true,
        codeLettrage,
        nombreLignes: lignes.length,
        dateLettrage,
        solde: 0
      };

    } catch (error) {
      await transaction.rollback();
      logger.error('Erreur lors du lettrage manuel', {
        error: error.message,
        compteNumero,
        ligneIds,
        utilisateur: utilisateurId
      });
      throw error;
    }
  }

  /**
   * Effectue un lettrage automatique basé sur des critères
   * @param {string} compteNumero - Numéro du compte
   * @param {Object} criteres - Critères de lettrage automatique
   * @param {string} utilisateurId - ID de l'utilisateur
   * @returns {Promise<Object>} Résultats du lettrage automatique
   */
  async lettrageAutomatique(compteNumero, criteres = {}, utilisateurId) {
    try {
      const {
        dateDebut,
        dateFin,
        toleranceMontant = 0.01,
        toleranceDate = 0, // jours
        lettrerParMontant = true,
        lettrerParReference = true
      } = criteres;

      // Récupérer les lignes non lettrées du compte
      const lignesNonLettrees = await this.getLignesNonLettrees(compteNumero, {
        dateDebut,
        dateFin
      });

      if (lignesNonLettrees.length < 2) {
        return {
          success: true,
          message: 'Aucun lettrage automatique possible',
          lettragesEffectues: 0,
          lignesTraitees: 0
        };
      }

      const lettragesEffectues = [];
      const lignesTraitees = new Set();

      // Lettrage par montant exact
      if (lettrerParMontant) {
        const lettragesMontant = await this.lettrerParMontantExact(
          lignesNonLettrees,
          toleranceMontant,
          utilisateurId
        );
        lettragesEffectues.push(...lettragesMontant);
        lettragesMontant.forEach(l => l.ligneIds.forEach(id => lignesTraitees.add(id)));
      }

      // Lettrage par référence
      if (lettrerParReference) {
        const lignesRestantes = lignesNonLettrees.filter(l => !lignesTraitees.has(l.id));
        const lettragesReference = await this.lettrerParReference(
          lignesRestantes,
          utilisateurId
        );
        lettragesEffectues.push(...lettragesReference);
        lettragesReference.forEach(l => l.ligneIds.forEach(id => lignesTraitees.add(id)));
      }

      logger.info('Lettrage automatique terminé', {
        compteNumero,
        lettragesEffectues: lettragesEffectues.length,
        lignesTraitees: lignesTraitees.size,
        utilisateur: utilisateurId
      });

      return {
        success: true,
        lettragesEffectues: lettragesEffectues.length,
        lignesTraitees: lignesTraitees.size,
        details: lettragesEffectues
      };

    } catch (error) {
      logger.error('Erreur lors du lettrage automatique', {
        error: error.message,
        compteNumero,
        utilisateur: utilisateurId
      });
      throw error;
    }
  }

  /**
   * Effectue un délettrage de lignes
   * @param {Array<string>} ligneIds - IDs des lignes à délettrer
   * @param {string} utilisateurId - ID de l'utilisateur
   * @returns {Promise<Object>} Résultat du délettrage
   */
  async delettrage(ligneIds, utilisateurId) {
    const transaction = await this.models.sequelize.transaction();
    
    try {
      // Vérifier que les lignes existent et sont lettrées
      const lignes = await this.models.LigneEcriture.findAll({
        where: {
          id: ligneIds,
          lettrage: { [this.models.sequelize.Op.ne]: null }
        },
        transaction
      });

      if (lignes.length === 0) {
        throw new ValidationError('Aucune ligne lettrée trouvée');
      }

      // Grouper par code de lettrage
      const groupesLettrage = {};
      lignes.forEach(ligne => {
        if (!groupesLettrage[ligne.lettrage]) {
          groupesLettrage[ligne.lettrage] = [];
        }
        groupesLettrage[ligne.lettrage].push(ligne);
      });

      // Effectuer le délettrage
      await this.models.LigneEcriture.update(
        {
          lettrage: null,
          dateLettrage: null,
          utilisateurLettrage: null
        },
        {
          where: { id: ligneIds },
          transaction
        }
      );

      await transaction.commit();

      logger.info('Délettrage effectué', {
        nombreLignes: lignes.length,
        codesLettrage: Object.keys(groupesLettrage),
        utilisateur: utilisateurId
      });

      return {
        success: true,
        nombreLignes: lignes.length,
        codesLettrage: Object.keys(groupesLettrage)
      };

    } catch (error) {
      await transaction.rollback();
      logger.error('Erreur lors du délettrage', {
        error: error.message,
        ligneIds,
        utilisateur: utilisateurId
      });
      throw error;
    }
  }

  /**
   * Récupère les lignes à lettrer pour un compte
   * @param {string} compteNumero - Numéro du compte
   * @param {Object} filtres - Filtres de recherche
   * @returns {Promise<Array>} Lignes à lettrer
   */
  async getLignesALettrer(compteNumero, filtres = {}) {
    try {
      const {
        dateDebut,
        dateFin,
        montantMin,
        montantMax,
        page = 1,
        limit = 50,
        seulement_non_lettrees = true
      } = filtres;

      const where = {
        compteNumero: compteNumero
      };

      // Filtre sur le lettrage
      if (seulement_non_lettrees) {
        where.lettrage = null;
      }

      // Filtre par montant
      if (montantMin !== undefined || montantMax !== undefined) {
        const { Op } = this.models.sequelize;
        where[Op.or] = [];

        if (montantMin !== undefined) {
          where[Op.or].push(
            { montantDebit: { [Op.gte]: montantMin } },
            { montantCredit: { [Op.gte]: montantMin } }
          );
        }

        if (montantMax !== undefined) {
          where[Op.or].push(
            { montantDebit: { [Op.lte]: montantMax } },
            { montantCredit: { [Op.lte]: montantMax } }
          );
        }
      }

      const include = [
        {
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: {
            statut: 'VALIDEE'
          },
          attributes: ['id', 'numeroEcriture', 'dateEcriture', 'libelle', 'reference'],
          include: [
            {
              model: this.models.Journal,
              as: 'journal',
              attributes: ['code', 'libelle']
            }
          ]
        },
        {
          model: this.models.CompteComptable,
          as: 'compte',
          attributes: ['numero', 'libelle', 'nature']
        }
      ];

      // Filtre par période sur l'écriture
      if (dateDebut || dateFin) {
        const { Op } = this.models.sequelize;
        include[0].where.dateEcriture = {};
        if (dateDebut) include[0].where.dateEcriture[Op.gte] = dateDebut;
        if (dateFin) include[0].where.dateEcriture[Op.lte] = dateFin;
      }

      const offset = (page - 1) * limit;

      const { count, rows } = await this.models.LigneEcriture.findAndCountAll({
        where,
        include,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['ecriture', 'dateEcriture', 'DESC'], ['createdAt', 'DESC']]
      });

      return {
        lignes: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      };

    } catch (error) {
      logger.error('Erreur lors de la récupération des lignes à lettrer', {
        error: error.message,
        compteNumero,
        filtres
      });
      throw error;
    }
  }

  /**
   * Calcule le solde lettré d'un compte sur une période
   * @param {string} compteNumero - Numéro du compte
   * @param {Object} periode - Période de calcul
   * @returns {Promise<number>} Solde lettré
   */
  async getSoldeLettre(compteNumero, periode = {}) {
    try {
      const { dateDebut, dateFin } = periode;

      const where = {
        compteNumero: compteNumero,
        lettrage: { [this.models.sequelize.Op.ne]: null }
      };

      const include = [{
        model: this.models.EcritureComptable,
        as: 'ecriture',
        where: { statut: 'VALIDEE' },
        attributes: []
      }];

      // Filtre par période
      if (dateDebut || dateFin) {
        const { Op } = this.models.sequelize;
        include[0].where.dateEcriture = {};
        if (dateDebut) include[0].where.dateEcriture[Op.gte] = dateDebut;
        if (dateFin) include[0].where.dateEcriture[Op.lte] = dateFin;
      }

      const result = await this.models.LigneEcriture.findOne({
        where,
        include,
        attributes: [
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_debit')), 'totalDebit'],
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_credit')), 'totalCredit']
        ],
        raw: true
      });

      const totalDebit = parseFloat(result?.totalDebit || 0);
      const totalCredit = parseFloat(result?.totalCredit || 0);

      return totalDebit - totalCredit;

    } catch (error) {
      logger.error('Erreur lors du calcul du solde lettré', {
        error: error.message,
        compteNumero,
        periode
      });
      throw error;
    }
  }

  /**
   * Calcule le solde non lettré d'un compte sur une période
   * @param {string} compteNumero - Numéro du compte
   * @param {Object} periode - Période de calcul
   * @returns {Promise<number>} Solde non lettré
   */
  async getSoldeNonLettre(compteNumero, periode = {}) {
    try {
      const { dateDebut, dateFin } = periode;

      const where = {
        compteNumero: compteNumero,
        lettrage: null
      };

      const include = [{
        model: this.models.EcritureComptable,
        as: 'ecriture',
        where: { statut: 'VALIDEE' },
        attributes: []
      }];

      // Filtre par période
      if (dateDebut || dateFin) {
        const { Op } = this.models.sequelize;
        include[0].where.dateEcriture = {};
        if (dateDebut) include[0].where.dateEcriture[Op.gte] = dateDebut;
        if (dateFin) include[0].where.dateEcriture[Op.lte] = dateFin;
      }

      const result = await this.models.LigneEcriture.findOne({
        where,
        include,
        attributes: [
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_debit')), 'totalDebit'],
          [this.models.sequelize.fn('SUM', this.models.sequelize.col('montant_credit')), 'totalCredit']
        ],
        raw: true
      });

      const totalDebit = parseFloat(result?.totalDebit || 0);
      const totalCredit = parseFloat(result?.totalCredit || 0);

      return totalDebit - totalCredit;

    } catch (error) {
      logger.error('Erreur lors du calcul du solde non lettré', {
        error: error.message,
        compteNumero,
        periode
      });
      throw error;
    }
  }

  // ==================== MÉTHODES UTILITAIRES PRIVÉES ====================

  /**
   * Calcule le solde d'un ensemble de lignes d'écriture
   * @param {Array} lignes - Lignes d'écriture
   * @returns {number} Solde (débit - crédit)
   */
  calculerSoldeLignes(lignes) {
    return lignes.reduce((solde, ligne) => {
      return solde + parseFloat(ligne.montantDebit || 0) - parseFloat(ligne.montantCredit || 0);
    }, 0);
  }

  /**
   * Génère un code de lettrage unique
   * @param {string} compteNumero - Numéro du compte
   * @param {Object} transaction - Transaction Sequelize
   * @returns {Promise<string>} Code de lettrage
   */
  async genererCodeLettrage(compteNumero, transaction) {
    // Récupérer le dernier code de lettrage pour ce compte
    const dernierLettrage = await this.models.LigneEcriture.findOne({
      where: {
        compteNumero: compteNumero,
        lettrage: { [this.models.sequelize.Op.ne]: null }
      },
      order: [['lettrage', 'DESC']],
      transaction
    });

    let numeroSequence = 1;
    if (dernierLettrage && dernierLettrage.lettrage) {
      // Extraire le numéro de séquence du dernier lettrage
      const match = dernierLettrage.lettrage.match(/(\d+)$/);
      if (match) {
        numeroSequence = parseInt(match[1]) + 1;
      }
    }

    // Format: AA001, AA002, etc. (AA = 2 premières lettres du compte)
    const prefixe = compteNumero.substring(0, 2);
    return `${prefixe}${numeroSequence.toString().padStart(3, '0')}`;
  }

  /**
   * Récupère les lignes non lettrées d'un compte
   * @param {string} compteNumero - Numéro du compte
   * @param {Object} filtres - Filtres additionnels
   * @returns {Promise<Array>} Lignes non lettrées
   */
  async getLignesNonLettrees(compteNumero, filtres = {}) {
    const { dateDebut, dateFin } = filtres;

    const where = {
      compteNumero: compteNumero,
      lettrage: null
    };

    const include = [{
      model: this.models.EcritureComptable,
      as: 'ecriture',
      where: { statut: 'VALIDEE' }
    }];

    // Filtre par période
    if (dateDebut || dateFin) {
      const { Op } = this.models.sequelize;
      include[0].where.dateEcriture = {};
      if (dateDebut) include[0].where.dateEcriture[Op.gte] = dateDebut;
      if (dateFin) include[0].where.dateEcriture[Op.lte] = dateFin;
    }

    return await this.models.LigneEcriture.findAll({
      where,
      include,
      order: [['ecriture', 'dateEcriture', 'ASC']]
    });
  }

  /**
   * Lettrage automatique par montant exact
   * @param {Array} lignes - Lignes à traiter
   * @param {number} tolerance - Tolérance de montant
   * @param {string} utilisateurId - ID utilisateur
   * @returns {Promise<Array>} Lettrages effectués
   */
  async lettrerParMontantExact(lignes, tolerance, utilisateurId) {
    const lettragesEffectues = [];
    const lignesTraitees = new Set();

    // Grouper les lignes par montant (avec tolérance)
    const groupesMontant = {};

    lignes.forEach(ligne => {
      if (lignesTraitees.has(ligne.id)) return;

      const montant = parseFloat(ligne.montantDebit || 0) - parseFloat(ligne.montantCredit || 0);
      const montantAbs = Math.abs(montant);

      // Chercher un groupe existant avec un montant proche
      let groupeTrouve = false;
      for (const [montantGroupe, lignesGroupe] of Object.entries(groupesMontant)) {
        if (Math.abs(parseFloat(montantGroupe) - montantAbs) <= tolerance) {
          lignesGroupe.push(ligne);
          groupeTrouve = true;
          break;
        }
      }

      if (!groupeTrouve) {
        groupesMontant[montantAbs] = [ligne];
      }
    });

    // Traiter chaque groupe pour trouver des lettrages équilibrés
    for (const [montant, lignesGroupe] of Object.entries(groupesMontant)) {
      if (lignesGroupe.length < 2) continue;

      const combinaisons = this.trouverCombinaisons(lignesGroupe, tolerance);

      for (const combinaison of combinaisons) {
        if (combinaison.every(ligne => !lignesTraitees.has(ligne.id))) {
          try {
            const codeLettrage = await this.genererCodeLettrage(
              combinaison[0].compteNumero
            );

            await this.models.LigneEcriture.update(
              {
                lettrage: codeLettrage,
                dateLettrage: new Date(),
                utilisateurLettrage: utilisateurId
              },
              {
                where: { id: combinaison.map(l => l.id) }
              }
            );

            lettragesEffectues.push({
              codeLettrage,
              ligneIds: combinaison.map(l => l.id),
              montant: parseFloat(montant),
              type: 'MONTANT_EXACT'
            });

            combinaison.forEach(ligne => lignesTraitees.add(ligne.id));
          } catch (error) {
            logger.warn('Erreur lors du lettrage automatique par montant', {
              error: error.message,
              ligneIds: combinaison.map(l => l.id)
            });
          }
        }
      }
    }

    return lettragesEffectues;
  }

  /**
   * Lettrage automatique par référence
   * @param {Array} lignes - Lignes à traiter
   * @param {string} utilisateurId - ID utilisateur
   * @returns {Promise<Array>} Lettrages effectués
   */
  async lettrerParReference(lignes, utilisateurId) {
    const lettragesEffectues = [];
    const lignesTraitees = new Set();

    // Grouper par référence d'écriture
    const groupesReference = {};

    lignes.forEach(ligne => {
      if (lignesTraitees.has(ligne.id) || !ligne.ecriture?.reference) return;

      const reference = ligne.ecriture.reference.trim();
      if (!groupesReference[reference]) {
        groupesReference[reference] = [];
      }
      groupesReference[reference].push(ligne);
    });

    // Traiter chaque groupe de référence
    for (const [reference, lignesGroupe] of Object.entries(groupesReference)) {
      if (lignesGroupe.length < 2) continue;

      const solde = this.calculerSoldeLignes(lignesGroupe);

      // Si le groupe est équilibré, effectuer le lettrage
      if (Math.abs(solde) <= 0.01) {
        try {
          const codeLettrage = await this.genererCodeLettrage(
            lignesGroupe[0].compteNumero
          );

          await this.models.LigneEcriture.update(
            {
              lettrage: codeLettrage,
              dateLettrage: new Date(),
              utilisateurLettrage: utilisateurId
            },
            {
              where: { id: lignesGroupe.map(l => l.id) }
            }
          );

          lettragesEffectues.push({
            codeLettrage,
            ligneIds: lignesGroupe.map(l => l.id),
            reference,
            type: 'REFERENCE'
          });

          lignesGroupe.forEach(ligne => lignesTraitees.add(ligne.id));
        } catch (error) {
          logger.warn('Erreur lors du lettrage automatique par référence', {
            error: error.message,
            reference,
            ligneIds: lignesGroupe.map(l => l.id)
          });
        }
      }
    }

    return lettragesEffectues;
  }

  /**
   * Trouve les combinaisons de lignes qui s'équilibrent
   * @param {Array} lignes - Lignes à analyser
   * @param {number} tolerance - Tolérance
   * @returns {Array} Combinaisons équilibrées
   */
  trouverCombinaisons(lignes, tolerance) {
    const combinaisons = [];

    // Algorithme simple : chercher des paires qui s'équilibrent
    for (let i = 0; i < lignes.length; i++) {
      for (let j = i + 1; j < lignes.length; j++) {
        const ligne1 = lignes[i];
        const ligne2 = lignes[j];

        const montant1 = parseFloat(ligne1.montantDebit || 0) - parseFloat(ligne1.montantCredit || 0);
        const montant2 = parseFloat(ligne2.montantDebit || 0) - parseFloat(ligne2.montantCredit || 0);

        if (Math.abs(montant1 + montant2) <= tolerance) {
          combinaisons.push([ligne1, ligne2]);
        }
      }
    }

    return combinaisons;
  }
}

module.exports = LettrageService;
