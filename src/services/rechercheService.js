'use strict';

const { logger } = require('../config/logger');
const {
  AppError,
  ValidationError,
  NotFoundError
} = require('../middleware/errorHandler');

/**
 * Service pour la recherche avancée et les filtres des écritures comptables
 */
class RechercheService {
  constructor(models) {
    this.models = models;
  }

  /**
   * Effectue une recherche avancée multi-critères
   * @param {Object} criteres - Critères de recherche
   * @returns {Promise<Object>} Résultats de recherche avec pagination
   */
  async rechercheAvancee(criteres = {}) {
    try {
      const {
        // Critères de base
        societeId,
        exerciceId,
        journalCode,
        statut,
        
        // Critères temporels
        dateDebut,
        dateFin,
        dateCreationDebut,
        dateCreationFin,
        
        // Critères de montant
        montantMin,
        montantMax,
        montantExact,
        
        // Critères de compte
        compteNumero,
        classeCompte,
        natureCompte,
        
        // Critères textuels
        libelle,
        reference,
        numeroEcriture,
        pieceJustificative,
        
        // Critères utilisateur
        utilisateurCreation,
        utilisateurValidation,
        
        // Critères de lettrage
        lettrage,
        statutLettrage, // 'LETTRE', 'NON_LETTRE', 'PARTIELLEMENT_LETTRE'
        
        // Options de recherche
        rechercheDansLignes = false,
        operateurLogique = 'ET', // 'ET' ou 'OU'
        
        // Pagination et tri
        page = 1,
        limit = 50,
        orderBy = 'dateEcriture',
        orderDirection = 'DESC'
      } = criteres;

      const { Op } = this.models.sequelize;
      const offset = (page - 1) * limit;

      // Construction des conditions WHERE pour les écritures
      const whereEcriture = {};
      const whereLigne = {};
      const havingConditions = [];

      // Critères de base sur les écritures
      if (societeId) whereEcriture.societeId = societeId;
      if (exerciceId) whereEcriture.exerciceId = exerciceId;
      if (journalCode) whereEcriture.journalCode = journalCode;
      if (statut) whereEcriture.statut = statut;
      if (utilisateurCreation) whereEcriture.utilisateurCreation = utilisateurCreation;
      if (utilisateurValidation) whereEcriture.utilisateurValidation = utilisateurValidation;

      // Critères temporels
      if (dateDebut || dateFin) {
        whereEcriture.dateEcriture = {};
        if (dateDebut) whereEcriture.dateEcriture[Op.gte] = dateDebut;
        if (dateFin) whereEcriture.dateEcriture[Op.lte] = dateFin;
      }

      if (dateCreationDebut || dateCreationFin) {
        whereEcriture.createdAt = {};
        if (dateCreationDebut) whereEcriture.createdAt[Op.gte] = dateCreationDebut;
        if (dateCreationFin) whereEcriture.createdAt[Op.lte] = dateCreationFin;
      }

      // Critères textuels sur les écritures
      const conditionsTextuelles = [];
      if (libelle) {
        conditionsTextuelles.push({ libelle: { [Op.iLike]: `%${libelle}%` } });
      }
      if (reference) {
        conditionsTextuelles.push({ reference: { [Op.iLike]: `%${reference}%` } });
      }
      if (numeroEcriture) {
        conditionsTextuelles.push({ numeroEcriture: { [Op.iLike]: `%${numeroEcriture}%` } });
      }
      if (pieceJustificative) {
        conditionsTextuelles.push({ pieceJustificative: { [Op.iLike]: `%${pieceJustificative}%` } });
      }

      if (conditionsTextuelles.length > 0) {
        if (operateurLogique === 'OU') {
          whereEcriture[Op.or] = conditionsTextuelles;
        } else {
          whereEcriture[Op.and] = conditionsTextuelles;
        }
      }

      // Critères sur les lignes d'écriture
      if (compteNumero) whereLigne.compteNumero = compteNumero;
      if (lettrage) whereLigne.lettrage = lettrage;

      // Critères de lettrage
      if (statutLettrage) {
        switch (statutLettrage) {
          case 'LETTRE':
            whereLigne.lettrage = { [Op.ne]: null };
            break;
          case 'NON_LETTRE':
            whereLigne.lettrage = null;
            break;
          case 'PARTIELLEMENT_LETTRE':
            // Cette logique sera implémentée avec une sous-requête
            break;
        }
      }

      // Critères de montant (sur les lignes)
      if (montantMin !== undefined || montantMax !== undefined || montantExact !== undefined) {
        const montantConditions = [];
        
        if (montantExact !== undefined) {
          montantConditions.push(
            { montantDebit: montantExact },
            { montantCredit: montantExact }
          );
        } else {
          if (montantMin !== undefined) {
            montantConditions.push(
              { montantDebit: { [Op.gte]: montantMin } },
              { montantCredit: { [Op.gte]: montantMin } }
            );
          }
          if (montantMax !== undefined) {
            montantConditions.push(
              { montantDebit: { [Op.lte]: montantMax } },
              { montantCredit: { [Op.lte]: montantMax } }
            );
          }
        }
        
        if (montantConditions.length > 0) {
          whereLigne[Op.or] = montantConditions;
        }
      }

      // Construction des includes
      const include = [
        {
          model: this.models.LigneEcriture,
          as: 'lignes',
          where: Object.keys(whereLigne).length > 0 ? whereLigne : undefined,
          required: Object.keys(whereLigne).length > 0,
          include: [
            {
              model: this.models.CompteComptable,
              as: 'compte',
              where: this.buildCompteWhere(classeCompte, natureCompte),
              required: classeCompte || natureCompte ? true : false
            }
          ]
        },
        {
          model: this.models.Journal,
          as: 'journal',
          attributes: ['code', 'libelle', 'type']
        },
        {
          model: this.models.ExerciceComptable,
          as: 'exercice',
          attributes: ['id', 'libelle', 'dateDebut', 'dateFin']
        },
        {
          model: this.models.Societe,
          as: 'societe',
          attributes: ['id', 'nom', 'sigle']
        }
      ];

      // Construction de l'ordre de tri
      const order = this.buildOrderClause(orderBy, orderDirection);

      // Exécution de la requête
      const { count, rows } = await this.models.EcritureComptable.findAndCountAll({
        where: whereEcriture,
        include,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order,
        distinct: true,
        subQuery: false
      });

      // Calcul des statistiques
      const statistiques = await this.calculerStatistiques(rows);

      logger.info('Recherche avancée effectuée', {
        criteres: Object.keys(criteres).length,
        resultats: count,
        page,
        limit
      });

      return {
        ecritures: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        },
        statistiques,
        criteres: criteres
      };

    } catch (error) {
      logger.error('Erreur lors de la recherche avancée', {
        error: error.message,
        criteres
      });
      throw error;
    }
  }

  /**
   * Effectue une recherche textuelle globale
   * @param {string} texte - Texte à rechercher
   * @param {Object} options - Options de recherche
   * @returns {Promise<Object>} Résultats de recherche
   */
  async rechercheTextuelle(texte, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        statut,
        dateDebut,
        dateFin,
        champs = ['libelle', 'reference', 'numeroEcriture', 'pieceJustificative'],
        rechercherDansLignes = true,
        caseSensitive = false,
        motExact = false,
        page = 1,
        limit = 50
      } = options;

      if (!texte || texte.trim().length < 2) {
        throw new ValidationError('Le texte de recherche doit contenir au moins 2 caractères');
      }

      const { Op } = this.models.sequelize;
      const offset = (page - 1) * limit;

      // Préparation du texte de recherche
      const texteRecherche = motExact ? texte.trim() : `%${texte.trim()}%`;
      const operateur = caseSensitive ? Op.like : Op.iLike;

      // Construction des conditions WHERE de base
      const whereEcriture = {};
      if (societeId) whereEcriture.societeId = societeId;
      if (exerciceId) whereEcriture.exerciceId = exerciceId;
      if (statut) whereEcriture.statut = statut;

      if (dateDebut || dateFin) {
        whereEcriture.dateEcriture = {};
        if (dateDebut) whereEcriture.dateEcriture[Op.gte] = dateDebut;
        if (dateFin) whereEcriture.dateEcriture[Op.lte] = dateFin;
      }

      // Construction des conditions de recherche textuelle
      const conditionsTextuelles = [];
      
      // Recherche dans les champs de l'écriture
      champs.forEach(champ => {
        if (['libelle', 'reference', 'numeroEcriture', 'pieceJustificative'].includes(champ)) {
          conditionsTextuelles.push({ [champ]: { [operateur]: texteRecherche } });
        }
      });

      // Recherche dans les lignes d'écriture si demandé
      let includeLignes = {
        model: this.models.LigneEcriture,
        as: 'lignes',
        include: [
          {
            model: this.models.CompteComptable,
            as: 'compte'
          }
        ]
      };

      if (rechercherDansLignes) {
        const conditionsLignes = [
          { libelle: { [operateur]: texteRecherche } }
        ];

        // Ajouter une condition OR pour inclure les écritures avec des lignes correspondantes
        conditionsTextuelles.push({
          '$lignes.libelle$': { [operateur]: texteRecherche }
        });

        includeLignes.required = false;
      }

      whereEcriture[Op.or] = conditionsTextuelles;

      const include = [
        includeLignes,
        {
          model: this.models.Journal,
          as: 'journal',
          attributes: ['code', 'libelle', 'type']
        },
        {
          model: this.models.ExerciceComptable,
          as: 'exercice',
          attributes: ['id', 'libelle']
        },
        {
          model: this.models.Societe,
          as: 'societe',
          attributes: ['id', 'nom', 'sigle']
        }
      ];

      const { count, rows } = await this.models.EcritureComptable.findAndCountAll({
        where: whereEcriture,
        include,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['dateEcriture', 'DESC'], ['createdAt', 'DESC']],
        distinct: true
      });

      // Mise en évidence des termes trouvés
      const resultatsAvecSurlignage = this.surlignerTermes(rows, texte, champs);

      logger.info('Recherche textuelle effectuée', {
        texte,
        resultats: count,
        options
      });

      return {
        ecritures: resultatsAvecSurlignage,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        },
        termeRecherche: texte,
        options
      };

    } catch (error) {
      logger.error('Erreur lors de la recherche textuelle', {
        error: error.message,
        texte,
        options
      });
      throw error;
    }
  }

  /**
   * Recherche par montant avec tolérance
   * @param {number} montantMin - Montant minimum
   * @param {number} montantMax - Montant maximum
   * @param {Object} options - Options de recherche
   * @returns {Promise<Object>} Résultats de recherche
   */
  async rechercheParMontant(montantMin, montantMax, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        compteNumero,
        dateDebut,
        dateFin,
        tolerance = 0,
        typeMontant = 'TOUS', // 'DEBIT', 'CREDIT', 'TOUS'
        page = 1,
        limit = 50
      } = options;

      const { Op } = this.models.sequelize;
      const offset = (page - 1) * limit;

      // Validation des montants
      if (montantMin !== undefined && montantMax !== undefined && montantMin > montantMax) {
        throw new ValidationError('Le montant minimum ne peut pas être supérieur au montant maximum');
      }

      // Construction des conditions WHERE
      const whereEcriture = {};
      const whereLigne = {};

      if (societeId) whereEcriture.societeId = societeId;
      if (exerciceId) whereEcriture.exerciceId = exerciceId;
      if (compteNumero) whereLigne.compteNumero = compteNumero;

      if (dateDebut || dateFin) {
        whereEcriture.dateEcriture = {};
        if (dateDebut) whereEcriture.dateEcriture[Op.gte] = dateDebut;
        if (dateFin) whereEcriture.dateEcriture[Op.lte] = dateFin;
      }

      // Conditions de montant avec tolérance
      const conditionsMontant = [];

      if (typeMontant === 'DEBIT' || typeMontant === 'TOUS') {
        const conditionDebit = {};
        if (montantMin !== undefined) {
          conditionDebit.montantDebit = { [Op.gte]: montantMin - tolerance };
        }
        if (montantMax !== undefined) {
          conditionDebit.montantDebit = {
            ...conditionDebit.montantDebit,
            [Op.lte]: montantMax + tolerance
          };
        }
        if (Object.keys(conditionDebit).length > 0) {
          conditionsMontant.push(conditionDebit);
        }
      }

      if (typeMontant === 'CREDIT' || typeMontant === 'TOUS') {
        const conditionCredit = {};
        if (montantMin !== undefined) {
          conditionCredit.montantCredit = { [Op.gte]: montantMin - tolerance };
        }
        if (montantMax !== undefined) {
          conditionCredit.montantCredit = {
            ...conditionCredit.montantCredit,
            [Op.lte]: montantMax + tolerance
          };
        }
        if (Object.keys(conditionCredit).length > 0) {
          conditionsMontant.push(conditionCredit);
        }
      }

      if (conditionsMontant.length > 0) {
        whereLigne[Op.or] = conditionsMontant;
      }

      const include = [
        {
          model: this.models.LigneEcriture,
          as: 'lignes',
          where: whereLigne,
          required: true,
          include: [
            {
              model: this.models.CompteComptable,
              as: 'compte'
            }
          ]
        },
        {
          model: this.models.Journal,
          as: 'journal'
        },
        {
          model: this.models.ExerciceComptable,
          as: 'exercice'
        }
      ];

      const { count, rows } = await this.models.EcritureComptable.findAndCountAll({
        where: whereEcriture,
        include,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['dateEcriture', 'DESC']],
        distinct: true
      });

      return {
        ecritures: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        },
        criteres: { montantMin, montantMax, tolerance, typeMontant }
      };

    } catch (error) {
      logger.error('Erreur lors de la recherche par montant', {
        error: error.message,
        montantMin,
        montantMax,
        options
      });
      throw error;
    }
  }

  /**
   * Recherche par compte avec hiérarchie
   * @param {string} compteNumero - Numéro du compte
   * @param {Object} options - Options de recherche
   * @returns {Promise<Object>} Résultats de recherche
   */
  async rechercheParCompte(compteNumero, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        dateDebut,
        dateFin,
        includeHierarchie = false,
        statut = 'VALIDEE',
        page = 1,
        limit = 50
      } = options;

      const { Op } = this.models.sequelize;
      const offset = (page - 1) * limit;

      // Validation du compte
      const compte = await this.models.CompteComptable.findOne({
        where: { numero: compteNumero }
      });

      if (!compte) {
        throw new NotFoundError('Compte comptable');
      }

      // Construction des conditions WHERE
      const whereEcriture = {};
      const whereLigne = {};

      if (societeId) whereEcriture.societeId = societeId;
      if (exerciceId) whereEcriture.exerciceId = exerciceId;
      if (statut) whereEcriture.statut = statut;

      if (dateDebut || dateFin) {
        whereEcriture.dateEcriture = {};
        if (dateDebut) whereEcriture.dateEcriture[Op.gte] = dateDebut;
        if (dateFin) whereEcriture.dateEcriture[Op.lte] = dateFin;
      }

      // Condition sur le compte
      if (includeHierarchie) {
        // Inclure tous les comptes qui commencent par le numéro donné
        whereLigne.compteNumero = { [Op.like]: `${compteNumero}%` };
      } else {
        whereLigne.compteNumero = compteNumero;
      }

      const include = [
        {
          model: this.models.LigneEcriture,
          as: 'lignes',
          where: whereLigne,
          required: true,
          include: [
            {
              model: this.models.CompteComptable,
              as: 'compte'
            }
          ]
        },
        {
          model: this.models.Journal,
          as: 'journal'
        },
        {
          model: this.models.ExerciceComptable,
          as: 'exercice'
        }
      ];

      const { count, rows } = await this.models.EcritureComptable.findAndCountAll({
        where: whereEcriture,
        include,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['dateEcriture', 'DESC']],
        distinct: true
      });

      // Calculer les totaux pour le compte
      const totaux = await this.calculerTotauxCompte(compteNumero, {
        dateDebut,
        dateFin,
        includeHierarchie,
        societeId,
        exerciceId
      });

      return {
        ecritures: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        },
        compte: compte,
        totaux,
        criteres: { compteNumero, includeHierarchie, dateDebut, dateFin }
      };

    } catch (error) {
      logger.error('Erreur lors de la recherche par compte', {
        error: error.message,
        compteNumero,
        options
      });
      throw error;
    }
  }

  /**
   * Recherche par période avec analyses
   * @param {string} dateDebut - Date de début
   * @param {string} dateFin - Date de fin
   * @param {Object} options - Options de recherche
   * @returns {Promise<Object>} Résultats de recherche avec analyses
   */
  async rechercheParPeriode(dateDebut, dateFin, options = {}) {
    try {
      const {
        societeId,
        exerciceId,
        journalCode,
        statut,
        groupeParJournal = false,
        groupeParMois = false,
        includeAnalyses = true,
        page = 1,
        limit = 50
      } = options;

      if (!dateDebut || !dateFin) {
        throw new ValidationError('Les dates de début et de fin sont obligatoires');
      }

      if (new Date(dateDebut) > new Date(dateFin)) {
        throw new ValidationError('La date de début ne peut pas être postérieure à la date de fin');
      }

      const { Op } = this.models.sequelize;
      const offset = (page - 1) * limit;

      // Construction des conditions WHERE
      const whereEcriture = {
        dateEcriture: {
          [Op.gte]: dateDebut,
          [Op.lte]: dateFin
        }
      };

      if (societeId) whereEcriture.societeId = societeId;
      if (exerciceId) whereEcriture.exerciceId = exerciceId;
      if (journalCode) whereEcriture.journalCode = journalCode;
      if (statut) whereEcriture.statut = statut;

      const include = [
        {
          model: this.models.LigneEcriture,
          as: 'lignes',
          include: [
            {
              model: this.models.CompteComptable,
              as: 'compte'
            }
          ]
        },
        {
          model: this.models.Journal,
          as: 'journal'
        },
        {
          model: this.models.ExerciceComptable,
          as: 'exercice'
        }
      ];

      const { count, rows } = await this.models.EcritureComptable.findAndCountAll({
        where: whereEcriture,
        include,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['dateEcriture', 'ASC']],
        distinct: true
      });

      let analyses = {};
      if (includeAnalyses) {
        analyses = await this.analyserPeriode(dateDebut, dateFin, {
          societeId,
          exerciceId,
          journalCode,
          statut
        });
      }

      let groupements = {};
      if (groupeParJournal) {
        groupements.parJournal = await this.grouperParJournal(rows);
      }
      if (groupeParMois) {
        groupements.parMois = await this.grouperParMois(rows);
      }

      return {
        ecritures: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        },
        periode: { dateDebut, dateFin },
        analyses,
        groupements,
        criteres: options
      };

    } catch (error) {
      logger.error('Erreur lors de la recherche par période', {
        error: error.message,
        dateDebut,
        dateFin,
        options
      });
      throw error;
    }
  }

  // ==================== MÉTHODES UTILITAIRES ====================

  /**
   * Construit les conditions WHERE pour les comptes
   * @param {string} classeCompte - Classe du compte
   * @param {string} natureCompte - Nature du compte
   * @returns {Object} Conditions WHERE
   */
  buildCompteWhere(classeCompte, natureCompte) {
    const where = {};
    if (classeCompte) where.classe = classeCompte;
    if (natureCompte) where.nature = natureCompte;
    return Object.keys(where).length > 0 ? where : undefined;
  }

  /**
   * Construit la clause ORDER BY
   * @param {string} orderBy - Champ de tri
   * @param {string} orderDirection - Direction du tri
   * @returns {Array} Clause ORDER
   */
  buildOrderClause(orderBy, orderDirection) {
    const validFields = ['dateEcriture', 'createdAt', 'numeroEcriture', 'libelle'];
    const validDirections = ['ASC', 'DESC'];

    const field = validFields.includes(orderBy) ? orderBy : 'dateEcriture';
    const direction = validDirections.includes(orderDirection.toUpperCase()) ?
      orderDirection.toUpperCase() : 'DESC';

    return [[field, direction], ['createdAt', 'DESC']];
  }

  /**
   * Calcule les statistiques des résultats de recherche
   * @param {Array} ecritures - Écritures trouvées
   * @returns {Object} Statistiques
   */
  async calculerStatistiques(ecritures) {
    const stats = {
      nombreEcritures: ecritures.length,
      nombreLignes: 0,
      totalDebit: 0,
      totalCredit: 0,
      repartitionParJournal: {},
      repartitionParStatut: {},
      repartitionParMois: {}
    };

    ecritures.forEach(ecriture => {
      // Statistiques par statut
      stats.repartitionParStatut[ecriture.statut] =
        (stats.repartitionParStatut[ecriture.statut] || 0) + 1;

      // Statistiques par journal
      if (ecriture.journal) {
        stats.repartitionParJournal[ecriture.journal.code] =
          (stats.repartitionParJournal[ecriture.journal.code] || 0) + 1;
      }

      // Statistiques par mois
      const mois = new Date(ecriture.dateEcriture).toISOString().substring(0, 7);
      stats.repartitionParMois[mois] = (stats.repartitionParMois[mois] || 0) + 1;

      // Statistiques des lignes
      if (ecriture.lignes) {
        stats.nombreLignes += ecriture.lignes.length;
        ecriture.lignes.forEach(ligne => {
          stats.totalDebit += parseFloat(ligne.montantDebit || 0);
          stats.totalCredit += parseFloat(ligne.montantCredit || 0);
        });
      }
    });

    return stats;
  }

  /**
   * Surligne les termes de recherche dans les résultats
   * @param {Array} ecritures - Écritures à traiter
   * @param {string} terme - Terme à surligner
   * @param {Array} champs - Champs à traiter
   * @returns {Array} Écritures avec surlignage
   */
  surlignerTermes(ecritures, terme, champs) {
    const regex = new RegExp(`(${terme})`, 'gi');

    return ecritures.map(ecriture => {
      const ecritureModifiee = { ...ecriture.toJSON() };

      champs.forEach(champ => {
        if (ecritureModifiee[champ] && typeof ecritureModifiee[champ] === 'string') {
          ecritureModifiee[champ] = ecritureModifiee[champ].replace(
            regex,
            '<mark>$1</mark>'
          );
        }
      });

      // Surligner aussi dans les lignes si demandé
      if (ecritureModifiee.lignes) {
        ecritureModifiee.lignes = ecritureModifiee.lignes.map(ligne => {
          if (ligne.libelle && typeof ligne.libelle === 'string') {
            ligne.libelle = ligne.libelle.replace(regex, '<mark>$1</mark>');
          }
          return ligne;
        });
      }

      return ecritureModifiee;
    });
  }

  /**
   * Calcule les totaux pour un compte
   * @param {string} compteNumero - Numéro du compte
   * @param {Object} options - Options de calcul
   * @returns {Promise<Object>} Totaux calculés
   */
  async calculerTotauxCompte(compteNumero, options = {}) {
    const { dateDebut, dateFin, includeHierarchie, societeId, exerciceId } = options;
    const { Op } = this.models.sequelize;

    const whereLigne = {};
    const whereEcriture = { statut: 'VALIDEE' };

    if (includeHierarchie) {
      whereLigne.compteNumero = { [Op.like]: `${compteNumero}%` };
    } else {
      whereLigne.compteNumero = compteNumero;
    }

    if (societeId) whereEcriture.societeId = societeId;
    if (exerciceId) whereEcriture.exerciceId = exerciceId;

    if (dateDebut || dateFin) {
      whereEcriture.dateEcriture = {};
      if (dateDebut) whereEcriture.dateEcriture[Op.gte] = dateDebut;
      if (dateFin) whereEcriture.dateEcriture[Op.lte] = dateFin;
    }

    const result = await this.models.LigneEcriture.findOne({
      where: whereLigne,
      include: [
        {
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: whereEcriture,
          attributes: []
        }
      ],
      attributes: [
        [this.models.sequelize.fn('SUM', this.models.sequelize.col('debit')), 'totalDebit'],
        [this.models.sequelize.fn('SUM', this.models.sequelize.col('credit')), 'totalCredit'],
        [this.models.sequelize.fn('COUNT', this.models.sequelize.col('ligne_ecritures.id')), 'nombreLignes']
      ],
      raw: true
    });

    const totalDebit = parseFloat(result?.totalDebit || 0);
    const totalCredit = parseFloat(result?.totalCredit || 0);
    const solde = totalDebit - totalCredit;

    return {
      totalDebit,
      totalCredit,
      solde,
      nombreLignes: parseInt(result?.nombreLignes || 0)
    };
  }

  /**
   * Analyse une période donnée
   * @param {string} dateDebut - Date de début
   * @param {string} dateFin - Date de fin
   * @param {Object} options - Options d'analyse
   * @returns {Promise<Object>} Analyses de la période
   */
  async analyserPeriode(dateDebut, dateFin, options = {}) {
    // Cette méthode sera étendue avec des analyses plus poussées
    return {
      periode: { dateDebut, dateFin },
      message: 'Analyses détaillées à implémenter'
    };
  }

  /**
   * Groupe les écritures par journal
   * @param {Array} ecritures - Écritures à grouper
   * @returns {Object} Groupement par journal
   */
  grouperParJournal(ecritures) {
    return ecritures.reduce((groupes, ecriture) => {
      const journalCode = ecriture.journal?.code || 'INCONNU';
      if (!groupes[journalCode]) {
        groupes[journalCode] = {
          journal: ecriture.journal,
          ecritures: [],
          total: 0
        };
      }
      groupes[journalCode].ecritures.push(ecriture);
      groupes[journalCode].total += 1;
      return groupes;
    }, {});
  }

  /**
   * Groupe les écritures par mois
   * @param {Array} ecritures - Écritures à grouper
   * @returns {Object} Groupement par mois
   */
  grouperParMois(ecritures) {
    return ecritures.reduce((groupes, ecriture) => {
      const mois = new Date(ecriture.dateEcriture).toISOString().substring(0, 7);
      if (!groupes[mois]) {
        groupes[mois] = {
          mois,
          ecritures: [],
          total: 0
        };
      }
      groupes[mois].ecritures.push(ecriture);
      groupes[mois].total += 1;
      return groupes;
    }, {});
  }
}

module.exports = RechercheService;
