'use strict';

/**
 * Migration pour ajouter les contraintes SYSCOHADA critiques
 * Phase 1 - Contraintes de base de données
 */

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 1. Contrainte équilibre débit/crédit sur les écritures
      await queryInterface.addConstraint('ecriture_comptables', {
        fields: ['total_debit', 'total_credit'],
        type: 'check',
        name: 'check_equilibre_ecriture',
        where: {
          [Sequelize.Op.and]: [
            Sequelize.literal('ABS(total_debit - total_credit) < 0.01')
          ]
        },
        transaction
      });

      // 2. Contrainte débit/crédit exclusif sur les lignes
      await queryInterface.addConstraint('ligne_ecritures', {
        fields: ['debit', 'credit'],
        type: 'check',
        name: 'check_debit_credit_exclusif',
        where: {
          [Sequelize.Op.and]: [
            Sequelize.literal('(debit > 0 AND credit = 0) OR (debit = 0 AND credit > 0)')
          ]
        },
        transaction
      });

      // 3. Contrainte montants positifs sur les lignes
      await queryInterface.addConstraint('ligne_ecritures', {
        fields: ['debit', 'credit'],
        type: 'check',
        name: 'check_montants_positifs',
        where: {
          [Sequelize.Op.and]: [
            Sequelize.literal('debit >= 0 AND credit >= 0')
          ]
        },
        transaction
      });

      // 4. Contrainte cohérence classe/numéro compte
      await queryInterface.addConstraint('compte_comptables', {
        fields: ['numero', 'classe'],
        type: 'check',
        name: 'check_coherence_classe_numero',
        where: {
          [Sequelize.Op.and]: [
            Sequelize.literal('LEFT(numero, 1)::INTEGER = classe')
          ]
        },
        transaction
      });

      // 5. Contrainte dates exercice société
      await queryInterface.addConstraint('societes', {
        fields: ['exercice_debut', 'exercice_fin'],
        type: 'check',
        name: 'check_dates_exercice',
        where: {
          [Sequelize.Op.and]: [
            Sequelize.literal('exercice_debut < exercice_fin')
          ]
        },
        transaction
      });

      // 6. Contrainte durée exercice (max 366 jours)
      await queryInterface.addConstraint('societes', {
        fields: ['exercice_debut', 'exercice_fin'],
        type: 'check',
        name: 'check_duree_exercice',
        where: {
          [Sequelize.Op.and]: [
            Sequelize.literal('(exercice_fin - exercice_debut) <= 366')
          ]
        },
        transaction
      });

      // 7. Contrainte plafond crédit positif pour les tiers
      await queryInterface.addConstraint('parties', {
        fields: ['plafond_credit'],
        type: 'check',
        name: 'check_plafond_credit_positif',
        where: {
          [Sequelize.Op.and]: [
            Sequelize.literal('plafond_credit >= 0')
          ]
        },
        transaction
      });

      // 8. Contrainte capital société positif
      await queryInterface.addConstraint('societes', {
        fields: ['capital'],
        type: 'check',
        name: 'check_capital_positif',
        where: {
          [Sequelize.Op.and]: [
            Sequelize.literal('capital >= 0')
          ]
        },
        transaction
      });

      // 9. Index performance pour les requêtes fréquentes
      await queryInterface.addIndex('ecriture_comptables', {
        fields: ['societe_id', 'date_ecriture', 'statut'],
        name: 'idx_ecritures_societe_date_statut',
        transaction
      });

      await queryInterface.addIndex('ligne_ecritures', {
        fields: ['compte_numero', 'tiers_id'],
        name: 'idx_lignes_compte_tiers',
        transaction
      });

      await queryInterface.addIndex('parties', {
        fields: ['societe_id', 'type', 'actif'],
        name: 'idx_parties_societe_type_actif',
        transaction
      });

      await queryInterface.addIndex('compte_comptables', {
        fields: ['societe_id', 'classe', 'actif'],
        name: 'idx_comptes_societe_classe_actif',
        transaction
      });

      await transaction.commit();
      console.log('✅ Contraintes SYSCOHADA ajoutées avec succès');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Erreur lors de l\'ajout des contraintes:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Suppression des contraintes dans l'ordre inverse
      await queryInterface.removeConstraint('societes', 'check_capital_positif', { transaction });
      await queryInterface.removeConstraint('parties', 'check_plafond_credit_positif', { transaction });
      await queryInterface.removeConstraint('societes', 'check_duree_exercice', { transaction });
      await queryInterface.removeConstraint('societes', 'check_dates_exercice', { transaction });
      await queryInterface.removeConstraint('compte_comptables', 'check_coherence_classe_numero', { transaction });
      await queryInterface.removeConstraint('ligne_ecritures', 'check_montants_positifs', { transaction });
      await queryInterface.removeConstraint('ligne_ecritures', 'check_debit_credit_exclusif', { transaction });
      await queryInterface.removeConstraint('ecriture_comptables', 'check_equilibre_ecriture', { transaction });

      // Suppression des index
      await queryInterface.removeIndex('compte_comptables', 'idx_comptes_societe_classe_actif', { transaction });
      await queryInterface.removeIndex('parties', 'idx_parties_societe_type_actif', { transaction });
      await queryInterface.removeIndex('ligne_ecritures', 'idx_lignes_compte_tiers', { transaction });
      await queryInterface.removeIndex('ecriture_comptables', 'idx_ecritures_societe_date_statut', { transaction });

      await transaction.commit();
      console.log('✅ Contraintes SYSCOHADA supprimées avec succès');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Erreur lors de la suppression des contraintes:', error);
      throw error;
    }
  }
};