/**
 * Migration pour créer la table des clés API
 * API Comptabilité SYSCOHADA
 */

'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('api_keys', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'Nom descriptif de la clé API'
      },
      key: {
        type: Sequelize.STRING(64),
        allowNull: false,
        unique: true,
        comment: 'Clé API (hashée)'
      },
      prefix: {
        type: Sequelize.STRING(10),
        allowNull: false,
        comment: 'Préfixe visible de la clé (pour identification)'
      },
      permissions: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: ['read'],
        comment: 'Permissions accordées à cette clé'
      },
      lastUsedAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Dernière utilisation de la clé'
      },
      expiresAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Date d\'expiration de la clé'
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
        allowNull: false,
        comment: 'Statut actif/inactif de la clé'
      },
      createdBy: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Créateur de la clé'
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Métadonnées additionnelles'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Index sur la clé pour les recherches rapides
    await queryInterface.addIndex('api_keys', ['key'], {
      unique: true,
      name: 'api_keys_key_unique'
    });

    // Index sur le statut actif
    await queryInterface.addIndex('api_keys', ['isActive'], {
      name: 'api_keys_is_active_idx'
    });

    // Index sur la date d'expiration
    await queryInterface.addIndex('api_keys', ['expiresAt'], {
      name: 'api_keys_expires_at_idx'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('api_keys');
  }
};