'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Service pour la gestion des configurations système et utilisateur
 */
class ConfigurationService {
  constructor(models) {
    this.models = models;
    this.cache = new Map(); // Cache en mémoire pour les configs fréquemment utilisées
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Obtient une configuration avec cache
   * @param {string} cle - Clé de configuration
   * @param {string} societeId - ID société (optionnel)
   * @returns {Promise<any>} Valeur de configuration
   */
  async getConfig(cle, societeId = null) {
    try {
      const cacheKey = `${cle}_${societeId || 'global'}`;
      
      // Vérifier le cache
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.value;
        }
        this.cache.delete(cacheKey);
      }

      // Récupérer depuis la base
      const valeur = await this.models.Configuration.getConfig(cle, societeId);
      
      // Mettre en cache
      this.cache.set(cacheKey, {
        value: valeur,
        timestamp: Date.now()
      });

      return valeur;

    } catch (error) {
      logger.error('Erreur lors de la récupération de configuration', {
        cle,
        societeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Définit une configuration
   * @param {string} cle - Clé de configuration
   * @param {any} valeur - Valeur à stocker
   * @param {Object} options - Options (type, description, societeId, etc.)
   * @returns {Promise<Object>} Configuration créée/mise à jour
   */
  async setConfig(cle, valeur, options = {}) {
    try {
      const config = await this.models.Configuration.setConfig(cle, valeur, options);
      
      // Invalider le cache
      const cacheKey = `${cle}_${options.societeId || 'global'}`;
      this.cache.delete(cacheKey);

      logger.info('Configuration mise à jour', {
        cle,
        valeur,
        societeId: options.societeId,
        type: options.type
      });

      return config;

    } catch (error) {
      logger.error('Erreur lors de la définition de configuration', {
        cle,
        valeur,
        options,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Obtient toutes les configurations d'une catégorie
   * @param {string} categorie - Catégorie de configuration
   * @param {string} societeId - ID société (optionnel)
   * @returns {Promise<Object>} Configurations groupées par clé
   */
  async getConfigsByCategorie(categorie, societeId = null) {
    try {
      return await this.models.Configuration.getConfigsByCategorie(categorie, societeId);
    } catch (error) {
      logger.error('Erreur lors de la récupération des configurations par catégorie', {
        categorie,
        societeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Obtient toutes les configurations avec leurs métadonnées
   * @param {string} societeId - ID société (optionnel)
   * @returns {Promise<Array>} Configurations complètes
   */
  async getAllConfigs(societeId = null) {
    try {
      const configs = await this.models.Configuration.findAll({
        where: { societeId },
        order: [['categorie', 'ASC'], ['cle', 'ASC']]
      });

      return configs.map(config => ({
        id: config.id,
        cle: config.cle,
        valeur: config.getValeurParsee(),
        valeurBrute: config.valeur,
        type: config.type,
        description: config.description,
        categorie: config.categorie,
        modifiable: config.modifiable,
        societeId: config.societeId,
        createdAt: config.createdAt,
        updatedAt: config.updatedAt
      }));

    } catch (error) {
      logger.error('Erreur lors de la récupération de toutes les configurations', {
        societeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Met à jour plusieurs configurations en une fois
   * @param {Array} configurations - Tableau de {cle, valeur, options}
   * @param {string} societeId - ID société (optionnel)
   * @returns {Promise<Array>} Configurations mises à jour
   */
  async updateMultipleConfigs(configurations, societeId = null) {
    const transaction = await this.models.sequelize.transaction();
    
    try {
      const results = [];

      for (const config of configurations) {
        const { cle, valeur, ...options } = config;
        const configOptions = { ...options, societeId };
        
        const result = await this.setConfig(cle, valeur, configOptions);
        results.push(result);
      }

      await transaction.commit();

      logger.info('Configurations multiples mises à jour', {
        nombre: configurations.length,
        societeId
      });

      return results;

    } catch (error) {
      await transaction.rollback();
      
      logger.error('Erreur lors de la mise à jour multiple des configurations', {
        configurations,
        societeId,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Réinitialise les configurations par défaut
   * @param {string} societeId - ID société (optionnel)
   * @returns {Promise<void>}
   */
  async resetToDefaults(societeId = null) {
    try {
      if (societeId) {
        // Supprimer les configs spécifiques à la société
        await this.models.Configuration.destroy({
          where: { societeId }
        });
      } else {
        // Réinitialiser les configs globales
        await this.models.Configuration.destroy({
          where: { societeId: null }
        });
        
        // Recréer les configs par défaut
        await this.models.Configuration.initialiserConfigsDefaut();
      }

      // Vider le cache
      this.clearCache();

      logger.info('Configurations réinitialisées aux valeurs par défaut', {
        societeId
      });

    } catch (error) {
      logger.error('Erreur lors de la réinitialisation des configurations', {
        societeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Exporte les configurations
   * @param {string} societeId - ID société (optionnel)
   * @returns {Promise<Object>} Configurations exportées
   */
  async exportConfigs(societeId = null) {
    try {
      const configs = await this.getAllConfigs(societeId);
      
      const exportData = {
        version: '1.0',
        exportDate: new Date(),
        societeId,
        configurations: configs.map(config => ({
          cle: config.cle,
          valeur: config.valeur,
          type: config.type,
          description: config.description,
          categorie: config.categorie,
          modifiable: config.modifiable
        }))
      };

      logger.info('Configurations exportées', {
        societeId,
        nombre: configs.length
      });

      return exportData;

    } catch (error) {
      logger.error('Erreur lors de l\'export des configurations', {
        societeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Importe les configurations
   * @param {Object} exportData - Données d'export
   * @param {string} societeId - ID société (optionnel)
   * @returns {Promise<Object>} Résultat de l'import
   */
  async importConfigs(exportData, societeId = null) {
    const transaction = await this.models.sequelize.transaction();
    
    try {
      const { configurations } = exportData;
      let imported = 0;
      let skipped = 0;

      for (const config of configurations) {
        if (!config.modifiable) {
          skipped++;
          continue;
        }

        await this.setConfig(config.cle, config.valeur, {
          type: config.type,
          description: config.description,
          categorie: config.categorie,
          modifiable: config.modifiable,
          societeId
        });
        
        imported++;
      }

      await transaction.commit();

      logger.info('Configurations importées', {
        societeId,
        imported,
        skipped,
        total: configurations.length
      });

      return {
        success: true,
        imported,
        skipped,
        total: configurations.length
      };

    } catch (error) {
      await transaction.rollback();
      
      logger.error('Erreur lors de l\'import des configurations', {
        exportData,
        societeId,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Vide le cache des configurations
   */
  clearCache() {
    this.cache.clear();
    logger.info('Cache des configurations vidé');
  }

  /**
   * Obtient les statistiques du cache
   * @returns {Object} Statistiques du cache
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      timeout: this.cacheTimeout,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * Valide une configuration avant sauvegarde
   * @param {string} cle - Clé de configuration
   * @param {any} valeur - Valeur à valider
   * @param {string} type - Type de la valeur
   * @returns {boolean} True si valide
   */
  validateConfig(cle, valeur, type) {
    // Validations spécifiques par clé
    const validations = {
      'devise_principale': (val) => ['XOF', 'XAF', 'EUR', 'USD'].includes(val),
      'exercice_duree_mois': (val) => val >= 1 && val <= 24,
      'backup_frequence_heures': (val) => val >= 1 && val <= 168,
      'audit_retention_jours': (val) => val >= 30 && val <= 2555,
      'session_duree_heures': (val) => val >= 1 && val <= 168
    };

    if (validations[cle]) {
      return validations[cle](valeur);
    }

    return true; // Pas de validation spécifique
  }
}

module.exports = ConfigurationService;
