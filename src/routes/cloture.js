'use strict';

const express = require('express');
const router = express.Router();
const { models } = require('../models');
const ClotureService = require('../services/clotureService');
const ClotureController = require('../controllers/clotureController');
const { protect } = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const { body, param } = require('express-validator');

// Initialiser les services et contrôleurs
const clotureService = new ClotureService(models);
const clotureController = new ClotureController(clotureService);

/**
 * @swagger
 * /api/v1/cloture/verifier/{exerciceId}:
 *   get:
 *     summary: Vérifie la complétude des saisies pour un exercice
 *     tags: [Clôture]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: exerciceId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice à vérifier
 *     responses:
 *       200:
 *         description: Résultat de la vérification
 *       400:
 *         description: Erreur de validation
 *       401:
 *         description: Non authentifié
 *       500:
 *         description: Erreur serveur
 */
router.get(
  '/verifier/:exerciceId',
  protect,
  param('exerciceId').isUUID(),
  validate,
  (req, res, next) => clotureController.verifierCompletudeSaisies(req, res, next)
);

/**
 * @swagger
 * /api/v1/cloture/resultat/{exerciceId}:
 *   get:
 *     summary: Calcule le résultat de l'exercice
 *     tags: [Clôture]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: exerciceId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice
 *     responses:
 *       200:
 *         description: Résultat de l'exercice
 *       400:
 *         description: Erreur de validation
 *       401:
 *         description: Non authentifié
 *       500:
 *         description: Erreur serveur
 */
router.get(
  '/resultat/:exerciceId',
  protect,
  param('exerciceId').isUUID(),
  validate,
  (req, res, next) => clotureController.calculerResultatExercice(req, res, next)
);

/**
 * @swagger
 * /api/v1/cloture/ecritures/{exerciceId}:
 *   post:
 *     summary: Génère les écritures de clôture pour un exercice
 *     tags: [Clôture]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: exerciceId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice
 *     responses:
 *       200:
 *         description: Écritures de clôture générées
 *       400:
 *         description: Erreur de validation
 *       401:
 *         description: Non authentifié
 *       500:
 *         description: Erreur serveur
 */
router.post(
  '/ecritures/:exerciceId',
  protect,
  param('exerciceId').isUUID(),
  validate,
  (req, res, next) => clotureController.genererEcrituresCloture(req, res, next)
);

/**
 * @swagger
 * /api/v1/cloture/exercice/{exerciceId}:
 *   post:
 *     summary: Clôture un exercice comptable
 *     tags: [Clôture]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: exerciceId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice à clôturer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               commentaire:
 *                 type: string
 *                 description: Commentaire de clôture
 *               genererEcritures:
 *                 type: boolean
 *                 description: Générer automatiquement les écritures de clôture
 *                 default: true
 *     responses:
 *       200:
 *         description: Exercice clôturé avec succès
 *       400:
 *         description: Erreur de validation
 *       401:
 *         description: Non authentifié
 *       500:
 *         description: Erreur serveur
 */
router.post(
  '/exercice/:exerciceId',
  protect,
  param('exerciceId').isUUID(),
  body('commentaire').optional().isString(),
  body('genererEcritures').optional().isBoolean(),
  validate,
  (req, res, next) => clotureController.cloturerExercice(req, res, next)
);

/**
 * @swagger
 * /api/v1/cloture/a-nouveaux/{exerciceId}:
 *   post:
 *     summary: Génère les écritures d'à-nouveaux pour l'exercice suivant
 *     tags: [Clôture]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: exerciceId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID de l'exercice clôturé
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               nouvelExerciceId:
 *                 type: string
 *                 format: uuid
 *                 description: ID du nouvel exercice
 *     responses:
 *       200:
 *         description: Écritures d'à-nouveaux générées avec succès
 *       400:
 *         description: Erreur de validation
 *       401:
 *         description: Non authentifié
 *       500:
 *         description: Erreur serveur
 */
router.post(
  '/a-nouveaux/:exerciceId',
  protect,
  param('exerciceId').isUUID(),
  body('nouvelExerciceId').isUUID(),
  validate,
  (req, res, next) => clotureController.genererANouveaux(req, res, next)
);

module.exports = router;