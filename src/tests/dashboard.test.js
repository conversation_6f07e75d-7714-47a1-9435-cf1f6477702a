'use strict';

/* eslint-env jest */

const { DashboardService } = require('../services/dashboardService');
const models = require('../models');

// Mock des modèles et services
jest.mock('../models', () => {
  const mockModels = {
    Societe: {
      findByPk: jest.fn()
    },
    CompteComptable: {
      findOne: jest.fn(),
      findAll: jest.fn()
    },
    ExerciceComptable: {
      findOne: jest.fn(),
      findByPk: jest.fn()
    },
    EcritureComptable: {
      findAll: jest.fn()
    },
    LigneEcriture: {
      findAll: jest.fn()
    },
    sequelize: {
      Op: {
        between: 'between',
        gte: 'gte',
        lte: 'lte',
        in: 'in',
        like: 'like',
        ne: 'ne'
      }
    }
  };
  return mockModels;
});

describe('DashboardService', () => {
  let dashboardService;
  
  beforeEach(() => {
    jest.clearAllMocks();
    dashboardService = new DashboardService(models);
    
    // Mock des méthodes du calculService
    dashboardService.calculService = {
      calculerSoldeCompte: jest.fn().mockResolvedValue({
        compteNumero: '401000',
        libelle: 'Fournisseurs',
        sensNaturel: 'CREDIT',
        totalDebit: 1000,
        totalCredit: 1500,
        solde: 500,
        sensActuel: 'CREDIT',
        soldeNaturel: 500,
        nombreLignes: 10,
        dateCalcul: new Date(),
        periode: {
          dateDebut: null,
          dateFin: new Date()
        }
      }),
      calculerBalanceGenerale: jest.fn().mockResolvedValue({
        periode: { dateDebut: new Date(), dateFin: new Date() },
        options: {
          niveauDetail: 'TOUS',
          classeComptes: null,
          seulementAvecMouvement: false,
          includeNonValidees: false
        },
        totauxGeneraux: {
          totalDebit: 5000,
          totalCredit: 5000,
          nombreComptes: 50,
          nombreLignes: 100,
          equilibre: true
        },
        totauxParClasse: [
          {
            classe: 1,
            libelle: 'Comptes de capitaux',
            totalDebit: 500,
            totalCredit: 1000,
            nombreComptes: 5
          },
          {
            classe: 4,
            libelle: 'Comptes de tiers',
            totalDebit: 2000,
            totalCredit: 2500,
            nombreComptes: 20
          },
          {
            classe: 6,
            libelle: 'Comptes de charges',
            totalDebit: 1500,
            totalCredit: 0,
            nombreComptes: 15
          },
          {
            classe: 7,
            libelle: 'Comptes de produits',
            totalDebit: 0,
            totalCredit: 2000,
            nombreComptes: 10
          }
        ],
        lignesBalance: [
          {
            compteNumero: '401000',
            libelle: 'Fournisseurs',
            classe: 4,
            nature: 'COLLECTIF',
            sensNaturel: 'CREDIT',
            niveau: 6,
            totalDebit: 1000,
            totalCredit: 1500,
            solde: 500,
            sensActuel: 'CREDIT',
            nombreLignes: 10
          }
        ],
        dateCalcul: new Date()
      }),
      validerPeriode: jest.fn()
    };
    
    // Mock des réponses des modèles
    models.Societe.findByPk.mockResolvedValue({
      id: '1',
      raisonSociale: 'Société Test',
      siret: '12345678901234'
    });
    
    models.ExerciceComptable.findOne.mockResolvedValue({
      id: '1',
      annee: '2025',
      dateDebut: new Date('2025-01-01'),
      dateFin: new Date('2025-12-31'),
      statut: 'OUVERT'
    });
    
    models.ExerciceComptable.findByPk.mockResolvedValue({
      id: '1',
      annee: '2025',
      dateDebut: new Date('2025-01-01'),
      dateFin: new Date('2025-12-31')
    });
  });
  
  describe('getKPIFinanciers', () => {
    it('devrait calculer les KPIs financiers', async () => {
      // Arrange
      const societeId = '1';
      const dateDebut = new Date('2025-01-01');
      const dateFin = new Date('2025-01-31');
      const options = {
        exerciceId: '1'
      };
      
      // Act
      const resultat = await dashboardService.getKPIFinanciers(societeId, { dateDebut, dateFin }, options);
      
      // Assert
      expect(models.Societe.findByPk).toHaveBeenCalledWith('1');
      expect(dashboardService.calculService.calculerBalanceGenerale).toHaveBeenCalled();
      expect(resultat).toHaveProperty('societe');
      expect(resultat).toHaveProperty('periode');
      expect(resultat).toHaveProperty('kpis');
      expect(resultat.kpis).toHaveProperty('chiffreAffaires');
      expect(resultat.kpis).toHaveProperty('resultatNet');
      expect(resultat.kpis).toHaveProperty('ratios');
    });
  });
  
  describe('getEvolutionChiffreAffaires', () => {
    it('devrait calculer l\'évolution du chiffre d\'affaires', async () => {
      // Arrange
      const societeId = '1';
      const periodes = [
        {
          dateDebut: new Date('2025-01-01'),
          dateFin: new Date('2025-01-31'),
          libelle: 'Janvier 2025'
        },
        {
          dateDebut: new Date('2025-02-01'),
          dateFin: new Date('2025-02-28'),
          libelle: 'Février 2025'
        }
      ];
      
      // Mock de la balance pour chaque période
      dashboardService.calculService.calculerBalanceGenerale
        .mockResolvedValueOnce({
          lignesBalance: [
            {
              compteNumero: '701000',
              libelle: 'Ventes de produits finis',
              totalDebit: 0,
              totalCredit: 10000,
              nombreLignes: 5
            }
          ]
        })
        .mockResolvedValueOnce({
          lignesBalance: [
            {
              compteNumero: '701000',
              libelle: 'Ventes de produits finis',
              totalDebit: 0,
              totalCredit: 12000,
              nombreLignes: 6
            }
          ]
        });
      
      // Act
      const resultat = await dashboardService.getEvolutionChiffreAffaires(societeId, periodes);
      
      // Assert
      expect(models.Societe.findByPk).toHaveBeenCalledWith('1');
      expect(dashboardService.calculService.calculerBalanceGenerale).toHaveBeenCalledTimes(2);
      expect(resultat).toHaveProperty('societe');
      expect(resultat).toHaveProperty('periodes');
      expect(resultat.periodes).toHaveLength(2);
      expect(resultat.periodes[1]).toHaveProperty('variation');
    });
  });
  
  describe('getAnalyseChargesProduits', () => {
    it('devrait analyser les charges et produits', async () => {
      // Arrange
      const societeId = '1';
      const dateDebut = new Date('2025-01-01');
      const dateFin = new Date('2025-01-31');
      const options = {
        exerciceId: '1',
        niveauDetail: 2
      };
      
      // Act
      const resultat = await dashboardService.getAnalyseChargesProduits(societeId, { dateDebut, dateFin }, options);
      
      // Assert
      expect(models.Societe.findByPk).toHaveBeenCalledWith('1');
      expect(dashboardService.calculService.calculerBalanceGenerale).toHaveBeenCalled();
      expect(resultat).toHaveProperty('societe');
      expect(resultat).toHaveProperty('periode');
      expect(resultat).toHaveProperty('charges');
      expect(resultat).toHaveProperty('produits');
      expect(resultat).toHaveProperty('resultatNet');
    });
  });
  
  describe('getRatiosFinanciers', () => {
    it('devrait calculer les ratios financiers', async () => {
      // Arrange
      const societeId = '1';
      const dateDebut = new Date('2025-01-01');
      const dateFin = new Date('2025-01-31');
      const options = {
        exerciceId: '1'
      };
      
      // Act
      const resultat = await dashboardService.getRatiosFinanciers(societeId, { dateDebut, dateFin }, options);
      
      // Assert
      expect(models.Societe.findByPk).toHaveBeenCalledWith('1');
      expect(resultat).toHaveProperty('societe');
      expect(resultat).toHaveProperty('periode');
      expect(resultat).toHaveProperty('ratios');
      expect(resultat.ratios).toHaveProperty('rentabilite');
      expect(resultat.ratios).toHaveProperty('liquidite');
      expect(resultat.ratios).toHaveProperty('endettement');
    });
  });
  
  describe('getAlertes', () => {
    it('devrait générer des alertes financières', async () => {
      // Arrange
      const societeId = '1';
      
      // Act
      const resultat = await dashboardService.getAlertes(societeId);
      
      // Assert
      expect(models.Societe.findByPk).toHaveBeenCalledWith('1');
      expect(models.ExerciceComptable.findOne).toHaveBeenCalled();
      expect(resultat).toHaveProperty('societe');
      expect(resultat).toHaveProperty('exercice');
      expect(resultat).toHaveProperty('alertes');
    });
  });
});