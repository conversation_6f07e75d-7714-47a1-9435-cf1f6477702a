'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class TemplateLigneEcriture extends Model {
    /**
     * Associations du modèle TemplateLigneEcriture
     */
    static associate(models) {
      // Une ligne de template appartient à un template
      TemplateLigneEcriture.belongsTo(models.TemplateEcriture, {
        foreignKey: 'templateId',
        as: 'template'
      });

      // Une ligne de template référence un compte comptable
      TemplateLigneEcriture.belongsTo(models.CompteComptable, {
        foreignKey: 'compteNumero',
        targetKey: 'numero',
        as: 'compte'
      });
    }

    /**
     * Méthodes statiques
     */

    /**
     * Valide les données d'une ligne de template
     * @param {Object} donnees - Données à valider
     * @returns {Object} Résultat de validation
     */
    static validateLigneTemplateData(donnees) {
      const errors = [];

      if (!donnees.compteNumero) {
        errors.push('Le compte comptable est obligatoire');
      }

      if (!donnees.libelle || donnees.libelle.trim().length < 2) {
        errors.push('Le libellé doit contenir au moins 2 caractères');
      }

      // Vérifier que le sens est valide
      const sensValides = ['DEBIT', 'CREDIT', 'VARIABLE'];
      if (!donnees.sens || !sensValides.includes(donnees.sens)) {
        errors.push('Le sens doit être DEBIT, CREDIT ou VARIABLE');
      }

      // Si montant fixe, vérifier qu'il est positif
      if (donnees.montantFixe !== null && donnees.montantFixe !== undefined) {
        const montant = parseFloat(donnees.montantFixe);
        if (isNaN(montant) || montant < 0) {
          errors.push('Le montant fixe doit être un nombre positif');
        }
      }

      return {
        valide: errors.length === 0,
        erreurs: errors
      };
    }

    /**
     * Méthodes d'instance
     */

    /**
     * Obtient les paramètres variables de la ligne
     * @returns {Array} Liste des paramètres
     */
    getParametresVariables() {
      const parametres = [];
      const regex = /\{\{(\w+)\}\}/g;
      
      // Rechercher dans le libellé
      let match;
      while ((match = regex.exec(this.libelle || '')) !== null) {
        if (!parametres.includes(match[1])) {
          parametres.push(match[1]);
        }
      }

      // Rechercher dans la formule de montant
      if (this.formuleMontant) {
        regex.lastIndex = 0; // Reset regex
        while ((match = regex.exec(this.formuleMontant)) !== null) {
          if (!parametres.includes(match[1])) {
            parametres.push(match[1]);
          }
        }
      }

      return parametres;
    }

    /**
     * Applique des valeurs aux paramètres variables
     * @param {Object} valeurs - Valeurs des paramètres
     * @returns {Object} Ligne avec valeurs appliquées
     */
    appliquerParametres(valeurs = {}) {
      let libelleApplique = this.libelle || '';
      let montantCalcule = this.montantFixe;

      // Remplacer les paramètres dans le libellé
      Object.keys(valeurs).forEach(param => {
        const regex = new RegExp(`\\{\\{${param}\\}\\}`, 'g');
        libelleApplique = libelleApplique.replace(regex, valeurs[param]);
      });

      // Calculer le montant si formule présente
      if (this.formuleMontant) {
        try {
          let formuleAppliquee = this.formuleMontant;
          
          // Remplacer les paramètres dans la formule
          Object.keys(valeurs).forEach(param => {
            const regex = new RegExp(`\\{\\{${param}\\}\\}`, 'g');
            formuleAppliquee = formuleAppliquee.replace(regex, valeurs[param]);
          });

          // Évaluer la formule (attention: seulement des opérations simples)
          if (/^[\d\s+\-*/.()]+$/.test(formuleAppliquee)) {
            montantCalcule = eval(formuleAppliquee);
          }
        } catch (error) {
          // En cas d'erreur, garder le montant fixe
          montantCalcule = this.montantFixe || 0;
        }
      }

      return {
        compteNumero: this.compteNumero,
        libelle: libelleApplique,
        sens: this.sens,
        montant: parseFloat(montantCalcule || 0),
        ordre: this.ordre,
        valeursAppliquees: valeurs
      };
    }

    /**
     * Convertit la ligne de template en ligne d'écriture
     * @param {Object} valeurs - Valeurs des paramètres
     * @returns {Object} Ligne d'écriture
     */
    toLigneEcriture(valeurs = {}) {
      const ligneAppliquee = this.appliquerParametres(valeurs);
      
      return {
        compteNumero: ligneAppliquee.compteNumero,
        libelle: ligneAppliquee.libelle,
        debit: ligneAppliquee.sens === 'DEBIT' ? ligneAppliquee.montant : 0,
        credit: ligneAppliquee.sens === 'CREDIT' ? ligneAppliquee.montant : 0,
        ordre: ligneAppliquee.ordre
      };
    }

    /**
     * Valide la cohérence de la ligne de template
     * @returns {Object} Résultat de validation
     */
    validerCoherence() {
      const erreurs = [];

      // Vérifier que si sens VARIABLE, il y a une formule ou un paramètre
      if (this.sens === 'VARIABLE' && !this.formuleMontant) {
        const parametres = this.getParametresVariables();
        if (parametres.length === 0) {
          erreurs.push('Une ligne VARIABLE doit avoir une formule ou des paramètres');
        }
      }

      // Vérifier que le montant fixe est cohérent avec le sens
      if (this.montantFixe && this.sens === 'VARIABLE') {
        erreurs.push('Une ligne VARIABLE ne peut pas avoir un montant fixe');
      }

      return {
        valide: erreurs.length === 0,
        erreurs
      };
    }

    /**
     * Formate la ligne pour l'affichage
     * @returns {Object} Ligne formatée
     */
    formater() {
      return {
        id: this.id,
        compteNumero: this.compteNumero,
        compte: this.compte ? {
          numero: this.compte.numero,
          libelle: this.compte.libelle,
          classe: this.compte.classe,
          nature: this.compte.nature
        } : null,
        libelle: this.libelle,
        sens: this.sens,
        montantFixe: this.montantFixe,
        formuleMontant: this.formuleMontant,
        ordre: this.ordre,
        parametresVariables: this.getParametresVariables()
      };
    }
  }

  TemplateLigneEcriture.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    templateId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'template_ecritures',
        key: 'id'
      }
    },
    compteNumero: {
      type: DataTypes.STRING(10),
      allowNull: false,
      references: {
        model: 'compte_comptables',
        key: 'numero'
      }
    },
    libelle: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: 'Libellé avec paramètres variables {{param}}'
    },
    sens: {
      type: DataTypes.ENUM('DEBIT', 'CREDIT', 'VARIABLE'),
      allowNull: false,
      comment: 'VARIABLE permet de déterminer le sens selon les paramètres'
    },
    montantFixe: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      comment: 'Montant fixe si pas de formule'
    },
    formuleMontant: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: 'Formule de calcul avec paramètres {{param}} + - * /'
    },
    ordre: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: 'Ordre d\'affichage dans le template'
    },
    obligatoire: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Ligne obligatoire dans l\'écriture générée'
    },
    commentaire: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Commentaire pour l\'utilisateur'
    }
  }, {
    sequelize,
    modelName: 'TemplateLigneEcriture',
    tableName: 'template_ligne_ecritures',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['template_id', 'ordre']
      },
      {
        fields: ['compte_numero']
      }
    ]
  });

  return TemplateLigneEcriture;
};
