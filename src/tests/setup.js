// Configuration globale pour les tests
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-secret-key-for-testing-only';
process.env.DB_NAME = 'test_compta_db';
process.env.DB_USER = 'postgres';
process.env.DB_PASS = 'password';
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '5432';

// Augmenter le timeout pour les tests de base de données
jest.setTimeout(30000);

// Supprimer les logs pendant les tests
const originalConsole = console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};
