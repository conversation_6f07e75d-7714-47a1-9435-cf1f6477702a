'use strict';

const { EtatService } = require('../services/etatService');
const models = require('../models');
const fs = require('fs');
const path = require('path');

// Mock des modèles et services
jest.mock('../models', () => {
  const mockModels = {
    Societe: {
      findByPk: jest.fn()
    },
    CompteComptable: {
      findOne: jest.fn(),
      findAll: jest.fn()
    },
    Journal: {
      findOne: jest.fn(),
      findAll: jest.fn()
    },
    ExerciceComptable: {
      findByPk: jest.fn()
    },
    EcritureComptable: {
      findAll: jest.fn()
    },
    LigneEcriture: {
      findAll: jest.fn()
    },
    sequelize: {
      Op: {
        between: 'between',
        gte: 'gte',
        lte: 'lte',
        in: 'in',
        like: 'like',
        ne: 'ne'
      }
    }
  };
  return mockModels;
});

jest.mock('fs', () => ({
  createWriteStream: jest.fn(() => ({
    on: jest.fn().mockReturnThis(),
    pipe: jest.fn().mockReturnThis()
  })),
  existsSync: jest.fn(),
  mkdirSync: jest.fn(),
  statSync: jest.fn(() => ({ size: 1024 })),
  writeFileSync: jest.fn()
}));

jest.mock('xlsx', () => ({
  utils: {
    book_new: jest.fn(),
    aoa_to_sheet: jest.fn(),
    json_to_sheet: jest.fn(),
    book_append_sheet: jest.fn()
  },
  writeFile: jest.fn()
}));

jest.mock('pdfkit', () => {
  return jest.fn().mockImplementation(() => ({
    pipe: jest.fn().mockReturnThis(),
    fontSize: jest.fn().mockReturnThis(),
    text: jest.fn().mockReturnThis(),
    moveDown: jest.fn().mockReturnThis(),
    font: jest.fn().mockReturnThis(),
    moveTo: jest.fn().mockReturnThis(),
    lineTo: jest.fn().mockReturnThis(),
    stroke: jest.fn().mockReturnThis(),
    addPage: jest.fn().mockReturnThis(),
    end: jest.fn(),
    page: {
      pageNumber: 1,
      height: 800,
      width: 600
    },
    y: 100
  }));
});

describe('EtatService', () => {
  let etatService;
  
  beforeEach(() => {
    jest.clearAllMocks();
    etatService = new EtatService(models);
    
    // Mock des méthodes du calculService
    etatService.calculService = {
      calculerSoldeCompte: jest.fn().mockResolvedValue({
        compteNumero: '401000',
        libelle: 'Fournisseurs',
        sensNaturel: 'CREDIT',
        totalDebit: 1000,
        totalCredit: 1500,
        solde: 500,
        sensActuel: 'CREDIT',
        soldeNaturel: 500,
        nombreLignes: 10,
        dateCalcul: new Date(),
        periode: {
          dateDebut: null,
          dateFin: new Date()
        }
      }),
      calculerTotauxJournal: jest.fn().mockResolvedValue({
        journalCode: 'ACH',
        libelle: 'Achats',
        type: 'ACHAT',
        periode: { dateDebut: new Date(), dateFin: new Date() },
        totaux: {
          totalDebit: 1000,
          totalCredit: 1000,
          difference: 0,
          equilibre: true,
          nombreLignes: 20,
          nombreEcritures: 10
        },
        detailsParCompte: []
      }),
      calculerBalanceGenerale: jest.fn().mockResolvedValue({
        periode: { dateDebut: new Date(), dateFin: new Date() },
        options: {
          niveauDetail: 'TOUS',
          classeComptes: null,
          seulementAvecMouvement: false,
          includeNonValidees: false
        },
        totauxGeneraux: {
          totalDebit: 5000,
          totalCredit: 5000,
          nombreComptes: 50,
          nombreLignes: 100,
          equilibre: true
        },
        totauxParClasse: [
          {
            classe: 4,
            libelle: 'Comptes de tiers',
            totalDebit: 2000,
            totalCredit: 2500,
            nombreComptes: 20
          }
        ],
        lignesBalance: [
          {
            compteNumero: '401000',
            libelle: 'Fournisseurs',
            classe: 4,
            nature: 'COLLECTIF',
            sensNaturel: 'CREDIT',
            niveau: 6,
            totalDebit: 1000,
            totalCredit: 1500,
            solde: 500,
            sensActuel: 'CREDIT',
            nombreLignes: 10
          }
        ],
        dateCalcul: new Date()
      }),
      validerPeriode: jest.fn()
    };
    
    // Mock des réponses des modèles
    models.Societe.findByPk.mockResolvedValue({
      id: '1',
      raisonSociale: 'Société Test',
      siret: '12345678901234'
    });
    
    models.ExerciceComptable.findByPk.mockResolvedValue({
      id: '1',
      annee: '2025',
      dateDebut: new Date('2025-01-01'),
      dateFin: new Date('2025-12-31')
    });
    
    models.CompteComptable.findOne.mockResolvedValue({
      numero: '401000',
      libelle: 'Fournisseurs',
      sens: 'CREDIT',
      nature: 'COLLECTIF'
    });
    
    models.CompteComptable.findAll.mockResolvedValue([
      {
        numero: '401000',
        libelle: 'Fournisseurs',
        sens: 'CREDIT',
        nature: 'COLLECTIF'
      }
    ]);
    
    models.Journal.findOne.mockResolvedValue({
      code: 'ACH',
      libelle: 'Achats',
      type: 'ACHAT'
    });
    
    models.Journal.findAll.mockResolvedValue([
      {
        code: 'ACH',
        libelle: 'Achats',
        type: 'ACHAT'
      }
    ]);
    
    models.EcritureComptable.findAll.mockResolvedValue([
      {
        id: '1',
        numeroEcriture: 'ACH2025001',
        dateEcriture: new Date('2025-01-15'),
        journalCode: 'ACH',
        libelle: 'Facture fournisseur',
        reference: 'FAC123',
        pieceJustificative: 'FAC123.pdf',
        statut: 'VALIDEE',
        lignes: [
          {
            id: '1',
            ecritureId: '1',
            compteNumero: '401000',
            libelle: 'Fournisseur ABC',
            debit: 0,
            credit: 1000,
            lettrage: 'A',
            dateLettrage: new Date('2025-01-20'),
            compte: {
              numero: '401000',
              libelle: 'Fournisseurs',
              sens: 'CREDIT'
            }
          },
          {
            id: '2',
            ecritureId: '1',
            compteNumero: '607000',
            libelle: 'Achat de marchandises',
            debit: 1000,
            credit: 0,
            lettrage: '',
            dateLettrage: null,
            compte: {
              numero: '607000',
              libelle: 'Achats de marchandises',
              sens: 'DEBIT'
            }
          }
        ],
        journal: {
          code: 'ACH',
          libelle: 'Achats',
          type: 'ACHAT'
        }
      }
    ]);
    
    models.LigneEcriture.findAll.mockResolvedValue([
      {
        id: '1',
        ecritureId: '1',
        compteNumero: '401000',
        libelle: 'Fournisseur ABC',
        debit: 0,
        credit: 1000,
        lettrage: 'A',
        dateLettrage: new Date('2025-01-20'),
        ecriture: {
          id: '1',
          numeroEcriture: 'ACH2025001',
          dateEcriture: new Date('2025-01-15'),
          journalCode: 'ACH',
          libelle: 'Facture fournisseur',
          reference: 'FAC123',
          pieceJustificative: 'FAC123.pdf',
          statut: 'VALIDEE',
          journal: {
            code: 'ACH',
            libelle: 'Achats',
            type: 'ACHAT'
          }
        },
        compte: {
          numero: '401000',
          libelle: 'Fournisseurs',
          sens: 'CREDIT'
        }
      }
    ]);
    
    // Mock des méthodes de génération de fichiers
    etatService.genererGrandLivreExcel = jest.fn().mockResolvedValue();
    etatService.genererGrandLivrePDF = jest.fn().mockResolvedValue();
    etatService.genererJournalExcel = jest.fn().mockResolvedValue();
    etatService.genererJournalPDF = jest.fn().mockResolvedValue();
    etatService.genererBalanceExcel = jest.fn().mockResolvedValue();
    etatService.genererBalancePDF = jest.fn().mockResolvedValue();
    etatService.genererBalanceAgeeExcel = jest.fn().mockResolvedValue();
    etatService.genererBalanceAgeePDF = jest.fn().mockResolvedValue();
    etatService.genererCentralisateurExcel = jest.fn().mockResolvedValue();
    etatService.genererCentralisateurPDF = jest.fn().mockResolvedValue();
  });
  
  describe('genererGrandLivre', () => {
    it('devrait générer le grand livre au format Excel', async () => {
      // Arrange
      const compteNumero = '401000';
      const dateDebut = new Date('2025-01-01');
      const dateFin = new Date('2025-01-31');
      const options = {
        societeId: '1',
        exerciceId: '1',
        format: 'excel'
      };
      
      fs.existsSync.mockReturnValue(false);
      
      // Act
      const resultat = await etatService.genererGrandLivre(compteNumero, dateDebut, dateFin, options);
      
      // Assert
      expect(models.Societe.findByPk).toHaveBeenCalledWith('1');
      expect(models.ExerciceComptable.findByPk).toHaveBeenCalledWith('1');
      expect(models.CompteComptable.findAll).toHaveBeenCalled();
      expect(models.LigneEcriture.findAll).toHaveBeenCalled();
      expect(etatService.calculService.calculerSoldeCompte).toHaveBeenCalled();
      expect(etatService.genererGrandLivreExcel).toHaveBeenCalled();
      expect(resultat).toHaveProperty('success', true);
      expect(resultat).toHaveProperty('format', 'excel');
    });
    
    it('devrait générer le grand livre au format PDF', async () => {
      // Arrange
      const compteNumero = '401000';
      const dateDebut = new Date('2025-01-01');
      const dateFin = new Date('2025-01-31');
      const options = {
        societeId: '1',
        exerciceId: '1',
        format: 'pdf'
      };
      
      fs.existsSync.mockReturnValue(false);
      
      // Act
      const resultat = await etatService.genererGrandLivre(compteNumero, dateDebut, dateFin, options);
      
      // Assert
      expect(etatService.genererGrandLivrePDF).toHaveBeenCalled();
      expect(resultat).toHaveProperty('success', true);
      expect(resultat).toHaveProperty('format', 'pdf');
    });
  });
  
  describe('genererJournal', () => {
    it('devrait générer le journal au format Excel', async () => {
      // Arrange
      const journalCode = 'ACH';
      const dateDebut = new Date('2025-01-01');
      const dateFin = new Date('2025-01-31');
      const options = {
        societeId: '1',
        exerciceId: '1',
        format: 'excel'
      };
      
      fs.existsSync.mockReturnValue(false);
      
      // Act
      const resultat = await etatService.genererJournal(journalCode, dateDebut, dateFin, options);
      
      // Assert
      expect(models.Journal.findAll).toHaveBeenCalled();
      expect(models.EcritureComptable.findAll).toHaveBeenCalled();
      expect(etatService.genererJournalExcel).toHaveBeenCalled();
      expect(resultat).toHaveProperty('success', true);
      expect(resultat).toHaveProperty('format', 'excel');
    });
  });
  
  describe('genererBalanceGenerale', () => {
    it('devrait générer la balance générale au format Excel', async () => {
      // Arrange
      const dateDebut = new Date('2025-01-01');
      const dateFin = new Date('2025-01-31');
      const options = {
        societeId: '1',
        exerciceId: '1',
        format: 'excel'
      };
      
      fs.existsSync.mockReturnValue(false);
      
      // Act
      const resultat = await etatService.genererBalanceGenerale(dateDebut, dateFin, options);
      
      // Assert
      expect(etatService.calculService.calculerBalanceGenerale).toHaveBeenCalled();
      expect(etatService.genererBalanceExcel).toHaveBeenCalled();
      expect(resultat).toHaveProperty('success', true);
      expect(resultat).toHaveProperty('format', 'excel');
    });
  });
  
  describe('formatDate', () => {
    it('devrait formater une date au format DD/MM/YYYY', () => {
      // Arrange
      const date = new Date('2025-01-15');
      
      // Act
      const resultat = etatService.formatDate(date);
      
      // Assert
      expect(resultat).toBe('15/01/2025');
    });
    
    it('devrait retourner une chaîne vide si la date est null', () => {
      // Act
      const resultat = etatService.formatDate(null);
      
      // Assert
      expect(resultat).toBe('');
    });
  });
});