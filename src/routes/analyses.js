'use strict';

const express = require('express');
const router = express.Router();
const analyseController = require('../controllers/analyseController');
const auth = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const { query, body } = require('express-validator');

/**
 * Routes pour les analyses financières avancées
 */

// Middleware d'authentification pour toutes les routes
router.use(auth.protect);

/**
 * @route POST /api/v1/analyses/evolution-comptes
 * @desc Analyse l'évolution des comptes sur plusieurs périodes
 * @access Private
 */
router.post('/evolution-comptes', [
  body('comptes').isArray().withMessage('Les comptes doivent être un tableau'),
  body('comptes.*').isString().withMessage('Les numéros de compte doivent être des chaînes'),
  body('periodes').isArray().withMessage('Les périodes doivent être un tableau'),
  body('periodes.*.dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  body('periodes.*.dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  body('periodes.*.libelle').optional().isString().withMessage('Le libellé doit être une chaîne'),
  query('exerciceId').optional().isString().withMessage('L\'ID de l\'exercice doit être une chaîne'),
  validate
], analyseController.analyseEvolutionComptes);

/**
 * @route GET /api/v1/analyses/anomalies
 * @desc Détecte les anomalies dans les écritures comptables
 * @access Private
 */
router.get('/anomalies', [
  query('dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  query('dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  query('exerciceId').optional().isString().withMessage('L\'ID de l\'exercice doit être une chaîne'),
  query('seuilMontant').optional().isFloat({ min: 0 }).withMessage('Le seuil de montant doit être un nombre positif'),
  query('seuilEcartType').optional().isFloat({ min: 0 }).withMessage('Le seuil d\'écart-type doit être un nombre positif'),
  query('seuilFrequence').optional().isInt({ min: 0 }).withMessage('Le seuil de fréquence doit être un entier positif'),
  validate
], analyseController.detectionAnomalies);

/**
 * @route GET /api/v1/analyses/previsions
 * @desc Calcule des prévisions financières
 * @access Private
 */
router.get('/previsions', [
  query('horizon').optional().isInt({ min: 1, max: 12 }).withMessage('L\'horizon doit être un entier entre 1 et 12'),
  query('methode').optional().isIn(['moyenne_mobile', 'regression_lineaire']).withMessage('La méthode doit être moyenne_mobile ou regression_lineaire'),
  query('periodeReference').optional().isIn(['jour', 'mois', 'trimestre', 'annee']).withMessage('La période de référence doit être jour, mois, trimestre ou annee'),
  query('nombrePeriodesPasses').optional().isInt({ min: 2, max: 60 }).withMessage('Le nombre de périodes passées doit être un entier entre 2 et 60'),
  validate
], analyseController.previsionsFinancieres);

/**
 * @route GET /api/v1/analyses/budget
 * @desc Compare les résultats réels avec les budgets
 * @access Private
 */
router.get('/budget', [
  query('dateDebut').isDate().withMessage('La date de début doit être une date valide'),
  query('dateFin').isDate().withMessage('La date de fin doit être une date valide'),
  query('exerciceId').optional().isString().withMessage('L\'ID de l\'exercice doit être une chaîne'),
  validate
], analyseController.comparaisonsBudgetaires);

/**
 * @route GET /api/v1/analyses/benchmark/:secteur
 * @desc Effectue un benchmarking sectoriel
 * @access Private
 */
router.get('/benchmark/:secteur', [
  query('ratios').optional().isString().withMessage('Les ratios doivent être une chaîne'),
  validate
], analyseController.benchmarkingSectoriel);

module.exports = router;