#### Calcul des Amortissements

```http
POST /api/v1/regularisations/amortissements
{
  "societeId": "uuid-societe",
  "exerciceId": "uuid-exercice",
  "periode": "2025-08",
  "dateEcriture": "2025-08-31",
  "journalCode": "OD",
  "libelle": "Dotations aux amortissements Août 2025",
  "immobilisations": [
    {
      "id": "uuid-immo-1",
      "inclure": true
    },
    {
      "id": "uuid-immo-2",
      "inclure": true
    },
    {
      "id": "uuid-immo-3",
      "inclure": true
    }
  ],
  "options": {
    "genererEcriture": true,
    "statut": "BROUILLARD"
  }
}
```

#### Calcul des Provisions

```http
POST /api/v1/regularisations/provisions-creances
{
  "societeId": "uuid-societe",
  "exerciceId": "uuid-exercice",
  "periode": "2025-08",
  "dateEcriture": "2025-08-31",
  "journalCode": "OD",
  "libelle": "Provisions créances douteuses Août 2025",
  "creances": [
    {
      "clientId": "uuid-client-1",
      "montant": 75000,
      "tauxProvision": 25,
      "inclure": true
    },
    {
      "clientId": "uuid-client-2",
      "montant": 250000,
      "tauxProvision": 50,
      "inclure": true
    }
  ],
  "options": {
    "genererEcriture": true,
    "statut": "BROUILLARD"
  }
}
```

#### Réponse Succès

```http
Status: 200 OK
{
  "success": true,
  "message": "Régularisation effectuée avec succès",
  "data": {
    "type": "AMORTISSEMENTS",
    "periode": "2025-08",
    "dateEcriture": "2025-08-31",
    "montantTotal": 658334,
    "ecritureId": "uuid-ecriture",
    "statut": "BROUILLARD",
    "details": [
      {
        "immobilisationId": "uuid-immo-1",
        "libelle": "Matériel informatique",
        "valeurBrute": 12000000,
        "dotation": 200000,
        "compteAmortissement": "281200"
      },
      {
        "immobilisationId": "uuid-immo-2",
        "libelle": "Véhicules",
        "valeurBrute": 25000000,
        "dotation": 416667,
        "compteAmortissement": "281800"
      },
      {
        "immobilisationId": "uuid-immo-3",
        "libelle": "Mobilier",
        "valeurBrute": 5000000,
        "dotation": 41667,
        "compteAmortissement": "281600"
      }
    ]
  }
}
```

### Gestion des Erreurs

| Code | Message | Action Utilisateur |
|------|---------|-------------------|
| 400 | "Période déjà régularisée" | Vérifier la période |
| 400 | "Aucun élément à régulariser" | Vérifier les sélections |
| 403 | "Droits insuffisants pour effectuer les régularisations" | Contacter l'administrateur |

### Considérations Spéciales

- Automatisation des régularisations récurrentes
- Modèles de régularisations personnalisables
- Historique des régularisations précédentes
- Simulation des impacts avant génération des écritures
- Annulation et reprise des régularisations précédentes

---

## Clôtures Intermédiaires

### Description

Les clôtures intermédiaires permettent de "figer" temporairement les comptes à la fin d'une période (mois, trimestre) pour produire des états financiers intermédiaires fiables, sans pour autant clôturer définitivement l'exercice.

### Interface Utilisateur

#### Écran Principal - Clôture Intermédiaire

```
┌─────────────────────────────────────────────────────┐
│ Clôture Intermédiaire                               │
├─────────────────────────────────────────────────────┤
│                                                     │
│  Société: SARL AFRICAN BUSINESS                     │
│  Exercice: Exercice 2025 (01/01/2025 - 31/12/2025)  │
│                                                     │
│  ┌─────────────────────────┐ ┌─────────────────────┐│
│  │ Période                 │ │ Type                ││
│  │ ┌─────────────────────┐ │ │ ┌─────────────────┐ ││
│  │ │ Août 2025           │ │ │ │ Mensuelle       │ ││
│  │ └─────────────────────┘ │ │ └─────────────────┘ ││
│  └─────────────────────────┘ └─────────────────────┘│
│                                                     │
│  Vérifications Pré-Clôture                          │
│  ┌─────────────────────────────────────────────────┐│
│  │ ✅ Toutes les écritures sont validées           ││
│  │ ✅ Toutes les régularisations sont effectuées   ││
│  │ ⚠️ Certains comptes clients ne sont pas lettrés ││
│  │ ✅ Les rapprochements bancaires sont à jour     ││
│  │ ✅ Les contrôles de cohérence sont effectués    ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  Options de Clôture                                 │
│  ┌─────────────────────────────────────────────────┐│
│  │ [x] Verrouiller les écritures de la période     ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────────────────────────────────────────┐│
│  │ [x] Générer les états financiers intermédiaires ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────────────────────────────────────────┐│
│  │ [ ] Envoyer notification aux responsables       ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────┐    ┌──────────────┐                │
│  │   Annuler   │    │    Clôturer  │                │
│  └─────────────┘    └──────────────┘                │
└─────────────────────────────────────────────────────┘
```

#### Confirmation de Clôture

```
┌─────────────────────────────────────────────────────┐
│ Confirmation de Clôture Intermédiaire               │
├─────────────────────────────────────────────────────┤
│                                                     │
│  ⚠️ Vous êtes sur le point de clôturer la période:  │
│  Août 2025                                          │
│                                                     │
│  Cette action aura les conséquences suivantes:      │
│  ┌─────────────────────────────────────────────────┐│
│  │ • Les écritures de la période seront verrouillées││
│  │ • Les états financiers intermédiaires seront     ││
│  │   générés                                        ││
│  │ • Les soldes des comptes seront figés pour       ││
│  │   cette période                                  ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  Avertissements                                     │
│  ┌─────────────────────────────────────────────────┐│
│  │ ⚠️ Certains comptes clients ne sont pas lettrés ││
│  │   Voulez-vous continuer malgré tout?            ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────────────────────────────────────────┐│
│  │ [ ] Je confirme vouloir clôturer cette période  ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────┐    ┌──────────────┐                │
│  │   Annuler   │    │  Confirmer   │                │
│  └─────────────┘    └──────────────┘                │
└─────────────────────────────────────────────────────┘
```

#### Résultats de Clôture

```
┌─────────────────────────────────────────────────────┐
│ Résultats de la Clôture Intermédiaire               │
├─────────────────────────────────────────────────────┤
│                                                     │
│  Période: Août 2025                                 │
│  Date de clôture: 07/09/2025 14:30                  │
│                                                     │
│  Résumé                                             │
│  ┌─────────────────────────────────────────────────┐│
│  │ Écritures verrouillées: 45                      ││
│  │ États financiers générés: 5                     ││
│  │ Soldes des comptes figés: 227                   ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  États Financiers Générés                           │
│  ┌─────────────────────────────────────────────────┐│
│  │ État                │ Format        │ Actions    ││
│  ├─────────────────────────────────────────────────┤│
│  │ Balance générale    │ PDF, Excel    │ 📥 🔍      ││
│  ├─────────────────────────────────────────────────┤│
│  │ Grand livre         │ PDF, Excel    │ 📥 🔍      ││
│  ├─────────────────────────────────────────────────┤│
│  │ Compte de résultat  │ PDF, Excel    │ 📥 🔍      ││
│  ├─────────────────────────────────────────────────┤│
│  │ Bilan               │ PDF, Excel    │ 📥 🔍      ││
│  ├─────────────────────────────────────────────────┤│
│  │ Tableau de trésorerie│ PDF, Excel   │ 📥 🔍      ││
│  └─────────────────────────────────────────────────┘│
│                                                     │
│  ┌─────────────────────────┐ ┌─────────────────────┐│
│  │ 📧 Envoyer par email    │ │ 📋 Voir rapport     ││
│  └─────────────────────────┘ └─────────────────────┘│
│                                                     │
│  ┌─────────────┐    ┌──────────────┐                │
│  │   Fermer    │    │ Voir états   │                │
│  └─────────────┘    └──────────────┘                │
└─────────────────────────────────────────────────────┘
```

### Vérifications Pré-Clôture

| Vérification | Description | Bloquant |
|--------------|-------------|----------|
| Écritures validées | Toutes les écritures de la période doivent être validées | Oui |
| Régularisations | Toutes les régularisations doivent être effectuées | Oui |
| Lettrage | Les comptes clients et fournisseurs doivent être lettrés | Non |
| Rapprochements bancaires | Les rapprochements bancaires doivent être à jour | Non |
| Contrôles de cohérence | Les contrôles de cohérence doivent être effectués | Non |

### Interactions API

#### Vérifications Pré-Clôture

```http
GET /api/v1/cloture/verifications?societeId=uuid-societe&exerciceId=uuid-exercice&periode=2025-08
```

#### Clôture Intermédiaire

```http
POST /api/v1/cloture/intermediaire
{
  "societeId": "uuid-societe",
  "exerciceId": "uuid-exercice",
  "periode": "2025-08",
  "type": "MENSUELLE",
  "options": {
    "verrouillerEcritures": true,
    "genererEtatsFinanciers": true,
    "envoyerNotifications": false
  },
  "confirmation": true
}
```

#### Réponse Succès

```http
Status: 200 OK
{
  "success": true,
  "message": "Clôture intermédiaire effectuée avec succès",
  "data": {
    "periode": "2025-08",
    "dateClotureIntermediaire": "2025-09-07T14:30:00Z",
    "ecrituresVerrouillees": 45,
    "etatsFinanciersGeneres": [
      {
        "type": "BALANCE_GENERALE",
        "formats": ["PDF", "EXCEL"],
        "url": "/api/v1/etats/balance?periode=2025-08&format=pdf"
      },
      {
        "type": "GRAND_LIVRE",
        "formats": ["PDF", "EXCEL"],
        "url": "/api/v1/etats/grand-livre?periode=2025-08&format=pdf"
      },
      {
        "type": "COMPTE_RESULTAT",
        "formats": ["PDF", "EXCEL"],
        "url": "/api/v1/etats/compte-resultat?periode=2025-08&format=pdf"
      },
      {
        "type": "BILAN",
        "formats": ["PDF", "EXCEL"],
        "url": "/api/v1/etats/bilan?periode=2025-08&format=pdf"
      },
      {
        "type": "TABLEAU_TRESORERIE",
        "formats": ["PDF", "EXCEL"],
        "url": "/api/v1/etats/tresorerie?periode=2025-08&format=pdf"
      }
    ],
    "soldesComptesFiges": 227
  }
}
```

### Gestion des Erreurs

| Code | Message | Action Utilisateur |
|------|---------|-------------------|
| 400 | "Des écritures sont encore en brouillard" | Valider toutes les écritures |
| 400 | "Les régularisations ne sont pas effectuées" | Effectuer les régularisations |
| 400 | "La période est déjà clôturée" | Vérifier la période |
| 403 | "Droits insuffisants pour effectuer la clôture" | Contacter l'administrateur |

### Considérations Spéciales

- Possibilité de rouvrir une période clôturée (avec autorisation spéciale)
- Comparaison automatique avec les périodes précédentes
- Génération de rapports d'analyse financière
- Archivage des données de la période clôturée
- Workflow d'approbation pour les clôtures trimestrielles

---

## Considérations Techniques

### Architecture Frontend-Backend

La mise en œuvre de ce workflow de gestion périodique repose sur une architecture robuste :

1. **Frontend** :
   - Composants React spécialisés pour chaque type d'opération
   - Visualisations interactives pour l'analyse des données
   - Formulaires intelligents avec validation en temps réel
   - Tableaux de bord pour le suivi des opérations périodiques

2. **Backend** :
   - API RESTful pour toutes les opérations
   - Moteurs de calcul spécialisés (amortissements, provisions, etc.)
   - Système de verrouillage pour les périodes clôturées
   - Génération automatisée des états financiers

### Optimisations de Performance

- **Traitement par lots** pour les opérations volumineuses
- **Calculs asynchrones** pour les opérations longues
- **Mise en cache** des résultats intermédiaires
- **Pagination** pour les grands ensembles de données
- **Exécution en arrière-plan** des tâches intensives

### Sécurité

- **Contrôle d'accès** granulaire pour chaque type d'opération
- **Journalisation** détaillée de toutes les actions
- **Validation des données** pour prévenir les manipulations
- **Séparation des responsabilités** entre les différents rôles
- **Audit trail** complet pour toutes les modifications

### Extensibilité

Le workflow est conçu pour être extensible :

- **Règles métier configurables** selon les besoins spécifiques
- **Formules de calcul personnalisables** pour les régularisations
- **Rapports personnalisés** pour les besoins spécifiques
- **Intégration avec des systèmes externes** (ERP, CRM, etc.)
- **API documentée** pour développements tiers

---

## Annexes

### A. Modèle de Données

```mermaid
erDiagram
    LETTRAGE {
        uuid id
        string codeLettrage
        date dateLettrage
        uuid utilisateurId
        string compteNumero
    }
    LIGNE_ECRITURE {
        uuid id
        uuid ecritureId
        string compteNumero
        decimal montantDebit
        decimal montantCredit
        string codeLettrage
    }
    RAPPROCHEMENT_BANCAIRE {
        uuid id
        uuid societeId
        string compteNumero
        string periode
        date dateRapprochement
        decimal soldeComptable
        decimal soldeBancaire
        decimal ecart
    }
    MOUVEMENT_BANCAIRE {
        uuid id
        uuid rapprochementId
        date dateMouvement
        string reference
        string libelle
        decimal montantDebit
        decimal montantCredit
        boolean rapproche
        uuid ecritureId
    }
    REGULARISATION {
        uuid id
        uuid societeId
        uuid exerciceId
        string type
        string periode
        date dateRegularisation
        uuid ecritureId
    }
    CLOTURE_INTERMEDIAIRE {
        uuid id
        uuid societeId
        uuid exerciceId
        string periode
        string type
        date dateCloture
        uuid utilisateurId
    }
    
    LETTRAGE ||--o{ LIGNE_ECRITURE : "concerne"
    RAPPROCHEMENT_BANCAIRE ||--o{ MOUVEMENT_BANCAIRE : "contient"
    MOUVEMENT_BANCAIRE ||--o| LIGNE_ECRITURE : "associé à"
    REGULARISATION ||--o| ECRITURE : "génère"
    CLOTURE_INTERMEDIAIRE ||--o{ ETAT_FINANCIER : "génère"
```

### B. Exemples de Lettrages

#### Lettrage Simple (Facture et Paiement)

```json
{
  "codeLettrage": "A",
  "dateLettrage": "2025-08-07",
  "compteNumero": "411001",
  "lignes": [
    {
      "ecritureId": "uuid-ecriture-1",
      "numero": "VT-2025-0042",
      "dateEcriture": "2025-07-15",
      "libelle": "Facture client",
      "montantDebit": 118000,
      "montantCredit": 0
    },
    {
      "ecritureId": "uuid-ecriture-2",
      "numero": "BQ-2025-0015",
      "dateEcriture": "2025-08-05",
      "libelle": "Règlement client",
      "montantDebit": 0,
      "montantCredit": 118000
    }
  ]
}
```

#### Lettrage Complexe (Plusieurs Factures et Paiements)

```json
{
  "codeLettrage": "B",
  "dateLettrage": "2025-08-07",
  "compteNumero": "401000",
  "lignes": [
    {
      "ecritureId": "uuid-ecriture-3",
      "numero": "AC-2025-0025",
      "dateEcriture": "2025-06-10",
      "libelle": "Facture fournisseur 1",
      "montantDebit": 0,
      "montantCredit": 50000
    },
    {
      "ecritureId": "uuid-ecriture-4",
      "numero": "AC-2025-0030",
      "dateEcriture": "2025-06-15",
      "libelle": "Facture fournisseur 2",
      "montantDebit": 0,
      "montantCredit": 75000
    },
    {
      "ecritureId": "uuid-ecriture-5",
      "numero": "BQ-2025-0020",
      "dateEcriture": "2025-07-01",
      "libelle": "Règlement fournisseur",
      "montantDebit": 125000,
      "montantCredit": 0
    }
  ]
}
```

### C. Checklist de Gestion Périodique

#### Checklist Hebdomadaire

- [ ] **Lettrage des comptes clients**
  - [ ] Identifier les paiements reçus
  - [ ] Lettrer les factures correspondantes
  - [ ] Suivre les soldes non lettrés

- [ ] **Lettrage des comptes fournisseurs**
  - [ ] Identifier les paiements effectués
  - [ ] Lettrer les factures correspondantes
  - [ ] Suivre les soldes non lettrés

- [ ] **Rapprochements bancaires**
  - [ ] Importer les relevés bancaires
  - [ ] Rapprocher les mouvements
  - [ ] Créer les écritures manquantes
  - [ ] Justifier les écarts

- [ ] **Contrôles de cohérence**
  - [ ] Vérifier l'équilibre des écritures
  - [ ] Identifier les soldes anormaux
  - [ ] Valider les écritures en brouillard

#### Checklist Mensuelle

- [ ] **Régularisations**
  - [ ] Calculer les amortissements
  - [ ] Évaluer les provisions
  - [ ] Comptabiliser les charges à payer
  - [ ] Comptabiliser les charges constatées d'avance
  - [ ] Comptabiliser les produits constatés d'avance

- [ ] **Clôture intermédiaire**
  - [ ] Effectuer les vérifications pré-clôture
  - [ ] Verrouiller les écritures de la période
  - [ ] Générer les états financiers intermédiaires
  - [ ] Analyser les résultats

- [ ] **Reporting**
  - [ ] Préparer les tableaux de bord
  - [ ] Analyser les écarts budgétaires
  - [ ] Communiquer les résultats

---

*Document préparé par l'équipe d'architecture, version 1.0*