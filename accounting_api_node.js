/**
 * API de Comptabilité SYSCOHADA
 * Langage: Node.js avec Express.js
 * Base de données: PostgreSQL
 * Description: Cette API gère les journaux, mouvements comptables, bilans, comptes de résultat et liasses fiscales.
 */

// Dépendances principales
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const port = process.env.PORT || 3000;

app.use(cors());
app.use(bodyParser.json());

// Connexion à PostgreSQL
const db = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'compta',
  password: 'votre_mdp',
  port: 5432,
});

// ---------------------- SCHEMAS -------------------------

// Table: journaux
// id, code, libelle

// Table: comptes
// numero, libelle, classe, type (actif/passif/charge/produit)

// Table: ecritures
// id, date, journal_id, compte_debit, compte_credit, montant, libelle, exercice, statut

// ---------------------- ROUTES -------------------------

// GET - Liste des journaux
app.get('/api/journaux', async (req, res) => {
  const result = await db.query('SELECT * FROM journaux ORDER BY code');
  res.json(result.rows);
});

// POST - Ajouter une écriture comptable
app.post('/api/ecritures', async (req, res) => {
  const {
    date,
    journal_id,
    compte_debit,
    compte_credit,
    montant,
    libelle,
    exercice,
    statut,
  } = req.body;

  const result = await db.query(
    `INSERT INTO ecritures (date, journal_id, compte_debit, compte_credit, montant, libelle, exercice, statut)
     VALUES ($1,$2,$3,$4,$5,$6,$7,$8) RETURNING *`,
    [date, journal_id, compte_debit, compte_credit, montant, libelle, exercice, statut]
  );
  res.status(201).json(result.rows[0]);
});

// GET - Grand livre pour un compte
app.get('/api/grand-livre/:compte/:exercice', async (req, res) => {
  const { compte, exercice } = req.params;
  const result = await db.query(
    `SELECT * FROM ecritures WHERE (compte_debit=$1 OR compte_credit=$1) AND exercice=$2 ORDER BY date`,
    [compte, exercice]
  );
  res.json(result.rows);
});

// GET - Balance comptable
app.get('/api/balance/:exercice', async (req, res) => {
  const { exercice } = req.params;
  const result = await db.query(`
    SELECT numero, libelle,
      SUM(CASE WHEN compte_debit = numero THEN montant ELSE 0 END) AS debit,
      SUM(CASE WHEN compte_credit = numero THEN montant ELSE 0 END) AS credit
    FROM comptes
    LEFT JOIN ecritures ON (compte_debit = numero OR compte_credit = numero)
    WHERE exercice = $1
    GROUP BY numero, libelle
    ORDER BY numero`,
    [exercice]
  );
  res.json(result.rows);
});

// GET - Compte de résultat (simplifié)
app.get('/api/resultat/:exercice', async (req, res) => {
  const { exercice } = req.params;
  const produit = await db.query(
    `SELECT SUM(montant) FROM ecritures JOIN comptes ON compte_credit = numero WHERE type = 'produit' AND exercice=$1`,
    [exercice]
  );
  const charge = await db.query(
    `SELECT SUM(montant) FROM ecritures JOIN comptes ON compte_debit = numero WHERE type = 'charge' AND exercice=$1`,
    [exercice]
  );
  res.json({ total_produits: produit.rows[0].sum, total_charges: charge.rows[0].sum, resultat: produit.rows[0].sum - charge.rows[0].sum });
});

// GET - Bilan (simplifié)
app.get('/api/bilan/:exercice', async (req, res) => {
  const actif = await db.query(
    `SELECT numero, libelle, SUM(montant) AS montant
     FROM comptes
     JOIN ecritures ON compte_debit = numero
     WHERE type='actif' AND exercice=$1 GROUP BY numero, libelle`,
    [req.params.exercice]
  );
  const passif = await db.query(
    `SELECT numero, libelle, SUM(montant) AS montant
     FROM comptes
     JOIN ecritures ON compte_credit = numero
     WHERE type='passif' AND exercice=$1 GROUP BY numero, libelle`,
    [req.params.exercice]
  );
  res.json({ actif: actif.rows, passif: passif.rows });
});

// Lancement du serveur
app.listen(port, () => {
  console.log(`API comptable active sur http://localhost:${port}`);
});
