'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Fonction helper pour vérifier si une colonne existe
    const columnExists = async (tableName, columnName) => {
      const [results] = await queryInterface.sequelize.query(
        `SELECT column_name FROM information_schema.columns 
         WHERE table_name = '${tableName}' AND column_name = '${columnName}' AND table_schema = 'public'`
      );
      return results.length > 0;
    };

    console.log('Enrichissement SYSCOHADA - Version simplifiée...');

    // Enrichissement EcritureComptable - champs manquants uniquement
    if (!(await columnExists('ecriture_comptables', 'total_debit'))) {
      await queryInterface.addColumn('ecriture_comptables', 'total_debit', {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0.00
      });
    }

    if (!(await columnExists('ecriture_comptables', 'total_credit'))) {
      await queryInterface.addColumn('ecriture_comptables', 'total_credit', {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0.00
      });
    }

    if (!(await columnExists('ecriture_comptables', 'reference_externe'))) {
      await queryInterface.addColumn('ecriture_comptables', 'reference_externe', {
        type: Sequelize.STRING(100),
        allowNull: true
      });
    }

    // Enrichissement LigneEcriture - champs manquants uniquement
    if (!(await columnExists('ligne_ecritures', 'date_echeance'))) {
      await queryInterface.addColumn('ligne_ecritures', 'date_echeance', {
        type: Sequelize.DATEONLY,
        allowNull: true
      });
    }

    if (!(await columnExists('ligne_ecritures', 'numero_piece'))) {
      await queryInterface.addColumn('ligne_ecritures', 'numero_piece', {
        type: Sequelize.STRING(50),
        allowNull: true
      });
    }

    if (!(await columnExists('ligne_ecritures', 'devise'))) {
      await queryInterface.addColumn('ligne_ecritures', 'devise', {
        type: Sequelize.STRING(3),
        allowNull: true
      });
    }

    if (!(await columnExists('ligne_ecritures', 'cours'))) {
      await queryInterface.addColumn('ligne_ecritures', 'cours', {
        type: Sequelize.DECIMAL(10, 6),
        allowNull: true
      });
    }

    if (!(await columnExists('ligne_ecritures', 'montant_devise'))) {
      await queryInterface.addColumn('ligne_ecritures', 'montant_devise', {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true
      });
    }

    console.log('Enrichissement SYSCOHADA terminé !');
  },

  async down(queryInterface, Sequelize) {
    // Suppression des colonnes ajoutées
    const columnsToRemove = [
      ['ligne_ecritures', 'montant_devise'],
      ['ligne_ecritures', 'cours'],
      ['ligne_ecritures', 'devise'],
      ['ligne_ecritures', 'numero_piece'],
      ['ligne_ecritures', 'date_echeance'],
      ['ecriture_comptables', 'reference_externe'],
      ['ecriture_comptables', 'total_credit'],
      ['ecriture_comptables', 'total_debit']
    ];

    for (const [table, column] of columnsToRemove) {
      try {
        await queryInterface.removeColumn(table, column);
      } catch (e) {
        console.log(`Colonne ${table}.${column} n'existe pas ou ne peut pas être supprimée`);
      }
    }
  }
};