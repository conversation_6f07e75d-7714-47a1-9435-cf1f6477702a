'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Service pour la gestion de l'audit trail et de la traçabilité
 */
class AuditService {
  constructor(models) {
    this.models = models;
  }

  /**
   * Enregistre une action d'audit
   * @param {Object} params - Paramètres de l'audit
   * @param {Object} req - Objet request Express (optionnel)
   * @returns {Promise<Object>} Log d'audit créé
   */
  async enregistrerAction(params, req = null) {
    try {
      const {
        action,
        table,
        recordId,
        anciensValeurs = null,
        nouvellesValeurs = null,
        utilisateurId = null,
        societeId = null,
        details = null
      } = params;

      // Extraire les informations de la requête si disponible
      let adresseIP = null;
      let userAgent = null;

      if (req) {
        adresseIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
        userAgent = req.get('User-Agent');
      }

      const auditLog = await this.models.AuditLog.enregistrerAction({
        action,
        table,
        recordId,
        anciensValeurs,
        nouvellesValeurs,
        utilisateurId,
        societeId,
        adresseIP,
        userAgent,
        details
      });

      logger.info('Action d\'audit enregistrée', {
        id: auditLog.id,
        action,
        table,
        recordId,
        utilisateurId,
        societeId
      });

      return auditLog;

    } catch (error) {
      logger.error('Erreur lors de l\'enregistrement de l\'audit', {
        params,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Recherche dans les logs d'audit avec filtres avancés
   * @param {Object} filtres - Critères de recherche
   * @returns {Promise<Object>} Résultats paginés
   */
  async rechercherLogs(filtres = {}) {
    try {
      const resultats = await this.models.AuditLog.rechercherLogs(filtres);
      
      // Enrichir les résultats avec des informations calculées
      const logsEnrichis = resultats.logs.map(log => {
        const logData = log.toJSON();
        logData.resumeChangements = log.getResumeChangements();
        return logData;
      });

      return {
        ...resultats,
        logs: logsEnrichis
      };

    } catch (error) {
      logger.error('Erreur lors de la recherche des logs d\'audit', {
        filtres,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Obtient l'historique complet d'un enregistrement
   * @param {string} table - Nom de la table
   * @param {string} recordId - ID de l'enregistrement
   * @returns {Promise<Array>} Historique chronologique
   */
  async getHistoriqueRecord(table, recordId) {
    try {
      const historique = await this.models.AuditLog.getHistoriqueRecord(table, recordId);
      
      return historique.map(log => {
        const logData = log.toJSON();
        logData.resumeChangements = log.getResumeChangements();
        return logData;
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'historique', {
        table,
        recordId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Obtient les statistiques d'audit pour une période
   * @param {Object} params - Paramètres de la période
   * @returns {Promise<Object>} Statistiques détaillées
   */
  async getStatistiquesAudit(params = {}) {
    try {
      const {
        dateDebut = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 jours par défaut
        dateFin = new Date(),
        societeId = null
      } = params;

      const { Op } = this.models.sequelize;
      const whereClause = {
        createdAt: {
          [Op.between]: [dateDebut, dateFin]
        }
      };

      if (societeId) {
        whereClause.societeId = societeId;
      }

      // Statistiques par action
      const statsParAction = await this.models.AuditLog.findAll({
        where: whereClause,
        attributes: [
          'action',
          [this.models.sequelize.fn('COUNT', this.models.sequelize.col('id')), 'nombre']
        ],
        group: ['action'],
        order: [[this.models.sequelize.fn('COUNT', this.models.sequelize.col('id')), 'DESC']]
      });

      // Statistiques par table
      const statsParTable = await this.models.AuditLog.findAll({
        where: whereClause,
        attributes: [
          'table',
          [this.models.sequelize.fn('COUNT', this.models.sequelize.col('id')), 'nombre']
        ],
        group: ['table'],
        order: [[this.models.sequelize.fn('COUNT', this.models.sequelize.col('id')), 'DESC']]
      });

      // Activité par jour
      const activiteParJour = await this.models.AuditLog.findAll({
        where: whereClause,
        attributes: [
          [this.models.sequelize.fn('DATE', this.models.sequelize.col('created_at')), 'date'],
          [this.models.sequelize.fn('COUNT', this.models.sequelize.col('id')), 'nombre']
        ],
        group: [this.models.sequelize.fn('DATE', this.models.sequelize.col('created_at'))],
        order: [[this.models.sequelize.fn('DATE', this.models.sequelize.col('created_at')), 'ASC']]
      });

      // Total des actions
      const totalActions = await this.models.AuditLog.count({ where: whereClause });

      return {
        periode: { dateDebut, dateFin },
        societeId,
        totalActions,
        statsParAction: statsParAction.map(stat => ({
          action: stat.action,
          nombre: parseInt(stat.dataValues.nombre)
        })),
        statsParTable: statsParTable.map(stat => ({
          table: stat.table,
          nombre: parseInt(stat.dataValues.nombre)
        })),
        activiteParJour: activiteParJour.map(stat => ({
          date: stat.dataValues.date,
          nombre: parseInt(stat.dataValues.nombre)
        }))
      };

    } catch (error) {
      logger.error('Erreur lors du calcul des statistiques d\'audit', {
        params,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Nettoie les anciens logs d'audit selon la politique de rétention
   * @param {Object} params - Paramètres de nettoyage
   * @returns {Promise<Object>} Résultat du nettoyage
   */
  async nettoyerAnciensLogs(params = {}) {
    try {
      const {
        joursRetention = 365, // 1 an par défaut
        actionsAConserver = ['LOGIN', 'LOGOUT'], // Actions critiques à conserver plus longtemps
        joursRetentionCritique = 2555 // 7 ans pour les actions critiques
      } = params;

      const dateSuppressionNormale = new Date(Date.now() - joursRetention * 24 * 60 * 60 * 1000);
      const dateSuppressionCritique = new Date(Date.now() - joursRetentionCritique * 24 * 60 * 60 * 1000);

      const { Op } = this.models.sequelize;

      // Supprimer les logs normaux anciens
      const logsNormauxSupprimes = await this.models.AuditLog.destroy({
        where: {
          createdAt: {
            [Op.lt]: dateSuppressionNormale
          },
          action: {
            [Op.notIn]: actionsAConserver
          }
        }
      });

      // Supprimer les logs critiques très anciens
      const logsCritiquesSupprimes = await this.models.AuditLog.destroy({
        where: {
          createdAt: {
            [Op.lt]: dateSuppressionCritique
          },
          action: {
            [Op.in]: actionsAConserver
          }
        }
      });

      const totalSupprime = logsNormauxSupprimes + logsCritiquesSupprimes;

      logger.info('Nettoyage des logs d\'audit terminé', {
        logsNormauxSupprimes,
        logsCritiquesSupprimes,
        totalSupprime,
        joursRetention,
        joursRetentionCritique
      });

      return {
        success: true,
        logsNormauxSupprimes,
        logsCritiquesSupprimes,
        totalSupprime,
        dateSuppressionNormale,
        dateSuppressionCritique
      };

    } catch (error) {
      logger.error('Erreur lors du nettoyage des logs d\'audit', {
        params,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Exporte les logs d'audit pour une période donnée
   * @param {Object} params - Paramètres d'export
   * @returns {Promise<Array>} Logs formatés pour export
   */
  async exporterLogs(params = {}) {
    try {
      const {
        dateDebut,
        dateFin,
        societeId = null,
        format = 'json' // json, csv
      } = params;

      const filtres = { dateDebut, dateFin, societeId, limit: 10000 }; // Limite élevée pour export
      const resultats = await this.rechercherLogs(filtres);

      if (format === 'csv') {
        // Convertir en format CSV
        const headers = ['Date', 'Action', 'Table', 'Record ID', 'Utilisateur', 'Société', 'IP', 'Détails'];
        const csvData = [headers];

        resultats.logs.forEach(log => {
          csvData.push([
            log.createdAt,
            log.action,
            log.table,
            log.recordId || '',
            log.utilisateurId || '',
            log.societe?.nom || '',
            log.adresseIP || '',
            log.details || ''
          ]);
        });

        return csvData;
      }

      return resultats.logs;

    } catch (error) {
      logger.error('Erreur lors de l\'export des logs d\'audit', {
        params,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = AuditService;
