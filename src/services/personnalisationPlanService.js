'use strict';

const { Op } = require('sequelize');
const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError,
  ConflictError 
} = require('../middleware/errorHandler');

/**
 * Service pour la personnalisation du plan comptable
 */
class PersonnalisationPlanService {
  constructor(models) {
    this.models = models;
  }

  /**
   * Crée un nouveau compte personnalisé
   * @param {string} societeId - ID de la société
   * @param {Object} compteData - Données du compte
   * @param {string} utilisateurId - ID de l'utilisateur créateur
   * @returns {Object} Compte créé
   */
  async creerComptePersonnalise(societeId, compteData, utilisateurId) {
    const transaction = await this.models.sequelize.transaction();

    try {
      const { numero, libelle, nature, sens, compteParent, raisonSociale, typeAnalytique } = compteData;

      // Vérifier que la société existe
      const societe = await this.models.Societe.findByPk(societeId, { transaction });
      if (!societe) {
        throw new NotFoundError('Société');
      }

      // Validation du numéro de compte
      const validationNumero = this.models.CompteComptable.validateNumeroPersonnalise(numero, compteParent);
      if (!validationNumero.valide) {
        throw new ValidationError(validationNumero.erreur);
      }

      // Vérifier que le compte n'existe pas déjà
      const compteExistant = await this.models.CompteComptable.findOne({
        where: { numero, societeId },
        transaction
      });

      if (compteExistant) {
        throw new ConflictError(`Le compte ${numero} existe déjà`);
      }

      // Vérifier le compte parent s'il est spécifié
      let compteParentObj = null;
      if (compteParent) {
        compteParentObj = await this.models.CompteComptable.findOne({
          where: { numero: compteParent, societeId },
          transaction
        });

        if (!compteParentObj) {
          throw new NotFoundError(`Compte parent ${compteParent}`);
        }

        if (!compteParentObj.canHaveSousComptes()) {
          throw new ValidationError('Le compte parent ne peut pas avoir de sous-comptes');
        }
      }

      // Déterminer la classe et le niveau
      const classe = parseInt(numero.charAt(0));
      const niveau = numero.length;

      // Créer le compte personnalisé
      const nouveauCompte = await this.models.CompteComptable.create({
        numero,
        libelle,
        classe,
        nature,
        sens,
        niveau,
        compteParent,
        societeId,
        personnalise: true,
        modifiable: true,
        dateCreation: new Date(),
        utilisateurCreation: utilisateurId,
        raisonSociale,
        actif: true,
        obligatoireLettrage: ['411', '401', '421'].some(prefix => numero.startsWith(prefix)),
        typeAnalytique: typeAnalytique || 'AUCUN'
      }, { transaction });

      await transaction.commit();

      logger.info('Compte personnalisé créé', {
        societeId,
        numero,
        libelle,
        utilisateurId
      });

      return nouveauCompte;

    } catch (error) {
      await transaction.rollback();
      
      logger.error('Erreur lors de la création du compte personnalisé', {
        societeId,
        compteData,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Modifie un compte personnalisé
   * @param {string} societeId - ID de la société
   * @param {string} numero - Numéro du compte
   * @param {Object} updateData - Données à mettre à jour
   * @param {string} utilisateurId - ID de l'utilisateur
   * @returns {Object} Compte modifié
   */
  async modifierComptePersonnalise(societeId, numero, updateData, utilisateurId) {
    try {
      const compte = await this.models.CompteComptable.findOne({
        where: { numero, societeId }
      });

      if (!compte) {
        throw new NotFoundError(`Compte ${numero}`);
      }

      if (!compte.isCompteModifiable()) {
        throw new ValidationError('Ce compte ne peut pas être modifié');
      }

      // Seuls certains champs peuvent être modifiés
      const champsModifiables = ['libelle', 'raisonSociale', 'actif', 'obligatoireLettrage', 'typeAnalytique'];
      const donneesFiltrées = {};

      for (const champ of champsModifiables) {
        if (updateData[champ] !== undefined) {
          donneesFiltrées[champ] = updateData[champ];
        }
      }

      await compte.update(donneesFiltrées);

      logger.info('Compte personnalisé modifié', {
        societeId,
        numero,
        modifications: donneesFiltrées,
        utilisateurId
      });

      return compte;

    } catch (error) {
      logger.error('Erreur lors de la modification du compte', {
        societeId,
        numero,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Supprime un compte personnalisé
   * @param {string} societeId - ID de la société
   * @param {string} numero - Numéro du compte
   * @param {string} utilisateurId - ID de l'utilisateur
   * @returns {boolean} Succès de la suppression
   */
  async supprimerComptePersonnalise(societeId, numero, utilisateurId) {
    const transaction = await this.models.sequelize.transaction();

    try {
      const compte = await this.models.CompteComptable.findOne({
        where: { numero, societeId },
        transaction
      });

      if (!compte) {
        throw new NotFoundError(`Compte ${numero}`);
      }

      // Vérifier si le compte peut être supprimé
      const verificationSuppression = await compte.canBeDeleted();
      if (!verificationSuppression.canDelete) {
        throw new ValidationError(verificationSuppression.reason);
      }

      await compte.destroy({ transaction });
      await transaction.commit();

      logger.info('Compte personnalisé supprimé', {
        societeId,
        numero,
        utilisateurId
      });

      return true;

    } catch (error) {
      await transaction.rollback();
      
      logger.error('Erreur lors de la suppression du compte', {
        societeId,
        numero,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Active ou désactive un compte
   * @param {string} societeId - ID de la société
   * @param {string} numero - Numéro du compte
   * @param {boolean} actif - État souhaité
   * @param {string} utilisateurId - ID de l'utilisateur
   * @returns {Object} Compte modifié
   */
  async toggleCompteActif(societeId, numero, actif, utilisateurId) {
    try {
      const compte = await this.models.CompteComptable.findOne({
        where: { numero, societeId }
      });

      if (!compte) {
        throw new NotFoundError(`Compte ${numero}`);
      }

      if (!compte.isCompteModifiable()) {
        throw new ValidationError('Ce compte ne peut pas être modifié');
      }

      await compte.update({ actif });

      logger.info('État du compte modifié', {
        societeId,
        numero,
        actif,
        utilisateurId
      });

      return compte;

    } catch (error) {
      logger.error('Erreur lors de la modification de l\'état du compte', {
        societeId,
        numero,
        actif,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Obtient les statistiques de personnalisation du plan comptable
   * @param {string} societeId - ID de la société
   * @returns {Object} Statistiques
   */
  async getStatistiquesPersonnalisation(societeId) {
    try {
      const [
        totalComptes,
        comptesPersonnalises,
        comptesActifs,
        comptesInactifs,
        comptesAvecLettrage,
        comptesAnalytiques
      ] = await Promise.all([
        // Total des comptes
        this.models.CompteComptable.count({
          where: { societeId }
        }),
        
        // Comptes personnalisés
        this.models.CompteComptable.count({
          where: { societeId, personnalise: true }
        }),
        
        // Comptes actifs
        this.models.CompteComptable.count({
          where: { societeId, actif: true }
        }),
        
        // Comptes inactifs
        this.models.CompteComptable.count({
          where: { societeId, actif: false }
        }),
        
        // Comptes avec lettrage obligatoire
        this.models.CompteComptable.count({
          where: { societeId, obligatoireLettrage: true }
        }),
        
        // Comptes avec gestion analytique
        this.models.CompteComptable.count({
          where: { 
            societeId, 
            typeAnalytique: { [Op.ne]: 'AUCUN' }
          }
        })
      ]);

      // Répartition par classe
      const repartitionClasses = await this.models.CompteComptable.findAll({
        where: { societeId },
        attributes: [
          'classe',
          [this.models.sequelize.fn('COUNT', this.models.sequelize.col('numero')), 'nombre'],
          [this.models.sequelize.fn('SUM', 
            this.models.sequelize.literal('CASE WHEN personnalise = true THEN 1 ELSE 0 END')
          ), 'personnalises']
        ],
        group: ['classe'],
        order: [['classe', 'ASC']]
      });

      return {
        totalComptes,
        comptesStandard: totalComptes - comptesPersonnalises,
        comptesPersonnalises,
        comptesActifs,
        comptesInactifs,
        comptesAvecLettrage,
        comptesAnalytiques,
        tauxPersonnalisation: totalComptes > 0 ? (comptesPersonnalises / totalComptes * 100).toFixed(2) : 0,
        repartitionClasses: repartitionClasses.map(item => ({
          classe: item.classe,
          nombre: parseInt(item.dataValues.nombre),
          personnalises: parseInt(item.dataValues.personnalises || 0)
        }))
      };

    } catch (error) {
      logger.error('Erreur lors du calcul des statistiques', {
        societeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Valide la cohérence du plan comptable personnalisé
   * @param {string} societeId - ID de la société
   * @returns {Object} Résultat de la validation
   */
  async validerCoherencePlan(societeId) {
    try {
      const erreurs = [];
      const avertissements = [];

      // Vérifier les comptes orphelins (avec compte parent inexistant)
      const comptesOrphelins = await this.models.CompteComptable.findAll({
        where: {
          societeId,
          compteParent: { [Op.ne]: null }
        },
        include: [{
          model: this.models.CompteComptable,
          as: 'parent',
          required: false
        }]
      });

      const orphelins = comptesOrphelins.filter(compte => !compte.parent);
      if (orphelins.length > 0) {
        erreurs.push(`${orphelins.length} compte(s) avec parent inexistant: ${orphelins.map(c => c.numero).join(', ')}`);
      }

      // Vérifier les numéros de compte invalides
      const comptesInvalides = await this.models.CompteComptable.findAll({
        where: { societeId }
      });

      for (const compte of comptesInvalides) {
        try {
          this.models.CompteComptable.validateNumeroSYCOHADA(compte.numero);
        } catch (error) {
          erreurs.push(`Compte ${compte.numero}: ${error.message}`);
        }
      }

      // Vérifier les comptes inactifs avec des écritures récentes
      const comptesInactifsAvecEcritures = await this.models.CompteComptable.findAll({
        where: { societeId, actif: false },
        include: [{
          model: this.models.LigneEcriture,
          as: 'lignesEcriture',
          where: {
            createdAt: {
              [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 jours
            }
          },
          required: false
        }]
      });

      const inactifsAvecEcritures = comptesInactifsAvecEcritures.filter(compte => 
        compte.lignesEcriture && compte.lignesEcriture.length > 0
      );

      if (inactifsAvecEcritures.length > 0) {
        avertissements.push(`${inactifsAvecEcritures.length} compte(s) inactif(s) avec écritures récentes`);
      }

      return {
        valide: erreurs.length === 0,
        erreurs,
        avertissements,
        nombreComptesVerifies: comptesInvalides.length
      };

    } catch (error) {
      logger.error('Erreur lors de la validation du plan comptable', {
        societeId,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = PersonnalisationPlanService;
