'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class EcritureComptable extends Model {
    /**
     * Associations du modèle EcritureComptable
     */
    static associate(models) {
      // Une écriture appartient à un journal
      EcritureComptable.belongsTo(models.Journal, {
        foreignKey: 'journalCode',
        targetKey: 'code',
        as: 'journal'
      });

      // Une écriture appartient à une société
      EcritureComptable.belongsTo(models.Societe, {
        foreignKey: 'societeId',
        as: 'societe'
      });

      // Une écriture a plusieurs lignes
      EcritureComptable.hasMany(models.LigneEcriture, {
        foreignKey: 'ecritureId',
        as: 'lignes'
      });

      // Une écriture appartient à un exercice comptable
      EcritureComptable.belongsTo(models.ExerciceComptable, {
        foreignKey: 'exerciceId',
        as: 'exercice'
      });
    }

    /**
     * Méthodes statiques
     */

    /**
     * Obtient les écritures d'un journal pour une période
     * @param {string} journalCode - Code du journal
     * @param {Date} dateDebut - Date de début
     * @param {Date} dateFin - Date de fin
     * @returns {Promise<Array>} Liste des écritures
     */
    static async getEcrituresJournal(journalCode, dateDebut, dateFin) {
      const { Op } = sequelize;
      return await this.findAll({
        where: {
          journalCode,
          dateEcriture: {
            [Op.between]: [dateDebut, dateFin]
          }
        },
        include: [
          {
            model: sequelize.models.LigneEcriture,
            as: 'lignes',
            include: [
              {
                model: sequelize.models.CompteComptable,
                as: 'compte'
              }
            ]
          }
        ],
        order: [['dateEcriture', 'ASC'], ['numeroEcriture', 'ASC']]
      });
    }

    /**
     * Obtient les écritures en brouillard d'une société
     * @param {string} societeId - ID de la société
     * @returns {Promise<Array>} Écritures en brouillard
     */
    static async getBrouillards(societeId) {
      return await this.findAll({
        where: {
          societeId,
          statut: 'BROUILLARD'
        },
        include: [
          {
            model: sequelize.models.LigneEcriture,
            as: 'lignes'
          },
          {
            model: sequelize.models.Journal,
            as: 'journal'
          }
        ],
        order: [['createdAt', 'DESC']]
      });
    }

    /**
     * Valide les données d'une écriture
     * @param {Object} donnees - Données à valider
     * @returns {Object} Résultat de validation
     */
    static validateEcritureData(donnees) {
      const errors = [];

      if (!donnees.dateEcriture) {
        errors.push('La date d\'écriture est obligatoire');
      }

      if (!donnees.libelle || donnees.libelle.trim().length < 3) {
        errors.push('Le libellé doit contenir au moins 3 caractères');
      }

      if (!donnees.journalCode) {
        errors.push('Le journal est obligatoire');
      }

      if (!donnees.societeId) {
        errors.push('La société est obligatoire');
      }

      return {
        valide: errors.length === 0,
        erreurs: errors
      };
    }

    /**
     * Méthodes d'instance
     */

    /**
     * Calcule les totaux débit et crédit de l'écriture
     * @returns {Object} Totaux {debit, credit}
     */
    async calculerTotaux() {
      const lignes = await this.getLignes();

      let totalDebit = 0;
      let totalCredit = 0;

      lignes.forEach(ligne => {
        totalDebit += parseFloat(ligne.debit || 0);
        totalCredit += parseFloat(ligne.credit || 0);
      });

      return {
        debit: totalDebit,
        credit: totalCredit,
        equilibre: Math.abs(totalDebit - totalCredit) < 0.01 // Tolérance centimes
      };
    }

    /**
     * Vérifie si l'écriture est équilibrée
     * @returns {Promise<boolean>} True si équilibrée
     */
    async estEquilibree() {
      const totaux = await this.calculerTotaux();
      return totaux.equilibre;
    }

    /**
     * Vérifie si l'écriture peut être validée
     * @returns {Promise<{peutEtreValidee: boolean, raisons: Array}>}
     */
    async peutEtreValidee() {
      const raisons = [];

      // Vérifier le statut
      if (this.statut !== 'BROUILLARD') {
        raisons.push('L\'écriture n\'est pas en brouillard');
      }

      // Vérifier l'équilibre
      const equilibree = await this.estEquilibree();
      if (!equilibree) {
        raisons.push('L\'écriture n\'est pas équilibrée');
      }

      // Vérifier qu'il y a des lignes
      const lignes = await this.getLignes();
      if (lignes.length < 2) {
        raisons.push('L\'écriture doit avoir au moins 2 lignes');
      }

      // Vérifier que l'exercice est ouvert
      if (this.exercice && this.exercice.statut !== 'OUVERT') {
        raisons.push('L\'exercice comptable n\'est pas ouvert');
      }

      return {
        peutEtreValidee: raisons.length === 0,
        raisons
      };
    }

    /**
     * Obtient un résumé de l'écriture
     * @returns {Object} Résumé
     */
    async getResume() {
      const totaux = await this.calculerTotaux();
      const lignes = await this.getLignes();

      return {
        id: this.id,
        numeroEcriture: this.numeroEcriture,
        dateEcriture: this.dateEcriture,
        libelle: this.libelle,
        journalCode: this.journalCode,
        statut: this.statut,
        nombreLignes: lignes.length,
        totalDebit: totaux.debit,
        totalCredit: totaux.credit,
        equilibree: totaux.equilibre,
        reference: this.reference,
        typeOperation: this.typeOperation,
        modeSaisie: this.modeSaisie,
        datePiece: this.datePiece,
        referenceExterne: this.referenceExterne
      };
    }

    /**
     * Vérifie si l'écriture est équilibrée selon SYSCOHADA
     */
    estEquilibree() {
      return Math.abs(parseFloat(this.totalDebit || 0) - parseFloat(this.totalCredit || 0)) < 0.01;
    }

    /**
     * Vérifie si l'écriture est entièrement lettrée
     */
    estEntierementLettree() {
      return this.lettrageGlobal === true;
    }

    /**
     * Vérifie si l'écriture contient des lignes lettrées
     */
    contientLignesLettrees() {
      return this.lettre === true;
    }

    /**
     * Met à jour les totaux de contrôle
     */
    async mettreAJourTotaux() {
      const totaux = await this.calculerTotaux();
      this.totalDebit = totaux.debit;
      this.totalCredit = totaux.credit;
      await this.save();
      return totaux;
    }

    /**
     * Valide l'écriture selon les règles SYSCOHADA
     */
    static validateSyscohadaData(donnees) {
      const errors = [];

      if (!donnees.numeroEcriture || donnees.numeroEcriture.trim().length < 1) {
        errors.push('Le numéro d\'écriture est obligatoire');
      }

      if (!donnees.dateEcriture) {
        errors.push('La date d\'écriture est obligatoire');
      }

      if (!donnees.libelle || donnees.libelle.trim().length < 2) {
        errors.push('Le libellé doit contenir au moins 2 caractères');
      }

      if (!donnees.journalCode) {
        errors.push('Le code journal est obligatoire');
      }

      if (donnees.totalDebit !== undefined && donnees.totalCredit !== undefined) {
        const debit = parseFloat(donnees.totalDebit || 0);
        const credit = parseFloat(donnees.totalCredit || 0);
        
        if (Math.abs(debit - credit) >= 0.01) {
          errors.push('L\'écriture doit être équilibrée (débit = crédit)');
        }
      }

      if (donnees.datePiece && donnees.dateEcriture) {
        const datePiece = new Date(donnees.datePiece);
        const dateEcriture = new Date(donnees.dateEcriture);
        
        if (datePiece > dateEcriture) {
          errors.push('La date de pièce ne peut pas être postérieure à la date d\'écriture');
        }
      }

      return {
        valide: errors.length === 0,
        erreurs: errors
      };
    }
  }

  EcritureComptable.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    numeroEcriture: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true
    },
    dateEcriture: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    journalCode: {
      type: DataTypes.STRING(10),
      allowNull: false,
      references: {
        model: 'journaux',
        key: 'code'
      }
    },
    libelle: {
      type: DataTypes.STRING(200),
      allowNull: false
    },
    reference: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    exerciceId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'exercice_comptables',
        key: 'id'
      }
    },
    statut: {
      type: DataTypes.ENUM('BROUILLARD', 'VALIDEE', 'CLOTUREE'),
      allowNull: false,
      defaultValue: 'BROUILLARD'
    },
    pieceJustificative: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'piece_justificative'
    },
    dateValidation: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'date_validation'
    },
    utilisateurCreation: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'utilisateur_creation'
    },
    utilisateurValidation: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'utilisateur_validation'
    },
    societeId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'societes',
        key: 'id'
      }
    },
    // Nouveaux champs SYSCOHADA
    datePiece: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'date_piece'
    },
    dateEcheance: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'date_echeance'
    },
    referenceExterne: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'reference_externe'
    },
    totalDebit: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0.00,
      field: 'total_debit'
    },
    totalCredit: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0.00,
      field: 'total_credit'
    },
    userValidationId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'user_validation_id'
    },
    dateValidationSysco: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'date_validation'
    },
    lettrageGlobal: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'lettrage_global'
    },
    lettre: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    typeOperation: {
      type: DataTypes.ENUM('VENTE', 'ACHAT', 'TRESORERIE', 'SALAIRE', 'AMORTISSEMENT', 'PROVISION', 'REGULARISATION', 'OUVERTURE', 'CLOTURE', 'AUTRE'),
      allowNull: false,
      defaultValue: 'AUTRE',
      field: 'type_operation'
    },
    modeSaisie: {
      type: DataTypes.ENUM('MANUELLE', 'AUTOMATIQUE', 'IMPORTEE'),
      allowNull: false,
      defaultValue: 'MANUELLE',
      field: 'mode_saisie'
    },
    origine: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    deviseOrigine: {
      type: DataTypes.STRING(3),
      allowNull: true,
      field: 'devise_origine'
    },
    coursChange: {
      type: DataTypes.DECIMAL(10, 6),
      allowNull: true,
      field: 'cours_change'
    },
    montantDeviseOrigine: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      field: 'montant_devise_origine'
    }
  }, {
    sequelize,
    modelName: 'EcritureComptable',
    tableName: 'ecriture_comptables',
    timestamps: true,
    underscored: true
  });

  return EcritureComptable;
};