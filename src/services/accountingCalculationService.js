'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');

/**
 * Service pour les calculs comptables avancés
 * Conforme aux normes SYSCOHADA - Phase 2
 */
class AccountingCalculationService {
  constructor(models) {
    this.models = models;
  }

  /**
   * Calcule la balance générale d'une société
   * @param {string} societeId - ID de la société
   * @param {Object} options - Options de calcul
   * @returns {Promise<Object>} Balance générale
   */
  async calculerBalanceGenerale(societeId, options = {}) {
    const {
      dateDebut,
      dateFin,
      niveauDetail = 'COMPTE', // CLASSE, COMPTE, SOUS_COMPTE
      includeNonMouvementes = false,
      deviseAffichage = 'XOF'
    } = options;

    try {
      // 1. Validation de la société
      const societe = await this.models.Societe.findByPk(societeId);
      if (!societe) {
        throw new NotFoundError('Société inexistante');
      }

      // 2. Construction de la requête selon le niveau de détail
      const whereClause = this.buildWhereClause(societeId, dateDebut, dateFin);
      
      // 3. Requête des soldes par compte
      const soldesComptes = await this.getSoldesComptes(whereClause, niveauDetail);

      // 4. Calcul des totaux par classe
      const totauxClasses = this.calculerTotauxClasses(soldesComptes);

      // 5. Validation équilibre général
      const equilibre = this.validerEquilibreGeneral(totauxClasses);

      // 6. Formatage selon SYSCOHADA
      const balance = this.formaterBalanceSYSCOHADA(soldesComptes, totauxClasses, {
        societe,
        dateDebut,
        dateFin,
        niveauDetail,
        deviseAffichage
      });

      logger.info('Balance générale calculée', {
        societeId,
        nombreComptes: soldesComptes.length,
        equilibre: equilibre.equilibre,
        dateDebut,
        dateFin
      });

      return {
        balance,
        totaux: totauxClasses,
        equilibre,
        metadata: {
          societe: societe.nom,
          periode: { dateDebut, dateFin },
          niveauDetail,
          nombreComptes: soldesComptes.length,
          dateCalcul: new Date()
        }
      };

    } catch (error) {
      logger.error('Erreur calcul balance générale', {
        error: error.message,
        societeId,
        options
      });
      throw error;
    }
  }

  /**
   * Calcule le grand livre d'un compte
   * @param {string} compteNumero - Numéro du compte
   * @param {string} societeId - ID de la société
   * @param {Object} options - Options de calcul
   * @returns {Promise<Object>} Grand livre du compte
   */
  async calculerGrandLivre(compteNumero, societeId, options = {}) {
    const {
      dateDebut,
      dateFin,
      includeReports = true,
      groupeParMois = false
    } = options;

    try {
      // 1. Validation du compte
      const compte = await this.models.CompteComptable.findOne({
        where: { numero: compteNumero, societeId }
      });

      if (!compte) {
        throw new NotFoundError('Compte comptable inexistant');
      }

      // 2. Calcul du solde d'ouverture
      const soldeOuverture = includeReports ? 
        await this.calculerSoldeOuverture(compteNumero, societeId, dateDebut) : 0;

      // 3. Récupération des mouvements
      const mouvements = await this.getMouvementsCompte(compteNumero, societeId, dateDebut, dateFin);

      // 4. Calcul des soldes progressifs
      const lignesGrandLivre = this.calculerSoldesProgressifs(mouvements, soldeOuverture);

      // 5. Groupement par mois si demandé
      const donnees = groupeParMois ? 
        this.grouperParMois(lignesGrandLivre) : lignesGrandLivre;

      // 6. Calculs de synthèse
      const synthese = this.calculerSyntheseGrandLivre(lignesGrandLivre, soldeOuverture);

      logger.info('Grand livre calculé', {
        compteNumero,
        societeId,
        nombreMouvements: mouvements.length,
        soldeOuverture,
        soldeFinal: synthese.soldeFinal
      });

      return {
        compte: {
          numero: compte.numero,
          intitule: compte.intitule,
          classe: compte.classe,
          nature: compte.nature
        },
        soldeOuverture,
        mouvements: donnees,
        synthese,
        metadata: {
          periode: { dateDebut, dateFin },
          nombreMouvements: mouvements.length,
          dateCalcul: new Date()
        }
      };

    } catch (error) {
      logger.error('Erreur calcul grand livre', {
        error: error.message,
        compteNumero,
        societeId,
        options
      });
      throw error;
    }
  }

  /**
   * Calcule les soldes des tiers (clients/fournisseurs)
   * @param {string} societeId - ID de la société
   * @param {Object} options - Options de calcul
   * @returns {Promise<Object>} Soldes des tiers
   */
  async calculerSoldesTiers(societeId, options = {}) {
    const {
      type = null, // CLIENT, FOURNISSEUR, ou null pour tous
      dateDebut,
      dateFin,
      seulementEchus = false,
      includeDetails = true
    } = options;

    try {
      // 1. Récupération des tiers
      const whereClauseTiers = { societeId };
      if (type) whereClauseTiers.type = type;

      const tiers = await this.models.Party.findAll({
        where: whereClauseTiers,
        attributes: ['id', 'code', 'nom', 'type', 'compteComptable']
      });

      // 2. Calcul des soldes pour chaque tiers
      const soldesTiers = [];
      
      for (const tier of tiers) {
        const solde = await this.calculerSoldeTiers(tier.id, societeId, {
          dateDebut,
          dateFin,
          includeDetails
        });

        // Filtrer les échus si demandé
        if (seulementEchus && solde.montant >= 0) continue;

        soldesTiers.push({
          tiers: {
            id: tier.id,
            code: tier.code,
            nom: tier.nom,
            type: tier.type,
            compteComptable: tier.compteComptable
          },
          solde: solde.montant,
          details: includeDetails ? solde.details : null,
          echeances: solde.echeances,
          retard: solde.retard
        });
      }

      // 3. Calculs de synthèse
      const synthese = this.calculerSyntheseTiers(soldesTiers, type);

      logger.info('Soldes tiers calculés', {
        societeId,
        type,
        nombreTiers: soldesTiers.length,
        totalSoldes: synthese.totalSoldes
      });

      return {
        tiers: soldesTiers,
        synthese,
        metadata: {
          periode: { dateDebut, dateFin },
          type,
          nombreTiers: soldesTiers.length,
          dateCalcul: new Date()
        }
      };

    } catch (error) {
      logger.error('Erreur calcul soldes tiers', {
        error: error.message,
        societeId,
        options
      });
      throw error;
    }
  }

  /**
   * Calcule les ratios financiers SYSCOHADA
   * @param {string} societeId - ID de la société
   * @param {Object} options - Options de calcul
   * @returns {Promise<Object>} Ratios financiers
   */
  async calculerRatiosFinanciers(societeId, options = {}) {
    const {
      dateDebut,
      dateFin,
      exercicePrecedent = false
    } = options;

    try {
      // 1. Récupération des données de base
      const donneesComptables = await this.getDonneesComptables(societeId, dateDebut, dateFin);
      
      // 2. Calcul des ratios de liquidité
      const ratiosLiquidite = this.calculerRatiosLiquidite(donneesComptables);

      // 3. Calcul des ratios de solvabilité
      const ratiosSolvabilite = this.calculerRatiosSolvabilite(donneesComptables);

      // 4. Calcul des ratios de rentabilité
      const ratiosRentabilite = this.calculerRatiosRentabilite(donneesComptables);

      // 5. Calcul des ratios d'activité
      const ratiosActivite = this.calculerRatiosActivite(donneesComptables);

      // 6. Analyse comparative si exercice précédent
      let evolution = null;
      if (exercicePrecedent) {
        evolution = await this.calculerEvolutionRatios(societeId, dateDebut, dateFin);
      }

      const ratios = {
        liquidite: ratiosLiquidite,
        solvabilite: ratiosSolvabilite,
        rentabilite: ratiosRentabilite,
        activite: ratiosActivite,
        evolution
      };

      logger.info('Ratios financiers calculés', {
        societeId,
        periode: { dateDebut, dateFin },
        nombreRatios: Object.keys(ratios).length
      });

      return {
        ratios,
        donneesBase: donneesComptables,
        interpretation: this.interpreterRatios(ratios),
        metadata: {
          periode: { dateDebut, dateFin },
          dateCalcul: new Date()
        }
      };

    } catch (error) {
      logger.error('Erreur calcul ratios financiers', {
        error: error.message,
        societeId,
        options
      });
      throw error;
    }
  }

  /**
   * Calcule la situation de trésorerie
   * @param {string} societeId - ID de la société
   * @param {Object} options - Options de calcul
   * @returns {Promise<Object>} Situation de trésorerie
   */
  async calculerSituationTresorerie(societeId, options = {}) {
    const {
      dateDebut,
      dateFin,
      previsionJours = 30,
      includePrevisionnels = true
    } = options;

    try {
      // 1. Soldes des comptes de trésorerie
      const soldesTresorerie = await this.getSoldesTresorerie(societeId, dateFin);

      // 2. Encaissements prévisionnels
      const encaissementsPrevisionnels = includePrevisionnels ? 
        await this.getEncaissementsPrevisionnels(societeId, dateFin, previsionJours) : [];

      // 3. Décaissements prévisionnels
      const decaissementsPrevisionnels = includePrevisionnels ?
        await this.getDecaissementsPrevisionnels(societeId, dateFin, previsionJours) : [];

      // 4. Calcul de la trésorerie prévisionnelle
      const tresoreriePrevisionnelle = this.calculerTresoreriePrevisionnelle(
        soldesTresorerie,
        encaissementsPrevisionnels,
        decaissementsPrevisionnels,
        previsionJours
      );

      // 5. Analyse des risques
      const analyseRisques = this.analyserRisquesTresorerie(tresoreriePrevisionnelle);

      logger.info('Situation trésorerie calculée', {
        societeId,
        tresorerieActuelle: soldesTresorerie.total,
        tresoreriePrevisionnelle: tresoreriePrevisionnelle.soldeFinal
      });

      return {
        tresorerieActuelle: soldesTresorerie,
        previsionnels: {
          encaissements: encaissementsPrevisionnels,
          decaissements: decaissementsPrevisionnels
        },
        tresoreriePrevisionnelle,
        analyseRisques,
        metadata: {
          periode: { dateDebut, dateFin },
          previsionJours,
          dateCalcul: new Date()
        }
      };

    } catch (error) {
      logger.error('Erreur calcul situation trésorerie', {
        error: error.message,
        societeId,
        options
      });
      throw error;
    }
  }

  // === MÉTHODES UTILITAIRES ===

  /**
   * Construit la clause WHERE pour les requêtes
   */
  buildWhereClause(societeId, dateDebut, dateFin) {
    const where = { societeId };
    
    if (dateDebut || dateFin) {
      where.dateEcriture = {};
      if (dateDebut) where.dateEcriture[this.models.Sequelize.Op.gte] = dateDebut;
      if (dateFin) where.dateEcriture[this.models.Sequelize.Op.lte] = dateFin;
    }

    return where;
  }

  /**
   * Récupère les soldes des comptes
   */
  async getSoldesComptes(whereClause, niveauDetail) {
    const { Op } = this.models.Sequelize;

    const query = `
      SELECT 
        c.numero,
        c.intitule,
        c.classe,
        c.nature,
        COALESCE(SUM(l.debit), 0) as total_debit,
        COALESCE(SUM(l.credit), 0) as total_credit,
        CASE 
          WHEN c.nature = 'DEBIT' THEN COALESCE(SUM(l.debit), 0) - COALESCE(SUM(l.credit), 0)
          ELSE COALESCE(SUM(l.credit), 0) - COALESCE(SUM(l.debit), 0)
        END as solde
      FROM compte_comptables c
      LEFT JOIN ligne_ecritures l ON c.numero = l.compte_numero
      LEFT JOIN ecriture_comptables e ON l.ecriture_id = e.id
      WHERE c.societe_id = :societeId
        AND (e.id IS NULL OR (e.statut = 'VALIDEE' ${whereClause.dateEcriture ? 
          'AND e.date_ecriture >= :dateDebut AND e.date_ecriture <= :dateFin' : ''}))
      GROUP BY c.numero, c.intitule, c.classe, c.nature
      HAVING ABS(COALESCE(SUM(l.debit), 0) - COALESCE(SUM(l.credit), 0)) > 0.01
      ORDER BY c.numero
    `;

    const replacements = {
      societeId: whereClause.societeId,
      ...(whereClause.dateEcriture?.gte && { dateDebut: whereClause.dateEcriture.gte }),
      ...(whereClause.dateEcriture?.lte && { dateFin: whereClause.dateEcriture.lte })
    };

    const [results] = await this.models.sequelize.query(query, {
      replacements,
      type: this.models.Sequelize.QueryTypes.SELECT
    });

    return results;
  }

  /**
   * Calcule les totaux par classe
   */
  calculerTotauxClasses(soldesComptes) {
    const totaux = {};

    for (const compte of soldesComptes) {
      const classe = compte.classe;
      if (!totaux[classe]) {
        totaux[classe] = {
          classe,
          totalDebit: 0,
          totalCredit: 0,
          solde: 0,
          nombreComptes: 0
        };
      }

      totaux[classe].totalDebit += parseFloat(compte.total_debit);
      totaux[classe].totalCredit += parseFloat(compte.total_credit);
      totaux[classe].solde += parseFloat(compte.solde);
      totaux[classe].nombreComptes++;
    }

    return totaux;
  }

  /**
   * Valide l'équilibre général
   */
  validerEquilibreGeneral(totauxClasses) {
    let totalDebit = 0;
    let totalCredit = 0;

    for (const classe of Object.values(totauxClasses)) {
      totalDebit += classe.totalDebit;
      totalCredit += classe.totalCredit;
    }

    const difference = Math.abs(totalDebit - totalCredit);
    const equilibre = difference < 0.01;

    return {
      equilibre,
      totalDebit,
      totalCredit,
      difference,
      tolerance: 0.01
    };
  }

  /**
   * Formate la balance selon SYSCOHADA
   */
  formaterBalanceSYSCOHADA(soldesComptes, totauxClasses, options) {
    const { societe, dateDebut, dateFin, niveauDetail, deviseAffichage } = options;

    return {
      entete: {
        societe: societe.nom,
        periode: `Du ${dateDebut || 'Début'} au ${dateFin || 'Fin'}`,
        devise: deviseAffichage,
        niveauDetail
      },
      comptes: soldesComptes.map(compte => ({
        numero: compte.numero,
        intitule: compte.intitule,
        classe: compte.classe,
        nature: compte.nature,
        mouvements: {
          debit: parseFloat(compte.total_debit),
          credit: parseFloat(compte.total_credit)
        },
        solde: {
          montant: Math.abs(parseFloat(compte.solde)),
          sens: parseFloat(compte.solde) >= 0 ? 
            (compte.nature === 'DEBIT' ? 'DEBITEUR' : 'CREDITEUR') :
            (compte.nature === 'DEBIT' ? 'CREDITEUR' : 'DEBITEUR')
        }
      })),
      totauxClasses: Object.values(totauxClasses)
    };
  }

  /**
   * Calcule le solde d'ouverture d'un compte
   */
  async calculerSoldeOuverture(compteNumero, societeId, dateDebut) {
    if (!dateDebut) return 0;

    const query = `
      SELECT 
        COALESCE(SUM(l.debit), 0) - COALESCE(SUM(l.credit), 0) as solde
      FROM ligne_ecritures l
      JOIN ecriture_comptables e ON l.ecriture_id = e.id
      WHERE l.compte_numero = :compteNumero
        AND e.societe_id = :societeId
        AND e.date_ecriture < :dateDebut
        AND e.statut = 'VALIDEE'
    `;

    const [result] = await this.models.sequelize.query(query, {
      replacements: { compteNumero, societeId, dateDebut },
      type: this.models.Sequelize.QueryTypes.SELECT
    });

    return parseFloat(result[0]?.solde || 0);
  }

  /**
   * Récupère les mouvements d'un compte
   */
  async getMouvementsCompte(compteNumero, societeId, dateDebut, dateFin) {
    const whereClause = {
      compteNumero,
      '$ecriture.societeId$': societeId,
      '$ecriture.statut$': 'VALIDEE'
    };

    if (dateDebut || dateFin) {
      whereClause['$ecriture.dateEcriture$'] = {};
      if (dateDebut) whereClause['$ecriture.dateEcriture$'][this.models.Sequelize.Op.gte] = dateDebut;
      if (dateFin) whereClause['$ecriture.dateEcriture$'][this.models.Sequelize.Op.lte] = dateFin;
    }

    return await this.models.LigneEcriture.findAll({
      where: whereClause,
      include: [{
        model: this.models.EcritureComptable,
        as: 'ecriture',
        attributes: ['id', 'numeroEcriture', 'dateEcriture', 'libelle', 'journalCode']
      }],
      order: [['ecriture', 'dateEcriture', 'ASC'], ['ecriture', 'numeroEcriture', 'ASC']]
    });
  }

  /**
   * Calcule les soldes progressifs
   */
  calculerSoldesProgressifs(mouvements, soldeOuverture) {
    let soldeProgressif = soldeOuverture;
    
    return mouvements.map(mouvement => {
      const debit = parseFloat(mouvement.montantDebit || 0);
      const credit = parseFloat(mouvement.montantCredit || 0);
      const mouvementNet = debit - credit;
      
      soldeProgressif += mouvementNet;

      return {
        date: mouvement.ecriture.dateEcriture,
        numeroEcriture: mouvement.ecriture.numeroEcriture,
        libelle: mouvement.libelle || mouvement.ecriture.libelle,
        journalCode: mouvement.ecriture.journalCode,
        debit,
        credit,
        soldeProgressif,
        tiersNom: mouvement.tiersNom
      };
    });
  }

  /**
   * Calcule la synthèse du grand livre
   */
  calculerSyntheseGrandLivre(lignes, soldeOuverture) {
    const totalDebit = lignes.reduce((sum, ligne) => sum + ligne.debit, 0);
    const totalCredit = lignes.reduce((sum, ligne) => sum + ligne.credit, 0);
    const soldeFinal = lignes.length > 0 ? lignes[lignes.length - 1].soldeProgressif : soldeOuverture;

    return {
      soldeOuverture,
      totalDebit,
      totalCredit,
      soldeFinal,
      nombreMouvements: lignes.length,
      variationPeriode: soldeFinal - soldeOuverture
    };
  }

  /**
   * Calcule le solde d'un tiers
   */
  async calculerSoldeTiers(tiersId, societeId, options = {}) {
    const { dateDebut, dateFin, includeDetails } = options;

    // Récupération des lignes du tiers
    const whereClause = {
      tiersId,
      '$ecriture.societeId$': societeId,
      '$ecriture.statut$': 'VALIDEE'
    };

    if (dateDebut || dateFin) {
      whereClause['$ecriture.dateEcriture$'] = {};
      if (dateDebut) whereClause['$ecriture.dateEcriture$'][this.models.Sequelize.Op.gte] = dateDebut;
      if (dateFin) whereClause['$ecriture.dateEcriture$'][this.models.Sequelize.Op.lte] = dateFin;
    }

    const lignes = await this.models.LigneEcriture.findAll({
      where: whereClause,
      include: [{
        model: this.models.EcritureComptable,
        as: 'ecriture',
        attributes: ['id', 'numeroEcriture', 'dateEcriture', 'libelle']
      }],
      order: [['ecriture', 'dateEcriture', 'DESC']]
    });

    // Calcul du solde
    const solde = lignes.reduce((sum, ligne) => {
      return sum + parseFloat(ligne.montantDebit || 0) - parseFloat(ligne.montantCredit || 0);
    }, 0);

    // Analyse des échéances
    const echeances = this.analyserEcheancesTiers(lignes);

    return {
      montant: solde,
      details: includeDetails ? lignes : null,
      echeances,
      retard: echeances.retard
    };
  }

  /**
   * Analyse les échéances d'un tiers
   */
  analyserEcheancesTiers(lignes) {
    const aujourd = new Date();
    let enRetard = 0;
    let aEcheoir = 0;

    for (const ligne of lignes) {
      if (ligne.dateEcheance) {
        const echeance = new Date(ligne.dateEcheance);
        const montant = parseFloat(ligne.montantDebit || 0) - parseFloat(ligne.montantCredit || 0);
        
        if (echeance < aujourd && montant > 0) {
          enRetard += montant;
        } else if (echeance >= aujourd && montant > 0) {
          aEcheoir += montant;
        }
      }
    }

    return {
      enRetard,
      aEcheoir,
      retard: enRetard > 0
    };
  }

  /**
   * Calcule la synthèse des tiers
   */
  calculerSyntheseTiers(soldesTiers, type) {
    const synthese = {
      nombreTiers: soldesTiers.length,
      totalSoldes: 0,
      soldesDebiteurs: 0,
      soldesCrediteurs: 0,
      tiersEnRetard: 0,
      montantRetard: 0
    };

    for (const tier of soldesTiers) {
      synthese.totalSoldes += Math.abs(tier.solde);
      
      if (tier.solde > 0) {
        synthese.soldesDebiteurs += tier.solde;
      } else {
        synthese.soldesCrediteurs += Math.abs(tier.solde);
      }

      if (tier.retard) {
        synthese.tiersEnRetard++;
        synthese.montantRetard += tier.echeances.enRetard;
      }
    }

    return synthese;
  }

  /**
   * Récupère les données comptables pour les ratios
   */
  async getDonneesComptables(societeId, dateDebut, dateFin) {
    // Cette méthode récupère les données nécessaires pour calculer les ratios
    // Implémentation simplifiée - à développer selon les besoins spécifiques
    
    const query = `
      SELECT 
        c.classe,
        c.nature,
        SUM(CASE WHEN c.nature = 'DEBIT' THEN COALESCE(l.debit, 0) - COALESCE(l.credit, 0)
                 ELSE COALESCE(l.credit, 0) - COALESCE(l.debit, 0) END) as solde
      FROM compte_comptables c
      LEFT JOIN ligne_ecritures l ON c.numero = l.compte_numero
      LEFT JOIN ecriture_comptables e ON l.ecriture_id = e.id
      WHERE c.societe_id = :societeId
        AND (e.id IS NULL OR (e.statut = 'VALIDEE' 
          AND e.date_ecriture >= :dateDebut AND e.date_ecriture <= :dateFin))
      GROUP BY c.classe, c.nature
    `;

    const [results] = await this.models.sequelize.query(query, {
      replacements: { societeId, dateDebut, dateFin },
      type: this.models.Sequelize.QueryTypes.SELECT
    });

    return this.structurerDonneesComptables(results);
  }

  /**
   * Structure les données comptables pour les ratios
   */
  structurerDonneesComptables(results) {
    const donnees = {
      actif: { immobilise: 0, circulant: 0, tresorerie: 0 },
      passif: { capitaux: 0, dettes: 0 },
      charges: 0,
      produits: 0
    };

    for (const result of results) {
      const classe = parseInt(result.classe);
      const solde = parseFloat(result.solde);

      switch (classe) {
        case 1: // Comptes de capitaux
          donnees.passif.capitaux += solde;
          break;
        case 2: // Comptes d'immobilisations
          donnees.actif.immobilise += solde;
          break;
        case 3: // Comptes de stocks
        case 4: // Comptes de tiers
          donnees.actif.circulant += solde;
          break;
        case 5: // Comptes financiers
          donnees.actif.tresorerie += solde;
          break;
        case 6: // Comptes de charges
          donnees.charges += solde;
          break;
        case 7: // Comptes de produits
          donnees.produits += solde;
          break;
      }
    }

    return donnees;
  }

  /**
   * Calcule les ratios de liquidité
   */
  calculerRatiosLiquidite(donnees) {
    const { actif } = donnees;
    const actifCirculant = actif.circulant + actif.tresorerie;
    const dettesCourtTerme = donnees.passif.dettes; // Simplification

    return {
      liquiditeGenerale: dettesCourtTerme !== 0 ? actifCirculant / dettesCourtTerme : null,
      liquiditeReduite: dettesCourtTerme !== 0 ? (actif.circulant + actif.tresorerie) / dettesCourtTerme : null,
      liquiditeImmediate: dettesCourtTerme !== 0 ? actif.tresorerie / dettesCourtTerme : null
    };
  }

  /**
   * Calcule les ratios de solvabilité
   */
  calculerRatiosSolvabilite(donnees) {
    const totalActif = donnees.actif.immobilise + donnees.actif.circulant + donnees.actif.tresorerie;
    const totalPassif = donnees.passif.capitaux + donnees.passif.dettes;

    return {
      autonomieFinanciere: totalPassif !== 0 ? donnees.passif.capitaux / totalPassif : null,
      endettement: donnees.passif.capitaux !== 0 ? donnees.passif.dettes / donnees.passif.capitaux : null,
      solvabiliteGenerale: donnees.passif.dettes !== 0 ? totalActif / donnees.passif.dettes : null
    };
  }

  /**
   * Calcule les ratios de rentabilité
   */
  calculerRatiosRentabilite(donnees) {
    const resultat = donnees.produits - donnees.charges;
    const totalActif = donnees.actif.immobilise + donnees.actif.circulant + donnees.actif.tresorerie;

    return {
      margeNette: donnees.produits !== 0 ? resultat / donnees.produits : null,
      rentabiliteActif: totalActif !== 0 ? resultat / totalActif : null,
      rentabiliteCapitaux: donnees.passif.capitaux !== 0 ? resultat / donnees.passif.capitaux : null
    };
  }

  /**
   * Calcule les ratios d'activité
   */
  calculerRatiosActivite(donnees) {
    const totalActif = donnees.actif.immobilise + donnees.actif.circulant + donnees.actif.tresorerie;

    return {
      rotationActif: totalActif !== 0 ? donnees.produits / totalActif : null,
      rotationStocks: donnees.actif.circulant !== 0 ? donnees.charges / donnees.actif.circulant : null // Simplification
    };
  }

  /**
   * Interprète les ratios calculés
   */
  interpreterRatios(ratios) {
    const interpretations = [];

    // Liquidité
    if (ratios.liquidite.liquiditeGenerale < 1) {
      interpretations.push({
        type: 'ALERTE',
        ratio: 'Liquidité générale',
        message: 'Liquidité insuffisante - Risque de difficultés de paiement'
      });
    }

    // Solvabilité
    if (ratios.solvabilite.autonomieFinanciere < 0.3) {
      interpretations.push({
        type: 'ATTENTION',
        ratio: 'Autonomie financière',
        message: 'Dépendance financière élevée'
      });
    }

    // Rentabilité
    if (ratios.rentabilite.margeNette < 0) {
      interpretations.push({
        type: 'CRITIQUE',
        ratio: 'Marge nette',
        message: 'Activité déficitaire'
      });
    }

    return interpretations;
  }

  /**
   * Récupère les soldes de trésorerie
   */
  async getSoldesTresorerie(societeId, dateFin) {
    const query = `
      SELECT 
        c.numero,
        c.intitule,
        COALESCE(SUM(l.debit), 0) - COALESCE(SUM(l.credit), 0) as solde
      FROM compte_comptables c
      LEFT JOIN ligne_ecritures l ON c.numero = l.compte_numero
      LEFT JOIN ecriture_comptables e ON l.ecriture_id = e.id
      WHERE c.societe_id = :societeId
        AND c.classe = 5
        AND (e.id IS NULL OR (e.statut = 'VALIDEE' AND e.date_ecriture <= :dateFin))
      GROUP BY c.numero, c.intitule
      HAVING ABS(COALESCE(SUM(l.debit), 0) - COALESCE(SUM(l.credit), 0)) > 0.01
      ORDER BY c.numero
    `;

    const [results] = await this.models.sequelize.query(query, {
      replacements: { societeId, dateFin },
      type: this.models.Sequelize.QueryTypes.SELECT
    });

    const comptes = results.map(r => ({
      numero: r.numero,
      intitule: r.intitule,
      solde: parseFloat(r.solde)
    }));

    const total = comptes.reduce((sum, compte) => sum + compte.solde, 0);

    return { comptes, total };
  }

  /**
   * Récupère les encaissements prévisionnels
   */
  async getEncaissementsPrevisionnels(societeId, dateDebut, jours) {
    const dateFin = new Date(dateDebut);
    dateFin.setDate(dateFin.getDate() + jours);

    const lignes = await this.models.LigneEcriture.findAll({
      where: {
        '$ecriture.societeId$': societeId,
        '$ecriture.statut$': 'VALIDEE',
        montantDebit: { [this.models.Sequelize.Op.gt]: 0 },
        dateEcheance: {
          [this.models.Sequelize.Op.between]: [dateDebut, dateFin]
        },
        lettrage: null
      },
      include: [{
        model: this.models.EcritureComptable,
        as: 'ecriture'
      }, {
        model: this.models.Party,
        as: 'tiers',
        attributes: ['nom', 'type']
      }],
      order: [['dateEcheance', 'ASC']]
    });

    return lignes.map(ligne => ({
      dateEcheance: ligne.dateEcheance,
      montant: parseFloat(ligne.montantDebit),
      tiers: ligne.tiers?.nom,
      libelle: ligne.libelle
    }));
  }

  /**
   * Récupère les décaissements prévisionnels
   */
  async getDecaissementsPrevisionnels(societeId, dateDebut, jours) {
    const dateFin = new Date(dateDebut);
    dateFin.setDate(dateFin.getDate() + jours);

    const lignes = await this.models.LigneEcriture.findAll({
      where: {
        '$ecriture.societeId$': societeId,
        '$ecriture.statut$': 'VALIDEE',
        montantCredit: { [this.models.Sequelize.Op.gt]: 0 },
        dateEcheance: {
          [this.models.Sequelize.Op.between]: [dateDebut, dateFin]
        },
        lettrage: null
      },
      include: [{
        model: this.models.EcritureComptable,
        as: 'ecriture'
      }, {
        model: this.models.Party,
        as: 'tiers',
        attributes: ['nom', 'type']
      }],
      order: [['dateEcheance', 'ASC']]
    });

    return lignes.map(ligne => ({
      dateEcheance: ligne.dateEcheance,
      montant: parseFloat(ligne.montantCredit),
      tiers: ligne.tiers?.nom,
      libelle: ligne.libelle
    }));
  }

  /**
   * Calcule la trésorerie prévisionnelle
   */
  calculerTresoreriePrevisionnelle(soldesTresorerie, encaissements, decaissements, jours) {
    let soldeProgressif = soldesTresorerie.total;
    const mouvements = [];

    // Fusion et tri des mouvements par date
    const tousMovements = [
      ...encaissements.map(e => ({ ...e, type: 'ENCAISSEMENT' })),
      ...decaissements.map(d => ({ ...d, type: 'DECAISSEMENT' }))
    ].sort((a, b) => new Date(a.dateEcheance) - new Date(b.dateEcheance));

    for (const mouvement of tousMovements) {
      if (mouvement.type === 'ENCAISSEMENT') {
        soldeProgressif += mouvement.montant;
      } else {
        soldeProgressif -= mouvement.montant;
      }

      mouvements.push({
        date: mouvement.dateEcheance,
        type: mouvement.type,
        montant: mouvement.montant,
        tiers: mouvement.tiers,
        libelle: mouvement.libelle,
        soldeProgressif
      });
    }

    return {
      soldeInitial: soldesTresorerie.total,
      mouvements,
      soldeFinal: soldeProgressif,
      variationPeriode: soldeProgressif - soldesTresorerie.total
    };
  }

  /**
   * Analyse les risques de trésorerie
   */
  analyserRisquesTresorerie(tresoreriePrevisionnelle) {
    const risques = [];
    const alertes = [];

    // Analyse du solde final
    if (tresoreriePrevisionnelle.soldeFinal < 0) {
      risques.push({
        type: 'CRITIQUE',
        message: 'Trésorerie prévisionnelle négative',
        montant: tresoreriePrevisionnelle.soldeFinal
      });
    } else if (tresoreriePrevisionnelle.soldeFinal < 10000) { // Seuil configurable
      alertes.push({
        type: 'ATTENTION',
        message: 'Trésorerie prévisionnelle faible',
        montant: tresoreriePrevisionnelle.soldeFinal
      });
    }

    // Analyse des mouvements
    for (const mouvement of tresoreriePrevisionnelle.mouvements) {
      if (mouvement.soldeProgressif < 0) {
        risques.push({
          type: 'ALERTE',
          message: `Trésorerie négative prévue le ${mouvement.date}`,
          date: mouvement.date,
          montant: mouvement.soldeProgressif
        });
      }
    }

    return { risques, alertes };
  }
}

module.exports = AccountingCalculationService;