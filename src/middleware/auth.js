/**
 * Middleware d'authentification par clé API
 * API Comptabilité SYSCOHADA
 */

const { UnauthorizedError, asyncHandler } = require('./errorHandler');
const { logger } = require('../config/logger');
const ApiKeyService = require('../services/apiKeyService');

/**
 * Extraction de la clé API depuis les headers
 */
const extractApiKey = (req) => {
  let apiKey = null;

  // Vérifier dans le header Authorization (Bearer)
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
    apiKey = req.headers.authorization.split(' ')[1];
  }
  
  // Vérifier dans le header X-API-Key
  if (!apiKey && req.headers['x-api-key']) {
    apiKey = req.headers['x-api-key'];
  }

  // Vérifier dans les query parameters (moins sécurisé, pour les tests)
  if (!apiKey && req.query.api_key) {
    apiKey = req.query.api_key;
  }

  return apiKey;
};

/**
 * Middleware de protection des routes
 */
const protect = asyncHandler(async (req, res, next) => {
  // Extraire la clé API
  const apiKey = extractApiKey(req);

  // Vérifier si la clé API existe
  if (!apiKey) {
    throw new UnauthorizedError('Accès refusé. Clé API manquante.');
  }

  try {
    // Valider la clé API
    const validatedKey = await ApiKeyService.validateApiKey(apiKey);
    
    if (!validatedKey) {
      throw new UnauthorizedError('Clé API invalide ou expirée');
    }
    
    // Ajouter les informations de la clé API à la requête
    req.apiKey = validatedKey;
    
    logger.info('Authentification par clé API réussie', {
      keyId: validatedKey.id,
      keyName: validatedKey.name,
      keyPrefix: validatedKey.prefix
    });

    next();
  } catch (error) {
    logger.warn('Tentative d\'authentification par clé API échouée', {
      keyPrefix: apiKey ? apiKey.substring(0, 10) + '...' : 'N/A',
      error: error.message
    });
    
    if (error instanceof UnauthorizedError) {
      throw error;
    }
    
    throw new UnauthorizedError('Erreur lors de la validation de la clé API');
  }
});

/**
 * Middleware optionnel - ajoute les infos de la clé API si présente
 */
const optionalAuth = asyncHandler(async (req, res, next) => {
  const apiKey = extractApiKey(req);

  if (apiKey) {
    try {
      const validatedKey = await ApiKeyService.validateApiKey(apiKey);
      if (validatedKey) {
        req.apiKey = validatedKey;
      }
    } catch (error) {
      // Ignorer les erreurs pour l'auth optionnelle
      logger.debug('Clé API optionnelle invalide', { error: error.message });
    }
  }

  next();
});

/**
 * Middleware de vérification des permissions
 */
const requirePermission = (permission) => {
  return asyncHandler(async (req, res, next) => {
    if (!req.apiKey) {
      throw new UnauthorizedError('Clé API requise');
    }

    if (!ApiKeyService.hasPermission(req.apiKey, permission)) {
      throw new UnauthorizedError(`Permission '${permission}' requise`);
    }

    next();
  });
};

/**
 * Route de vérification de la clé API
 */
const verifyApiKeyRoute = asyncHandler(async (req, res) => {
  // Le middleware protect a déjà vérifié la clé API
  res.json({
    success: true,
    message: 'Clé API valide',
    data: {
      apiKey: {
        id: req.apiKey.id,
        name: req.apiKey.name,
        prefix: req.apiKey.prefix,
        permissions: req.apiKey.permissions
      }
    }
  });
});

module.exports = {
  extractApiKey,
  protect,
  optionalAuth,
  requirePermission,
  verifyApiKeyRoute
};
