'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Créer la table devises
    await queryInterface.createTable('devises', {
      code: {
        type: Sequelize.STRING(3),
        primaryKey: true,
        allowNull: false
      },
      libelle: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      symbole: {
        type: Sequelize.STRING(10),
        allowNull: false
      },
      decimales: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 2
      },
      actif: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Index pour la table devises
    await queryInterface.addIndex('devises', {
      fields: ['actif'],
      name: 'idx_devises_actif'
    });

    await queryInterface.addIndex('devises', {
      fields: ['libelle'],
      name: 'idx_devises_libelle'
    });

    // Créer la table taux_changes
    await queryInterface.createTable('taux_changes', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      devise_source: {
        type: Sequelize.STRING(3),
        allowNull: false,
        references: {
          model: 'devises',
          key: 'code'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      devise_cible: {
        type: Sequelize.STRING(3),
        allowNull: false,
        references: {
          model: 'devises',
          key: 'code'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      taux: {
        type: Sequelize.DECIMAL(15, 6),
        allowNull: false
      },
      date_application: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      source: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'MANUEL'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Index pour la table taux_changes
    await queryInterface.addIndex('taux_changes', {
      fields: ['devise_source', 'devise_cible', 'date_application'],
      name: 'idx_taux_source_cible_date'
    });

    await queryInterface.addIndex('taux_changes', {
      fields: ['devise_source', 'devise_cible'],
      name: 'idx_taux_paire_devises'
    });

    await queryInterface.addIndex('taux_changes', {
      fields: ['date_application'],
      name: 'idx_taux_date_application'
    });

    await queryInterface.addIndex('taux_changes', {
      fields: ['source'],
      name: 'idx_taux_source'
    });
  },

  async down (queryInterface, Sequelize) {
    // Supprimer les index de taux_changes
    await queryInterface.removeIndex('taux_changes', 'idx_taux_source_cible_date');
    await queryInterface.removeIndex('taux_changes', 'idx_taux_paire_devises');
    await queryInterface.removeIndex('taux_changes', 'idx_taux_date_application');
    await queryInterface.removeIndex('taux_changes', 'idx_taux_source');

    // Supprimer les index de devises
    await queryInterface.removeIndex('devises', 'idx_devises_actif');
    await queryInterface.removeIndex('devises', 'idx_devises_libelle');

    // Supprimer les tables
    await queryInterface.dropTable('taux_changes');
    await queryInterface.dropTable('devises');
  }
};
