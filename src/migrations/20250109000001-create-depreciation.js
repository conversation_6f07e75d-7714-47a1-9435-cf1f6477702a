'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('depreciations', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      societeId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'societes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      code: {
        type: Sequelize.STRING(20),
        allowNull: false
      },
      libelle: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      categorie: {
        type: Sequelize.ENUM(
          'TERRAIN',
          'BATIMENT',
          'MATERIEL_TRANSPORT',
          'MATERIEL_BUREAU',
          'MATERIEL_INFORMATIQUE',
          'MOBILIER',
          'INSTALLATION',
          'MATERIEL_INDUSTRIEL',
          'BREVETS_LICENCES',
          'LOGICIELS',
          'AUTRES'
        ),
        allowNull: false
      },
      compteImmobilisation: {
        type: Sequelize.STRING(10),
        allowNull: false
      },
      compteAmortissement: {
        type: Sequelize.STRING(10),
        allowNull: false
      },
      compteDotation: {
        type: Sequelize.STRING(10),
        allowNull: false,
        defaultValue: '681100'
      },
      valeurAcquisition: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      valeurResiduelle: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        defaultValue: 0
      },
      valeurAmortissable: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      dateAcquisition: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      dateMiseEnService: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      dateFinAmortissement: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      methodeAmortissement: {
        type: Sequelize.ENUM(
          'LINEAIRE',
          'DEGRESSIF',
          'PROGRESSIF',
          'VARIABLE',
          'EXCEPTIONNEL'
        ),
        allowNull: false,
        defaultValue: 'LINEAIRE'
      },
      dureeAmortissement: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      tauxAmortissement: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false
      },
      coefficientDegressif: {
        type: Sequelize.DECIMAL(3, 2),
        allowNull: true
      },
      prorataTemporisPremierExercice: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      cumulAmortissements: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0
      },
      valeurNetteComptable: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false
      },
      dotationExerciceCourant: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0
      },
      statut: {
        type: Sequelize.ENUM(
          'EN_SERVICE',
          'HORS_SERVICE',
          'CEDE',
          'REFORME',
          'TOTALEMENT_AMORTI'
        ),
        allowNull: false,
        defaultValue: 'EN_SERVICE'
      },
      amortissementTermine: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      numeroSerie: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      localisation: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      fournisseur: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      numeroFacture: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      regimeFiscal: {
        type: Sequelize.ENUM(
          'NORMAL',
          'SIMPLIFIE',
          'EXONERE'
        ),
        allowNull: false,
        defaultValue: 'NORMAL'
      },
      deductibleTVA: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      actif: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });

    // Index pour les performances
    await queryInterface.addIndex('depreciations', ['societeId']);
    await queryInterface.addIndex('depreciations', ['code', 'societeId'], {
      unique: true,
      name: 'depreciations_code_societe_unique'
    });
    await queryInterface.addIndex('depreciations', ['categorie']);
    await queryInterface.addIndex('depreciations', ['statut']);
    await queryInterface.addIndex('depreciations', ['dateMiseEnService']);
    await queryInterface.addIndex('depreciations', ['compteImmobilisation']);
    await queryInterface.addIndex('depreciations', ['methodeAmortissement']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('depreciations');
  }
};