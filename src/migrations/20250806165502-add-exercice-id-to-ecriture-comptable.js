'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Ajouter la colonne exercice_id à la table ecriture_comptables
    await queryInterface.addColumn('ecriture_comptables', 'exercice_id', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'exercice_comptables',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // Ajouter un index pour améliorer les performances
    await queryInterface.addIndex('ecriture_comptables', {
      fields: ['exercice_id'],
      name: 'idx_ecriture_exercice_id'
    });
  },

  async down (queryInterface, Sequelize) {
    // Supprimer l'index
    await queryInterface.removeIndex('ecriture_comptables', 'idx_ecriture_exercice_id');

    // Supprimer la colonne
    await queryInterface.removeColumn('ecriture_comptables', 'exercice_id');
  }
};
