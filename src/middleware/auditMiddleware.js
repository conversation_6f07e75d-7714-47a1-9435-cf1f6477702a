'use strict';

const AuditService = require('../services/auditService');
const { logger } = require('../config/logger');

/**
 * Middleware pour l'audit automatique des opérations CRUD
 */
class AuditMiddleware {
  constructor(models) {
    this.models = models;
    this.auditService = new AuditService(models);
    
    // Tables à auditer automatiquement
    this.tablesAuditees = [
      'societes',
      'compte_comptables', 
      'journaux',
      'exercice_comptables',
      'parametre_comptables',
      'ecriture_comptables',
      'ligne_ecritures'
    ];
  }

  /**
   * Middleware pour auditer les créations
   */
  auditCreate(tableName) {
    return async (req, res, next) => {
      if (!this.tablesAuditees.includes(tableName)) {
        return next();
      }

      // Intercepter la réponse pour récupérer les données créées
      const originalSend = res.send;
      res.send = async function(data) {
        try {
          const responseData = typeof data === 'string' ? JSON.parse(data) : data;
          
          if (responseData.success && responseData.data) {
            await this.auditService.enregistrerAction({
              action: 'CREATE',
              table: tableName,
              recordId: responseData.data.id || responseData.data.code || responseData.data.numero,
              nouvellesValeurs: responseData.data,
              utilisateurId: req.user?.id,
              societeId: responseData.data.societeId || req.body.societeId,
              details: `Création via API ${req.method} ${req.originalUrl}`
            }, req);
          }
        } catch (error) {
          logger.error('Erreur audit CREATE', { error: error.message, tableName });
        }
        
        originalSend.call(this, data);
      }.bind(this);

      next();
    };
  }

  /**
   * Middleware pour auditer les modifications
   */
  auditUpdate(tableName) {
    return async (req, res, next) => {
      if (!this.tablesAuditees.includes(tableName)) {
        return next();
      }

      try {
        // Récupérer les anciennes valeurs avant modification
        const recordId = req.params.id || req.params.code || req.params.numero;
        let anciensValeurs = null;

        if (recordId) {
          const model = this.getModelByTableName(tableName);
          if (model) {
            const record = await model.findByPk(recordId);
            if (record) {
              anciensValeurs = record.toJSON();
            }
          }
        }

        // Stocker les anciennes valeurs dans la requête
        req.auditData = { anciensValeurs, recordId };

        // Intercepter la réponse
        const originalSend = res.send;
        res.send = async function(data) {
          try {
            const responseData = typeof data === 'string' ? JSON.parse(data) : data;
            
            if (responseData.success && responseData.data) {
              await this.auditService.enregistrerAction({
                action: 'UPDATE',
                table: tableName,
                recordId: req.auditData.recordId,
                anciensValeurs: req.auditData.anciensValeurs,
                nouvellesValeurs: responseData.data,
                utilisateurId: req.user?.id,
                societeId: responseData.data.societeId || req.auditData.anciensValeurs?.societeId,
                details: `Modification via API ${req.method} ${req.originalUrl}`
              }, req);
            }
          } catch (error) {
            logger.error('Erreur audit UPDATE', { error: error.message, tableName });
          }
          
          originalSend.call(this, data);
        }.bind(this);

      } catch (error) {
        logger.error('Erreur préparation audit UPDATE', { error: error.message, tableName });
      }

      next();
    };
  }

  /**
   * Middleware pour auditer les suppressions
   */
  auditDelete(tableName) {
    return async (req, res, next) => {
      if (!this.tablesAuditees.includes(tableName)) {
        return next();
      }

      try {
        // Récupérer les données avant suppression
        const recordId = req.params.id || req.params.code || req.params.numero;
        let anciensValeurs = null;

        if (recordId) {
          const model = this.getModelByTableName(tableName);
          if (model) {
            const record = await model.findByPk(recordId);
            if (record) {
              anciensValeurs = record.toJSON();
            }
          }
        }

        // Stocker les données dans la requête
        req.auditData = { anciensValeurs, recordId };

        // Intercepter la réponse
        const originalSend = res.send;
        res.send = async function(data) {
          try {
            const responseData = typeof data === 'string' ? JSON.parse(data) : data;
            
            if (responseData.success) {
              await this.auditService.enregistrerAction({
                action: 'DELETE',
                table: tableName,
                recordId: req.auditData.recordId,
                anciensValeurs: req.auditData.anciensValeurs,
                utilisateurId: req.user?.id,
                societeId: req.auditData.anciensValeurs?.societeId,
                details: `Suppression via API ${req.method} ${req.originalUrl}`
              }, req);
            }
          } catch (error) {
            logger.error('Erreur audit DELETE', { error: error.message, tableName });
          }
          
          originalSend.call(this, data);
        }.bind(this);

      } catch (error) {
        logger.error('Erreur préparation audit DELETE', { error: error.message, tableName });
      }

      next();
    };
  }

  /**
   * Middleware pour auditer les connexions/déconnexions
   */
  auditAuth() {
    return async (req, res, next) => {
      const originalSend = res.send;
      res.send = async function(data) {
        try {
          const responseData = typeof data === 'string' ? JSON.parse(data) : data;
          
          if (responseData.success) {
            let action = 'LOGIN';
            let details = 'Connexion utilisateur';
            
            if (req.originalUrl.includes('logout')) {
              action = 'LOGOUT';
              details = 'Déconnexion utilisateur';
            }

            await this.auditService.enregistrerAction({
              action,
              table: 'auth',
              utilisateurId: req.user?.id || responseData.data?.user?.id,
              details: `${details} via ${req.originalUrl}`
            }, req);
          }
        } catch (error) {
          logger.error('Erreur audit AUTH', { error: error.message });
        }
        
        originalSend.call(this, data);
      }.bind(this);

      next();
    };
  }

  /**
   * Middleware pour auditer les imports/exports
   */
  auditImportExport() {
    return async (req, res, next) => {
      const originalSend = res.send;
      res.send = async function(data) {
        try {
          const responseData = typeof data === 'string' ? JSON.parse(data) : data;
          
          if (responseData.success) {
            let action = 'IMPORT';
            let details = 'Import de données';
            
            if (req.originalUrl.includes('export')) {
              action = 'EXPORT';
              details = 'Export de données';
            }

            await this.auditService.enregistrerAction({
              action,
              table: 'import_export',
              utilisateurId: req.user?.id,
              societeId: req.params.societeId || req.query.societeId,
              details: `${details} - ${req.originalUrl}`
            }, req);
          }
        } catch (error) {
          logger.error('Erreur audit IMPORT/EXPORT', { error: error.message });
        }
        
        originalSend.call(this, data);
      }.bind(this);

      next();
    };
  }

  /**
   * Obtient le modèle Sequelize par nom de table
   */
  getModelByTableName(tableName) {
    const modelMap = {
      'societes': this.models.Societe,
      'compte_comptables': this.models.CompteComptable,
      'journaux': this.models.Journal,
      'exercice_comptables': this.models.ExerciceComptable,
      'parametre_comptables': this.models.ParametreComptable,
      'ecriture_comptables': this.models.EcritureComptable,
      'ligne_ecritures': this.models.LigneEcriture
    };

    return modelMap[tableName];
  }

  /**
   * Middleware générique pour audit automatique basé sur la route
   */
  autoAudit() {
    return (req, res, next) => {
      // Déterminer la table et l'action basées sur la route
      const path = req.route?.path || req.originalUrl;
      const method = req.method;

      // Mapping des routes vers les tables
      const routeTableMap = {
        '/societes': 'societes',
        '/comptes': 'compte_comptables',
        '/journaux': 'journaux',
        '/exercices': 'exercice_comptables',
        '/parametres': 'parametre_comptables'
      };

      let tableName = null;
      for (const [route, table] of Object.entries(routeTableMap)) {
        if (path.includes(route)) {
          tableName = table;
          break;
        }
      }

      if (!tableName) {
        return next();
      }

      // Appliquer le bon middleware selon la méthode HTTP
      switch (method) {
        case 'POST':
          return this.auditCreate(tableName)(req, res, next);
        case 'PUT':
        case 'PATCH':
          return this.auditUpdate(tableName)(req, res, next);
        case 'DELETE':
          return this.auditDelete(tableName)(req, res, next);
        default:
          return next();
      }
    };
  }
}

module.exports = AuditMiddleware;
