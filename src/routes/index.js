/**
 * Routes principales de l'API Comptabilité SYSCOHADA
 */

const express = require('express');
const router = express.Router();

// Import des routes spécialisées
const authRoutes = require('./auth');
const apiKeysRoutes = require('./apiKeys');
const societesRoutes = require('./societes');
const partiesRoutes = require('./parties');
const comptesRoutes = require('./comptes');
const journauxRoutes = require('./journaux');
const exercicesRoutes = require('./exercices');
const parametresRoutes = require('./parametres');
const planComptableRoutes = require('./planComptable');
const ecrituresRoutes = require('./ecritures');
const lettrageRoutes = require('./lettrage');
const templatesRoutes = require('./templates');
const importExportRoutes = require('./importExport');
const movementsRoutes = require('./movements');
const reportsRoutes = require('./reports');
const depreciationsRoutes = require('./depreciations');
const exercisesRoutes = require('./exercises');
const calculRoutes = require('./calculs');
const etatsRoutes = require('./etats');
const dashboardRoutes = require('./dashboard');
const analysesRoutes = require('./analyses');
const clotureRoutes = require('./cloture');

// Middleware pour toutes les routes API
router.use((req, res, next) => {
  // Ajouter des headers communs
  res.set({
    'X-API-Version': '1.0.0',
    'X-Powered-By': 'API Comptabilité SYSCOHADA'
  });
  next();
});

/**
 * Route de base de l'API
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'API Comptabilité SYSCOHADA',
    version: '1.0.0',
    endpoints: {
      auth: '/api/v1/auth',
      apiKeys: '/api/v1/api-keys',
      societes: '/api/v1/societes',
      parties: '/api/v1/parties',
      comptes: '/api/v1/comptes',
      journaux: '/api/v1/journaux',
      exercices: '/api/v1/exercices',
      parametres: '/api/v1/parametres',
      planComptable: '/api/v1/plan-comptable',
      ecritures: '/api/v1/ecritures',
      lettrage: '/api/v1/lettrage',
      templates: '/api/v1/templates',
      importExport: '/api/v1/import-export',
      movements: '/api/v1/movements',
      reports: '/api/v1/reports',
      depreciations: '/api/v1/depreciations',
      exercises: '/api/v1/exercises',
      calculs: '/api/v1/calculs',
      etats: '/api/v1/etats',
      dashboard: '/api/v1/dashboard',
      analyses: '/api/v1/analyses',
      cloture: '/api/v1/cloture'
    },
    documentation: '/api-docs'
  });
});

/**
 * Route de statut de l'API
 */
router.get('/status', (req, res) => {
  res.json({
    success: true,
    status: 'operational',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// Routes spécialisées
router.use('/auth', authRoutes);
router.use('/api-keys', apiKeysRoutes);
router.use('/societes', societesRoutes);
router.use('/', partiesRoutes); // Routes pour les tiers (parties)
router.use('/comptes', comptesRoutes);
router.use('/journaux', journauxRoutes);
router.use('/exercices', exercicesRoutes);
router.use('/parametres', parametresRoutes);
router.use('/plan-comptable', planComptableRoutes);
router.use('/ecritures', ecrituresRoutes);
router.use('/lettrage', lettrageRoutes);
router.use('/templates', templatesRoutes);
router.use('/import-export', importExportRoutes);
router.use('/movements', movementsRoutes);
router.use('/reports', reportsRoutes);
router.use('/depreciations', depreciationsRoutes);
router.use('/exercises', exercisesRoutes);
router.use('/calculs', calculRoutes);
router.use('/etats', etatsRoutes);
router.use('/dashboard', dashboardRoutes);
router.use('/analyses', analysesRoutes);
router.use('/cloture', clotureRoutes);

module.exports = router;
