'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');
const EntryValidationService = require('./entryValidationService');

/**
 * Service pour la génération automatique de mouvements comptables
 * Conforme aux normes SYSCOHADA - Phase 2
 */
class MovementService {
  constructor(models) {
    this.models = models;
    this.entryValidationService = new EntryValidationService(models);
  }

  /**
   * Génère une écriture de vente automatique
   * @param {Object} params - Paramètres de la vente
   * @returns {Promise<Object>} Écriture générée avec validation
   */
  async genererEcritureVente(params) {
    const {
      societeId,
      clientId,
      montantHT,
      tauxTva,
      dateVente,
      numeroFacture,
      libelle,
      compteVente,
      journalCode,
      utilisateurCreation
    } = params;

    try {
      // 1. Validation des entités
      await this.validateEntities(societeId, { clientId });

      // 2. Récupération du client
      const client = await this.models.Party.findByPk(clientId);
      if (!client || client.type === 'FOURNISSEUR') {
        throw new ValidationError('Client invalide ou inexistant');
      }

      // 3. Calculs TVA et TTC
      const montantTva = montantHT * (tauxTva / 100);
      const montantTTC = montantHT + montantTva;

      // 4. Génération du numéro d'écriture
      const numeroEcriture = await this.genererNumeroEcriture(journalCode, dateVente);

      // 5. Préparation des données d'écriture
      const donneesEcriture = {
        numeroEcriture,
        dateEcriture: dateVente,
        journalCode,
        libelle,
        reference: numeroFacture,
        societeId,
        typeOperation: 'VENTE',
        modeSaisie: 'AUTOMATIQUE',
        utilisateurCreation,
        totalDebit: montantTTC,
        totalCredit: montantTTC
      };

      // 6. Préparation des lignes
      const lignes = [
        {
          compteNumero: client.compteComptable || await this.getCompteClient(client),
          libelle: `${libelle} - ${client.nom}`,
          debit: montantTTC,
          credit: 0,
          tiersId: clientId,
          tiersNom: client.nom,
          numeroPiece: numeroFacture,
          typePiece: 'FACTURE'
        },
        {
          compteNumero: compteVente,
          libelle: `${libelle} - Vente HT`,
          debit: 0,
          credit: montantHT
        }
      ];

      // Ajouter ligne TVA si applicable
      if (montantTva > 0) {
        lignes.push({
          compteNumero: '445700', // TVA collectée
          libelle: `${libelle} - TVA ${tauxTva}%`,
          debit: 0,
          credit: montantTva
        });
      }

      // 7. Validation avant création
      const validation = await this.entryValidationService.validateEntry(
        donneesEcriture, 
        lignes, 
        societeId
      );

      if (!validation.valide) {
        throw new ValidationError('Écriture de vente invalide', validation.erreurs);
      }

      // 8. Création de l'écriture
      const ecriture = await this.creerEcritureAvecLignes(donneesEcriture, lignes);

      // 9. Calculs de contrôle
      const calculs = {
        montantHT,
        montantTva,
        montantTTC,
        tauxTva,
        nombreLignes: lignes.length
      };

      logger.info('Écriture de vente générée automatiquement', {
        ecritureId: ecriture.id,
        numeroFacture,
        montantTTC,
        clientId: client.id
      });

      return {
        ecriture,
        lignes: await ecriture.getLignes(),
        calculs,
        validation
      };

    } catch (error) {
      logger.error('Erreur génération écriture vente', {
        error: error.message,
        params
      });
      throw error;
    }
  }

  /**
   * Génère une écriture d'achat automatique
   * @param {Object} params - Paramètres de l'achat
   * @returns {Promise<Object>} Écriture générée avec validation
   */
  async genererEcritureAchat(params) {
    const {
      societeId,
      fournisseurId,
      montantHT,
      tauxTva,
      dateAchat,
      numeroFacture,
      libelle,
      compteAchat,
      journalCode,
      utilisateurCreation
    } = params;

    try {
      // 1. Validation des entités
      await this.validateEntities(societeId, { fournisseurId });

      // 2. Récupération du fournisseur
      const fournisseur = await this.models.Party.findByPk(fournisseurId);
      if (!fournisseur || fournisseur.type === 'CLIENT') {
        throw new ValidationError('Fournisseur invalide ou inexistant');
      }

      // 3. Calculs TVA et TTC
      const montantTva = montantHT * (tauxTva / 100);
      const montantTTC = montantHT + montantTva;

      // 4. Génération du numéro d'écriture
      const numeroEcriture = await this.genererNumeroEcriture(journalCode, dateAchat);

      // 5. Préparation des données d'écriture
      const donneesEcriture = {
        numeroEcriture,
        dateEcriture: dateAchat,
        journalCode,
        libelle,
        reference: numeroFacture,
        societeId,
        typeOperation: 'ACHAT',
        modeSaisie: 'AUTOMATIQUE',
        utilisateurCreation,
        totalDebit: montantTTC,
        totalCredit: montantTTC
      };

      // 6. Préparation des lignes
      const lignes = [
        {
          compteNumero: compteAchat,
          libelle: `${libelle} - Achat HT`,
          debit: montantHT,
          credit: 0
        },
        {
          compteNumero: fournisseur.compteComptable || await this.getCompteFournisseur(fournisseur),
          libelle: `${libelle} - ${fournisseur.nom}`,
          debit: 0,
          credit: montantTTC,
          tiersId: fournisseurId,
          tiersNom: fournisseur.nom,
          numeroPiece: numeroFacture,
          typePiece: 'FACTURE'
        }
      ];

      // Ajouter ligne TVA si applicable
      if (montantTva > 0) {
        lignes.push({
          compteNumero: '445200', // TVA déductible
          libelle: `${libelle} - TVA ${tauxTva}%`,
          debit: montantTva,
          credit: 0
        });
      }

      // 7. Validation avant création
      const validation = await this.entryValidationService.validateEntry(
        donneesEcriture, 
        lignes, 
        societeId
      );

      if (!validation.valide) {
        throw new ValidationError('Écriture d\'achat invalide', validation.erreurs);
      }

      // 8. Création de l'écriture
      const ecriture = await this.creerEcritureAvecLignes(donneesEcriture, lignes);

      // 9. Calculs de contrôle
      const calculs = {
        montantHT,
        montantTva,
        montantTTC,
        tauxTva,
        nombreLignes: lignes.length
      };

      logger.info('Écriture d\'achat générée automatiquement', {
        ecritureId: ecriture.id,
        numeroFacture,
        montantTTC,
        fournisseurId: fournisseur.id
      });

      return {
        ecriture,
        lignes: await ecriture.getLignes(),
        calculs,
        validation
      };

    } catch (error) {
      logger.error('Erreur génération écriture achat', {
        error: error.message,
        params
      });
      throw error;
    }
  }

  /**
   * Génère une écriture de règlement client
   * @param {Object} params - Paramètres du règlement
   * @returns {Promise<Object>} Écriture générée avec lettrage
   */
  async genererReglementClient(params) {
    const {
      societeId,
      clientId,
      montant,
      dateReglement,
      modeReglement,
      numeroReglement,
      libelle,
      compteTresorerie,
      journalCode,
      utilisateurCreation
    } = params;

    try {
      // 1. Validation des entités
      await this.validateEntities(societeId, { clientId });

      // 2. Récupération du client
      const client = await this.models.Party.findByPk(clientId);
      if (!client) {
        throw new ValidationError('Client inexistant');
      }

      // 3. Génération du numéro d'écriture
      const numeroEcriture = await this.genererNumeroEcriture(journalCode, dateReglement);

      // 4. Préparation des données d'écriture
      const donneesEcriture = {
        numeroEcriture,
        dateEcriture: dateReglement,
        journalCode,
        libelle,
        reference: numeroReglement,
        societeId,
        typeOperation: 'TRESORERIE',
        modeSaisie: 'AUTOMATIQUE',
        utilisateurCreation,
        totalDebit: montant,
        totalCredit: montant
      };

      // 5. Préparation des lignes
      const lignes = [
        {
          compteNumero: compteTresorerie,
          libelle: `${libelle} - Encaissement`,
          debit: montant,
          credit: 0,
          modeReglement,
          numeroPiece: numeroReglement,
          typePiece: this.getTypePieceFromMode(modeReglement)
        },
        {
          compteNumero: client.compteComptable || await this.getCompteClient(client),
          libelle: `${libelle} - ${client.nom}`,
          debit: 0,
          credit: montant,
          tiersId: clientId,
          tiersNom: client.nom,
          modeReglement,
          numeroPiece: numeroReglement
        }
      ];

      // 6. Validation avant création
      const validation = await this.entryValidationService.validateEntry(
        donneesEcriture, 
        lignes, 
        societeId
      );

      if (!validation.valide) {
        throw new ValidationError('Écriture de règlement invalide', validation.erreurs);
      }

      // 7. Création de l'écriture
      const ecriture = await this.creerEcritureAvecLignes(donneesEcriture, lignes);

      // 8. Tentative de lettrage automatique
      const lettrage = await this.tenterLettrageAutomatique(clientId, montant, ecriture.id);

      logger.info('Écriture de règlement client générée', {
        ecritureId: ecriture.id,
        montant,
        clientId,
        lettrage: lettrage.success
      });

      return {
        ecriture,
        lignes: await ecriture.getLignes(),
        lettrage,
        validation
      };

    } catch (error) {
      logger.error('Erreur génération règlement client', {
        error: error.message,
        params
      });
      throw error;
    }
  }

  /**
   * Génère une écriture de règlement fournisseur
   * @param {Object} params - Paramètres du règlement
   * @returns {Promise<Object>} Écriture générée avec lettrage
   */
  async genererReglementFournisseur(params) {
    const {
      societeId,
      fournisseurId,
      montant,
      dateReglement,
      modeReglement,
      numeroReglement,
      libelle,
      compteTresorerie,
      journalCode,
      utilisateurCreation
    } = params;

    try {
      // 1. Validation des entités
      await this.validateEntities(societeId, { fournisseurId });

      // 2. Récupération du fournisseur
      const fournisseur = await this.models.Party.findByPk(fournisseurId);
      if (!fournisseur) {
        throw new ValidationError('Fournisseur inexistant');
      }

      // 3. Génération du numéro d'écriture
      const numeroEcriture = await this.genererNumeroEcriture(journalCode, dateReglement);

      // 4. Préparation des données d'écriture
      const donneesEcriture = {
        numeroEcriture,
        dateEcriture: dateReglement,
        journalCode,
        libelle,
        reference: numeroReglement,
        societeId,
        typeOperation: 'TRESORERIE',
        modeSaisie: 'AUTOMATIQUE',
        utilisateurCreation,
        totalDebit: montant,
        totalCredit: montant
      };

      // 5. Préparation des lignes
      const lignes = [
        {
          compteNumero: fournisseur.compteComptable || await this.getCompteFournisseur(fournisseur),
          libelle: `${libelle} - ${fournisseur.nom}`,
          debit: montant,
          credit: 0,
          tiersId: fournisseurId,
          tiersNom: fournisseur.nom,
          modeReglement,
          numeroPiece: numeroReglement
        },
        {
          compteNumero: compteTresorerie,
          libelle: `${libelle} - Décaissement`,
          debit: 0,
          credit: montant,
          modeReglement,
          numeroPiece: numeroReglement,
          typePiece: this.getTypePieceFromMode(modeReglement)
        }
      ];

      // 6. Validation avant création
      const validation = await this.entryValidationService.validateEntry(
        donneesEcriture, 
        lignes, 
        societeId
      );

      if (!validation.valide) {
        throw new ValidationError('Écriture de règlement invalide', validation.erreurs);
      }

      // 7. Création de l'écriture
      const ecriture = await this.creerEcritureAvecLignes(donneesEcriture, lignes);

      // 8. Tentative de lettrage automatique
      const lettrage = await this.tenterLettrageAutomatique(fournisseurId, montant, ecriture.id);

      logger.info('Écriture de règlement fournisseur générée', {
        ecritureId: ecriture.id,
        montant,
        fournisseurId,
        lettrage: lettrage.success
      });

      return {
        ecriture,
        lignes: await ecriture.getLignes(),
        lettrage,
        validation
      };

    } catch (error) {
      logger.error('Erreur génération règlement fournisseur', {
        error: error.message,
        params
      });
      throw error;
    }
  }

  /**
   * Génère une écriture d'amortissement
   * @param {Object} params - Paramètres de l'amortissement
   * @returns {Promise<Object>} Écriture générée
   */
  async genererAmortissement(params) {
    const {
      societeId,
      immobilisationId,
      montantAmortissement,
      dateAmortissement,
      libelle,
      compteImmobilisation,
      compteAmortissement,
      compteDotation,
      journalCode,
      utilisateurCreation
    } = params;

    try {
      // 1. Validation de la société
      await this.validateEntities(societeId);

      // 2. Génération du numéro d'écriture
      const numeroEcriture = await this.genererNumeroEcriture(journalCode, dateAmortissement);

      // 3. Préparation des données d'écriture
      const donneesEcriture = {
        numeroEcriture,
        dateEcriture: dateAmortissement,
        journalCode,
        libelle,
        societeId,
        typeOperation: 'AMORTISSEMENT',
        modeSaisie: 'AUTOMATIQUE',
        utilisateurCreation,
        totalDebit: montantAmortissement,
        totalCredit: montantAmortissement
      };

      // 4. Préparation des lignes
      const lignes = [
        {
          compteNumero: compteDotation,
          libelle: `${libelle} - Dotation`,
          debit: montantAmortissement,
          credit: 0
        },
        {
          compteNumero: compteAmortissement,
          libelle: `${libelle} - Amortissement`,
          debit: 0,
          credit: montantAmortissement
        }
      ];

      // 5. Validation avant création
      const validation = await this.entryValidationService.validateEntry(
        donneesEcriture, 
        lignes, 
        societeId
      );

      if (!validation.valide) {
        throw new ValidationError('Écriture d\'amortissement invalide', validation.erreurs);
      }

      // 6. Création de l'écriture
      const ecriture = await this.creerEcritureAvecLignes(donneesEcriture, lignes);

      // 7. Calculs de contrôle
      const calculs = {
        montantAmortissement,
        compteImmobilisation,
        compteAmortissement,
        compteDotation
      };

      logger.info('Écriture d\'amortissement générée', {
        ecritureId: ecriture.id,
        montantAmortissement,
        compteImmobilisation
      });

      return {
        ecriture,
        lignes: await ecriture.getLignes(),
        calculs,
        validation
      };

    } catch (error) {
      logger.error('Erreur génération amortissement', {
        error: error.message,
        params
      });
      throw error;
    }
  }

  /**
   * Génère des écritures de paie
   * @param {Object} params - Paramètres de la paie
   * @returns {Promise<Object>} Écritures générées
   */
  async genererEcriturePaie(params) {
    const {
      societeId,
      periode,
      salaires,
      charges,
      dateVersement,
      libelle,
      journalCode,
      utilisateurCreation
    } = params;

    try {
      // 1. Validation de la société
      await this.validateEntities(societeId);

      const ecritures = [];
      const totaux = {
        salaires: 0,
        charges: 0,
        nets: 0
      };

      // 2. Génération des écritures pour chaque salarié
      for (const salarie of salaires) {
        const numeroEcriture = await this.genererNumeroEcriture(journalCode, dateVersement);
        
        const salaireNet = salarie.salaireBrut - (salarie.cotisationsSalariales || 0);
        totaux.salaires += salarie.salaireBrut;
        totaux.nets += salaireNet;

        const donneesEcriture = {
          numeroEcriture,
          dateEcriture: dateVersement,
          journalCode,
          libelle: `${libelle} - ${salarie.nom}`,
          societeId,
          typeOperation: 'SALAIRE',
          modeSaisie: 'AUTOMATIQUE',
          utilisateurCreation,
          totalDebit: salarie.salaireBrut,
          totalCredit: salarie.salaireBrut
        };

        const lignes = [
          {
            compteNumero: '661100', // Salaires
            libelle: `Salaire brut - ${salarie.nom}`,
            debit: salarie.salaireBrut,
            credit: 0
          },
          {
            compteNumero: '421100', // Personnel - Rémunérations dues
            libelle: `Salaire net - ${salarie.nom}`,
            debit: 0,
            credit: salaireNet
          }
        ];

        // Ajouter cotisations salariales si applicable
        if (salarie.cotisationsSalariales > 0) {
          lignes.push({
            compteNumero: '431100', // Sécurité sociale
            libelle: `Cotisations salariales - ${salarie.nom}`,
            debit: 0,
            credit: salarie.cotisationsSalariales
          });
        }

        const ecriture = await this.creerEcritureAvecLignes(donneesEcriture, lignes);
        ecritures.push(ecriture);
      }

      // 3. Écriture des charges patronales si applicable
      if (charges && Object.keys(charges).length > 0) {
        const numeroEcritureCharges = await this.genererNumeroEcriture(journalCode, dateVersement);
        const totalCharges = Object.values(charges).reduce((sum, val) => sum + val, 0);
        totaux.charges = totalCharges;

        const donneesEcritureCharges = {
          numeroEcriture: numeroEcritureCharges,
          dateEcriture: dateVersement,
          journalCode,
          libelle: `${libelle} - Charges patronales`,
          societeId,
          typeOperation: 'SALAIRE',
          modeSaisie: 'AUTOMATIQUE',
          utilisateurCreation,
          totalDebit: totalCharges,
          totalCredit: totalCharges
        };

        const lignesCharges = [
          {
            compteNumero: '664100', // Charges sociales
            libelle: 'Charges patronales',
            debit: totalCharges,
            credit: 0
          },
          {
            compteNumero: '431200', // Organismes sociaux
            libelle: 'Charges patronales à payer',
            debit: 0,
            credit: totalCharges
          }
        ];

        const ecritureCharges = await this.creerEcritureAvecLignes(donneesEcritureCharges, lignesCharges);
        ecritures.push(ecritureCharges);
      }

      const ventilation = {
        nombreSalaries: salaires.length,
        nombreEcritures: ecritures.length,
        periode
      };

      logger.info('Écritures de paie générées', {
        nombreEcritures: ecritures.length,
        totalSalaires: totaux.salaires,
        periode
      });

      return {
        ecritures,
        totaux,
        ventilation,
        validation: { valide: true, message: 'Écritures de paie générées avec succès' }
      };

    } catch (error) {
      logger.error('Erreur génération écritures paie', {
        error: error.message,
        params
      });
      throw error;
    }
  }

  // === MÉTHODES UTILITAIRES ===

  /**
   * Valide l'existence des entités
   */
  async validateEntities(societeId, entities = {}) {
    const societe = await this.models.Societe.findByPk(societeId);
    if (!societe) {
      throw new NotFoundError('Société inexistante');
    }

    for (const [key, id] of Object.entries(entities)) {
      if (id) {
        const entity = await this.models.Party.findByPk(id);
        if (!entity) {
          throw new NotFoundError(`${key} inexistant`);
        }
      }
    }
  }

  /**
   * Génère un numéro d'écriture automatique
   */
  async genererNumeroEcriture(journalCode, date) {
    const annee = date.getFullYear();
    const mois = String(date.getMonth() + 1).padStart(2, '0');
    
    const dernierNumero = await this.models.EcritureComptable.findOne({
      where: {
        journalCode,
        numeroEcriture: {
          [this.models.Sequelize.Op.like]: `${journalCode}${annee}${mois}%`
        }
      },
      order: [['numeroEcriture', 'DESC']]
    });

    let sequence = 1;
    if (dernierNumero) {
      const match = dernierNumero.numeroEcriture.match(/(\d+)$/);
      if (match) {
        sequence = parseInt(match[1]) + 1;
      }
    }

    return `${journalCode}${annee}${mois}${String(sequence).padStart(4, '0')}`;
  }

  /**
   * Crée une écriture avec ses lignes
   */
  async creerEcritureAvecLignes(donneesEcriture, lignes) {
    const transaction = await this.models.sequelize.transaction();

    try {
      const ecriture = await this.models.EcritureComptable.create(donneesEcriture, { transaction });

      for (const ligne of lignes) {
        await this.models.LigneEcriture.create({
          ...ligne,
          ecritureId: ecriture.id
        }, { transaction });
      }

      await transaction.commit();
      return ecriture;

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Obtient le compte comptable d'un client
   */
  async getCompteClient(client) {
    if (client.compteComptable) return client.compteComptable;
    
    // Génération automatique du compte client
    const baseCompte = '411';
    const sequence = await this.getNextAccountSequence(baseCompte);
    const compteGenere = `${baseCompte}${String(sequence).padStart(3, '0')}`;
    
    // Mise à jour du client
    await client.update({ compteComptable: compteGenere });
    
    return compteGenere;
  }

  /**
   * Obtient le compte comptable d'un fournisseur
   */
  async getCompteFournisseur(fournisseur) {
    if (fournisseur.compteComptable) return fournisseur.compteComptable;
    
    // Génération automatique du compte fournisseur
    const baseCompte = '401';
    const sequence = await this.getNextAccountSequence(baseCompte);
    const compteGenere = `${baseCompte}${String(sequence).padStart(3, '0')}`;
    
    // Mise à jour du fournisseur
    await fournisseur.update({ compteComptable: compteGenere });
    
    return compteGenere;
  }

  /**
   * Obtient la prochaine séquence pour un compte
   */
  async getNextAccountSequence(baseCompte) {
    const dernierCompte = await this.models.CompteComptable.findOne({
      where: {
        numero: {
          [this.models.Sequelize.Op.like]: `${baseCompte}%`
        }
      },
      order: [['numero', 'DESC']]
    });

    if (!dernierCompte) return 1;

    const match = dernierCompte.numero.match(/(\d+)$/);
    return match ? parseInt(match[1]) + 1 : 1;
  }

  /**
   * Convertit le mode de règlement en type de pièce
   */
  getTypePieceFromMode(modeReglement) {
    const mapping = {
      'ESPECES': 'ESPECES',
      'CHEQUE': 'CHEQUE',
      'VIREMENT': 'VIREMENT',
      'CARTE_BANCAIRE': 'AUTRE',
      'TRAITE': 'AUTRE',
      'BILLET_ORDRE': 'AUTRE'
    };
    return mapping[modeReglement] || 'AUTRE';
  }

  /**
   * Tente un lettrage automatique
   */
  async tenterLettrageAutomatique(tiersId, montant, ecritureId) {
    try {
      // Recherche des lignes non lettrées pour ce tiers
      const lignesNonLettrees = await this.models.LigneEcriture.findAll({
        where: {
          tiersId,
          lettrage: null,
          [this.models.Sequelize.Op.or]: [
            { debit: montant },
            { credit: montant }
          ]
        },
        include: [{
          model: this.models.EcritureComptable,
          as: 'ecriture',
          where: {
            statut: 'VALIDEE'
          }
        }]
      });

      if (lignesNonLettrees.length > 0) {
        const lettrage = `L${Date.now()}`;
        
        // Application du lettrage
        for (const ligne of lignesNonLettrees) {
          await ligne.update({
            lettrage,
            dateLettrage: new Date()
          });
        }

        return {
          success: true,
          lettrage,
          lignesLettrees: lignesNonLettrees.length
        };
      }

      return {
        success: false,
        message: 'Aucune ligne correspondante trouvée pour le lettrage'
      };

    } catch (error) {
      logger.error('Erreur lettrage automatique', { error: error.message, tiersId, montant });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Obtient les templates d'écritures
   */
  async getTemplatesEcritures(societeId) {
    return await this.models.TemplateEcriture.findAll({
      where: { societeId },
      include: [{
        model: this.models.TemplateLigneEcriture,
        as: 'lignes'
      }],
      order: [['nom', 'ASC']]
    });
  }

  /**
   * Valide un mouvement avant génération
   */
  async validateMovement(type, donnees) {
    const validations = {
      'VENTE': this.validateVenteData,
      'ACHAT': this.validateAchatData,
      'REGLEMENT_CLIENT': this.validateReglementData,
      'REGLEMENT_FOURNISSEUR': this.validateReglementData,
      'AMORTISSEMENT': this.validateAmortissementData,
      'PAIE': this.validatePaieData
    };

    const validator = validations[type];
    if (!validator) {
      return {
        valide: false,
        erreurs: [`Type de mouvement ${type} non supporté`]
      };
    }

    return validator.call(this, donnees);
  }

  /**
   * Validation des données de vente
   */
  validateVenteData(donnees) {
    const erreurs = [];
    
    if (!donnees.montantHT || donnees.montantHT <= 0) {
      erreurs.push('Montant HT obligatoire et positif');
    }
    
    if (!donnees.clientId) {
      erreurs.push('Client obligatoire');
    }
    
    if (!donnees.dateVente) {
      erreurs.push('Date de vente obligatoire');
    }

    return {
      valide: erreurs.length === 0,
      erreurs
    };
  }

  /**
   * Validation des données d'achat
   */
  validateAchatData(donnees) {
    const erreurs = [];
    
    if (!donnees.montantHT || donnees.montantHT <= 0) {
      erreurs.push('Montant HT obligatoire et positif');
    }
    
    if (!donnees.fournisseurId) {
      erreurs.push('Fournisseur obligatoire');
    }
    
    if (!donnees.dateAchat) {
      erreurs.push('Date d\'achat obligatoire');
    }

    return {
      valide: erreurs.length === 0,
      erreurs
    };
  }

  /**
   * Validation des données de règlement
   */
  validateReglementData(donnees) {
    const erreurs = [];
    
    if (!donnees.montant || donnees.montant <= 0) {
      erreurs.push('Montant obligatoire et positif');
    }
    
    if (!donnees.dateReglement) {
      erreurs.push('Date de règlement obligatoire');
    }

    return {
      valide: erreurs.length === 0,
      erreurs
    };
  }

  /**
   * Validation des données d'amortissement
   */
  validateAmortissementData(donnees) {
    const erreurs = [];
    
    if (!donnees.montantAmortissement || donnees.montantAmortissement <= 0) {
      erreurs.push('Montant d\'amortissement obligatoire et positif');
    }
    
    if (!donnees.compteImmobilisation) {
      erreurs.push('Compte d\'immobilisation obligatoire');
    }
    
    if (!donnees.compteAmortissement) {
      erreurs.push('Compte d\'amortissement obligatoire');
    }

    return {
      valide: erreurs.length === 0,
      erreurs
    };
  }

  /**
   * Validation des données de paie
   */
  validatePaieData(donnees) {
    const erreurs = [];
    
    if (!donnees.salaires || !Array.isArray(donnees.salaires) || donnees.salaires.length === 0) {
      erreurs.push('Liste des salaires obligatoire et non vide');
    }
    
    if (!donnees.periode) {
      erreurs.push('Période obligatoire');
    }

    return {
      valide: erreurs.length === 0,
      erreurs
    };
  }
}

module.exports = MovementService;