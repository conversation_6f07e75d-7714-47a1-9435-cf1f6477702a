'use strict';

/**
 * Utilitaires de calcul pour les tableaux de bord financiers
 * Conforme aux normes SYSCOHADA
 */

/**
 * Calcule le chiffre d'affaires à partir des classes de comptes
 * @param {Object} classeComptes - Données des classes de comptes
 * @returns {number} Chiffre d'affaires
 */
function calculerChiffreAffaires(classeComptes) {
  // Classe 7 = Produits, principalement les comptes 70
  const classe7 = classeComptes[7] || { totalDebit: 0, totalCredit: 0 };
  return classe7.totalCredit - classe7.totalDebit;
}

/**
 * Calcule le résultat net à partir des classes de comptes
 * @param {Object} classeComptes - Données des classes de comptes
 * @returns {number} Résultat net
 */
function calculerResultatNet(classeComptes) {
  // Produits (classe 7) - Charges (classe 6)
  const produits = classeComptes[7] || { totalDebit: 0, totalCredit: 0 };
  const charges = classeComptes[6] || { totalDebit: 0, totalCredit: 0 };
  
  const totalProduits = produits.totalCredit - produits.totalDebit;
  const totalCharges = charges.totalDebit - charges.totalCredit;
  
  return totalProduits - totalCharges;
}

/**
 * Calcule la marge commerciale à partir des classes de comptes
 * @param {Object} classeComptes - Données des classes de comptes
 * @returns {number} Marge commerciale
 */
function calculerMargeCommerciale(classeComptes) {
  // Ventes de marchandises (comptes 70) - Achats de marchandises (comptes 60)
  const produits = classeComptes[7] || { totalDebit: 0, totalCredit: 0 };
  const charges = classeComptes[6] || { totalDebit: 0, totalCredit: 0 };
  
  // Approximation: on considère que les ventes sont dans la classe 7 et les achats dans la classe 6
  // Dans une implémentation réelle, il faudrait filtrer plus précisément sur les comptes 70 et 60
  const ventes = produits.totalCredit - produits.totalDebit;
  const achats = charges.totalDebit - charges.totalCredit;
  
  return ventes - achats;
}

/**
 * Calcule la trésorerie à partir des classes de comptes
 * @param {Object} classeComptes - Données des classes de comptes
 * @returns {number} Trésorerie
 */
function calculerTresorerie(classeComptes) {
  // Classe 5 = Comptes de trésorerie
  const classe5 = classeComptes[5] || { totalDebit: 0, totalCredit: 0 };
  return classe5.totalDebit - classe5.totalCredit;
}

/**
 * Calcule le total de l'actif à partir des classes de comptes
 * @param {Object} classeComptes - Données des classes de comptes
 * @returns {number} Total actif
 */
function calculerTotalActif(classeComptes) {
  // Actif = Classes 2 (immobilisations) + 3 (stocks) + 4 (tiers débiteurs) + 5 (trésorerie)
  let totalActif = 0;
  
  // Classe 2 - Immobilisations
  const classe2 = classeComptes[2] || { totalDebit: 0, totalCredit: 0 };
  totalActif += classe2.totalDebit - classe2.totalCredit;
  
  // Classe 3 - Stocks
  const classe3 = classeComptes[3] || { totalDebit: 0, totalCredit: 0 };
  totalActif += classe3.totalDebit - classe3.totalCredit;
  
  // Classe 4 - Tiers (seulement les comptes débiteurs)
  const classe4 = classeComptes[4] || { totalDebit: 0, totalCredit: 0 };
  // Approximation: on prend le solde débiteur net
  const soldeClasse4 = classe4.totalDebit - classe4.totalCredit;
  if (soldeClasse4 > 0) {
    totalActif += soldeClasse4;
  }
  
  // Classe 5 - Trésorerie
  const classe5 = classeComptes[5] || { totalDebit: 0, totalCredit: 0 };
  totalActif += classe5.totalDebit - classe5.totalCredit;
  
  return totalActif;
}

/**
 * Calcule le total du passif à partir des classes de comptes
 * @param {Object} classeComptes - Données des classes de comptes
 * @returns {number} Total passif
 */
function calculerTotalPassif(classeComptes) {
  // Passif = Classes 1 (capitaux) + 4 (tiers créditeurs)
  let totalPassif = 0;
  
  // Classe 1 - Capitaux
  const classe1 = classeComptes[1] || { totalDebit: 0, totalCredit: 0 };
  totalPassif += classe1.totalCredit - classe1.totalDebit;
  
  // Classe 4 - Tiers (seulement les comptes créditeurs)
  const classe4 = classeComptes[4] || { totalDebit: 0, totalCredit: 0 };
  // Approximation: on prend le solde créditeur net
  const soldeClasse4 = classe4.totalCredit - classe4.totalDebit;
  if (soldeClasse4 > 0) {
    totalPassif += soldeClasse4;
  }
  
  return totalPassif;
}

/**
 * Calcule le total des charges à partir des classes de comptes
 * @param {Object} classeComptes - Données des classes de comptes
 * @returns {number} Total charges
 */
function calculerTotalCharges(classeComptes) {
  // Classe 6 = Charges
  const classe6 = classeComptes[6] || { totalDebit: 0, totalCredit: 0 };
  return classe6.totalDebit - classe6.totalCredit;
}

/**
 * Calcule le total des produits à partir des classes de comptes
 * @param {Object} classeComptes - Données des classes de comptes
 * @returns {number} Total produits
 */
function calculerTotalProduits(classeComptes) {
  // Classe 7 = Produits
  const classe7 = classeComptes[7] || { totalDebit: 0, totalCredit: 0 };
  return classe7.totalCredit - classe7.totalDebit;
}

/**
 * Calcule le ratio de liquidité à partir des classes de comptes
 * @param {Object} classeComptes - Données des classes de comptes
 * @returns {number} Ratio de liquidité
 */
function calculerRatioLiquidite(classeComptes) {
  // Actif circulant / Passif circulant
  // Actif circulant = Classe 3 (stocks) + Classe 4 (tiers débiteurs) + Classe 5 (trésorerie)
  // Passif circulant = Classe 4 (tiers créditeurs)
  
  let actifCirculant = 0;
  
  // Classe 3 - Stocks
  const classe3 = classeComptes[3] || { totalDebit: 0, totalCredit: 0 };
  actifCirculant += classe3.totalDebit - classe3.totalCredit;
  
  // Classe 4 - Tiers débiteurs
  const classe4 = classeComptes[4] || { totalDebit: 0, totalCredit: 0 };
  // Approximation: on prend le solde débiteur net
  const soldeDebiteurClasse4 = classe4.totalDebit - classe4.totalCredit;
  if (soldeDebiteurClasse4 > 0) {
    actifCirculant += soldeDebiteurClasse4;
  }
  
  // Classe 5 - Trésorerie
  const classe5 = classeComptes[5] || { totalDebit: 0, totalCredit: 0 };
  actifCirculant += classe5.totalDebit - classe5.totalCredit;
  
  // Passif circulant = Classe 4 (tiers créditeurs)
  // Approximation: on prend le solde créditeur net
  const soldeCrediteurClasse4 = classe4.totalCredit - classe4.totalDebit;
  const passifCirculant = soldeCrediteurClasse4 > 0 ? soldeCrediteurClasse4 : 0;
  
  return passifCirculant !== 0 ? actifCirculant / passifCirculant : 0;
}

/**
 * Calcule le ratio d'endettement à partir des classes de comptes
 * @param {Object} classeComptes - Données des classes de comptes
 * @returns {number} Ratio d'endettement
 */
function calculerRatioEndettement(classeComptes) {
  // Dettes totales / Capitaux propres
  
  // Dettes totales = Classe 4 (tiers créditeurs)
  const classe4 = classeComptes[4] || { totalDebit: 0, totalCredit: 0 };
  const soldeCrediteurClasse4 = classe4.totalCredit - classe4.totalDebit;
  const dettesTotales = soldeCrediteurClasse4 > 0 ? soldeCrediteurClasse4 : 0;
  
  // Capitaux propres = Classe 1
  const classe1 = classeComptes[1] || { totalDebit: 0, totalCredit: 0 };
  const capitauxPropres = classe1.totalCredit - classe1.totalDebit;
  
  return capitauxPropres !== 0 ? dettesTotales / capitauxPropres : 0;
}

/**
 * Calcule le ratio d'autonomie financière à partir des classes de comptes
 * @param {Object} classeComptes - Données des classes de comptes
 * @returns {number} Ratio d'autonomie financière
 */
function calculerRatioAutonomieFinanciere(classeComptes) {
  // Capitaux propres / Total passif
  
  // Capitaux propres = Classe 1
  const classe1 = classeComptes[1] || { totalDebit: 0, totalCredit: 0 };
  const capitauxPropres = classe1.totalCredit - classe1.totalDebit;
  
  // Total passif
  const totalPassif = calculerTotalPassif(classeComptes);
  
  return totalPassif !== 0 ? capitauxPropres / totalPassif : 0;
}

module.exports = {
  calculerChiffreAffaires,
  calculerResultatNet,
  calculerMargeCommerciale,
  calculerTresorerie,
  calculerTotalActif,
  calculerTotalPassif,
  calculerTotalCharges,
  calculerTotalProduits,
  calculerRatioLiquidite,
  calculerRatioEndettement,
  calculerRatioAutonomieFinanciere
};