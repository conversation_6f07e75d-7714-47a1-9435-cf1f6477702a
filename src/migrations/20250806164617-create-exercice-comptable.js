'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('exercice_comptables', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      societe_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'societes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      libelle: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      date_debut: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      date_fin: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      statut: {
        type: Sequelize.ENUM('OUVERT', 'CLOTURE', 'ARCHIVE'),
        allowNull: false,
        defaultValue: 'OUVERT'
      },
      exercice_precedent_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'exercice_comptables',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      report_a_nouveau: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0
      },
      date_ouverture: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      date_cloture: {
        type: Sequelize.DATE,
        allowNull: true
      },
      utilisateur_cloture: {
        type: Sequelize.UUID,
        allowNull: true,
        comment: 'ID de l\'utilisateur qui a clôturé l\'exercice'
      },
      commentaire_cloture: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Index pour les recherches par société et dates
    await queryInterface.addIndex('exercice_comptables', {
      fields: ['societe_id', 'date_debut', 'date_fin'],
      name: 'idx_exercice_societe_dates'
    });

    // Index pour les recherches par société et statut
    await queryInterface.addIndex('exercice_comptables', {
      fields: ['societe_id', 'statut'],
      name: 'idx_exercice_societe_statut'
    });

    // Index unique pour éviter les doublons de libellé par société
    await queryInterface.addIndex('exercice_comptables', {
      fields: ['societe_id', 'libelle'],
      unique: true,
      name: 'idx_exercice_societe_libelle_unique'
    });
  },

  async down (queryInterface, Sequelize) {
    // Supprimer les index
    await queryInterface.removeIndex('exercice_comptables', 'idx_exercice_societe_dates');
    await queryInterface.removeIndex('exercice_comptables', 'idx_exercice_societe_statut');
    await queryInterface.removeIndex('exercice_comptables', 'idx_exercice_societe_libelle_unique');

    // Supprimer la table
    await queryInterface.dropTable('exercice_comptables');
  }
};
