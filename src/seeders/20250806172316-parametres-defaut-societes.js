'use strict';

const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Récupérer toutes les sociétés existantes
    const societes = await queryInterface.sequelize.query(
      'SELECT id FROM societes',
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (societes.length === 0) {
      console.log('Aucune société trouvée');
      return;
    }

    // Paramètres par défaut
    const parametresDefaut = {
      // Paramètres TVA
      'tva_taux_normal': { valeur: '18', type: 'NUMBER', categorie: 'TVA', description: 'Taux de TVA normal (%)' },
      'tva_taux_reduit': { valeur: '9', type: 'NUMBER', categorie: 'TVA', description: 'Taux de TVA réduit (%)' },
      'tva_compte_collectee': { valeur: '4431', type: 'STRING', categorie: 'TVA', description: 'Compte TVA collectée' },
      'tva_compte_deductible': { valeur: '4455', type: 'STRING', categorie: 'TVA', description: 'Compte TVA déductible' },

      // Paramètres fiscaux
      'is_taux': { valeur: '25', type: 'NUMBER', categorie: 'FISCAL', description: 'Taux d\'impôt sur les sociétés (%)' },
      'taxe_professionnelle_taux': { valeur: '2', type: 'NUMBER', categorie: 'FISCAL', description: 'Taux taxe professionnelle (%)' },
      'seuil_ca_tva': { valeur: '50000000', type: 'NUMBER', categorie: 'FISCAL', description: 'Seuil CA pour assujettissement TVA' },

      // Paramètres comptables
      'compte_resultat_benefice': { valeur: '130', type: 'STRING', categorie: 'COMPTES', description: 'Compte résultat bénéficiaire' },
      'compte_resultat_perte': { valeur: '139', type: 'STRING', categorie: 'COMPTES', description: 'Compte résultat déficitaire' },
      'compte_report_nouveau': { valeur: '110', type: 'STRING', categorie: 'COMPTES', description: 'Compte report à nouveau' },

      // Paramètres de reporting
      'devise_presentation': { valeur: 'XOF', type: 'STRING', categorie: 'REPORTING', description: 'Devise de présentation des états' },
      'arrondi_montants': { valeur: 'true', type: 'BOOLEAN', categorie: 'REPORTING', description: 'Arrondir les montants' },
      'decimales_montants': { valeur: '2', type: 'NUMBER', categorie: 'REPORTING', description: 'Nombre de décimales pour les montants' },

      // Paramètres système
      'exercice_duree_max': { valeur: '366', type: 'NUMBER', categorie: 'SYSTEME', description: 'Durée maximale d\'un exercice (jours)' },
      'cloture_auto_ecritures': { valeur: 'true', type: 'BOOLEAN', categorie: 'SYSTEME', description: 'Validation automatique des écritures à la clôture' },
      'lettrage_obligatoire': { valeur: '["411", "401", "421"]', type: 'JSON', categorie: 'SYSTEME', description: 'Comptes à lettrage obligatoire' }
    };

    const maintenant = new Date();
    const parametresACreer = [];

    // Créer les paramètres pour chaque société
    for (const societe of societes) {
      for (const [cle, config] of Object.entries(parametresDefaut)) {
        parametresACreer.push({
          id: uuidv4(),
          societe_id: societe.id,
          cle,
          valeur: config.valeur,
          type: config.type,
          categorie: config.categorie,
          description: config.description,
          created_at: maintenant,
          updated_at: maintenant
        });
      }
    }

    // Insérer tous les paramètres
    await queryInterface.bulkInsert('parametre_comptables', parametresACreer);

    console.log(`✅ Paramètres par défaut créés pour ${societes.length} société(s)`);
    console.log(`📊 Total: ${parametresACreer.length} paramètres créés`);
  },

  async down (queryInterface, Sequelize) {
    // Supprimer tous les paramètres par défaut
    const parametresDefaut = [
      'tva_taux_normal', 'tva_taux_reduit', 'tva_compte_collectee', 'tva_compte_deductible',
      'is_taux', 'taxe_professionnelle_taux', 'seuil_ca_tva',
      'compte_resultat_benefice', 'compte_resultat_perte', 'compte_report_nouveau',
      'devise_presentation', 'arrondi_montants', 'decimales_montants',
      'exercice_duree_max', 'cloture_auto_ecritures', 'lettrage_obligatoire'
    ];

    await queryInterface.bulkDelete('parametre_comptables', {
      cle: {
        [Sequelize.Op.in]: parametresDefaut
      }
    });

    console.log('✅ Paramètres par défaut supprimés');
  }
};
