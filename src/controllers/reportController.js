'use strict';

const { logger } = require('../config/logger');
const { 
  AppError, 
  ValidationError, 
  NotFoundError 
} = require('../middleware/errorHandler');
const AccountingCalculationService = require('../services/accountingCalculationService');
const ReportGenerationService = require('../services/reportGenerationService');

/**
 * Controller pour la génération des états financiers SYSCOHADA
 * Conforme aux normes SYSCOHADA - Phase 2
 */
class ReportController {
  constructor() {
    this.calculationService = null;
    this.reportService = null;
  }

  /**
   * Initialise les services avec les modèles
   * @param {Object} models - Modèles Sequelize
   */
  init(models) {
    this.calculationService = new AccountingCalculationService(models);
    this.reportService = new ReportGenerationService(models);
  }

  /**
   * Génère la balance générale
   * GET /api/v1/reports/balance-generale
   */
  async genererBalanceGenerale(req, res, next) {
    try {
      const {
        societeId,
        dateDebut,
        dateFin,
        niveauDetail = 'COMPTE',
        includeNonMouvementes = false,
        format = 'JSON' // JSON, PDF, EXCEL
      } = req.query;

      // Validation des paramètres obligatoires
      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'ID société obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      // Calcul de la balance
      const balance = await this.calculationService.calculerBalanceGenerale(societeId, {
        dateDebut: dateDebut ? new Date(dateDebut) : null,
        dateFin: dateFin ? new Date(dateFin) : null,
        niveauDetail,
        includeNonMouvementes: includeNonMouvementes === 'true'
      });

      // Génération selon le format demandé
      let resultat;
      switch (format.toUpperCase()) {
        case 'PDF':
          resultat = await this.reportService.genererBalancePDF(balance);
          res.setHeader('Content-Type', 'application/pdf');
          res.setHeader('Content-Disposition', `attachment; filename="balance-generale-${societeId}.pdf"`);
          return res.send(resultat);

        case 'EXCEL':
          resultat = await this.reportService.genererBalanceExcel(balance);
          res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          res.setHeader('Content-Disposition', `attachment; filename="balance-generale-${societeId}.xlsx"`);
          return res.send(resultat);

        default:
          resultat = balance;
      }

      logger.info('Balance générale générée', {
        societeId,
        format,
        nombreComptes: balance.balance.comptes.length,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Balance générale générée avec succès',
        data: resultat
      });

    } catch (error) {
      logger.error('Erreur génération balance générale', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère le grand livre d'un compte
   * GET /api/v1/reports/grand-livre/:compteNumero
   */
  async genererGrandLivre(req, res, next) {
    try {
      const { compteNumero } = req.params;
      const {
        societeId,
        dateDebut,
        dateFin,
        includeReports = true,
        groupeParMois = false,
        format = 'JSON'
      } = req.query;

      // Validation des paramètres obligatoires
      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'ID société obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      // Calcul du grand livre
      const grandLivre = await this.calculationService.calculerGrandLivre(compteNumero, societeId, {
        dateDebut: dateDebut ? new Date(dateDebut) : null,
        dateFin: dateFin ? new Date(dateFin) : null,
        includeReports: includeReports === 'true',
        groupeParMois: groupeParMois === 'true'
      });

      // Génération selon le format demandé
      let resultat;
      switch (format.toUpperCase()) {
        case 'PDF':
          resultat = await this.reportService.genererGrandLivrePDF(grandLivre);
          res.setHeader('Content-Type', 'application/pdf');
          res.setHeader('Content-Disposition', `attachment; filename="grand-livre-${compteNumero}.pdf"`);
          return res.send(resultat);

        case 'EXCEL':
          resultat = await this.reportService.genererGrandLivreExcel(grandLivre);
          res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          res.setHeader('Content-Disposition', `attachment; filename="grand-livre-${compteNumero}.xlsx"`);
          return res.send(resultat);

        default:
          resultat = grandLivre;
      }

      logger.info('Grand livre généré', {
        compteNumero,
        societeId,
        format,
        nombreMouvements: grandLivre.metadata.nombreMouvements,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Grand livre généré avec succès',
        data: resultat
      });

    } catch (error) {
      logger.error('Erreur génération grand livre', {
        error: error.message,
        params: req.params,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère l'état des soldes tiers
   * GET /api/v1/reports/soldes-tiers
   */
  async genererSoldesTiers(req, res, next) {
    try {
      const {
        societeId,
        type, // CLIENT, FOURNISSEUR
        dateDebut,
        dateFin,
        seulementEchus = false,
        includeDetails = false,
        format = 'JSON'
      } = req.query;

      // Validation des paramètres obligatoires
      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'ID société obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      // Calcul des soldes tiers
      const soldesTiers = await this.calculationService.calculerSoldesTiers(societeId, {
        type,
        dateDebut: dateDebut ? new Date(dateDebut) : null,
        dateFin: dateFin ? new Date(dateFin) : null,
        seulementEchus: seulementEchus === 'true',
        includeDetails: includeDetails === 'true'
      });

      // Génération selon le format demandé
      let resultat;
      switch (format.toUpperCase()) {
        case 'PDF':
          resultat = await this.reportService.genererSoldesTiersPDF(soldesTiers);
          res.setHeader('Content-Type', 'application/pdf');
          res.setHeader('Content-Disposition', `attachment; filename="soldes-tiers-${type || 'tous'}.pdf"`);
          return res.send(resultat);

        case 'EXCEL':
          resultat = await this.reportService.genererSoldesTiersExcel(soldesTiers);
          res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          res.setHeader('Content-Disposition', `attachment; filename="soldes-tiers-${type || 'tous'}.xlsx"`);
          return res.send(resultat);

        default:
          resultat = soldesTiers;
      }

      logger.info('Soldes tiers générés', {
        societeId,
        type,
        format,
        nombreTiers: soldesTiers.metadata.nombreTiers,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Soldes tiers générés avec succès',
        data: resultat
      });

    } catch (error) {
      logger.error('Erreur génération soldes tiers', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère le bilan SYSCOHADA
   * GET /api/v1/reports/bilan
   */
  async genererBilan(req, res, next) {
    try {
      const {
        societeId,
        exerciceId,
        dateArrete,
        format = 'JSON',
        modele = 'NORMAL' // NORMAL, SIMPLIFIE
      } = req.query;

      // Validation des paramètres obligatoires
      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'ID société obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      // Génération du bilan
      const bilan = await this.reportService.genererBilanSYSCOHADA(societeId, {
        exerciceId,
        dateArrete: dateArrete ? new Date(dateArrete) : null,
        modele
      });

      // Génération selon le format demandé
      let resultat;
      switch (format.toUpperCase()) {
        case 'PDF':
          resultat = await this.reportService.genererBilanPDF(bilan);
          res.setHeader('Content-Type', 'application/pdf');
          res.setHeader('Content-Disposition', `attachment; filename="bilan-${societeId}.pdf"`);
          return res.send(resultat);

        case 'EXCEL':
          resultat = await this.reportService.genererBilanExcel(bilan);
          res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          res.setHeader('Content-Disposition', `attachment; filename="bilan-${societeId}.xlsx"`);
          return res.send(resultat);

        default:
          resultat = bilan;
      }

      logger.info('Bilan SYSCOHADA généré', {
        societeId,
        exerciceId,
        format,
        modele,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Bilan SYSCOHADA généré avec succès',
        data: resultat
      });

    } catch (error) {
      logger.error('Erreur génération bilan', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère le compte de résultat SYSCOHADA
   * GET /api/v1/reports/compte-resultat
   */
  async genererCompteResultat(req, res, next) {
    try {
      const {
        societeId,
        exerciceId,
        dateDebut,
        dateFin,
        format = 'JSON',
        modele = 'NORMAL' // NORMAL, SIMPLIFIE
      } = req.query;

      // Validation des paramètres obligatoires
      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'ID société obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      // Génération du compte de résultat
      const compteResultat = await this.reportService.genererCompteResultatSYSCOHADA(societeId, {
        exerciceId,
        dateDebut: dateDebut ? new Date(dateDebut) : null,
        dateFin: dateFin ? new Date(dateFin) : null,
        modele
      });

      // Génération selon le format demandé
      let resultat;
      switch (format.toUpperCase()) {
        case 'PDF':
          resultat = await this.reportService.genererCompteResultatPDF(compteResultat);
          res.setHeader('Content-Type', 'application/pdf');
          res.setHeader('Content-Disposition', `attachment; filename="compte-resultat-${societeId}.pdf"`);
          return res.send(resultat);

        case 'EXCEL':
          resultat = await this.reportService.genererCompteResultatExcel(compteResultat);
          res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          res.setHeader('Content-Disposition', `attachment; filename="compte-resultat-${societeId}.xlsx"`);
          return res.send(resultat);

        default:
          resultat = compteResultat;
      }

      logger.info('Compte de résultat SYSCOHADA généré', {
        societeId,
        exerciceId,
        format,
        modele,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Compte de résultat SYSCOHADA généré avec succès',
        data: resultat
      });

    } catch (error) {
      logger.error('Erreur génération compte de résultat', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère les ratios financiers
   * GET /api/v1/reports/ratios-financiers
   */
  async genererRatiosFinanciers(req, res, next) {
    try {
      const {
        societeId,
        dateDebut,
        dateFin,
        exercicePrecedent = false,
        format = 'JSON'
      } = req.query;

      // Validation des paramètres obligatoires
      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'ID société obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      // Calcul des ratios financiers
      const ratios = await this.calculationService.calculerRatiosFinanciers(societeId, {
        dateDebut: dateDebut ? new Date(dateDebut) : null,
        dateFin: dateFin ? new Date(dateFin) : null,
        exercicePrecedent: exercicePrecedent === 'true'
      });

      // Génération selon le format demandé
      let resultat;
      switch (format.toUpperCase()) {
        case 'PDF':
          resultat = await this.reportService.genererRatiosPDF(ratios);
          res.setHeader('Content-Type', 'application/pdf');
          res.setHeader('Content-Disposition', `attachment; filename="ratios-financiers-${societeId}.pdf"`);
          return res.send(resultat);

        case 'EXCEL':
          resultat = await this.reportService.genererRatiosExcel(ratios);
          res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          res.setHeader('Content-Disposition', `attachment; filename="ratios-financiers-${societeId}.xlsx"`);
          return res.send(resultat);

        default:
          resultat = ratios;
      }

      logger.info('Ratios financiers générés', {
        societeId,
        format,
        exercicePrecedent,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Ratios financiers générés avec succès',
        data: resultat
      });

    } catch (error) {
      logger.error('Erreur génération ratios financiers', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère la situation de trésorerie
   * GET /api/v1/reports/situation-tresorerie
   */
  async genererSituationTresorerie(req, res, next) {
    try {
      const {
        societeId,
        dateDebut,
        dateFin,
        previsionJours = 30,
        includePrevisionnels = true,
        format = 'JSON'
      } = req.query;

      // Validation des paramètres obligatoires
      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'ID société obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      // Calcul de la situation de trésorerie
      const situationTresorerie = await this.calculationService.calculerSituationTresorerie(societeId, {
        dateDebut: dateDebut ? new Date(dateDebut) : null,
        dateFin: dateFin ? new Date(dateFin) : null,
        previsionJours: parseInt(previsionJours),
        includePrevisionnels: includePrevisionnels === 'true'
      });

      // Génération selon le format demandé
      let resultat;
      switch (format.toUpperCase()) {
        case 'PDF':
          resultat = await this.reportService.genererTresoreriePDF(situationTresorerie);
          res.setHeader('Content-Type', 'application/pdf');
          res.setHeader('Content-Disposition', `attachment; filename="situation-tresorerie-${societeId}.pdf"`);
          return res.send(resultat);

        case 'EXCEL':
          resultat = await this.reportService.genererTresorerieExcel(situationTresorerie);
          res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          res.setHeader('Content-Disposition', `attachment; filename="situation-tresorerie-${societeId}.xlsx"`);
          return res.send(resultat);

        default:
          resultat = situationTresorerie;
      }

      logger.info('Situation trésorerie générée', {
        societeId,
        format,
        previsionJours,
        tresorerieActuelle: situationTresorerie.tresorerieActuelle.total,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Situation de trésorerie générée avec succès',
        data: resultat
      });

    } catch (error) {
      logger.error('Erreur génération situation trésorerie', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Génère un journal comptable
   * GET /api/v1/reports/journal/:journalCode
   */
  async genererJournal(req, res, next) {
    try {
      const { journalCode } = req.params;
      const {
        societeId,
        dateDebut,
        dateFin,
        format = 'JSON'
      } = req.query;

      // Validation des paramètres obligatoires
      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'ID société obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      // Génération du journal
      const journal = await this.reportService.genererJournalComptable(journalCode, societeId, {
        dateDebut: dateDebut ? new Date(dateDebut) : null,
        dateFin: dateFin ? new Date(dateFin) : null
      });

      // Génération selon le format demandé
      let resultat;
      switch (format.toUpperCase()) {
        case 'PDF':
          resultat = await this.reportService.genererJournalPDF(journal);
          res.setHeader('Content-Type', 'application/pdf');
          res.setHeader('Content-Disposition', `attachment; filename="journal-${journalCode}.pdf"`);
          return res.send(resultat);

        case 'EXCEL':
          resultat = await this.reportService.genererJournalExcel(journal);
          res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          res.setHeader('Content-Disposition', `attachment; filename="journal-${journalCode}.xlsx"`);
          return res.send(resultat);

        default:
          resultat = journal;
      }

      logger.info('Journal comptable généré', {
        journalCode,
        societeId,
        format,
        nombreEcritures: journal.ecritures.length,
        utilisateur: req.user?.id
      });

      res.json({
        success: true,
        message: 'Journal comptable généré avec succès',
        data: resultat
      });

    } catch (error) {
      logger.error('Erreur génération journal', {
        error: error.message,
        params: req.params,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Liste les rapports disponibles
   * GET /api/v1/reports/available
   */
  async getReportsDisponibles(req, res, next) {
    try {
      const { societeId } = req.query;

      if (!societeId) {
        return res.status(400).json({
          success: false,
          message: 'ID société obligatoire',
          code: 'SOCIETE_ID_MANQUANT'
        });
      }

      const rapportsDisponibles = await this.reportService.getReportsDisponibles(societeId);

      res.json({
        success: true,
        message: 'Rapports disponibles récupérés avec succès',
        data: rapportsDisponibles
      });

    } catch (error) {
      logger.error('Erreur récupération rapports disponibles', {
        error: error.message,
        query: req.query,
        utilisateur: req.user?.id
      });
      next(error);
    }
  }
}

module.exports = new ReportController();