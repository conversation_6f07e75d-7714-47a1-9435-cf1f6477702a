/**
 * Routes pour la gestion des comptes comptables
 * API Comptabilité SYSCOHADA
 */

const express = require('express');
const router = express.Router();

const {
  getAllComptes,
  getCompteByNumero,
  createCompte,
  updateCompte,
  deleteCompte,
  getComptesByClasse,
  getHierarchieComptes
} = require('../controllers/compteController');

const { validate, schemas } = require('../middleware/validation');

/**
 * @route   GET /api/v1/comptes
 * @desc    Récupérer tous les comptes avec filtres
 * @access  Public
 */
router.get('/', getAllComptes);

/**
 * @route   GET /api/v1/comptes/hierarchie
 * @desc    Récupérer la hiérarchie des comptes
 * @access  Public
 */
router.get('/hierarchie', getHierarchieComptes);

/**
 * @route   GET /api/v1/comptes/classe/:classe
 * @desc    Récupérer les comptes par classe
 * @access  Public
 */
router.get('/classe/:classe', getComptesByClasse);

/**
 * @route   GET /api/v1/comptes/:numero
 * @desc    Récupérer un compte par numéro
 * @access  Public
 */
router.get('/:numero', getCompteByNumero);

/**
 * @route   POST /api/v1/comptes
 * @desc    Créer un nouveau compte comptable
 * @access  Public
 */
router.post('/',
  validate(schemas.compte.create),
  createCompte
);

/**
 * @route   PUT /api/v1/comptes/:numero
 * @desc    Mettre à jour un compte comptable
 * @access  Public
 */
router.put('/:numero',
  validate(schemas.societe.update), // Utiliser un schéma de mise à jour
  updateCompte
);

/**
 * @route   DELETE /api/v1/comptes/:numero
 * @desc    Supprimer un compte comptable
 * @access  Public
 */
router.delete('/:numero', deleteCompte);

module.exports = router;
