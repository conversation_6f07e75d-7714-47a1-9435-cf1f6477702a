'use strict';

const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Récupérer une société existante pour les exercices de test
    const societes = await queryInterface.sequelize.query(
      'SELECT id FROM societes LIMIT 1',
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (societes.length === 0) {
      console.log('Aucune société trouvée, création d\'une société de test...');

      const societeId = uuidv4();
      await queryInterface.bulkInsert('societes', [{
        id: societeId,
        nom: 'Société Test SYSCOHADA',
        adresse: '123 Avenue de la Comptabilité, Abidjan',
        telephone: '+225 01 02 03 04 05',
        email: '<EMAIL>',
        numero_contribuable: 'CI-TEST-2024-001',
        forme_juridique: 'SARL',
        capital: 1000000.00,
        exercice_debut: '2024-01-01',
        exercice_fin: '2024-12-31',
        devise: 'XOF',
        created_at: new Date(),
        updated_at: new Date()
      }]);

      societes.push({ id: societeId });
    }

    const societeId = societes[0].id;

    // Créer des exercices de test
    const exercices = [
      {
        id: uuidv4(),
        societe_id: societeId,
        libelle: 'Exercice 2023',
        date_debut: '2023-01-01',
        date_fin: '2023-12-31',
        statut: 'CLOTURE',
        exercice_precedent_id: null,
        report_a_nouveau: 0,
        date_ouverture: '2023-01-01T00:00:00Z',
        date_cloture: '2024-01-15T10:30:00Z',
        utilisateur_cloture: uuidv4(),
        commentaire_cloture: 'Clôture automatique de l\'exercice 2023',
        created_at: new Date('2023-01-01'),
        updated_at: new Date('2024-01-15')
      },
      {
        id: uuidv4(),
        societe_id: societeId,
        libelle: 'Exercice 2024',
        date_debut: '2024-01-01',
        date_fin: '2024-12-31',
        statut: 'OUVERT',
        exercice_precedent_id: null, // Sera mis à jour après insertion
        report_a_nouveau: 125000.50,
        date_ouverture: '2024-01-01T00:00:00Z',
        date_cloture: null,
        utilisateur_cloture: null,
        commentaire_cloture: null,
        created_at: new Date('2024-01-01'),
        updated_at: new Date('2024-01-01')
      },
      {
        id: uuidv4(),
        societe_id: societeId,
        libelle: 'Exercice 2025',
        date_debut: '2025-01-01',
        date_fin: '2025-12-31',
        statut: 'OUVERT',
        exercice_precedent_id: null, // Sera mis à jour après insertion
        report_a_nouveau: 0,
        date_ouverture: '2025-01-01T00:00:00Z',
        date_cloture: null,
        utilisateur_cloture: null,
        commentaire_cloture: null,
        created_at: new Date('2025-01-01'),
        updated_at: new Date('2025-01-01')
      }
    ];

    // Insérer les exercices
    await queryInterface.bulkInsert('exercice_comptables', exercices);

    // Mettre à jour les références d'exercices précédents
    await queryInterface.sequelize.query(`
      UPDATE exercice_comptables
      SET exercice_precedent_id = (
        SELECT id FROM exercice_comptables e2
        WHERE e2.societe_id = exercice_comptables.societe_id
        AND e2.libelle = 'Exercice 2023'
      )
      WHERE libelle = 'Exercice 2024'
    `);

    await queryInterface.sequelize.query(`
      UPDATE exercice_comptables
      SET exercice_precedent_id = (
        SELECT id FROM exercice_comptables e2
        WHERE e2.societe_id = exercice_comptables.societe_id
        AND e2.libelle = 'Exercice 2024'
      )
      WHERE libelle = 'Exercice 2025'
    `);

    console.log('✅ Exercices de test créés avec succès');
  },

  async down (queryInterface, Sequelize) {
    // Supprimer tous les exercices de test
    await queryInterface.bulkDelete('exercice_comptables', {
      libelle: {
        [Sequelize.Op.in]: ['Exercice 2023', 'Exercice 2024', 'Exercice 2025']
      }
    });

    // Optionnel : supprimer la société de test si elle a été créée
    await queryInterface.bulkDelete('societes', {
      nom: 'Société Test SYSCOHADA'
    });

    console.log('✅ Exercices de test supprimés');
  }
};
