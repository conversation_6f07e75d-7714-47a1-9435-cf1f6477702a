#!/usr/bin/env node

/**
 * CLI pour l'API Comptabilité SYSCOHADA
 * 
 * Cet outil permet d'interagir avec l'API de comptabilité via la ligne de commande
 */

const axios = require('axios');
const { program } = require('commander');
const inquirer = require('inquirer');
const chalk = require('chalk');
const Table = require('cli-table3');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const ora = require('ora');

// Chargement des variables d'environnement
dotenv.config();

// Configuration de base
const CONFIG_DIR = path.join(process.env.HOME || process.env.USERPROFILE, '.syscohada-cli');
const CONFIG_FILE = path.join(CONFIG_DIR, 'config.json');
const DEFAULT_API_URL = process.env.API_URL || 'http://localhost:3000/api/v1';

// Création du dossier de configuration s'il n'existe pas
if (!fs.existsSync(CONFIG_DIR)) {
  fs.mkdirSync(CONFIG_DIR, { recursive: true });
}

// Chargement de la configuration
let config = {
  apiUrl: DEFAULT_API_URL,
  apiKey: null,
  currentSociete: null
};

if (fs.existsSync(CONFIG_FILE)) {
  try {
    config = { ...config, ...JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8')) };
  } catch (error) {
    console.error('Erreur lors du chargement de la configuration:', error.message);
  }
}

// Sauvegarde de la configuration
const saveConfig = () => {
  try {
    fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
  } catch (error) {
    console.error('Erreur lors de la sauvegarde de la configuration:', error.message);
  }
};

// Configuration d'Axios
const api = axios.create({
  baseURL: config.apiUrl,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Ajout de la clé API à chaque requête si disponible
api.interceptors.request.use(
  (config) => {
    if (global.apiKey) {
      config.headers.Authorization = `Bearer ${global.apiKey}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Variable globale pour stocker la clé API
global.apiKey = config.apiKey;

// Middleware pour vérifier l'authentification
const requireAuth = (callback) => {
  return (...args) => {
    if (!global.apiKey) {
      console.error(chalk.red('Vous devez configurer une clé API pour utiliser cette commande.'));
      console.log(chalk.yellow('Utilisez la commande "auth:setup" pour configurer votre clé API.'));
      return;
    }
    callback(...args);
  };
};

// Initialisation du programme
program
  .name('syscohada-cli')
  .description('CLI pour interagir avec l\'API Comptabilité SYSCOHADA')
  .version('1.0.0');

// Commande de configuration
program
  .command('config')
  .description('Configurer l\'URL de l\'API')
  .action(async () => {
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'apiUrl',
        message: 'URL de l\'API:',
        default: config.apiUrl
      }
    ]);

    config.apiUrl = answers.apiUrl;
    api.defaults.baseURL = config.apiUrl;
    saveConfig();
    console.log(chalk.green(`URL de l'API configurée: ${config.apiUrl}`));
  });

// Commande de configuration de la clé API
program
  .command('auth:setup')
  .description('Configurer la clé API')
  .action(async () => {
    const answers = await inquirer.prompt([
      {
        type: 'password',
        name: 'apiKey',
        message: 'Clé API:',
        mask: '*',
        validate: (input) => {
          if (!input) return 'La clé API est requise';
          if (!input.startsWith('sk_')) return 'La clé API doit commencer par "sk_"';
          return true;
        }
      }
    ]);

    const spinner = ora('Vérification de la clé API...').start();
    
    try {
      // Tester la clé API
      const testApi = axios.create({
        baseURL: config.apiUrl,
        timeout: 10000,
        headers: {
          'Authorization': `Bearer ${answers.apiKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      const response = await testApi.get('/auth/verify');
      
      global.apiKey = answers.apiKey;
      config.apiKey = global.apiKey;
      saveConfig();
      
      spinner.succeed('Clé API configurée avec succès');
      
      // Afficher les informations de la clé
      const keyInfo = response.data.data.apiKey;
      console.log(chalk.green('\n✅ Informations de la clé API:'));
      console.log(`   Nom: ${keyInfo.name}`);
      console.log(`   Préfixe: ${keyInfo.prefix}`);
      console.log(`   Permissions: ${keyInfo.permissions.join(', ')}`);
      
    } catch (error) {
      spinner.fail('Clé API invalide');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.error || 'Clé API invalide'}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  });

// Commande pour vérifier la clé API
program
  .command('auth:verify')
  .description('Vérifier la validité de la clé API')
  .action(requireAuth(async () => {
    const spinner = ora('Vérification de la clé API...').start();
    
    try {
      const response = await api.get('/auth/verify');
      spinner.succeed('Clé API valide');
      
      const keyInfo = response.data.data.apiKey;
      const table = new Table({
        head: [chalk.cyan('Propriété'), chalk.cyan('Valeur')]
      });
      
      table.push(
        ['ID', keyInfo.id],
        ['Nom', keyInfo.name],
        ['Préfixe', keyInfo.prefix],
        ['Permissions', keyInfo.permissions.join(', ')]
      );
      
      console.log(table.toString());
    } catch (error) {
      spinner.fail('Clé API invalide');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.error || 'Clé API invalide'}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// Commande pour supprimer la clé API
program
  .command('auth:clear')
  .description('Supprimer la clé API configurée')
  .action(() => {
    global.apiKey = null;
    config.apiKey = null;
    saveConfig();
    console.log(chalk.green('Clé API supprimée de la configuration'));
  });

// Commande pour vérifier le statut de l'API
program
  .command('status')
  .description('Vérifier le statut de l\'API')
  .action(async () => {
    const spinner = ora('Vérification du statut de l\'API...').start();
    
    try {
      const response = await api.get('/');
      spinner.succeed('API opérationnelle');
      
      const table = new Table({
        head: [chalk.cyan('Propriété'), chalk.cyan('Valeur')]
      });
      
      table.push(
        ['Message', response.data.message],
        ['Version', response.data.version],
        ['Environnement', response.data.environment || 'N/A']
      );
      
      console.log(table.toString());
    } catch (error) {
      spinner.fail('API inaccessible');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  });

// === GESTION DES CLÉS API (Admin uniquement) ===

// Commande pour lister les clés API
program
  .command('apikeys:list')
  .description('Lister toutes les clés API (admin uniquement)')
  .option('-i, --include-inactive', 'Inclure les clés inactives')
  .option('-l, --limit <number>', 'Nombre de clés à afficher', '20')
  .action(requireAuth(async (options) => {
    const spinner = ora('Récupération des clés API...').start();
    
    try {
      const params = new URLSearchParams();
      if (options.includeInactive) params.append('includeInactive', 'true');
      if (options.limit) params.append('limit', options.limit);
      
      const response = await api.get(`/api-keys?${params.toString()}`);
      spinner.succeed(`${response.data.data.length} clés API trouvées`);
      
      if (response.data.data.length === 0) {
        console.log(chalk.yellow('Aucune clé API trouvée'));
        return;
      }
      
      const table = new Table({
        head: [
          chalk.cyan('Préfixe'), 
          chalk.cyan('Nom'), 
          chalk.cyan('Permissions'), 
          chalk.cyan('Statut'),
          chalk.cyan('Dernière utilisation'),
          chalk.cyan('Expire le')
        ]
      });
      
      response.data.data.forEach(key => {
        const status = key.isActive ? chalk.green('Active') : chalk.red('Inactive');
        const lastUsed = key.lastUsedAt ? 
          new Date(key.lastUsedAt).toLocaleDateString() : 
          chalk.gray('Jamais');
        const expires = key.expiresAt ? 
          new Date(key.expiresAt).toLocaleDateString() : 
          chalk.gray('Jamais');
        
        table.push([
          key.prefix,
          key.name,
          key.permissions.join(', '),
          status,
          lastUsed,
          expires
        ]);
      });
      
      console.log(table.toString());
      
      if (response.data.pagination) {
        console.log(chalk.gray(`\nPage ${response.data.pagination.page}/${response.data.pagination.totalPages} - Total: ${response.data.pagination.total}`));
      }
      
    } catch (error) {
      spinner.fail('Échec de la récupération des clés API');
      if (error.response && error.response.status === 403) {
        console.error(chalk.red('Erreur: Permission admin requise pour cette commande'));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// Commande pour créer une clé API
program
  .command('apikeys:create')
  .description('Créer une nouvelle clé API (admin uniquement)')
  .action(requireAuth(async () => {
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'name',
        message: 'Nom de la clé API:',
        validate: (input) => input.length >= 3 ? true : 'Le nom doit contenir au moins 3 caractères'
      },
      {
        type: 'checkbox',
        name: 'permissions',
        message: 'Permissions:',
        choices: [
          { name: 'Lecture (read)', value: 'read', checked: true },
          { name: 'Écriture (write)', value: 'write' },
          { name: 'Administration (admin)', value: 'admin' }
        ],
        validate: (input) => input.length > 0 ? true : 'Au moins une permission doit être sélectionnée'
      },
      {
        type: 'input',
        name: 'expiresAt',
        message: 'Date d\'expiration (YYYY-MM-DD, optionnel):',
        validate: (input) => {
          if (!input) return true;
          const date = new Date(input);
          return !isNaN(date.getTime()) && date > new Date() ? true : 'Date invalide ou dans le passé';
        }
      },
      {
        type: 'input',
        name: 'description',
        message: 'Description (optionnel):'
      }
    ]);

    const spinner = ora('Création de la clé API...').start();
    
    try {
      const payload = {
        name: answers.name,
        permissions: answers.permissions,
        metadata: {}
      };
      
      if (answers.expiresAt) {
        payload.expiresAt = new Date(answers.expiresAt).toISOString();
      }
      
      if (answers.description) {
        payload.metadata.description = answers.description;
      }
      
      const response = await api.post('/api-keys', payload);
      spinner.succeed('Clé API créée avec succès');
      
      console.log(chalk.green('\n✅ Nouvelle clé API créée:'));
      console.log(`   ID: ${response.data.data.id}`);
      console.log(`   Nom: ${response.data.data.name}`);
      console.log(`   Préfixe: ${response.data.data.prefix}`);
      console.log(`   Permissions: ${response.data.data.permissions.join(', ')}`);
      console.log(`   Expire le: ${response.data.data.expiresAt ? new Date(response.data.data.expiresAt).toLocaleDateString() : 'Jamais'}`);
      console.log('');
      console.log(chalk.yellow('🔐 Clé API (à conserver précieusement):'));
      console.log(chalk.bold(`   ${response.data.data.key}`));
      console.log('');
      console.log(chalk.red('⚠️  IMPORTANT: Cette clé ne sera plus jamais affichée!'));
      console.log('   Copiez-la et conservez-la en lieu sûr.');
      
    } catch (error) {
      spinner.fail('Échec de la création de la clé API');
      if (error.response && error.response.status === 403) {
        console.error(chalk.red('Erreur: Permission admin requise pour cette commande'));
      } else if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// Commande pour désactiver une clé API
program
  .command('apikeys:deactivate <prefix>')
  .description('Désactiver une clé API par son préfixe (admin uniquement)')
  .action(requireAuth(async (prefix) => {
    const spinner = ora('Recherche de la clé API...').start();
    
    try {
      // D'abord, récupérer la liste des clés pour trouver l'ID
      const listResponse = await api.get('/api-keys?includeInactive=true');
      const targetKey = listResponse.data.data.find(key => key.prefix === prefix);
      
      if (!targetKey) {
        spinner.fail(`Clé API avec le préfixe "${prefix}" non trouvée`);
        return;
      }
      
      spinner.text = 'Désactivation de la clé API...';
      
      const response = await api.put(`/api-keys/${targetKey.id}`, {
        isActive: false
      });
      
      spinner.succeed('Clé API désactivée avec succès');
      console.log(chalk.green(`Clé "${response.data.data.name}" (${response.data.data.prefix}) désactivée`));
      
    } catch (error) {
      spinner.fail('Échec de la désactivation de la clé API');
      if (error.response && error.response.status === 403) {
        console.error(chalk.red('Erreur: Permission admin requise pour cette commande'));
      } else if (error.response && error.response.status === 404) {
        console.error(chalk.red(`Erreur: Clé API avec le préfixe "${prefix}" non trouvée`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// Commande pour supprimer une clé API
program
  .command('apikeys:delete <prefix>')
  .description('Supprimer définitivement une clé API par son préfixe (admin uniquement)')
  .action(requireAuth(async (prefix) => {
    const spinner = ora('Recherche de la clé API...').start();
    
    try {
      // D'abord, récupérer la liste des clés pour trouver l'ID
      const listResponse = await api.get('/api-keys?includeInactive=true');
      const targetKey = listResponse.data.data.find(key => key.prefix === prefix);
      
      if (!targetKey) {
        spinner.fail(`Clé API avec le préfixe "${prefix}" non trouvée`);
        return;
      }
      
      spinner.stop();
      
      // Confirmation
      const { confirm } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirm',
          message: `Êtes-vous sûr de vouloir supprimer définitivement la clé "${targetKey.name}" (${targetKey.prefix}) ?`,
          default: false
        }
      ]);
      
      if (!confirm) {
        console.log(chalk.yellow('Suppression annulée'));
        return;
      }
      
      const deleteSpinner = ora('Suppression de la clé API...').start();
      
      await api.delete(`/api-keys/${targetKey.id}`);
      
      deleteSpinner.succeed('Clé API supprimée avec succès');
      console.log(chalk.green(`Clé "${targetKey.name}" (${targetKey.prefix}) supprimée définitivement`));
      
    } catch (error) {
      spinner.fail('Échec de la suppression de la clé API');
      if (error.response && error.response.status === 403) {
        console.error(chalk.red('Erreur: Permission admin requise pour cette commande'));
      } else if (error.response && error.response.status === 404) {
        console.error(chalk.red(`Erreur: Clé API avec le préfixe "${prefix}" non trouvée`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === SOCIÉTÉS ===

// Commande pour lister les sociétés
program
  .command('societes:list')
  .description('Lister toutes les sociétés')
  .action(requireAuth(async () => {
    const spinner = ora('Récupération des sociétés...').start();
    
    try {
      const response = await api.get('/societes');
      spinner.succeed(`${response.data.data.length} sociétés trouvées`);
      
      if (response.data.data.length === 0) {
        console.log(chalk.yellow('Aucune société trouvée'));
        return;
      }
      
      const table = new Table({
        head: [
          chalk.cyan('ID'), 
          chalk.cyan('Nom'), 
          chalk.cyan('RCCM'), 
          chalk.cyan('Pays'),
          chalk.cyan('Devise')
        ]
      });
      
      response.data.data.forEach(societe => {
        table.push([
          societe.id.substring(0, 8) + '...',
          societe.nom,
          societe.rccm || 'N/A',
          societe.pays,
          societe.deviseCode || 'XOF'
        ]);
      });
      
      console.log(table.toString());
    } catch (error) {
      spinner.fail('Échec de la récupération des sociétés');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour créer une société
program
  .command('societes:create')
  .description('Créer une nouvelle société')
  .action(requireAuth(async () => {
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'nom',
        message: 'Nom de la société:',
        validate: (input) => input.length >= 3 ? true : 'Le nom doit contenir au moins 3 caractères'
      },
      {
        type: 'input',
        name: 'rccm',
        message: 'Numéro RCCM (optionnel):',
      },
      {
        type: 'input',
        name: 'nif',
        message: 'Numéro d\'identification fiscale (optionnel):',
      },
      {
        type: 'input',
        name: 'adresse',
        message: 'Adresse:',
      },
      {
        type: 'input',
        name: 'pays',
        message: 'Pays:',
        default: 'Sénégal'
      },
      {
        type: 'input',
        name: 'telephone',
        message: 'Téléphone (optionnel):',
      },
      {
        type: 'input',
        name: 'email',
        message: 'Email (optionnel):',
      }
    ]);

    const spinner = ora('Création de la société...').start();
    
    try {
      const response = await api.post('/societes', answers);
      spinner.succeed('Société créée avec succès');
      console.log(chalk.green(`ID: ${response.data.data.id}`));
      console.log(chalk.green(`Nom: ${response.data.data.nom}`));
    } catch (error) {
      spinner.fail('Échec de la création de la société');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// Commande pour sélectionner une société courante
program
  .command('societes:select')
  .description('Sélectionner une société comme contexte courant')
  .action(requireAuth(async () => {
    const spinner = ora('Récupération des sociétés...').start();
    
    try {
      const response = await api.get('/societes');
      spinner.succeed('Sociétés récupérées');
      
      if (response.data.data.length === 0) {
        console.log(chalk.yellow('Aucune société trouvée'));
        return;
      }
      
      const choices = response.data.data.map(societe => ({
        name: `${societe.nom} (${societe.pays})`,
        value: societe.id,
        short: societe.nom
      }));
      
      const { societeId } = await inquirer.prompt([
        {
          type: 'list',
          name: 'societeId',
          message: 'Sélectionnez une société:',
          choices
        }
      ]);
      
      const selectedSociete = response.data.data.find(s => s.id === societeId);
      config.currentSociete = {
        id: selectedSociete.id,
        nom: selectedSociete.nom
      };
      saveConfig();
      
      console.log(chalk.green(`Société courante: ${selectedSociete.nom}`));
    } catch (error) {
      spinner.fail('Échec de la récupération des sociétés');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// === EXERCICES COMPTABLES ===

// Commande pour lister les exercices comptables
program
  .command('exercices:list')
  .description('Lister les exercices comptables de la société courante')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const spinner = ora('Récupération des exercices...').start();
    
    try {
      const response = await api.get(`/exercices?societeId=${config.currentSociete.id}`);
      spinner.succeed(`${response.data.data.length} exercices trouvés`);
      
      if (response.data.data.length === 0) {
        console.log(chalk.yellow('Aucun exercice trouvé'));
        return;
      }
      
      const table = new Table({
        head: [
          chalk.cyan('ID'), 
          chalk.cyan('Année'), 
          chalk.cyan('Date début'), 
          chalk.cyan('Date fin'),
          chalk.cyan('Statut')
        ]
      });
      
      response.data.data.forEach(exercice => {
        let statutColor;
        switch (exercice.statut) {
          case 'OUVERT':
            statutColor = chalk.green;
            break;
          case 'CLOTURE':
            statutColor = chalk.red;
            break;
          default:
            statutColor = chalk.yellow;
        }
        
        table.push([
          exercice.id.substring(0, 8) + '...',
          exercice.annee,
          exercice.dateDebut,
          exercice.dateFin,
          statutColor(exercice.statut)
        ]);
      });
      
      console.log(table.toString());
    } catch (error) {
      spinner.fail('Échec de la récupération des exercices');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour créer un exercice comptable
program
  .command('exercices:create')
  .description('Créer un nouvel exercice comptable')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const currentYear = new Date().getFullYear();
    
    const answers = await inquirer.prompt([
      {
        type: 'number',
        name: 'annee',
        message: 'Année de l\'exercice:',
        default: currentYear,
        validate: (input) => input >= 2000 && input <= 2100 ? true : 'Année invalide'
      },
      {
        type: 'input',
        name: 'dateDebut',
        message: 'Date de début (YYYY-MM-DD):',
        default: `${currentYear}-01-01`,
        validate: (input) => /^\d{4}-\d{2}-\d{2}$/.test(input) ? true : 'Format de date invalide (YYYY-MM-DD)'
      },
      {
        type: 'input',
        name: 'dateFin',
        message: 'Date de fin (YYYY-MM-DD):',
        default: `${currentYear}-12-31`,
        validate: (input) => /^\d{4}-\d{2}-\d{2}$/.test(input) ? true : 'Format de date invalide (YYYY-MM-DD)'
      }
    ]);
    
    const exercice = {
      ...answers,
      societeId: config.currentSociete.id
    };
    
    const spinner = ora('Création de l\'exercice...').start();
    
    try {
      const response = await api.post('/exercices', exercice);
      spinner.succeed('Exercice créé avec succès');
      console.log(chalk.green(`ID: ${response.data.data.id}`));
      console.log(chalk.green(`Année: ${response.data.data.annee}`));
      console.log(chalk.green(`Période: ${response.data.data.dateDebut} au ${response.data.data.dateFin}`));
    } catch (error) {
      spinner.fail('Échec de la création de l\'exercice');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === ÉCRITURES COMPTABLES ===

// Commande pour lister les écritures comptables
program
  .command('ecritures:list')
  .description('Lister les écritures comptables')
  .option('-j, --journal <code>', 'Filtrer par code journal')
  .option('-d, --debut <date>', 'Date de début (YYYY-MM-DD)')
  .option('-f, --fin <date>', 'Date de fin (YYYY-MM-DD)')
  .option('-s, --statut <statut>', 'Filtrer par statut (BROUILLARD, VALIDEE, CLOTUREE)')
  .option('-p, --page <number>', 'Numéro de page', '1')
  .option('-l, --limit <number>', 'Nombre d\'éléments par page', '10')
  .action(requireAuth(async (options) => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const params = {
      societeId: config.currentSociete.id,
      page: options.page,
      limit: options.limit
    };
    
    if (options.journal) params.journalCode = options.journal;
    if (options.debut) params.dateDebut = options.debut;
    if (options.fin) params.dateFin = options.fin;
    if (options.statut) params.statut = options.statut;
    
    const spinner = ora('Récupération des écritures...').start();
    
    try {
      const response = await api.get('/ecritures', { params });
      spinner.succeed(`${response.data.total} écritures trouvées (page ${response.data.page}/${response.data.totalPages})`);
      
      if (response.data.data.length === 0) {
        console.log(chalk.yellow('Aucune écriture trouvée'));
        return;
      }
      
      const table = new Table({
        head: [
          chalk.cyan('ID'), 
          chalk.cyan('Date'), 
          chalk.cyan('Journal'), 
          chalk.cyan('Libellé'),
          chalk.cyan('Montant'),
          chalk.cyan('Statut')
        ],
        colWidths: [10, 12, 10, 30, 12, 12]
      });
      
      response.data.data.forEach(ecriture => {
        let statutColor;
        switch (ecriture.statut) {
          case 'BROUILLARD':
            statutColor = chalk.yellow;
            break;
          case 'VALIDEE':
            statutColor = chalk.green;
            break;
          case 'CLOTUREE':
            statutColor = chalk.blue;
            break;
          default:
            statutColor = chalk.white;
        }
        
        // Calcul du montant total (somme des débits ou crédits)
        const montantTotal = ecriture.lignes.reduce((sum, ligne) => sum + (ligne.debit || ligne.credit), 0);
        
        table.push([
          ecriture.id.substring(0, 8),
          ecriture.dateEcriture.split('T')[0],
          ecriture.journalCode,
          ecriture.libelle.substring(0, 28) + (ecriture.libelle.length > 28 ? '...' : ''),
          montantTotal.toFixed(2),
          statutColor(ecriture.statut)
        ]);
      });
      
      console.log(table.toString());
      
      console.log(chalk.dim(`Page ${response.data.page}/${response.data.totalPages}, ${response.data.total} écritures au total`));
    } catch (error) {
      spinner.fail('Échec de la récupération des écritures');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour créer une écriture comptable
program
  .command('ecritures:create')
  .description('Créer une nouvelle écriture comptable')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    // Récupération des journaux disponibles
    const spinnerJournaux = ora('Récupération des journaux...').start();
    let journaux = [];
    
    try {
      const response = await api.get(`/journaux?societeId=${config.currentSociete.id}`);
      journaux = response.data.data;
      spinnerJournaux.succeed(`${journaux.length} journaux trouvés`);
      
      if (journaux.length === 0) {
        console.log(chalk.yellow('Aucun journal trouvé. Veuillez d\'abord créer un journal.'));
        return;
      }
    } catch (error) {
      spinnerJournaux.fail('Échec de la récupération des journaux');
      console.error(chalk.red(`Erreur: ${error.message}`));
      return;
    }
    
    // Informations de base de l'écriture
    const ecritureInfo = await inquirer.prompt([
      {
        type: 'input',
        name: 'dateEcriture',
        message: 'Date de l\'écriture (YYYY-MM-DD):',
        default: new Date().toISOString().split('T')[0],
        validate: (input) => /^\d{4}-\d{2}-\d{2}$/.test(input) ? true : 'Format de date invalide (YYYY-MM-DD)'
      },
      {
        type: 'input',
        name: 'libelle',
        message: 'Libellé de l\'écriture:',
        validate: (input) => input.length >= 3 ? true : 'Le libellé doit contenir au moins 3 caractères'
      },
      {
        type: 'input',
        name: 'reference',
        message: 'Référence (optionnel):',
      },
      {
        type: 'input',
        name: 'pieceJustificative',
        message: 'Pièce justificative (optionnel):',
      },
      {
        type: 'list',
        name: 'journalCode',
        message: 'Journal:',
        choices: journaux.map(journal => ({
          name: `${journal.code} - ${journal.libelle}`,
          value: journal.code,
          short: journal.code
        }))
      }
    ]);
    
    // Création des lignes d'écriture
    const lignes = [];
    let continuerAjout = true;
    let totalDebit = 0;
    let totalCredit = 0;
    
    console.log(chalk.cyan('\nAjout des lignes d\'écriture:'));
    console.log(chalk.dim('(Ajoutez au moins 2 lignes et assurez-vous que les débits et crédits s\'équilibrent)'));
    
    while (continuerAjout) {
      const ligne = await inquirer.prompt([
        {
          type: 'input',
          name: 'compteNumero',
          message: 'Numéro de compte:',
          validate: (input) => /^\d{6,10}$/.test(input) ? true : 'Le numéro de compte doit contenir entre 6 et 10 chiffres'
        },
        {
          type: 'input',
          name: 'libelle',
          message: 'Libellé de la ligne:',
          default: ecritureInfo.libelle,
          validate: (input) => input.length >= 2 ? true : 'Le libellé doit contenir au moins 2 caractères'
        },
        {
          type: 'list',
          name: 'type',
          message: 'Type de montant:',
          choices: [
            { name: 'Débit', value: 'debit' },
            { name: 'Crédit', value: 'credit' }
          ]
        },
        {
          type: 'number',
          name: 'montant',
          message: 'Montant:',
          validate: (input) => input > 0 ? true : 'Le montant doit être supérieur à 0'
        },
        {
          type: 'input',
          name: 'reference',
          message: 'Référence de ligne (optionnel):',
        }
      ]);
      
      const ligneEcriture = {
        compteNumero: ligne.compteNumero,
        libelle: ligne.libelle,
        debit: ligne.type === 'debit' ? ligne.montant : 0,
        credit: ligne.type === 'credit' ? ligne.montant : 0,
        reference: ligne.reference
      };
      
      lignes.push(ligneEcriture);
      
      if (ligne.type === 'debit') {
        totalDebit += ligne.montant;
      } else {
        totalCredit += ligne.montant;
      }
      
      console.log(chalk.green(`Ligne ajoutée: ${ligne.compteNumero} - ${ligne.libelle} - ${ligne.type}: ${ligne.montant}`));
      console.log(chalk.dim(`Total Débit: ${totalDebit.toFixed(2)} | Total Crédit: ${totalCredit.toFixed(2)}`));
      
      if (lignes.length >= 2) {
        const { continuer } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'continuer',
            message: 'Ajouter une autre ligne?',
            default: totalDebit !== totalCredit
          }
        ]);
        
        continuerAjout = continuer;
        
        if (!continuer && totalDebit !== totalCredit) {
          console.log(chalk.yellow('Attention: Les débits et crédits ne sont pas équilibrés!'));
          const { forcer } = await inquirer.prompt([
            {
              type: 'confirm',
              name: 'forcer',
              message: 'Voulez-vous quand même continuer?',
              default: false
            }
          ]);
          
          if (forcer) {
            continuerAjout = false;
          } else {
            continuerAjout = true;
          }
        }
      }
    }
    
    // Création de l'écriture
    const ecriture = {
      donnees: {
        ...ecritureInfo,
        societeId: config.currentSociete.id
      },
      lignes
    };
    
    const spinner = ora('Création de l\'écriture...').start();
    
    try {
      const response = await api.post('/ecritures', ecriture);
      spinner.succeed('Écriture créée avec succès');
      console.log(chalk.green(`ID: ${response.data.data.id}`));
      console.log(chalk.green(`Journal: ${response.data.data.journalCode}`));
      console.log(chalk.green(`Date: ${response.data.data.dateEcriture.split('T')[0]}`));
      console.log(chalk.green(`Statut: ${response.data.data.statut}`));
    } catch (error) {
      spinner.fail('Échec de la création de l\'écriture');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// Commande pour afficher les détails d'une écriture
program
  .command('ecritures:show <id>')
  .description('Afficher les détails d\'une écriture comptable')
  .action(requireAuth(async (id) => {
    const spinner = ora('Récupération des détails de l\'écriture...').start();
    
    try {
      const response = await api.get(`/ecritures/${id}`);
      spinner.succeed('Détails récupérés');
      
      const ecriture = response.data.data;
      
      console.log(chalk.bold('\nInformations générales:'));
      console.log(`ID: ${ecriture.id}`);
      console.log(`Date: ${ecriture.dateEcriture.split('T')[0]}`);
      console.log(`Journal: ${ecriture.journalCode}`);
      console.log(`Libellé: ${ecriture.libelle}`);
      console.log(`Référence: ${ecriture.reference || 'N/A'}`);
      console.log(`Pièce justificative: ${ecriture.pieceJustificative || 'N/A'}`);
      console.log(`Statut: ${chalk.bold(ecriture.statut)}`);
      
      console.log(chalk.bold('\nLignes d\'écriture:'));
      
      const table = new Table({
        head: [
          chalk.cyan('Compte'), 
          chalk.cyan('Libellé'), 
          chalk.cyan('Débit'), 
          chalk.cyan('Crédit'),
          chalk.cyan('Référence')
        ]
      });
      
      let totalDebit = 0;
      let totalCredit = 0;
      
      ecriture.lignes.forEach(ligne => {
        totalDebit += ligne.debit || 0;
        totalCredit += ligne.credit || 0;
        
        table.push([
          ligne.compteNumero,
          ligne.libelle,
          ligne.debit ? ligne.debit.toFixed(2) : '',
          ligne.credit ? ligne.credit.toFixed(2) : '',
          ligne.reference || ''
        ]);
      });
      
      console.log(table.toString());
      
      console.log(chalk.bold('\nTotaux:'));
      console.log(`Total Débit: ${totalDebit.toFixed(2)}`);
      console.log(`Total Crédit: ${totalCredit.toFixed(2)}`);
      
      if (totalDebit === totalCredit) {
        console.log(chalk.green('Écriture équilibrée ✓'));
      } else {
        console.log(chalk.red(`Écriture déséquilibrée! Différence: ${Math.abs(totalDebit - totalCredit).toFixed(2)}`));
      }
    } catch (error) {
      spinner.fail('Échec de la récupération des détails');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour valider une écriture
program
  .command('ecritures:valider <id>')
  .description('Valider une écriture comptable (passage de BROUILLARD à VALIDEE)')
  .action(requireAuth(async (id) => {
    const spinner = ora('Validation de l\'écriture...').start();
    
    try {
      const response = await api.post(`/ecritures/${id}/valider`);
      spinner.succeed('Écriture validée avec succès');
      console.log(chalk.green(`ID: ${response.data.data.id}`));
      console.log(chalk.green(`Nouveau statut: ${response.data.data.statut}`));
    } catch (error) {
      spinner.fail('Échec de la validation');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === PLAN COMPTABLE ===

// Commande pour lister les comptes du plan comptable
program
  .command('comptes:list')
  .description('Lister les comptes du plan comptable')
  .option('-c, --classe <classe>', 'Filtrer par classe (1-9)')
  .option('-p, --page <number>', 'Numéro de page', '1')
  .option('-l, --limit <number>', 'Nombre d\'éléments par page', '20')
  .action(requireAuth(async (options) => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const params = {
      societeId: config.currentSociete.id,
      page: options.page,
      limit: options.limit
    };
    
    if (options.classe) params.classe = options.classe;
    
    const spinner = ora('Récupération du plan comptable...').start();
    
    try {
      const response = await api.get('/plan-comptable', { params });
      spinner.succeed(`${response.data.total} comptes trouvés (page ${response.data.page}/${response.data.totalPages})`);
      
      if (response.data.data.length === 0) {
        console.log(chalk.yellow('Aucun compte trouvé'));
        return;
      }
      
      const table = new Table({
        head: [
          chalk.cyan('Numéro'), 
          chalk.cyan('Intitulé'), 
          chalk.cyan('Type'),
          chalk.cyan('Solde')
        ],
        colWidths: [10, 40, 15, 15]
      });
      
      response.data.data.forEach(compte => {
        let typeColor;
        switch (compte.type) {
          case 'ACTIF':
            typeColor = chalk.green;
            break;
          case 'PASSIF':
            typeColor = chalk.red;
            break;
          case 'CHARGE':
            typeColor = chalk.yellow;
            break;
          case 'PRODUIT':
            typeColor = chalk.blue;
            break;
          default:
            typeColor = chalk.white;
        }
        
        table.push([
          compte.numero,
          compte.intitule.substring(0, 38) + (compte.intitule.length > 38 ? '...' : ''),
          typeColor(compte.type),
          compte.solde ? compte.solde.toFixed(2) : '0.00'
        ]);
      });
      
      console.log(table.toString());
      
      console.log(chalk.dim(`Page ${response.data.page}/${response.data.totalPages}, ${response.data.total} comptes au total`));
    } catch (error) {
      spinner.fail('Échec de la récupération du plan comptable');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// === ÉTATS FINANCIERS ===

// Commande pour générer un état financier
program
  .command('etats:generer')
  .description('Générer un état financier')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    // Récupération des exercices disponibles
    const spinnerExercices = ora('Récupération des exercices...').start();
    let exercices = [];
    
    try {
      const response = await api.get(`/exercices?societeId=${config.currentSociete.id}`);
      exercices = response.data.data;
      spinnerExercices.succeed(`${exercices.length} exercices trouvés`);
      
      if (exercices.length === 0) {
        console.log(chalk.yellow('Aucun exercice trouvé. Veuillez d\'abord créer un exercice.'));
        return;
      }
    } catch (error) {
      spinnerExercices.fail('Échec de la récupération des exercices');
      console.error(chalk.red(`Erreur: ${error.message}`));
      return;
    }
    
    const { exerciceId, typeEtat } = await inquirer.prompt([
      {
        type: 'list',
        name: 'exerciceId',
        message: 'Sélectionnez un exercice:',
        choices: exercices.map(exercice => ({
          name: `${exercice.annee} (${exercice.dateDebut} - ${exercice.dateFin})`,
          value: exercice.id,
          short: exercice.annee
        }))
      },
      {
        type: 'list',
        name: 'typeEtat',
        message: 'Type d\'état financier:',
        choices: [
          { name: 'Grand Livre', value: 'grand-livre' },
          { name: 'Balance', value: 'balance' },
          { name: 'Journal', value: 'journal' },
          { name: 'Bilan', value: 'bilan' },
          { name: 'Compte de Résultat', value: 'resultat' }
        ]
      }
    ]);
    
    let params = {};
    
    if (typeEtat === 'journal') {
      // Récupération des journaux disponibles
      const spinnerJournaux = ora('Récupération des journaux...').start();
      let journaux = [];
      
      try {
        const response = await api.get(`/journaux?societeId=${config.currentSociete.id}`);
        journaux = response.data.data;
        spinnerJournaux.succeed(`${journaux.length} journaux trouvés`);
      } catch (error) {
        spinnerJournaux.fail('Échec de la récupération des journaux');
        console.error(chalk.red(`Erreur: ${error.message}`));
        return;
      }
      
      const { journalCode } = await inquirer.prompt([
        {
          type: 'list',
          name: 'journalCode',
          message: 'Sélectionnez un journal:',
          choices: journaux.map(journal => ({
            name: `${journal.code} - ${journal.libelle}`,
            value: journal.code,
            short: journal.code
          }))
        }
      ]);
      
      params.journalCode = journalCode;
    } else if (typeEtat === 'grand-livre') {
      const { compteDebut, compteFin } = await inquirer.prompt([
        {
          type: 'input',
          name: 'compteDebut',
          message: 'Compte de début (optionnel):',
          validate: (input) => input === '' || /^\d{1,10}$/.test(input) ? true : 'Format de compte invalide'
        },
        {
          type: 'input',
          name: 'compteFin',
          message: 'Compte de fin (optionnel):',
          validate: (input) => input === '' || /^\d{1,10}$/.test(input) ? true : 'Format de compte invalide'
        }
      ]);
      
      if (compteDebut) params.compteDebut = compteDebut;
      if (compteFin) params.compteFin = compteFin;
    }
    
    const { format } = await inquirer.prompt([
      {
        type: 'list',
        name: 'format',
        message: 'Format de sortie:',
        choices: [
          { name: 'JSON (affichage console)', value: 'json' },
          { name: 'PDF (téléchargement)', value: 'pdf' },
          { name: 'Excel (téléchargement)', value: 'xlsx' }
        ]
      }
    ]);
    
    params = {
      ...params,
      exerciceId,
      format
    };
    
    const spinner = ora(`Génération de l'état ${typeEtat}...`).start();
    
    try {
      const response = await api.get(`/etats/${typeEtat}`, { params });
      spinner.succeed('État généré avec succès');
      
      if (format === 'json') {
        console.log(chalk.bold('\nRésultat:'));
        
        if (typeEtat === 'balance') {
          const table = new Table({
            head: [
              chalk.cyan('Compte'), 
              chalk.cyan('Intitulé'), 
              chalk.cyan('Débit'), 
              chalk.cyan('Crédit'),
              chalk.cyan('Solde Débiteur'),
              chalk.cyan('Solde Créditeur')
            ]
          });
          
          response.data.data.lignes.forEach(ligne => {
            table.push([
              ligne.compte,
              ligne.intitule.substring(0, 20) + (ligne.intitule.length > 20 ? '...' : ''),
              ligne.totalDebit.toFixed(2),
              ligne.totalCredit.toFixed(2),
              ligne.soldeDebiteur ? ligne.soldeDebiteur.toFixed(2) : '',
              ligne.soldeCrediteur ? ligne.soldeCrediteur.toFixed(2) : ''
            ]);
          });
          
          console.log(table.toString());
          
          console.log(chalk.bold('\nTotaux:'));
          console.log(`Total Débit: ${response.data.data.totalDebit.toFixed(2)}`);
          console.log(`Total Crédit: ${response.data.data.totalCredit.toFixed(2)}`);
          console.log(`Total Solde Débiteur: ${response.data.data.totalSoldeDebiteur.toFixed(2)}`);
          console.log(`Total Solde Créditeur: ${response.data.data.totalSoldeCrediteur.toFixed(2)}`);
        } else {
          console.log(JSON.stringify(response.data.data, null, 2));
        }
      } else {
        console.log(chalk.green(`Fichier généré: ${response.data.fichier}`));
      }
    } catch (error) {
      spinner.fail(`Échec de la génération de l'état ${typeEtat}`);
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === ANALYSES FINANCIÈRES ===

// Commande pour générer une analyse financière
program
  .command('analyses:generer')
  .description('Générer une analyse financière')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    // Récupération des exercices disponibles
    const spinnerExercices = ora('Récupération des exercices...').start();
    let exercices = [];
    
    try {
      const response = await api.get(`/exercices?societeId=${config.currentSociete.id}`);
      exercices = response.data.data;
      spinnerExercices.succeed(`${exercices.length} exercices trouvés`);
      
      if (exercices.length === 0) {
        console.log(chalk.yellow('Aucun exercice trouvé. Veuillez d\'abord créer un exercice.'));
        return;
      }
    } catch (error) {
      spinnerExercices.fail('Échec de la récupération des exercices');
      console.error(chalk.red(`Erreur: ${error.message}`));
      return;
    }
    
    const { exerciceId, typeAnalyse } = await inquirer.prompt([
      {
        type: 'list',
        name: 'exerciceId',
        message: 'Sélectionnez un exercice:',
        choices: exercices.map(exercice => ({
          name: `${exercice.annee} (${exercice.dateDebut} - ${exercice.dateFin})`,
          value: exercice.id,
          short: exercice.annee
        }))
      },
      {
        type: 'list',
        name: 'typeAnalyse',
        message: 'Type d\'analyse:',
        choices: [
          { name: 'Ratios de liquidité', value: 'liquidite' },
          { name: 'Ratios de rentabilité', value: 'rentabilite' },
          { name: 'Ratios d\'endettement', value: 'endettement' },
          { name: 'Analyse complète', value: 'complete' }
        ]
      }
    ]);
    
    const spinner = ora(`Génération de l'analyse ${typeAnalyse}...`).start();
    
    try {
      const response = await api.get(`/analyses/${typeAnalyse}`, { 
        params: { 
          exerciceId,
          societeId: config.currentSociete.id
        } 
      });
      
      spinner.succeed('Analyse générée avec succès');
      
      console.log(chalk.bold('\nRésultats de l\'analyse:'));
      
      if (typeAnalyse === 'complete') {
        // Affichage des différentes sections d'analyse
        Object.keys(response.data.data).forEach(section => {
          console.log(chalk.cyan(`\n=== ${section.toUpperCase()} ===`));
          
          const ratios = response.data.data[section];
          const table = new Table({
            head: [chalk.cyan('Ratio'), chalk.cyan('Valeur'), chalk.cyan('Interprétation')]
          });
          
          Object.keys(ratios).forEach(ratio => {
            let valeurColor = chalk.white;
            
            if (ratios[ratio].interpretation) {
              if (ratios[ratio].interpretation.includes('bon') || 
                  ratios[ratio].interpretation.includes('satisfaisant')) {
                valeurColor = chalk.green;
              } else if (ratios[ratio].interpretation.includes('attention') || 
                         ratios[ratio].interpretation.includes('surveiller')) {
                valeurColor = chalk.yellow;
              } else if (ratios[ratio].interpretation.includes('problème') || 
                         ratios[ratio].interpretation.includes('insuffisant')) {
                valeurColor = chalk.red;
              }
            }
            
            table.push([
              ratio,
              valeurColor(ratios[ratio].valeur),
              ratios[ratio].interpretation || ''
            ]);
          });
          
          console.log(table.toString());
        });
      } else {
        // Affichage d'une seule section d'analyse
        const table = new Table({
          head: [chalk.cyan('Ratio'), chalk.cyan('Valeur'), chalk.cyan('Interprétation')]
        });
        
        Object.keys(response.data.data).forEach(ratio => {
          let valeurColor = chalk.white;
          
          if (response.data.data[ratio].interpretation) {
            if (response.data.data[ratio].interpretation.includes('bon') || 
                response.data.data[ratio].interpretation.includes('satisfaisant')) {
              valeurColor = chalk.green;
            } else if (response.data.data[ratio].interpretation.includes('attention') || 
                       response.data.data[ratio].interpretation.includes('surveiller')) {
              valeurColor = chalk.yellow;
            } else if (response.data.data[ratio].interpretation.includes('problème') || 
                       response.data.data[ratio].interpretation.includes('insuffisant')) {
              valeurColor = chalk.red;
            }
          }
          
          table.push([
            ratio,
            valeurColor(response.data.data[ratio].valeur),
            response.data.data[ratio].interpretation || ''
          ]);
        });
        
        console.log(table.toString());
      }
      
      if (response.data.conclusion) {
        console.log(chalk.bold('\nConclusion:'));
        console.log(response.data.conclusion);
      }
      
    } catch (error) {
      spinner.fail(`Échec de la génération de l'analyse ${typeAnalyse}`);
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === DASHBOARD ===

// Commande pour afficher le tableau de bord
program
  .command('dashboard')
  .description('Afficher le tableau de bord financier')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const spinner = ora('Chargement du tableau de bord...').start();
    
    try {
      const response = await api.get('/dashboard', { 
        params: { societeId: config.currentSociete.id } 
      });
      
      spinner.succeed('Tableau de bord chargé avec succès');
      
      console.log(chalk.bold(`\nTableau de bord - ${config.currentSociete.nom}`));
      
      // Affichage des indicateurs clés
      console.log(chalk.cyan('\n=== INDICATEURS CLÉS ==='));
      
      const indicateursTable = new Table({
        head: [chalk.cyan('Indicateur'), chalk.cyan('Valeur')]
      });
      
      Object.keys(response.data.data.indicateurs).forEach(indicateur => {
        indicateursTable.push([
          indicateur,
          response.data.data.indicateurs[indicateur]
        ]);
      });
      
      console.log(indicateursTable.toString());
      
      // Affichage des écritures récentes
      console.log(chalk.cyan('\n=== ÉCRITURES RÉCENTES ==='));
      
      if (response.data.data.ecrituresRecentes.length === 0) {
        console.log(chalk.yellow('Aucune écriture récente'));
      } else {
        const ecrituresTable = new Table({
          head: [
            chalk.cyan('Date'), 
            chalk.cyan('Journal'), 
            chalk.cyan('Libellé'),
            chalk.cyan('Montant'),
            chalk.cyan('Statut')
          ],
          colWidths: [12, 10, 30, 12, 12]
        });
        
        response.data.data.ecrituresRecentes.forEach(ecriture => {
          let statutColor;
          switch (ecriture.statut) {
            case 'BROUILLARD':
              statutColor = chalk.yellow;
              break;
            case 'VALIDEE':
              statutColor = chalk.green;
              break;
            case 'CLOTUREE':
              statutColor = chalk.blue;
              break;
            default:
              statutColor = chalk.white;
          }
          
          ecrituresTable.push([
            ecriture.dateEcriture.split('T')[0],
            ecriture.journalCode,
            ecriture.libelle.substring(0, 28) + (ecriture.libelle.length > 28 ? '...' : ''),
            ecriture.montant.toFixed(2),
            statutColor(ecriture.statut)
          ]);
        });
        
        console.log(ecrituresTable.toString());
      }
      
      // Affichage des comptes principaux
      console.log(chalk.cyan('\n=== COMPTES PRINCIPAUX ==='));
      
      const comptesTable = new Table({
        head: [
          chalk.cyan('Compte'), 
          chalk.cyan('Intitulé'),
          chalk.cyan('Solde')
        ]
      });
      
      response.data.data.comptesPrincipaux.forEach(compte => {
        let soldeColor = chalk.white;
        
        if (compte.solde > 0) {
          soldeColor = chalk.green;
        } else if (compte.solde < 0) {
          soldeColor = chalk.red;
        }
        
        comptesTable.push([
          compte.numero,
          compte.intitule,
          soldeColor(compte.solde.toFixed(2))
        ]);
      });
      
      console.log(comptesTable.toString());
      
    } catch (error) {
      spinner.fail('Échec du chargement du tableau de bord');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === AMORTISSEMENTS ===

// Commande pour lister les amortissements
program
  .command('amortissements:list')
  .description('Lister les amortissements')
  .option('-c, --categorie <categorie>', 'Filtrer par catégorie')
  .option('-s, --statut <statut>', 'Filtrer par statut')
  .option('-p, --page <number>', 'Numéro de page', '1')
  .option('-l, --limit <number>', 'Nombre d\'éléments par page', '20')
  .action(requireAuth(async (options) => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const params = {
      societeId: config.currentSociete.id,
      page: options.page,
      limit: options.limit
    };
    
    if (options.categorie) params.categorie = options.categorie;
    if (options.statut) params.statut = options.statut;
    
    const spinner = ora('Récupération des amortissements...').start();
    
    try {
      const response = await api.get('/depreciations', { params });
      spinner.succeed(`${response.data.total} amortissements trouvés (page ${response.data.page}/${response.data.totalPages})`);
      
      if (response.data.data.length === 0) {
        console.log(chalk.yellow('Aucun amortissement trouvé'));
        return;
      }
      
      const table = new Table({
        head: [
          chalk.cyan('Code'), 
          chalk.cyan('Libellé'), 
          chalk.cyan('Catégorie'),
          chalk.cyan('Valeur Acquisition'),
          chalk.cyan('VNC'),
          chalk.cyan('Statut')
        ],
        colWidths: [10, 25, 15, 15, 15, 15]
      });
      
      response.data.data.forEach(amortissement => {
        let statutColor;
        switch (amortissement.statut) {
          case 'EN_SERVICE':
            statutColor = chalk.green;
            break;
          case 'HORS_SERVICE':
            statutColor = chalk.yellow;
            break;
          case 'TOTALEMENT_AMORTI':
            statutColor = chalk.blue;
            break;
          default:
            statutColor = chalk.white;
        }
        
        table.push([
          amortissement.code,
          amortissement.libelle.substring(0, 23) + (amortissement.libelle.length > 23 ? '...' : ''),
          amortissement.categorie,
          amortissement.valeurAcquisition.toFixed(2),
          amortissement.valeurNetteComptable.toFixed(2),
          statutColor(amortissement.statut)
        ]);
      });
      
      console.log(table.toString());
      
      console.log(chalk.dim(`Page ${response.data.page}/${response.data.totalPages}, ${response.data.total} amortissements au total`));
    } catch (error) {
      spinner.fail('Échec de la récupération des amortissements');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour créer un amortissement
program
  .command('amortissements:create')
  .description('Créer un nouvel amortissement')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'code',
        message: 'Code de l\'amortissement:',
        validate: (input) => input.length >= 1 ? true : 'Le code est obligatoire'
      },
      {
        type: 'input',
        name: 'libelle',
        message: 'Libellé:',
        validate: (input) => input.length >= 3 ? true : 'Le libellé doit contenir au moins 3 caractères'
      },
      {
        type: 'list',
        name: 'categorie',
        message: 'Catégorie:',
        choices: [
          'TERRAIN', 'BATIMENT', 'MATERIEL_TRANSPORT', 'MATERIEL_BUREAU',
          'MATERIEL_INFORMATIQUE', 'MOBILIER', 'INSTALLATION', 'MATERIEL_INDUSTRIEL',
          'BREVETS_LICENCES', 'LOGICIELS', 'AUTRES'
        ]
      },
      {
        type: 'input',
        name: 'compteImmobilisation',
        message: 'Compte d\'immobilisation (2xxxxx):',
        validate: (input) => /^2\d{5}$/.test(input) ? true : 'Le compte doit commencer par 2 et contenir 6 chiffres'
      },
      {
        type: 'input',
        name: 'compteAmortissement',
        message: 'Compte d\'amortissement (28xxxx):',
        validate: (input) => /^28\d{4}$/.test(input) ? true : 'Le compte doit commencer par 28 et contenir 6 chiffres'
      },
      {
        type: 'number',
        name: 'valeurAcquisition',
        message: 'Valeur d\'acquisition:',
        validate: (input) => input > 0 ? true : 'La valeur doit être supérieure à 0'
      },
      {
        type: 'input',
        name: 'dateAcquisition',
        message: 'Date d\'acquisition (YYYY-MM-DD):',
        validate: (input) => /^\d{4}-\d{2}-\d{2}$/.test(input) ? true : 'Format de date invalide (YYYY-MM-DD)'
      },
      {
        type: 'input',
        name: 'dateMiseEnService',
        message: 'Date de mise en service (YYYY-MM-DD):',
        validate: (input) => /^\d{4}-\d{2}-\d{2}$/.test(input) ? true : 'Format de date invalide (YYYY-MM-DD)'
      },
      {
        type: 'list',
        name: 'methodeAmortissement',
        message: 'Méthode d\'amortissement:',
        choices: ['LINEAIRE', 'DEGRESSIF', 'PROGRESSIF', 'VARIABLE', 'EXCEPTIONNEL'],
        default: 'LINEAIRE'
      },
      {
        type: 'number',
        name: 'dureeAmortissement',
        message: 'Durée d\'amortissement (en années):',
        validate: (input) => input > 0 && input <= 100 ? true : 'La durée doit être entre 1 et 100 ans'
      }
    ]);
    
    const amortissement = {
      ...answers,
      societeId: config.currentSociete.id
    };
    
    const spinner = ora('Création de l\'amortissement...').start();
    
    try {
      const response = await api.post('/depreciations', amortissement);
      spinner.succeed('Amortissement créé avec succès');
      console.log(chalk.green(`ID: ${response.data.data.id}`));
      console.log(chalk.green(`Code: ${response.data.data.code}`));
      console.log(chalk.green(`Libellé: ${response.data.data.libelle}`));
      console.log(chalk.green(`Taux d'amortissement: ${response.data.data.tauxAmortissement}%`));
    } catch (error) {
      spinner.fail('Échec de la création de l\'amortissement');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// Commande pour afficher le plan d'amortissement
program
  .command('amortissements:plan <id>')
  .description('Afficher le plan d\'amortissement')
  .action(requireAuth(async (id) => {
    const spinner = ora('Récupération du plan d\'amortissement...').start();
    
    try {
      const response = await api.get(`/depreciations/${id}/plan`);
      spinner.succeed('Plan d\'amortissement récupéré');
      
      const plan = response.data.data;
      
      console.log(chalk.bold('\nInformations de l\'amortissement:'));
      console.log(`Code: ${plan.amortissement.code}`);
      console.log(`Libellé: ${plan.amortissement.libelle}`);
      console.log(`Valeur d'acquisition: ${plan.amortissement.valeurAcquisition}`);
      console.log(`Méthode: ${plan.amortissement.methodeAmortissement}`);
      console.log(`Durée: ${plan.amortissement.dureeAmortissement} ans`);
      
      console.log(chalk.bold('\nPlan d\'amortissement:'));
      
      const table = new Table({
        head: [
          chalk.cyan('Exercice'), 
          chalk.cyan('Valeur début'), 
          chalk.cyan('Dotation'),
          chalk.cyan('Cumul'),
          chalk.cyan('VNC fin'),
          chalk.cyan('Statut')
        ]
      });
      
      plan.lignes.forEach(ligne => {
        let statutColor;
        switch (ligne.statut) {
          case 'REALISE':
            statutColor = chalk.green;
            break;
          case 'PREVISIONNEL':
            statutColor = chalk.yellow;
            break;
          default:
            statutColor = chalk.white;
        }
        
        table.push([
          ligne.exercice,
          ligne.valeurDebutPeriode.toFixed(2),
          ligne.dotationPeriode.toFixed(2),
          ligne.cumulFinPeriode.toFixed(2),
          ligne.valeurFinPeriode.toFixed(2),
          statutColor(ligne.statut)
        ]);
      });
      
      console.log(table.toString());
      
    } catch (error) {
      spinner.fail('Échec de la récupération du plan');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour calculer les dotations d'un exercice
program
  .command('amortissements:calculer-dotations')
  .description('Calculer les dotations d\'amortissement pour un exercice')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const currentYear = new Date().getFullYear();
    
    const { exercice } = await inquirer.prompt([
      {
        type: 'number',
        name: 'exercice',
        message: 'Exercice (année):',
        default: currentYear,
        validate: (input) => input >= 2000 && input <= 2100 ? true : 'Année invalide'
      }
    ]);
    
    const spinner = ora('Calcul des dotations...').start();
    
    try {
      const response = await api.post('/depreciations/calculate-dotations', {
        societeId: config.currentSociete.id,
        exercice
      });
      
      spinner.succeed('Dotations calculées avec succès');
      
      console.log(chalk.bold(`\nDotations pour l'exercice ${exercice}:`));
      
      const table = new Table({
        head: [
          chalk.cyan('Code'), 
          chalk.cyan('Libellé'), 
          chalk.cyan('Dotation'),
          chalk.cyan('Statut')
        ]
      });
      
      response.data.data.forEach(dotation => {
        table.push([
          dotation.code,
          dotation.libelle.substring(0, 25) + (dotation.libelle.length > 25 ? '...' : ''),
          dotation.dotation.toFixed(2),
          dotation.statut
        ]);
      });
      
      console.log(table.toString());
      
      const totalDotations = response.data.data.reduce((sum, d) => sum + d.dotation, 0);
      console.log(chalk.bold(`\nTotal des dotations: ${totalDotations.toFixed(2)}`));
      
    } catch (error) {
      spinner.fail('Échec du calcul des dotations');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// Commande pour générer les écritures d'amortissement
program
  .command('amortissements:generer-ecritures')
  .description('Générer les écritures d\'amortissement pour un exercice')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const currentYear = new Date().getFullYear();
    
    const { exercice } = await inquirer.prompt([
      {
        type: 'number',
        name: 'exercice',
        message: 'Exercice (année):',
        default: currentYear,
        validate: (input) => input >= 2000 && input <= 2100 ? true : 'Année invalide'
      }
    ]);
    
    const spinner = ora('Génération des écritures d\'amortissement...').start();
    
    try {
      const response = await api.post('/depreciations/generate-entries', {
        societeId: config.currentSociete.id,
        exercice
      });
      
      spinner.succeed('Écritures générées avec succès');
      
      console.log(chalk.bold(`\nÉcritures d'amortissement générées pour l'exercice ${exercice}:`));
      console.log(`Nombre d'écritures: ${response.data.data.nombreEcritures}`);
      console.log(`Montant total: ${response.data.data.montantTotal.toFixed(2)}`);
      
      if (response.data.data.ecritures && response.data.data.ecritures.length > 0) {
        const table = new Table({
          head: [
            chalk.cyan('ID Écriture'), 
            chalk.cyan('Libellé'), 
            chalk.cyan('Montant')
          ]
        });
        
        response.data.data.ecritures.forEach(ecriture => {
          table.push([
            ecriture.id.substring(0, 8) + '...',
            ecriture.libelle.substring(0, 40) + (ecriture.libelle.length > 40 ? '...' : ''),
            ecriture.montant.toFixed(2)
          ]);
        });
        
        console.log(table.toString());
      }
      
    } catch (error) {
      spinner.fail('Échec de la génération des écritures');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === CLÔTURES D'EXERCICE ===

// Commande pour valider les pré-requis de clôture
program
  .command('exercices:valider-cloture <id>')
  .description('Valider les pré-requis pour la clôture d\'un exercice')
  .action(requireAuth(async (id) => {
    const spinner = ora('Validation des pré-requis de clôture...').start();
    
    try {
      const response = await api.post(`/exercises/${id}/validate-closure`);
      spinner.succeed('Validation terminée');
      
      const validation = response.data.data;
      
      console.log(chalk.bold(`\nValidation de clôture - Exercice ${validation.exercice.libelle}`));
      console.log(`Période: ${validation.exercice.dateDebut} au ${validation.exercice.dateFin}`);
      console.log(`Statut: ${validation.exercice.statut}`);
      
      // Résultat global
      if (validation.validationGlobale) {
        console.log(chalk.green('\n✅ EXERCICE PRÊT POUR CLÔTURE'));
      } else {
        console.log(chalk.red('\n❌ EXERCICE NON PRÊT POUR CLÔTURE'));
      }
      
      // Détail des validations
      console.log(chalk.bold('\nDétail des validations:'));
      
      const table = new Table({
        head: [
          chalk.cyan('Validation'), 
          chalk.cyan('Statut'), 
          chalk.cyan('Message')
        ]
      });
      
      Object.keys(validation.validations).forEach(key => {
        const val = validation.validations[key];
        const statut = val.valide ? chalk.green('✓') : chalk.red('✗');
        
        table.push([
          key,
          statut,
          val.message
        ]);
      });
      
      console.log(table.toString());
      
      // Recommandations
      if (validation.recommandations && validation.recommandations.length > 0) {
        console.log(chalk.bold('\nRecommandations:'));
        validation.recommandations.forEach((rec, index) => {
          let typeColor;
          switch (rec.type) {
            case 'CRITIQUE':
              typeColor = chalk.red;
              break;
            case 'IMPORTANT':
              typeColor = chalk.yellow;
              break;
            default:
              typeColor = chalk.blue;
          }
          
          console.log(`${index + 1}. ${typeColor(rec.type)}: ${rec.message}`);
        });
      }
      
    } catch (error) {
      spinner.fail('Échec de la validation');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour simuler une clôture
program
  .command('exercices:simuler-cloture <id>')
  .description('Simuler la clôture d\'un exercice')
  .action(requireAuth(async (id) => {
    const spinner = ora('Simulation de la clôture...').start();
    
    try {
      const response = await api.post(`/exercises/${id}/simulate-closure`);
      spinner.succeed('Simulation terminée');
      
      const simulation = response.data.data;
      
      console.log(chalk.bold(`\nSimulation de clôture - Exercice ${simulation.exercice.libelle}`));
      
      // Résultat calculé
      console.log(chalk.bold('\nRésultat de l\'exercice:'));
      console.log(`Total charges: ${simulation.resultatCalcul.totalCharges.toFixed(2)}`);
      console.log(`Total produits: ${simulation.resultatCalcul.totalProduits.toFixed(2)}`);
      
      const resultatColor = simulation.resultatCalcul.resultatNet >= 0 ? chalk.green : chalk.red;
      console.log(`Résultat net: ${resultatColor(simulation.resultatCalcul.resultatNet.toFixed(2))} (${simulation.resultatCalcul.typeResultat})`);
      
      // Étapes simulées
      console.log(chalk.bold('\nÉtapes du processus de clôture:'));
      
      const table = new Table({
        head: [
          chalk.cyan('Étape'), 
          chalk.cyan('Description'), 
          chalk.cyan('Durée estimée')
        ]
      });
      
      simulation.etapesSimulees.forEach(etape => {
        table.push([
          etape.etape,
          etape.description,
          etape.dureeEstimee
        ]);
      });
      
      console.log(table.toString());
      
      console.log(chalk.bold(`\nDurée totale estimée: ${simulation.dureeEstimeeTotal}`));
      
      // Validation globale
      if (simulation.validation.validationGlobale) {
        console.log(chalk.green('\n✅ Exercice prêt pour clôture'));
      } else {
        console.log(chalk.red('\n❌ Pré-requis non satisfaits'));
        console.log(chalk.yellow('Utilisez "exercices:valider-cloture" pour plus de détails'));
      }
      
    } catch (error) {
      spinner.fail('Échec de la simulation');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour clôturer un exercice
program
  .command('exercices:cloturer <id>')
  .description('Clôturer un exercice comptable')
  .option('-f, --force', 'Forcer la clôture même si les pré-requis ne sont pas satisfaits')
  .option('--no-resultat', 'Ne pas générer les écritures de résultat')
  .option('--no-reouverture', 'Ne pas créer l\'exercice suivant')
  .option('--archiver', 'Archiver les données de l\'exercice')
  .action(requireAuth(async (id, options) => {
    // Confirmation
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Êtes-vous sûr de vouloir clôturer cet exercice ? Cette action est irréversible.',
        default: false
      }
    ]);
    
    if (!confirm) {
      console.log(chalk.yellow('Clôture annulée'));
      return;
    }
    
    const spinner = ora('Clôture de l\'exercice en cours...').start();
    
    try {
      const payload = {
        forcerCloture: options.force || false,
        genererEcrituresResultat: options.resultat !== false,
        genererEcrituresReouverture: options.reouverture !== false,
        archiverDonnees: options.archiver || false
      };
      
      const response = await api.post(`/exercises/${id}/close`, payload);
      spinner.succeed('Exercice clôturé avec succès');
      
      const cloture = response.data.data;
      
      console.log(chalk.bold(`\nClôture terminée - Exercice ${cloture.exercice.libelle}`));
      console.log(`Date de clôture: ${new Date(cloture.exercice.dateCloture).toLocaleString()}`);
      console.log(`Résultat de l'exercice: ${cloture.exercice.resultatExercice.toFixed(2)}`);
      
      // Étapes réalisées
      console.log(chalk.bold('\nÉtapes réalisées:'));
      
      const table = new Table({
        head: [
          chalk.cyan('Étape'), 
          chalk.cyan('Statut'), 
          chalk.cyan('Détails')
        ]
      });
      
      cloture.etapes.forEach(etape => {
        const statutColor = etape.statut === 'TERMINE' ? chalk.green : chalk.red;
        let details = '';
        
        if (etape.ecritures) {
          details = `${etape.ecritures.length} écriture(s)`;
        } else if (etape.resultat) {
          details = `Résultat: ${etape.resultat.resultatNet.toFixed(2)}`;
        } else if (etape.exerciceSuivant) {
          details = `Exercice ${etape.exerciceSuivant.libelle} créé`;
        } else if (etape.message) {
          details = etape.message;
        }
        
        table.push([
          etape.etape,
          statutColor(etape.statut),
          details
        ]);
      });
      
      console.log(table.toString());
      
      console.log(chalk.green('\n🎉 Clôture d\'exercice terminée avec succès!'));
      
    } catch (error) {
      spinner.fail('Échec de la clôture');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// Commande pour rouvrir un exercice
program
  .command('exercices:rouvrir <id>')
  .description('Rouvrir un exercice clôturé')
  .action(requireAuth(async (id) => {
    // Confirmation
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Êtes-vous sûr de vouloir rouvrir cet exercice ? Cela annulera la clôture.',
        default: false
      }
    ]);
    
    if (!confirm) {
      console.log(chalk.yellow('Réouverture annulée'));
      return;
    }
    
    const spinner = ora('Réouverture de l\'exercice...').start();
    
    try {
      const response = await api.post(`/exercises/${id}/reopen`);
      spinner.succeed('Exercice rouvert avec succès');
      
      const reouverture = response.data.data;
      
      console.log(chalk.bold(`\nRéouverture terminée - Exercice ${reouverture.exercice.libelle}`));
      console.log(`Nouveau statut: ${reouverture.exercice.statut}`);
      console.log(`Date de réouverture: ${new Date(reouverture.dateReouverture).toLocaleString()}`);
      
      console.log(chalk.green('\n✅ Exercice rouvert avec succès!'));
      console.log(chalk.yellow('Vous pouvez maintenant modifier les écritures de cet exercice.'));
      
    } catch (error) {
      spinner.fail('Échec de la réouverture');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// Commande pour afficher le statut de clôture
program
  .command('exercices:statut-cloture <id>')
  .description('Afficher le statut de clôture d\'un exercice')
  .action(requireAuth(async (id) => {
    const spinner = ora('Récupération du statut de clôture...').start();
    
    try {
      const response = await api.get(`/exercises/${id}/closure-status`);
      spinner.succeed('Statut récupéré');
      
      const statut = response.data.data;
      
      console.log(chalk.bold(`\nStatut de clôture - Exercice ${statut.exercice.libelle}`));
      console.log(`Période: ${statut.exercice.dateDebut} au ${statut.exercice.dateFin}`);
      console.log(`Statut actuel: ${statut.exercice.statut}`);
      
      if (statut.exercice.dateCloture) {
        console.log(`Date de clôture: ${new Date(statut.exercice.dateCloture).toLocaleString()}`);
        console.log(`Résultat: ${statut.exercice.resultatExercice.toFixed(2)}`);
      }
      
      // Actions disponibles
      console.log(chalk.bold('\nActions disponibles:'));
      
      const table = new Table({
        head: [
          chalk.cyan('Action'), 
          chalk.cyan('Description'), 
          chalk.cyan('Disponible')
        ]
      });
      
      statut.actions.forEach(action => {
        const disponible = statut.peutCloture || statut.peutRouvrir || action.code === 'VALIDER_PREREQUIS' || action.code === 'SIMULER_CLOTURE';
        const disponibleColor = disponible ? chalk.green('✓') : chalk.red('✗');
        
        table.push([
          action.libelle,
          action.description,
          disponibleColor
        ]);
      });
      
      console.log(table.toString());
      
      // Recommandations
      if (statut.validation && statut.validation.recommandations && statut.validation.recommandations.length > 0) {
        console.log(chalk.bold('\nRecommandations:'));
        statut.validation.recommandations.forEach((rec, index) => {
          console.log(`${index + 1}. ${rec.message}`);
        });
      }
      
    } catch (error) {
      spinner.fail('Échec de la récupération du statut');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour vue d'ensemble des clôtures
program
  .command('exercices:vue-clotures')
  .description('Vue d\'ensemble des clôtures d\'exercices')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const spinner = ora('Récupération de la vue d\'ensemble...').start();
    
    try {
      const response = await api.get('/exercises/closure-overview', {
        params: { societeId: config.currentSociete.id }
      });
      
      spinner.succeed('Vue d\'ensemble récupérée');
      
      const data = response.data.data;
      
      console.log(chalk.bold(`\nVue d'ensemble des clôtures - ${config.currentSociete.nom}`));
      
      // Statistiques
      console.log(chalk.bold('\nStatistiques:'));
      console.log(`Total exercices: ${data.statistiques.total}`);
      console.log(`Exercices ouverts: ${chalk.green(data.statistiques.ouverts)}`);
      console.log(`Exercices clôturés: ${chalk.blue(data.statistiques.clotures)}`);
      console.log(`Exercices en brouillon: ${chalk.yellow(data.statistiques.brouillons)}`);
      console.log(`Prêts pour clôture: ${chalk.green(data.statistiques.pretsPourCloture)}`);
      
      // Liste des exercices
      console.log(chalk.bold('\nListe des exercices:'));
      
      const table = new Table({
        head: [
          chalk.cyan('Libellé'), 
          chalk.cyan('Période'), 
          chalk.cyan('Statut'),
          chalk.cyan('Résultat'),
          chalk.cyan('Prêt clôture'),
          chalk.cyan('Actions')
        ]
      });
      
      data.exercices.forEach(exercice => {
        let statutColor;
        switch (exercice.exercice.statut) {
          case 'OUVERT':
            statutColor = chalk.green;
            break;
          case 'CLOTURE':
            statutColor = chalk.blue;
            break;
          case 'BROUILLON':
            statutColor = chalk.yellow;
            break;
          default:
            statutColor = chalk.white;
        }
        
        const pretCloture = exercice.peutCloture ? chalk.green('✓') : chalk.red('✗');
        const resultat = exercice.exercice.resultatExercice ? 
          exercice.exercice.resultatExercice.toFixed(2) : 'N/A';
        
        const actions = exercice.actions.map(a => a.code).join(', ');
        
        table.push([
          exercice.exercice.libelle,
          `${exercice.exercice.dateDebut} - ${exercice.exercice.dateFin}`,
          statutColor(exercice.exercice.statut),
          resultat,
          pretCloture,
          actions.substring(0, 20) + (actions.length > 20 ? '...' : '')
        ]);
      });
      
      console.log(table.toString());
      
    } catch (error) {
      spinner.fail('Échec de la récupération de la vue d\'ensemble');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// === JOURNAUX ===

// Commande pour lister les journaux
program
  .command('journaux:list')
  .description('Lister tous les journaux comptables')
  .option('-t, --type <type>', 'Filtrer par type de journal')
  .option('-a, --actifs-seulement', 'Afficher seulement les journaux actifs')
  .action(requireAuth(async (options) => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const spinner = ora('Récupération des journaux...').start();
    
    try {
      let url = '/journaux';
      const params = new URLSearchParams();
      params.append('societeId', config.currentSociete.id);
      
      if (options.type) params.append('type', options.type);
      if (options.actifsSeulement) url = '/journaux/actifs';
      
      const response = await api.get(`${url}?${params.toString()}`);
      spinner.succeed(`${response.data.data.length} journaux trouvés`);
      
      if (response.data.data.length === 0) {
        console.log(chalk.yellow('Aucun journal trouvé'));
        return;
      }
      
      const table = new Table({
        head: [
          chalk.cyan('Code'), 
          chalk.cyan('Libellé'), 
          chalk.cyan('Type'), 
          chalk.cyan('Actif'),
          chalk.cyan('Séquence')
        ]
      });
      
      response.data.data.forEach(journal => {
        const actif = journal.actif ? chalk.green('✓') : chalk.red('✗');
        const sequence = journal.prochainNumero || 'N/A';
        
        table.push([
          journal.code,
          journal.libelle,
          journal.type,
          actif,
          sequence
        ]);
      });
      
      console.log(table.toString());
    } catch (error) {
      spinner.fail('Échec de la récupération des journaux');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour créer un journal
program
  .command('journaux:create')
  .description('Créer un nouveau journal comptable')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'code',
        message: 'Code du journal (ex: VT, AC, BQ):',
        validate: (input) => {
          if (!input || input.length < 2 || input.length > 10) {
            return 'Le code doit contenir entre 2 et 10 caractères';
          }
          return true;
        }
      },
      {
        type: 'input',
        name: 'libelle',
        message: 'Libellé du journal:',
        validate: (input) => input.length >= 3 ? true : 'Le libellé doit contenir au moins 3 caractères'
      },
      {
        type: 'list',
        name: 'type',
        message: 'Type de journal:',
        choices: [
          { name: 'Ventes', value: 'VENTE' },
          { name: 'Achats', value: 'ACHAT' },
          { name: 'Banque', value: 'BANQUE' },
          { name: 'Caisse', value: 'CAISSE' },
          { name: 'Opérations diverses', value: 'OD' },
          { name: 'À nouveaux', value: 'AN' },
          { name: 'Paie', value: 'PAIE' }
        ]
      },
      {
        type: 'input',
        name: 'description',
        message: 'Description (optionnel):'
      }
    ]);

    const spinner = ora('Création du journal...').start();
    
    try {
      const journal = {
        ...answers,
        societeId: config.currentSociete.id,
        actif: true
      };
      
      const response = await api.post('/journaux', journal);
      spinner.succeed('Journal créé avec succès');
      console.log(chalk.green(`Code: ${response.data.data.code}`));
      console.log(chalk.green(`Libellé: ${response.data.data.libelle}`));
    } catch (error) {
      spinner.fail('Échec de la création du journal');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === TEMPLATES ===

// Commande pour lister les templates
program
  .command('templates:list')
  .description('Lister les modèles d\'écritures')
  .option('-c, --categorie <categorie>', 'Filtrer par catégorie')
  .option('-p, --publics', 'Afficher seulement les templates publics')
  .action(requireAuth(async (options) => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const spinner = ora('Récupération des templates...').start();
    
    try {
      const params = new URLSearchParams();
      params.append('societeId', config.currentSociete.id);
      
      if (options.categorie) params.append('categorie', options.categorie);
      if (options.publics) params.append('public', 'true');
      
      const response = await api.get(`/templates?${params.toString()}`);
      spinner.succeed(`${response.data.data.length} templates trouvés`);
      
      if (response.data.data.length === 0) {
        console.log(chalk.yellow('Aucun template trouvé'));
        return;
      }
      
      const table = new Table({
        head: [
          chalk.cyan('Nom'), 
          chalk.cyan('Catégorie'), 
          chalk.cyan('Journal'), 
          chalk.cyan('Public'),
          chalk.cyan('Lignes')
        ]
      });
      
      response.data.data.forEach(template => {
        const isPublic = template.public ? chalk.green('✓') : chalk.red('✗');
        const nbLignes = template.lignes ? template.lignes.length : 0;
        
        table.push([
          template.nom,
          template.categorie,
          template.journalCode,
          isPublic,
          nbLignes
        ]);
      });
      
      console.log(table.toString());
    } catch (error) {
      spinner.fail('Échec de la récupération des templates');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour créer un template
program
  .command('templates:create')
  .description('Créer un nouveau modèle d\'écriture')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    // D'abord récupérer les journaux disponibles
    const journauxResponse = await api.get(`/journaux?societeId=${config.currentSociete.id}`);
    const journaux = journauxResponse.data.data;
    
    if (journaux.length === 0) {
      console.error(chalk.red('Aucun journal disponible. Créez d\'abord un journal.'));
      return;
    }
    
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'nom',
        message: 'Nom du template:',
        validate: (input) => input.length >= 3 ? true : 'Le nom doit contenir au moins 3 caractères'
      },
      {
        type: 'input',
        name: 'libelle',
        message: 'Libellé par défaut:',
        validate: (input) => input.length >= 2 ? true : 'Le libellé doit contenir au moins 2 caractères'
      },
      {
        type: 'list',
        name: 'journalCode',
        message: 'Journal:',
        choices: journaux.map(j => ({ name: `${j.code} - ${j.libelle}`, value: j.code }))
      },
      {
        type: 'list',
        name: 'categorie',
        message: 'Catégorie:',
        choices: [
          { name: 'Vente', value: 'VENTE' },
          { name: 'Achat', value: 'ACHAT' },
          { name: 'Banque', value: 'BANQUE' },
          { name: 'Caisse', value: 'CAISSE' },
          { name: 'Paie', value: 'PAIE' },
          { name: 'Amortissement', value: 'AMORTISSEMENT' },
          { name: 'Provision', value: 'PROVISION' },
          { name: 'Autre', value: 'AUTRE' }
        ]
      },
      {
        type: 'input',
        name: 'description',
        message: 'Description (optionnel):'
      },
      {
        type: 'confirm',
        name: 'public',
        message: 'Template public (accessible à toutes les sociétés)?',
        default: false
      }
    ]);

    console.log(chalk.yellow('\nAjout des lignes du template:'));
    const lignes = [];
    let continuer = true;
    
    while (continuer) {
      const ligne = await inquirer.prompt([
        {
          type: 'input',
          name: 'compteNumero',
          message: `Ligne ${lignes.length + 1} - Numéro de compte:`,
          validate: (input) => input.length >= 3 ? true : 'Le numéro de compte doit contenir au moins 3 caractères'
        },
        {
          type: 'list',
          name: 'sens',
          message: 'Sens:',
          choices: [
            { name: 'Débit', value: 'DEBIT' },
            { name: 'Crédit', value: 'CREDIT' }
          ]
        },
        {
          type: 'input',
          name: 'libelle',
          message: 'Libellé de la ligne (optionnel):'
        },
        {
          type: 'input',
          name: 'montantFormule',
          message: 'Formule de montant (ex: {montant}, {montant}*0.18):',
          default: '{montant}'
        }
      ]);
      
      lignes.push(ligne);
      
      const { ajouterLigne } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'ajouterLigne',
          message: 'Ajouter une autre ligne?',
          default: false
        }
      ]);
      
      continuer = ajouterLigne;
    }

    const spinner = ora('Création du template...').start();
    
    try {
      const template = {
        donnees: {
          ...answers,
          societeId: config.currentSociete.id
        },
        lignes
      };
      
      const response = await api.post('/templates', template);
      spinner.succeed('Template créé avec succès');
      console.log(chalk.green(`ID: ${response.data.data.id}`));
      console.log(chalk.green(`Nom: ${response.data.data.nom}`));
      console.log(chalk.green(`Nombre de lignes: ${lignes.length}`));
    } catch (error) {
      spinner.fail('Échec de la création du template');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === LETTRAGE ===

// Commande pour lister les comptes à lettrer
program
  .command('lettrage:comptes')
  .description('Lister les comptes avec des écritures non lettrées')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const spinner = ora('Récupération des comptes à lettrer...').start();
    
    try {
      const response = await api.get(`/lettrage/comptes-a-lettrer?societeId=${config.currentSociete.id}`);
      spinner.succeed(`${response.data.data.length} comptes avec écritures non lettrées`);
      
      if (response.data.data.length === 0) {
        console.log(chalk.green('Tous les comptes sont lettrés !'));
        return;
      }
      
      const table = new Table({
        head: [
          chalk.cyan('Compte'), 
          chalk.cyan('Libellé'), 
          chalk.cyan('Écritures non lettrées'), 
          chalk.cyan('Solde non lettré')
        ]
      });
      
      response.data.data.forEach(compte => {
        table.push([
          compte.numero,
          compte.libelle,
          compte.nbEcrituresNonLettrees,
          compte.soldeNonLettre.toFixed(2)
        ]);
      });
      
      console.log(table.toString());
    } catch (error) {
      spinner.fail('Échec de la récupération des comptes');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour lettrage automatique
program
  .command('lettrage:auto <compteNumero>')
  .description('Effectuer un lettrage automatique sur un compte')
  .option('-t, --tolerance <montant>', 'Tolérance de montant', '0')
  .action(requireAuth(async (compteNumero, options) => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const spinner = ora('Lettrage automatique en cours...').start();
    
    try {
      const payload = {
        compteNumero,
        societeId: config.currentSociete.id,
        criteres: {
          toleranceMontant: parseFloat(options.tolerance)
        }
      };
      
      const response = await api.post('/lettrage/automatique', payload);
      spinner.succeed('Lettrage automatique terminé');
      
      const stats = response.data.data;
      console.log(chalk.green(`\nRésultats du lettrage automatique:`));
      console.log(`Groupes lettrés: ${stats.groupesLettres}`);
      console.log(`Écritures lettrées: ${stats.ecrituresLettrees}`);
      console.log(`Montant total lettré: ${stats.montantTotalLettre.toFixed(2)}`);
      
      if (stats.details && stats.details.length > 0) {
        console.log(chalk.yellow('\nDétails des lettrages:'));
        stats.details.forEach((detail, index) => {
          console.log(`${index + 1}. Code: ${detail.codeLettrage}, Écritures: ${detail.nbEcritures}, Montant: ${detail.montant.toFixed(2)}`);
        });
      }
      
    } catch (error) {
      spinner.fail('Échec du lettrage automatique');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === TIERS/PARTIES ===

// Commande pour lister les tiers
program
  .command('tiers:list')
  .description('Lister tous les tiers')
  .option('-t, --type <type>', 'Filtrer par type (CLIENT, FOURNISSEUR, AUTRE)')
  .option('-a, --actifs', 'Afficher seulement les tiers actifs')
  .action(requireAuth(async (options) => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const spinner = ora('Récupération des tiers...').start();
    
    try {
      const params = new URLSearchParams();
      params.append('societeId', config.currentSociete.id);
      
      if (options.type) params.append('type', options.type);
      if (options.actifs) params.append('actif', 'true');
      
      const response = await api.get(`/parties?${params.toString()}`);
      spinner.succeed(`${response.data.data.length} tiers trouvés`);
      
      if (response.data.data.length === 0) {
        console.log(chalk.yellow('Aucun tiers trouvé'));
        return;
      }
      
      const table = new Table({
        head: [
          chalk.cyan('Code'), 
          chalk.cyan('Nom'), 
          chalk.cyan('Type'), 
          chalk.cyan('Email'),
          chalk.cyan('Actif')
        ]
      });
      
      response.data.data.forEach(tiers => {
        const actif = tiers.actif ? chalk.green('✓') : chalk.red('✗');
        
        table.push([
          tiers.code,
          tiers.nom,
          tiers.type,
          tiers.email || 'N/A',
          actif
        ]);
      });
      
      console.log(table.toString());
    } catch (error) {
      spinner.fail('Échec de la récupération des tiers');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour créer un tiers
program
  .command('tiers:create')
  .description('Créer un nouveau tiers')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'code',
        message: 'Code du tiers:',
        validate: (input) => input.length >= 2 ? true : 'Le code doit contenir au moins 2 caractères'
      },
      {
        type: 'input',
        name: 'nom',
        message: 'Nom du tiers:',
        validate: (input) => input.length >= 2 ? true : 'Le nom doit contenir au moins 2 caractères'
      },
      {
        type: 'list',
        name: 'type',
        message: 'Type de tiers:',
        choices: [
          { name: 'Client', value: 'CLIENT' },
          { name: 'Fournisseur', value: 'FOURNISSEUR' },
          { name: 'Autre', value: 'AUTRE' }
        ]
      },
      {
        type: 'input',
        name: 'email',
        message: 'Email (optionnel):'
      },
      {
        type: 'input',
        name: 'telephone',
        message: 'Téléphone (optionnel):'
      },
      {
        type: 'input',
        name: 'adresse',
        message: 'Adresse (optionnel):'
      }
    ]);

    const spinner = ora('Création du tiers...').start();
    
    try {
      const tiers = {
        ...answers,
        societeId: config.currentSociete.id,
        actif: true
      };
      
      const response = await api.post('/parties', tiers);
      spinner.succeed('Tiers créé avec succès');
      console.log(chalk.green(`ID: ${response.data.data.id}`));
      console.log(chalk.green(`Code: ${response.data.data.code}`));
      console.log(chalk.green(`Nom: ${response.data.data.nom}`));
    } catch (error) {
      spinner.fail('Échec de la création du tiers');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === IMPORT/EXPORT ===

// Commande pour importer des données
program
  .command('import:ecritures <fichier>')
  .description('Importer des écritures depuis un fichier Excel')
  .option('-j, --journal <code>', 'Code du journal par défaut')
  .option('-v, --valider', 'Valider automatiquement les écritures importées')
  .action(requireAuth(async (fichier, options) => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const fs = require('fs');
    const FormData = require('form-data');
    
    if (!fs.existsSync(fichier)) {
      console.error(chalk.red(`Fichier non trouvé: ${fichier}`));
      return;
    }
    
    const spinner = ora('Import des écritures en cours...').start();
    
    try {
      const form = new FormData();
      form.append('file', fs.createReadStream(fichier));
      form.append('societeId', config.currentSociete.id);
      
      if (options.journal) form.append('journalDefaut', options.journal);
      if (options.valider) form.append('validerAutomatiquement', 'true');
      
      const response = await api.post('/import-export/ecritures/import', form, {
        headers: {
          ...form.getHeaders(),
          'Authorization': `Bearer ${global.apiKey}`
        }
      });
      
      spinner.succeed('Import terminé avec succès');
      
      const stats = response.data.data;
      console.log(chalk.green(`\nRésultats de l'import:`));
      console.log(`Écritures importées: ${stats.ecrituresImportees}`);
      console.log(`Lignes importées: ${stats.lignesImportees}`);
      
      if (stats.erreurs && stats.erreurs.length > 0) {
        console.log(chalk.yellow(`\nErreurs (${stats.erreurs.length}):`));
        stats.erreurs.slice(0, 10).forEach((erreur, index) => {
          console.log(`${index + 1}. Ligne ${erreur.ligne}: ${erreur.message}`);
        });
        if (stats.erreurs.length > 10) {
          console.log(chalk.gray(`... et ${stats.erreurs.length - 10} autres erreurs`));
        }
      }
      
    } catch (error) {
      spinner.fail('Échec de l\'import');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// Commande pour exporter des données
program
  .command('export:ecritures')
  .description('Exporter les écritures vers un fichier Excel')
  .option('-j, --journal <code>', 'Filtrer par journal')
  .option('-d, --debut <date>', 'Date de début (YYYY-MM-DD)')
  .option('-f, --fin <date>', 'Date de fin (YYYY-MM-DD)')
  .option('-o, --output <fichier>', 'Nom du fichier de sortie', 'export_ecritures.xlsx')
  .action(requireAuth(async (options) => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const spinner = ora('Export des écritures en cours...').start();
    
    try {
      const params = new URLSearchParams();
      params.append('societeId', config.currentSociete.id);
      params.append('format', 'excel');
      
      if (options.journal) params.append('journalCode', options.journal);
      if (options.debut) params.append('dateDebut', options.debut);
      if (options.fin) params.append('dateFin', options.fin);
      
      const response = await api.get(`/import-export/ecritures/export?${params.toString()}`, {
        responseType: 'stream'
      });
      
      const fs = require('fs');
      const writer = fs.createWriteStream(options.output);
      response.data.pipe(writer);
      
      writer.on('finish', () => {
        spinner.succeed(`Export terminé: ${options.output}`);
      });
      
      writer.on('error', (error) => {
        spinner.fail('Erreur lors de l\'écriture du fichier');
        console.error(chalk.red(`Erreur: ${error.message}`));
      });
      
    } catch (error) {
      spinner.fail('Échec de l\'export');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || 'Erreur lors de l\'export'}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === RAPPORTS ===

// Commande pour générer un rapport
program
  .command('rapports:generer')
  .description('Générer un rapport comptable')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'type',
        message: 'Type de rapport:',
        choices: [
          { name: 'Balance générale', value: 'balance' },
          { name: 'Grand livre', value: 'grand-livre' },
          { name: 'Journal', value: 'journal' },
          { name: 'Balance âgée', value: 'balance-agee' },
          { name: 'Compte de résultat', value: 'compte-resultat' },
          { name: 'Bilan', value: 'bilan' }
        ]
      },
      {
        type: 'input',
        name: 'dateDebut',
        message: 'Date de début (YYYY-MM-DD):',
        validate: (input) => /^\d{4}-\d{2}-\d{2}$/.test(input) ? true : 'Format de date invalide'
      },
      {
        type: 'input',
        name: 'dateFin',
        message: 'Date de fin (YYYY-MM-DD):',
        validate: (input) => /^\d{4}-\d{2}-\d{2}$/.test(input) ? true : 'Format de date invalide'
      },
      {
        type: 'list',
        name: 'format',
        message: 'Format de sortie:',
        choices: [
          { name: 'PDF', value: 'pdf' },
          { name: 'Excel', value: 'excel' },
          { name: 'JSON', value: 'json' }
        ]
      }
    ]);
    
    const spinner = ora('Génération du rapport...').start();
    
    try {
      const params = {
        societeId: config.currentSociete.id,
        dateDebut: answers.dateDebut,
        dateFin: answers.dateFin,
        format: answers.format
      };
      
      const response = await api.post(`/reports/${answers.type}`, params, {
        responseType: answers.format === 'json' ? 'json' : 'stream'
      });
      
      if (answers.format === 'json') {
        spinner.succeed('Rapport généré');
        console.log(JSON.stringify(response.data, null, 2));
      } else {
        const extension = answers.format === 'pdf' ? 'pdf' : 'xlsx';
        const filename = `rapport_${answers.type}_${Date.now()}.${extension}`;
        
        const fs = require('fs');
        const writer = fs.createWriteStream(filename);
        response.data.pipe(writer);
        
        writer.on('finish', () => {
          spinner.succeed(`Rapport généré: ${filename}`);
        });
        
        writer.on('error', (error) => {
          spinner.fail('Erreur lors de l\'écriture du fichier');
          console.error(chalk.red(`Erreur: ${error.message}`));
        });
      }
      
    } catch (error) {
      spinner.fail('Échec de la génération du rapport');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || 'Erreur lors de la génération'}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === PARAMÈTRES ===

// Commande pour lister les paramètres
program
  .command('parametres:list')
  .description('Lister les paramètres de la société')
  .action(requireAuth(async () => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const spinner = ora('Récupération des paramètres...').start();
    
    try {
      const response = await api.get(`/parametres?societeId=${config.currentSociete.id}`);
      spinner.succeed('Paramètres récupérés');
      
      if (response.data.data.length === 0) {
        console.log(chalk.yellow('Aucun paramètre configuré'));
        return;
      }
      
      const table = new Table({
        head: [
          chalk.cyan('Clé'), 
          chalk.cyan('Valeur'), 
          chalk.cyan('Description')
        ]
      });
      
      response.data.data.forEach(param => {
        table.push([
          param.cle,
          param.valeur,
          param.description || 'N/A'
        ]);
      });
      
      console.log(table.toString());
    } catch (error) {
      spinner.fail('Échec de la récupération des paramètres');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour définir un paramètre
program
  .command('parametres:set <cle> <valeur>')
  .description('Définir un paramètre')
  .option('-d, --description <desc>', 'Description du paramètre')
  .action(requireAuth(async (cle, valeur, options) => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const spinner = ora('Configuration du paramètre...').start();
    
    try {
      const parametre = {
        cle,
        valeur,
        societeId: config.currentSociete.id
      };
      
      if (options.description) {
        parametre.description = options.description;
      }
      
      const response = await api.post('/parametres', parametre);
      spinner.succeed('Paramètre configuré');
      console.log(chalk.green(`${cle} = ${valeur}`));
    } catch (error) {
      spinner.fail('Échec de la configuration du paramètre');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// === PLAN COMPTABLE ===

// Commande pour afficher le plan comptable personnalisé
program
  .command('plan:personnalise')
  .description('Afficher le plan comptable personnalisé de la société')
  .option('-c, --classe <classe>', 'Filtrer par classe (1-8)')
  .action(requireAuth(async (options) => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const spinner = ora('Récupération du plan comptable personnalisé...').start();
    
    try {
      const params = new URLSearchParams();
      params.append('societeId', config.currentSociete.id);
      
      if (options.classe) params.append('classe', options.classe);
      
      const response = await api.get(`/plan-comptable/personnalise?${params.toString()}`);
      spinner.succeed(`${response.data.data.length} comptes personnalisés trouvés`);
      
      if (response.data.data.length === 0) {
        console.log(chalk.yellow('Aucun compte personnalisé trouvé'));
        return;
      }
      
      const table = new Table({
        head: [
          chalk.cyan('Numéro'), 
          chalk.cyan('Libellé'), 
          chalk.cyan('Type'), 
          chalk.cyan('Actif')
        ]
      });
      
      response.data.data.forEach(compte => {
        const actif = compte.actif ? chalk.green('✓') : chalk.red('✗');
        
        table.push([
          compte.numero,
          compte.libelle,
          compte.type,
          actif
        ]);
      });
      
      console.log(table.toString());
    } catch (error) {
      spinner.fail('Échec de la récupération du plan comptable');
      console.error(chalk.red(`Erreur: ${error.message}`));
    }
  }));

// Commande pour personnaliser un compte
program
  .command('plan:personnaliser <numero>')
  .description('Personnaliser un compte du plan comptable')
  .action(requireAuth(async (numero) => {
    if (!config.currentSociete) {
      console.error(chalk.red('Aucune société sélectionnée'));
      console.log(chalk.yellow('Utilisez la commande "societes:select" pour sélectionner une société'));
      return;
    }
    
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'libelle',
        message: 'Nouveau libellé du compte:',
        validate: (input) => input.length >= 3 ? true : 'Le libellé doit contenir au moins 3 caractères'
      },
      {
        type: 'confirm',
        name: 'actif',
        message: 'Compte actif?',
        default: true
      },
      {
        type: 'input',
        name: 'description',
        message: 'Description (optionnel):'
      }
    ]);
    
    const spinner = ora('Personnalisation du compte...').start();
    
    try {
      const personnalisation = {
        numero,
        societeId: config.currentSociete.id,
        ...answers
      };
      
      const response = await api.post('/plan-comptable/personnaliser', personnalisation);
      spinner.succeed('Compte personnalisé avec succès');
      console.log(chalk.green(`Compte ${numero} personnalisé`));
    } catch (error) {
      spinner.fail('Échec de la personnalisation du compte');
      if (error.response) {
        console.error(chalk.red(`Erreur: ${error.response.data.message || JSON.stringify(error.response.data)}`));
      } else {
        console.error(chalk.red(`Erreur: ${error.message}`));
      }
    }
  }));

// Exécution du programme
program.parse(process.argv);

// Si aucune commande n'est spécifiée, afficher l'aide
if (!process.argv.slice(2).length) {
  program.outputHelp();
}