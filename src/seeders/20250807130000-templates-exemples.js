'use strict';

const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Récupérer une société et des journaux pour les exemples
    const societes = await queryInterface.sequelize.query(
      'SELECT id FROM societes LIMIT 1',
      { type: Sequelize.QueryTypes.SELECT }
    );

    const journaux = await queryInterface.sequelize.query(
      'SELECT code FROM journaux WHERE code IN (\'VT\', \'AC\', \'BQ\', \'CA\', \'OD\')',
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (societes.length === 0 || journaux.length === 0) {
      console.log('Aucune société ou journal trouvé, skip des templates d\'exemple');
      return;
    }

    const societeId = societes[0].id;
    const journalVT = journaux.find(j => j.code === 'VT')?.code || journaux[0].code;
    const journalAC = journaux.find(j => j.code === 'AC')?.code || journaux[0].code;
    const journalBQ = journaux.find(j => j.code === 'BQ')?.code || journaux[0].code;
    const journalCA = journaux.find(j => j.code === 'CA')?.code || journaux[0].code;

    // Templates d'exemple (simplifiés avec comptes existants)
    const templates = [
      {
        id: uuidv4(),
        nom: 'Encaissement client',
        description: 'Template pour l\'encaissement d\'un client',
        libelle: 'Encaissement {{client}} - {{mode_paiement}}',
        journal_code: journalBQ,
        categorie: 'BANQUE',
        societe_id: societeId,
        actif: true,
        public: true,
        tags_recherche: 'encaissement client banque paiement',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        nom: 'Paiement fournisseur',
        description: 'Template pour le paiement d\'un fournisseur',
        libelle: 'Paiement {{fournisseur}} - {{mode_paiement}}',
        journal_code: journalBQ,
        categorie: 'BANQUE',
        societe_id: societeId,
        actif: true,
        public: true,
        tags_recherche: 'paiement fournisseur banque',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        nom: 'Dépense de caisse',
        description: 'Template pour une dépense de caisse',
        libelle: 'Dépense caisse - {{motif}}',
        journal_code: journalCA,
        categorie: 'CAISSE',
        societe_id: societeId,
        actif: true,
        public: true,
        tags_recherche: 'caisse dépense sortie',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // Insérer les templates
    await queryInterface.bulkInsert('template_ecritures', templates);

    // Lignes pour le template "Encaissement client"
    const lignesEncaissement = [
      {
        id: uuidv4(),
        template_id: templates[0].id,
        compte_numero: '521',
        libelle: 'Banque',
        sens: 'DEBIT',
        formule_montant: '{{montant}}',
        ordre: 1,
        obligatoire: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        template_id: templates[0].id,
        compte_numero: '411',
        libelle: 'Client {{client}}',
        sens: 'CREDIT',
        formule_montant: '{{montant}}',
        ordre: 2,
        obligatoire: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // Lignes pour le template "Paiement fournisseur"
    const lignesPaiement = [
      {
        id: uuidv4(),
        template_id: templates[1].id,
        compte_numero: '401',
        libelle: 'Fournisseur {{fournisseur}}',
        sens: 'DEBIT',
        formule_montant: '{{montant}}',
        ordre: 1,
        obligatoire: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        template_id: templates[1].id,
        compte_numero: '521',
        libelle: 'Banque',
        sens: 'CREDIT',
        formule_montant: '{{montant}}',
        ordre: 2,
        obligatoire: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // Lignes pour le template "Dépense de caisse"
    const lignesDepense = [
      {
        id: uuidv4(),
        template_id: templates[2].id,
        compte_numero: '334',
        libelle: '{{motif}}',
        sens: 'DEBIT',
        formule_montant: '{{montant}}',
        ordre: 1,
        obligatoire: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        template_id: templates[2].id,
        compte_numero: '571',
        libelle: 'Caisse',
        sens: 'CREDIT',
        formule_montant: '{{montant}}',
        ordre: 2,
        obligatoire: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    // Insérer toutes les lignes
    const toutesLignes = [
      ...lignesEncaissement,
      ...lignesPaiement,
      ...lignesDepense
    ];

    await queryInterface.bulkInsert('template_ligne_ecritures', toutesLignes);

    console.log(`✅ ${templates.length} templates d'exemple créés avec ${toutesLignes.length} lignes`);
  },

  async down(queryInterface, Sequelize) {
    // Supprimer les lignes de templates
    await queryInterface.bulkDelete('template_ligne_ecritures', {
      template_id: {
        [Sequelize.Op.in]: queryInterface.sequelize.literal(
          '(SELECT id FROM template_ecritures WHERE public = true)'
        )
      }
    });

    // Supprimer les templates publics (exemples)
    await queryInterface.bulkDelete('template_ecritures', {
      public: true
    });
  }
};
